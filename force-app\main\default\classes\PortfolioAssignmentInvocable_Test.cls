@isTest
public with sharing class PortfolioAssignmentInvocable_Test {
    
    @isTest
    static void callTest() {
        Test.setMock(HttpCalloutMock.class, new PortfolioAssignment_Mock());
        PortfolioAssignmentInvocable.InvocableRequest request = new PortfolioAssignmentInvocable.InvocableRequest();
        request.operation = 'assign';
        request.agencyCode = 'AG0001';
        request.contractId = 'CO0001';
        request.cip = 'CIP0001';
        List<PortfolioAssignmentInvocable.InvocableRequest> requests = new List<PortfolioAssignmentInvocable.InvocableRequest>{ request };

        Test.startTest();
        PortfolioAssignmentInvocable.call(requests);
        Test.stopTest();
    }

    @isTest
    static void callErrorTest() {
        Test.setMock(HttpCalloutMock.class, new PortfolioAssignmentError_Mock());
        PortfolioAssignmentInvocable.InvocableRequest request = new PortfolioAssignmentInvocable.InvocableRequest();
        request.operation = 'assign';
        request.agencyCode = 'AG0001';
        request.contractId = 'CO0001';
        request.cip = 'CIP0001';
        List<PortfolioAssignmentInvocable.InvocableRequest> requests = new List<PortfolioAssignmentInvocable.InvocableRequest>{ request };

        try {
            Test.startTest();
            PortfolioAssignmentInvocable.call(requests);
            Test.stopTest();

            //Assert.fail('An exception should have been thrown');
        } catch (Exception e) {
            //Assert.isTrue(true);
        }
    }

    public with sharing class PortfolioAssignment_Mock implements HttpCalloutMock {
        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');
            res.setBody('{"errorCode": "500", "errorDescription": "Internal Server Error"}');
            res.setStatusCode(200);
            return res;
        }
    }

    public with sharing class PortfolioAssignmentError_Mock implements HttpCalloutMock {
        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');
            res.setBody('{"result": "OK"}');
            res.setStatusCode(500);
            return res;
        }
    }
}