/*********************************************************************************
* <AUTHOR>
* @description    Agency Engine class for the ranking of the existing agencies
* @date           2024-12-24
* @group          Agency Engine V2
**********************************************************************************/
public without sharing class EngineAgencyRanking_V2 {
    public class AgencyRankingException extends Exception {}

    // Constants
    private final static String MOTOR_PARAMETERS_MATRIX = 'Parametri_Motore';
    private final static String LOOKUP_TABLE_INPUT_MERITOCRATIC = 'Meritocratico';
    private final static String BEYOND_THE_CAP = 'Oltre il CAP';
    private final static String MAIN_SALESPOINT_SUBTYPE = 'Punto vendita agenziale';
    private final static String PROSPECT_PURO = 'Prospect Puro';
    private final static String CLIENT_EX_CLIENTE_2_ANNI = 'Cliente Esistente o Ex cliente entro 2 anni';
    private final static String PROSPECT_ANAGRAFATO =  'Prospect Anagrafato';
    private final static String PRODOTTO_UNICA=  'Unica';
    private final static String PRODOTTO_PREVIDENZA=  'Previdenza';
    private final static String PRODOTTO_UNISALUTE=  'UniSalute';
    private final static String PRODOTTO_BPER=  'BPER';    
    private static String agencyCodeForProAnagClienteExCliente2Anni = '';
    private static Set<Account> agencyFilterdList = new Set<Account>(); 
    private static Set<String> agencyFilteredIdList= new Set<String>(); 


    private final static Integer TOP_AGENCIES_LIMIT = 3;
    private final static List<String> AGENCY_VALID_TYPES = new List<String> {'Privata', 'Assicop', 'Sogeint'};    
    private  static Map<String,Set<Account>> clientTypeAndAgencyId = new Map<String,Set<Account>>();
    private final static Map<String, String> AREAS_TO_DISCRETIONALITY_FIELDS = new Map<String, String> {
        'Motor' => 'DiscretionalityMotor__c', 
        'Welfare' => 'DiscretionalityWelfare__c', 
        'Property' => 'DiscretionalityProperty__c',
        'Enterprise' => 'DiscretionalityEnterprise__c'
    };
    @TestVisible private static List<CalculationMatrixRow> testingMatrixRows;

    @TestVisible private static String mockedResponse = '{\n' +
    '    "isSuccess": true,\n' +
    '    "subjectType": "Prospect Puro",\n' +
    '    "matchingSalespoints": [\n' +
    '        {\n' +
    '            "salespointId": "a1B5g0000001XYZ",\n' +
    '            "name": "SalesPoint 1",\n' +
    '            "score": 95.5,\n' +
    '            "distance": 12.3,\n' +
    '            "discretionality": false,\n' +
    '            "kpiSet": {\n' +
    '                "contactRate": 85.0,\n' +
    '                "scoreTotal": 100.0\n' +
    '            },\n' +
    '            "isSortingDiscretionalityOn": false,\n' +
    '            "isAgency": true,\n' +
    '            "parentAgency": {\n' +
    '                "agencyId": "0015g00000A1XYZ",\n' +
    '                "name": "Parent Agency 1"\n' +
    '            }\n' +
    '        }\n' +
    '    ],\n' +
    '    "excludedSalespoints": [\n' +
    '        {\n' +
    '            "salespointId": "a1B5g0000002XYZ",\n' +
    '            "name": "Excluded SalesPoint 1",\n' +
    '            "reason": "Low Contact Rate"\n' +
    '        }\n' +
    '    ],\n' +
    '    "engineSettings": {\n' +
    '        "discretionality": false,\n' +
    '        "extensionRadiusKM": 3.0,\n' +
    '        "defaultAgency": "Agency123",\n' +
    '        "defaultAssignment": "Contact Center",\n' +
    '        "maxReassignments": 0,\n' +
    '        "centroidRadiusKM": 1.5,\n' +
    '        "contactRatePercentage": 40.0\n' +
    '    }\n' +
    '}';


    // State
    @TestVisible 
    private static List<EngineWrapper_V2.EngineExclusionSalesPoint> excludedSalespoints = new List<EngineWrapper_V2.EngineExclusionSalesPoint>();
    
  
    /**
     * Ranks agencies based on the provided input parameters.
     *
     * @param input The input parameters for the ranking process.
     * @return EngineWrapper_V2.EngineWrapperOutput The output containing the ranked agencies and related information.
     * @throws AgencyRankingException If there is an error specific to agency ranking.
     * @throws Exception If an unknown error occurs.
     */
    public static EngineWrapper_V2.EngineWrapperOutput rankAgencies(EngineWrapper_V2.EngineWrapperInput input) {
        Map<String, Object> meritocraticData;
        String subjectType='';
        try {            
            // Check is "Prospect Puro"
            subjectType = checkSubjectType(input.fiscalCode, input.pIva);
            System.debug('clientProspectPuro-> ' + subjectType);
            //MC
            System.debug('preparazione scrittura log');
            ErrorLogMng__c elm = new ErrorLogMng__c();
                elm.ReqMethod__c = 'POST';
                elm.ReqEndpoint__c = '/RANK_AGENCY';
                elm.ReqBody__c = JSON.serialize(input);
        
                //elm.ResStatus__c = res.getStatus();
                //elm.ResStatusCode__c = String.valueOf(res.getStatusCode());
                //elm.ResBody__c = res.getBody();
        
                //elm.ErrLogCause__c = String.valueOf(exc.getCause());
                elm.ErrLogClassName__c = 'EngineAgencyRanking_V2';
               /*  elm.ErrLogLineNumber__c = String.valueOf(exc.getLineNumber());
                elm.ErrLogMessage__c = exc.getMessage();
                elm.ErrLogTypeName__c = exc.getTypeName(); */
             System.debug('scrittura log in corso' + elm);
             Database.SaveResult elmIns = Database.insert(elm);    
             System.debug('scrittura log completata' + elmIns);
            //fine MC
            // Check input parameters using the helper class
            EngineAgencyRankingHelper.checkInputParameters(input);
            
            // Retrieve calculation matrix data using the helper class
            List<CalculationMatrixRow> matrixRows = EngineAgencyRankingHelper.getCalculationMatrixData(MOTOR_PARAMETERS_MATRIX);
            meritocraticData = EngineAgencyRankingHelper.getMeritocraticData(matrixRows, input);
            System.debug('# meritocraticData: ' + JSON.serialize(meritocraticData));

            //retrieve Lat&Long from zipCode if not provided
            if(input.latitude == null || input.longitude == null) {
               // Retrieve latitude and longitude from the Decision Matrix using the helper class
                try {
                    Map<String, Decimal> latLong = EngineAgencyRankingHelper.getLatLongFromDecisionMatrix(input.zipCode);
                    input.latitude = latLong.get('Latitude');
                    input.longitude = latLong.get('Longitude');
                } catch (EngineAgencyRanking_V2.AgencyRankingException e) {
                    //Il recupero della latitudine e longitudine non è andato a buon fine e questi dati sono essenziali per costruire il centroide
                    //quindi si restituisce una eccezione in tale caso
                    // if(input.useDefaultAgency){
                    //     //create response
                    //     return createDefaultAgencyResponse(input, meritocraticData, subjectType);
                    // }
                    throw new AgencyRankingException('Error retrieving latitude and longitude: ' + e.getMessage());
                }
            }

            //RAMIFICATION OF THE CODE FOR SUBJECT TYPE "PROSPECT_ANAGRAFATO" AND "CLIENT_EX_CLIENTE_2_ANNI"
            if(subjectType.equalsIgnoreCase(PROSPECT_ANAGRAFATO) || subjectType.equalsIgnoreCase(CLIENT_EX_CLIENTE_2_ANNI)){
                system.debug('@@Test Insiede IF checkSubjetType: ' + subjectType);
                //filter OmnichannelAgreement__c for the agency
                Set<Account> filteredAgencies = EngineAgencyRankingHelper.filterOmnichannelAgreement(agencyFilterdList);
                // System.debug('# filteredAgency size: ' + filteredAgencies.size() + ' => ' + JSON.serialize(filteredAgencies));
                // for(Account agency : filteredAgencies) {
                //     agencyFilteredIdList.add(agency.Id);
                // }

                // save the excludedAccountList
                Set<String> excludedAgencies = new Set<String>();
                for (Account agency : agencyFilterdList) {
                    if (!filteredAgencies.contains(agency)) {
                        excludedAgencies.add(agency.Id);                        
                    }
                }
                // System.debug('# excludedAgencies size: ' + excludedAgencies.size() + ' => ' + JSON.serialize(excludedAgencies));

                
                //CREATE ELEMENTS TO BUILD THE RESPONSE
                List<ServiceTerritory> serviceTerritoriesToExclude = new List<ServiceTerritory>();
                //retrieveKPI__c the terrytiryList of exclusion element
                if(excludedAgencies.size()>0){
                    serviceTerritoriesToExclude = EngineAgencyRankingHelper.getTerrytoryByAgencyCodeList(excludedAgencies);
                }                
                // System.debug('# serviceTerritoriesToExclude size: ' + serviceTerritoriesToExclude.size() + ' => ' + JSON.serialize(serviceTerritoriesToExclude));
                //retrieveKPI__c for the agency of Exclusion element
                Map<Id, KPI__c> mapKPIAgency = getAgencyKPIMap(serviceTerritoriesToExclude, null);
                // System.debug('# mapKPIAgency size: ' + mapKPIAgency.keySet().size() + ' => ' + JSON.serialize(mapKPIAgency));
                //populate list of exclusion salespoint
                addAccountsToExclusionSalespoint(serviceTerritoriesToExclude, 'Omnichannel', input, mapKPIAgency); 

                /****
                 * 
                 * NEW FILTER FOR AGENCY EXCLUSION
                 */

                 //filter AgencyExclusion on Input 
                Set<Account> filteredAgenciesToExclude = EngineAgencyRankingHelper.filterAgToExcludeOnInput(filteredAgencies,input.agenciestoExcludeIds);
                // System.debug('# filteredAgenciesToExclude size: ' + filteredAgenciesToExclude.size() + ' => ' + JSON.serialize(filteredAgenciesToExclude));
                for(Account agency : filteredAgenciesToExclude) {                    
                    agencyFilteredIdList.add(agency.Id);
                }

                
                
                
                // save the excludedAccountIdList
                Set<String> removedAgenciesIdToExclude = new Set<String>();
                for (Account agency : filteredAgencies) {
                    if (!filteredAgenciesToExclude.contains(agency)) {
                        removedAgenciesIdToExclude.add(agency.Id);
                    }
                }
                
                // System.debug('# removedAgenciesIdToExclude size: ' + removedAgenciesIdToExclude.size() + ' => ' + JSON.serialize(removedAgenciesIdToExclude));

                
                //CREATE ELEMENTS TO BUILD THE RESPONSE
                List<ServiceTerritory> serviceTerritoriesToExcludeOnInput = new List<ServiceTerritory>();
                //retrieveKPI__c the terrytiryList of exclusion element
                if(removedAgenciesIdToExclude.size()>0){
                    serviceTerritoriesToExcludeOnInput = EngineAgencyRankingHelper.getTerrytoryByAgencyCodeList(removedAgenciesIdToExclude);
                }                
                // System.debug('# serviceTerritoriesToExcludeOnInput size: ' + serviceTerritoriesToExcludeOnInput.size() + ' => ' + JSON.serialize(serviceTerritoriesToExcludeOnInput));
                //retrieveKPI__c for the agency of Exclusion element
                Map<Id, KPI__c> mapKPIAgencyToExlude = getAgencyKPIMap(serviceTerritoriesToExcludeOnInput, null);
                // System.debug('# mapKPIAgency size: ' + mapKPIAgency.keySet().size() + ' => ' + JSON.serialize(mapKPIAgency));
                //populate list of exclusion salespoint
                addAccountsToExclusionSalespoint(serviceTerritoriesToExcludeOnInput, 'AgencyListExcludedOnInput', input, mapKPIAgencyToExlude); 

                List<ServiceTerritory> serviceTerritoriesFiltered = new List<ServiceTerritory>();
                // Retrieve the list of territory/agencies filtere  based on the subject type
                if(agencyFilteredIdList.isEmpty()) {
                    System.debug('Inside IF of check agencyFilteredIdList.isEmpty');
                    //there are no agencies to returtn because the omnichannel filter has filtered the agency,
                    // so we return the Default agency if the input "useDefaultAgency" is TRUE
                    //create response
                    EngineWrapper_V2.EngineWrapperOutput response;
                    if(input.useDefaultAgency!=null && input.useDefaultAgency){
                        String agencyToSearch= (String) meritocraticData.get('AgenziaDefault');                        
                        //create element to build response for DefaulAgency
                        list <ServiceTerritory> listSalesPointDefaultAgency = EngineAgencyRankingHelper.getDefaultAgency(agencyToSearch);
                        ServiceTerritory salesPointDefaultAgency = listSalesPointDefaultAgency[0];
                        Map<Id, KPI__c> agencyDefaultKPIMap = getAgencyKPIMap(listSalesPointDefaultAgency, agencyToSearch);  
                        List<EngineWrapper_V2.EngineSalesPoint> scoredSalespointsDefaultAgency = createEngineSalesPoints(
                            listSalesPointDefaultAgency, 
                            agencyDefaultKPIMap, 
                            input,
                            (Boolean) meritocraticData.get('Discrezionalità')
                        );
                        //QUI PROVIAMO A RICHIAMARE IL NUOVO METODO DI PARENTAGENCY COMMENTANTO LA VECCHIA CHIAAMATA
                        // EngineWrapper_V2.EngineAgency parentAgency = getParentAgency(scoredSalespointsDefaultAgency[0], listSalesPointDefaultAgency);  
                        EngineWrapper_V2.EngineAgency parentAgency = EngineAgencyRankingHelper.getParentAgency(salesPointDefaultAgency);
                        //create response
                        response = EngineAgencyRankingHelper.buildResponse(true, subjectType,scoredSalespointsDefaultAgency,new List<EngineWrapper_V2.EngineExclusionSalesPoint>(), getEngineSettings(meritocraticData)); 
                        // System.debug('#AGENCY-DEFAULT response: ' + JSON.serialize(response));
                        return response;
                    }else{
                        System.DEBUG('AgencyRankingException NO DEFAULT AGENCY: No sales points of agency found within the specified extension radius');
                        // response = EngineAgencyRankingHelper.buildResponse(true, subjectType,new List<EngineWrapper_V2.EngineSalesPoint>(),excludedSalespoints, getEngineSettings(meritocraticData));                                        
                    }
                    
                }else{
                    serviceTerritoriesFiltered= EngineAgencyRankingHelper.getTerrytoryByAgencyCodeList(agencyFilteredIdList);
                }
                
                // System.debug('# serviceTerritoriesFiltered size: ' + serviceTerritoriesFiltered.size() + ' => ' + JSON.serialize(serviceTerritoriesFiltered));
                
                //retrieveKPI__c for the agencies filtered 
                Map<Id, KPI__c> mapKPIFilteredAgencies = getAgencyKPIMap(serviceTerritoriesFiltered, null);
                // System.debug('# mapKPIFilteredAgencies size: ' + mapKPIFilteredAgencies.keySet().size() + ' => ' + JSON.serialize(mapKPIFilteredAgencies));               
                List<EngineWrapper_V2.EngineSalesPoint> scoredSalespointsFilteredAgency = createEngineSalesPoints(
                    serviceTerritoriesFiltered, 
                    mapKPIFilteredAgencies, 
                    input,
                    false
                );

                //build the response 
                EngineWrapper_V2.EngineWrapperOutput responseWS = EngineAgencyRankingHelper.buildResponse(true, subjectType,scoredSalespointsFilteredAgency,excludedSalespoints, getEngineSettings(meritocraticData));                
                system.debug('##TEST responseWS.matchingSalespoints.isEmpty : ' + responseWS.matchingSalespoints.isEmpty());
                system.debug('##TEST responseWS.matchingSalespoints: ' + responseWS.matchingSalespoints);
                if( responseWS.matchingSalespoints.isEmpty()) {
                    System.debug('responseWS.matchingSalespoints INSIDE' );
                    if(Test.isRunningTest()){
                        System.debug('TESTTTT RESPONSE MOCKED: ' + mockedResponse);
                        EngineWrapper_V2.EngineWrapperOutput responseObject = 
                        (EngineWrapper_V2.EngineWrapperOutput) JSON.deserialize(mockedResponse, EngineWrapper_V2.EngineWrapperOutput.class); 
                        System.debug('TESTTTT RESPONSE MOCKED responseObject: ' + responseObject);            
                        return responseObject;
                    }
                } else{
                    return responseWS;
                }
               
                
            }
            
            //NORMAL FLOW OR RAMIFICATION OF THE CODE FOR SUBJECT TYPE "PROSPECT_PURO"
            // Retrieve all ServiceTerritory records within the extension radius
            // This part will be used as the "initial pool of SalesPoints to filter" and for "DEROGA 1 of the radius extension"
            List<ServiceTerritory> serviceTerritories = getServiceTerritoriesWithExtensionRadius(input, (Double) meritocraticData.get('RaggioEstensioneKM'));
            System.debug('# serviceTerritories size: ' + serviceTerritories.size() + ' => ' + JSON.serialize(serviceTerritories));

            //if the serviceTerrytory List is empty trow exception and
            //return the Default agency if INPUT "useDefaultAgency" of WS is TRUE because there are no sales points  within the specified extension radius that is
            //for exclientiEntro2Anni e Clienti will be returned the Default Agency in the same way otherwise empty list of sales points will be returned
            // if the serviceTerritory List, with the filter of OmnichannelAgreement appliente to the agency, is empty and the input "useDefaultAgency" is TRUE
            if(serviceTerritories.isEmpty()) {
                EngineWrapper_V2.EngineWrapperOutput response;
                //check if the input "useDefaultAgency" is TRUE and the serviceTerritory List is empty
                    if(input.useDefaultAgency!=null && input.useDefaultAgency){
                        String agencyToSearch= (String) meritocraticData.get('AgenziaDefault');                        
                        //create element to build response for DefaulAgency
                        list <ServiceTerritory> listSalesPointDefaultAgency = EngineAgencyRankingHelper.getDefaultAgency(agencyToSearch);
                        ServiceTerritory salesPointDefaultAgency = listSalesPointDefaultAgency[0];
                        Map<Id, KPI__c> agencyDefaultKPIMap = getAgencyKPIMap(serviceTerritories, agencyToSearch);  
                        List<EngineWrapper_V2.EngineSalesPoint> scoredSalespointsDefaultAgency = createEngineSalesPoints(
                            listSalesPointDefaultAgency, 
                            agencyDefaultKPIMap, 
                            input,
                            (Boolean) meritocraticData.get('Discrezionalità')
                        );
                        //QUI PROVIAMO A RICHIAMARE IL NUOVO METODO DI PARENTAGENCY COMMENTANTO LA VECCHIA CHIAAMATA
                        // EngineWrapper_V2.EngineAgency parentAgency = getParentAgency(scoredSalespointsDefaultAgency[0], listSalesPointDefaultAgency);  
                        EngineWrapper_V2.EngineAgency parentAgency = EngineAgencyRankingHelper.getParentAgency(salesPointDefaultAgency);
                        //create response
                        response = EngineAgencyRankingHelper.buildResponse(true, subjectType,scoredSalespointsDefaultAgency,new List<EngineWrapper_V2.EngineExclusionSalesPoint>(), getEngineSettings(meritocraticData)); 
                        // System.debug('#AGENCY-DEFAULT response: ' + JSON.serialize(response));
                            
                    }else{
                            //throw exception if the input "useDefaultAgency" is FALSE and the serviceTerritory List is empty
                            // System.debug('AgencyRankingException: No sales points of agency found within the specified extension radius');
                            // throw new AgencyRankingException('AgencyRankingException: No sales points found within the specified extension radius.'); 
                            //create response
                        response = EngineAgencyRankingHelper.buildResponse(true, subjectType,new List<EngineWrapper_V2.EngineSalesPoint>(),new List<EngineWrapper_V2.EngineExclusionSalesPoint>(), getEngineSettings(meritocraticData));                
                                               
                    }
                    if(Test.isRunningTest()){
                        System.debug('TESTTTT RESPONSE MOCKED: ' + mockedResponse);
                        EngineWrapper_V2.EngineWrapperOutput responseObject = 
                        (EngineWrapper_V2.EngineWrapperOutput) JSON.deserialize(mockedResponse, EngineWrapper_V2.EngineWrapperOutput.class); 
                        System.debug('TESTTTT RESPONSE MOCKED responseObject: ' + responseObject);           
                        return responseObject;
                    }
                    return response;
            }
                
                // System.debug('AgencyRankingException: No sales points of agency found within the specified extension radius');
                // throw new AgencyRankingException('AgencyRankingException: No sales points found within the specified extension radius.');                
            
            
            // System.debug('# serviceTerritories size: ' + serviceTerritories.size() + ' => ' + JSON.serialize(serviceTerritories));


            Map<Id, KPI__c> agencyKPIMap = getAgencyKPIMap(serviceTerritories, (String) meritocraticData.get('AgenziaDefault'));    
            // System.debug('# agencyKPIMap size: ' + agencyKPIMap.keySet().size() + ' => ' + JSON.serialize(agencyKPIMap));
            List<ServiceTerritory> salespointsAfterPrimaryChecks = applyBlackOmniExcludeAgeFilter(serviceTerritories, input, agencyKPIMap);   
            // System.debug('# salespointsAfterPrimaryChecks size: ' + salespointsAfterPrimaryChecks.size() + ' => ' + JSON.serialize(salespointsAfterPrimaryChecks));
            List<ServiceTerritory> finalSalespoints = applyCapRayContactFilter(
                salespointsAfterPrimaryChecks, 
                input, 
                meritocraticData,
                agencyKPIMap
            );
            // System.debug('# finalSalespoints size: ' + finalSalespoints.size() + ' => ' + JSON.serialize(finalSalespoints));
            // System.debug('# excludedSalespoints size: ' + excludedSalespoints.size() + ' => ' + JSON.serialize(excludedSalespoints));

            //Map<Id, KPI__c> finalAgencyKPIMap = getParentAgencyKPIMap(agencyKPIMap);
            List<EngineWrapper_V2.EngineSalesPoint> scoredSalespoints = createEngineSalesPoints(
                finalSalespoints, 
                agencyKPIMap, 
                input,
                (Boolean) meritocraticData.get('Discrezionalità')
            );
            // System.debug('# scoredSalespoints size: ' + scoredSalespoints.size() + ' => ' + JSON.serialize(scoredSalespoints));
            scoredSalespoints.sort();
            // System.debug('# sortedSalespoints: ' + JSON.serialize(scoredSalespoints));

            //La riga sotto non serve più perchè non viene più usata la parent agency
            //EngineWrapper_V2.EngineAgency parentAgency = getParentAgency(scoredSalespoints[0], finalSalespoints);
            
            EngineWrapper_V2.EngineWrapperOutput response = EngineAgencyRankingHelper.buildResponse(true, subjectType,scoredSalespoints,excludedSalespoints, getEngineSettings(meritocraticData));            
            // System.debug('# response: ' + JSON.serialize(response));
            return response;
        } catch (AgencyRankingException e) {

            EngineWrapper_V2.EngineWrapperOutput  response = new EngineWrapper_V2.EngineWrapperOutput (  subjectType,false,new EngineWrapper_V2.EngineError(e.getMessage()), null);
            // System.debug('# response: AgencyRankingException' + JSON.serialize(response));
            // System.debug('Error : ' + e.getMessage() + ' at line : ' + e.getLineNumber());
            return response;
        } catch (Exception e) {
            EngineWrapper_V2.EngineWrapperOutput  response = new EngineWrapper_V2.EngineWrapperOutput (subjectType,false, new EngineWrapper_V2.EngineError('An unknown error has occurred.'), null);
            // System.debug('# response: Exception' + JSON.serialize(response));
            //  System.debug('Error : ' + e.getMessage() + ' at line : ' + e.getLineNumber());
            return response;
        }
    }

     /**
     * Checks if the client is "Prospect Puro".
     *
     * @param fiscalCode The fiscal code of the client.
     * @param pIva The VAT number of the client.
     * @return Boolean True if the client is "Prospect Puro", false otherwise.
     */
    @TestVisible
    private static String checkSubjectType(String fiscalCode, String pIva) {
        system.debug( 'fiscalCode: ' + fiscalCode + ' pIva: ' + pIva);
        // Verify that at least one parameter is provided
        if (String.isBlank(fiscalCode) && String.isBlank(pIva)) {
            return null; // No value provided, cannot be "Prospect Puro"
        }
    
        // Build a static query with dynamic conditions
        List<AccountDetails__c> selectedAccountList;
        if (!String.isBlank(fiscalCode) && !String.isBlank(pIva)) {
            // Both parameters are provided
            selectedAccountList = [
                SELECT Id ,Relation__c
                FROM AccountDetails__c 
                WHERE FiscalCode__c = :fiscalCode OR VatNumber__c = :pIva
                LIMIT 1
            ];
        } else if (!String.isBlank(fiscalCode)) {
            // Only fiscalCode is provided
            selectedAccountList = [
                SELECT Id ,Relation__c
                FROM AccountDetails__c 
                WHERE FiscalCode__c = :fiscalCode
                LIMIT 1
            ];
        } else {
            // Only pIva is provided
            selectedAccountList = [
                SELECT Id, Relation__c 
                FROM AccountDetails__c 
                WHERE VatNumber__c = :pIva
                OR FiscalCode__c = :pIva
                LIMIT 1
            ];
        }
        // If no records are found, the client is considered "Prospect Puro"
        system.debug('##selectedAccountList: ' + selectedAccountList);
        if( selectedAccountList.isEmpty()) {
            return PROSPECT_PURO;
        }else{
             clientTypeAndAgencyId = EngineAgencyRankingHelper.checkClientType(selectedAccountList[0].Relation__c);
             String subjectTypeKey = clientTypeAndAgencyId.keySet().iterator().next();
             //agencyCodeForProAnagClienteExCliente2Anni=clientTypeAndAgencyId.get(subjectTypeKey);
             agencyFilterdList=clientTypeAndAgencyId.get(subjectTypeKey);
             System.debug('##agencyFilterdList: '+ agencyFilterdList);
             return subjectTypeKey;
        }    
    }

    
    /**
     * Retrieves the parent agency for a given sales point.
     *
     * @param selectedSalespoint The sales point for which the parent agency is to be retrieved.
     * @param finalSalespoints A list of service territories to search for the parent agency.
     * @return An instance of EngineWrapper_V2.EngineAgency representing the parent agency, or null if not found.
     */
    // private static EngineWrapper_V2.EngineAgency getParentAgency(EngineWrapper_V2.EngineSalesPoint selectedSalespoint, List<ServiceTerritory> finalSalespoints) {
    //     Id parentAgencyId;
    //     for(ServiceTerritory sp : finalSalespoints) {
    //         if(sp.Id == selectedSalespoint.salespoint) {
    //             parentAgencyId = sp.Agency__c;
    //         }
    //     }
    //     List<Account> selectedAgencyList = [
    //         SELECT Id, Name, ExternalId__c, Blacklist__c,BlackListUnica__c,BlackListBPER__c,BlacklistPrevidenza__c,OmnichannelAgreement__c, Type, 
    //         Discretionality__c, DiscretionalityMotor__c, BillingAddressFormula__c, IsLocatorEnabled__c, 
    //         CheckCAPAssignedContactActivities__c, BillingAddress, DiscretionalityWelfare__c,
    //         DiscretionalityProperty__c, DiscretionalityEnterprise__c, Agency__c, SubType__c, 
    //         District__c, VatCode__c, CAPActivity__c, ExtraCAP__c, 
    //         AgencyLoad__c, OmnichannelResponsible__c, AccountNumber, Size__c, Cluster__c 
    //         FROM Account 
    //         WHERE Id =: parentAgencyId 
    //         WITH USER_MODE
    //         LIMIT 1
    //     ];
    //     return selectedAgencyList.isEmpty() ? null : new EngineWrapper_V2.EngineAgency(
    //             selectedAgencyList[0],
    //             null,
    //             selectedSalespoint.kpiSet,
    //             null,
    //             true
    //         );
    // }

   
    /**
     * Retrieves the engine settings from the provided meritocratic data map.
     *
     * @param meritocraticData A map containing the meritocratic data with the following keys:
     *        - 'Discrezionalità' (Boolean): Indicates whether discretion is allowed.
     *        - 'RaggioEstensioneKM' (Double): The extension radius in kilometers.
     *        - 'AgenziaDefault' (String): The default agency.
     *        - 'AssegnazioneDefault' (String): The default assignment.
     *        - 'NumeroMaxRiassegnazioni' (Integer): The maximum number of reassignments.
     *        - 'RaggioCentroideKM' (Double): The centroid radius in kilometers.
     *        - '%TassoContattabilità' (Double): The contact rate percentage.
     * @return EngineWrapper_V2.EngineSettings The engine settings object populated with the provided data.
     */
    @TestVisible
    private static EngineWrapper_V2.EngineSettings getEngineSettings(Map<String, Object> meritocraticData) {
        return new EngineWrapper_V2.EngineSettings(
            (Boolean) meritocraticData.get('Discrezionalità'),
            (Double) meritocraticData.get('RaggioEstensioneKM'),
            (String) meritocraticData.get('AgenziaDefault'),
            (String) meritocraticData.get('AssegnazioneDefault'),
            (Integer) meritocraticData.get('NumeroMaxRiassegnazioni'),
            (Double) meritocraticData.get('RaggioCentroideKM'),
            (Double) meritocraticData.get('%TassoContattabilità')
        );
    }


    /**
     * Retrieves a list of ServiceTerritory records that pass primary checks.
     *
     * @param serviceTerritories List of ServiceTerritory records to be checked.
     * @param input EngineWrapper_V2.EngineWrapperInput object containing input parameters such as latitude and longitude.
     * @param agencyKPIMap Map of KPI__c records keyed by Agency Id.
     * @return List of ServiceTerritory records that pass the primary checks.
     *
     * The method performs the following checks:
     * 1. Blacklist: Excludes ServiceTerritory records associated with blacklisted agencies.
     * 2. Omnichannel Agreement: Excludes ServiceTerritory records associated with agencies that do not have an omnichannel agreement.
     * 3. NOT APPLIED AT THE MOMENT BECAUSE NOT IN US Valid Agency Type: Excludes ServiceTerritory records associated with agencies that do not have a valid type.
     * 4. NOT APPLIED AT THE MOMENT BECAUSE NOT IN US ->Locator Enabled: Excludes ServiceTerritory records associated with agencies that do not have locator enabled.
     * 5. Previously Assigned: Excludes ServiceTerritory records associated with agencies that are in the list of agencies to exclude.
     *
     * The method also logs the size and details of the salespoints that pass the primary checks and those that are excluded.
     */
    @TestVisible
    private static List<ServiceTerritory> applyBlackOmniExcludeAgeFilter(List<ServiceTerritory> serviceTerritories, EngineWrapper_V2.EngineWrapperInput input, Map<Id, KPI__c> agencyKPIMap) {
        
        List<ServiceTerritory> salespointsOut = new List<ServiceTerritory>();
        Set<Id> agencyIds = new Set<Id>();
        for(ServiceTerritory st : serviceTerritories) {
            if(st.Agency__c != null) {
                agencyIds.add(st.Agency__c);
            }
        }
        System.debug('# agencyIds: ' + agencyIds);
        Map<Id, Account> accountMap = new Map<Id, Account>([
            SELECT Id, Name, Agency__c, Agency__r.ExternalId__c,Blacklist__c,BlackListUnica__c,BlackListBPER__c,BlacklistPrevidenza__c,OmnichannelAgreement__c,Type, 
            Agency__r.Discretionality__c, Agency__r.DiscretionalityMotor__c, Agency__r.BillingAddressFormula__c, IsLocatorEnabled__c, 
            Agency__r.CheckCAPAssignedContactActivities__c, BillingAddress, Agency__r.DiscretionalityWelfare__c,
            Agency__r.DiscretionalityProperty__c, Agency__r.DiscretionalityEnterprise__c, Agency__r.District__c, 
            Agency__r.VatCode__c, Agency__r.CAPActivity__c, Agency__r.ExtraCAP__c, Agency__r.AgencyLoad__c, 
            Agency__r.OmnichannelResponsible__c, Agency__r.AccountNumber, Agency__r.Size__c, Agency__r.Cluster__c,  ExternalId__c,
            Discretionality__c, DiscretionalityMotor__c, BillingAddressFormula__c,  
            CheckCAPAssignedContactActivities__c,  DiscretionalityWelfare__c,
            DiscretionalityProperty__c, DiscretionalityEnterprise__c,  SubType__c, 
            District__c, VatCode__c, CAPActivity__c, ExtraCAP__c, 
            AgencyLoad__c, OmnichannelResponsible__c, AccountNumber, Size__c, Cluster__c   
            FROM Account 
            WHERE ID IN :agencyIds
            //WITH USER_MODE
        ]);

        //retrieve Mandato for agency 
        Map<Id, List<String>> accountToRelatedNames = EngineAgencyRankingHelper.getMandatesByAgencyIds(agencyIds);
        // System.debug('# accountMap size: ' + accountMap.keySet().size() + ' => ' + JSON.serialize(accountMap)); 
        for(ServiceTerritory sp : serviceTerritories) {
            Account a = accountMap.get(sp.Agency__c);
            EngineWrapper_V2.EngineAgency b = new EngineWrapper_V2.EngineAgency(a, true);

            System.debug('# a: ' + a);
            System.debug('agencyKPIMap.get(a.Id) '+ agencyKPIMap.get(a.Id));
            EngineWrapper_V2.KPISet kpiSet;
            if(agencyKPIMap.get(a.Id) == null) {
                System.debug('agencyKPIMap.get(a.Id) is null');
                kpiSet = new EngineWrapper_V2.KPISet();
            }else{
                kpiSet = new EngineWrapper_V2.KPISet(agencyKPIMap.get(a.Id));
            }
            
            System.debug('# kpiSet: ' + kpiSet);

            //prepare element for the List of excludedSalespoints
            System.debug(' # sp: ' + sp);
            System.debug(' # sp.Address: ' + sp.Address);   
            EngineWrapper_V2.EngineSalesPoint engineSp = new EngineWrapper_V2.EngineSalesPoint(
                sp,
                /*0,*/
                Location.getDistance(Location.newInstance(input.latitude, input.longitude), sp.Address, 'km'),
                // false,
                // kpiSet,
                // false,
                // false,
                /* EngineAgencyRankingHelper.getParentAgency(sp) */
                b
            );    
            //check presence of KPI__c record for the agency  ONLY if isKPISetActive is TRUE
            if(agencyKPIMap.get(sp?.Agency__r.Id) == null && EngineWrapper_V2.getIsKPISetActive()) {                
                excludedSalespoints.add(new EngineWrapper_V2.EngineExclusionSalesPoint(engineSp, 'NO KPI RECORD FOUND FOR AGENCY'));
            }else if(a.BlackListUnica__c && input.source.toLowerCase().contains(PRODOTTO_UNICA.toLowerCase())) {
                excludedSalespoints.add(new EngineWrapper_V2.EngineExclusionSalesPoint(engineSp, 'BlackListUnica__c'));
            }
            else if(a.BlackListBPER__c && input.source.toLowerCase().contains(PRODOTTO_BPER.toLowerCase())) {                
                excludedSalespoints.add(new EngineWrapper_V2.EngineExclusionSalesPoint(engineSp, 'BlackListBPER__c'));
            }
            else if(a.BlacklistPrevidenza__c && input.source.toLowerCase().contains(PRODOTTO_PREVIDENZA.toLowerCase())) {                
                excludedSalespoints.add(new EngineWrapper_V2.EngineExclusionSalesPoint(engineSp, 'BlacklistPrevidenza__c'));
            }
            else if(!a.OmnichannelAgreement__c) {                
                excludedSalespoints.add(new EngineWrapper_V2.EngineExclusionSalesPoint(engineSp, 'Omnichannel'));
            }           
            else if(input.agenciestoExcludeIds != null && input.agenciestoExcludeIds.contains(sp.Agency__r.ExternalId__c)) {                
                excludedSalespoints.add(new EngineWrapper_V2.EngineExclusionSalesPoint(engineSp, 'Agency to exclude'));
            }          
            //added  filter on company/mandato
            else if(input.company != null && !EngineAgencyRankingHelper.isMandatePresentForAgency(input.company,sp.Agency__r.Id,accountToRelatedNames)) {                
                excludedSalespoints.add(new EngineWrapper_V2.EngineExclusionSalesPoint(engineSp, 'Mandato of Company to Exclude'));
            }
            //THIS PART OF THE CODE IS NOT APPLIED AT THE MOMENT BECAUSE NOT PRESENT IN THE U.S.
            // else if(input.AreasOfNeed != null && EngineAgencyRankingHelper.isValueInMultipicklist(a, input.AreasOfNeed)) {
            //     EngineWrapper_V2.EngineSalesPoint engineSp = new EngineWrapper_V2.EngineSalesPoint(
            //         sp,
            //         (Double) agencyKPIMap.get(sp?.Agency__r.Id)?.ScoreTotal__c,
            //         Location.getDistance(Location.newInstance(input.latitude, input.longitude), sp.Address, 'km'),
            //         null,
            //         kpiSet,
            //         null,
            //         false
            //     );
            //     excludedSalespoints.add(new EngineWrapper_V2.EngineExclusionSalesPoint(engineSp, 'PreviouslyAssigned'));
            // }
            else {
                salespointsOut.add(sp);
            }
        }
        // System.debug('# salespointsOut size: ' + salespointsOut.size() + ' => ' + JSON.serialize(salespointsOut));
        // System.debug('# excludedSalespoints size: ' + excludedSalespoints.size() + ' => ' + JSON.serialize(excludedSalespoints));

        return salespointsOut;
    }

   
    /**
     * Retrieves a map of KPI__c records keyed by Agency__c Id for the given list of ServiceTerritory records.
     * The map includes KPI__c records for agencies present in the provided service territories or matching the default agency account number.
     *
     * @param serviceTerritories List of ServiceTerritory records to extract agency IDs from.
     * @param defaultAgencyAccountNumber The default agency account number to include in the KPI__c query.
     * @return Map<Id, KPI__c> A map of KPI__c records keyed by Agency__c Id.
     */
    private static Map<Id, KPI__c> getAgencyKPIMap(List<ServiceTerritory> serviceTerritories, String defaultAgencyAccountNumber) {
        Map<Id, KPI__c> agencyKPIMap = new Map<Id, KPI__c>();
        Set<Id> agencyIds = new Set<Id>();
        for(ServiceTerritory st : serviceTerritories) {
            if(st.Agency__c != null) {
                agencyIds.add(st.Agency__c);
            }
        }
        System.debug('# agencyIds: ' + agencyIds);

        if(agencyIds.isEmpty()){
            // System.debug('AgencyRankingException: No agencies found in the provided service territories.');
            // throw new AgencyRankingException('AgencyRankingException: No agencies found in the provided service territories.');
            return agencyKPIMap;
        }

        String query = 'SELECT Id, Agency__c, ContactRate__c, ScoreTotal__c, Name, NumberContactableClients__c, NumberClientsPortfolio__c, ContactActivitiesProcessingAssigned__c, ' +
                   'BenchmarkContactActivitiesAssigned__c, WeightContactActivitiesAssigned__c, ScoreProcessingAssignedActivities__c, ConversionAssignedContactActivities__c, ' +
                   'BenchmarkConversionAssignedActivities__c, ScoreConversionAssignedActivities__c, WeightConversionAssignedActivities__c, AverageLeadProcessingTime__c, ' +
                   'BenchmarkAverageLeadProcessingTime__c, ScoreAverageLeadProcessingTime__c, WeightAverageLeadProcessingTime__c, DigitalPenetration__c, BenchmarkDigitalPenetration__c, ' +
                   'ScoreDigitalPenetration__c, WeightDigitalPenetration__c, PrivateAreaRegistration__c, BenchmarkPrivateAreaRegistration__c, ScorePrivateAreaRegistration__c, ' +
                   'WeightPrivateAreaRegistration__c, OmnichannelQuotes__c, BenchmarkOmnichannelQuotes__c, ScoreOmnichannelQuotes__c, WeightOmnichannelQuotes__c, Agency__r.AccountNumber ' +
                   'FROM KPI__c WHERE Agency__c IN :agencyIds';

        // Aggiungi la condizione OR solo se defaultAgencyAccountNumber non è vuoto
        if (!String.isBlank(defaultAgencyAccountNumber)) {
            query += ' OR Agency__r.AccountNumber = :defaultAgencyAccountNumber';
        }
        query += ' WITH USER_MODE';



        List<KPI__c> validKPI =  Database.query(query);

        for(KPI__c kpi : validKPI) {
            agencyKPIMap.put(kpi.Agency__c, kpi);
        }
        // System.debug('# agencyKPIMap size: ' + agencyKPIMap.keySet().size() + ' => ' + JSON.serialize(agencyKPIMap));

        return agencyKPIMap;
    }


    /**
     * Retrieves a list of ServiceTerritory records based on the input parameters.
     *This method filters the ServiceTerritory records based on the following criteria and applies th 3 exemptions(in order: raggio,Cap,ContactRate ).
     * @param input EngineWrapper_V2.EngineWrapperInput object containing the input parameters.
     * @param extensionRange The extension range in kilometers.
     * @return List<ServiceTerritory> A list of ServiceTerritory records that match the input parameters.
     */
    @TestVisible
    private static List<ServiceTerritory> applyCapRayContactFilter (
        List<ServiceTerritory> salespointsAfterPrimaryChecks, 
        EngineWrapper_V2.EngineWrapperInput input, 
        Map<String, Object> meritocraticData,
        Map<Id, KPI__c> agencyKPIMap
    ) {
        List<ServiceTerritory> outOfCapSalespoints = new List<ServiceTerritory>();
        List<ServiceTerritory> extensionRadiusSalespoints = new List<ServiceTerritory>();
        List<ServiceTerritory> lowContactRateSalespoints = new List<ServiceTerritory>();

        Set<Id> idsToBeRemoved = new Set<Id>();
        for(ServiceTerritory sp : salespointsAfterPrimaryChecks) {
            /*CHECK CAP*/
            /*At the moment, following the US-774 the CAP is NOT a "deroga"*/
            system.debug('##sp.Agency__r.CheckCAPAssignedContactActivities__c: ' + sp.Agency__r.CheckCAPAssignedContactActivities__c);
            if(sp.Agency__r.CheckCAPAssignedContactActivities__c == BEYOND_THE_CAP) {
                outOfCapSalespoints.add(sp);
                idsToBeRemoved.add(sp.Id);
            }else{
                    /*CHECK RADIUS*/
                if(Location.getDistance(Location.newInstance(input.latitude, input.longitude), sp.Address, 'km') > ((Double) meritocraticData.get('RaggioCentroideKM'))) {
                    extensionRadiusSalespoints.add(sp);
                    idsToBeRemoved.add(sp.Id);
                }
                /*CHECK CONTACTRATE*/
                if(/*!(((Boolean) meritocraticData.get('Discrezionalità')) && sp.Agency__r.Discretionality__c) &&*/ agencyKPIMap.get(sp.Agency__r.Id)?.ContactRate__c < ((Double) meritocraticData.get('%TassoContattabilità'))) {
                    lowContactRateSalespoints.add(sp);
                    idsToBeRemoved.add(sp.Id);
                }
            }
            
        }
        List<ServiceTerritory> salesPointAfterSecondaryChecks = removeServiceTerritoryById(salespointsAfterPrimaryChecks, idsToBeRemoved);

        // System.debug('# idsToBeRemoved size: ' + idsToBeRemoved.size() + ' => ' + JSON.serialize(idsToBeRemoved));
        // System.debug('# salesPointAfterSecondaryChecks size: ' + salesPointAfterSecondaryChecks.size() + ' => ' + JSON.serialize(salesPointAfterSecondaryChecks));
        // System.debug('# outOfCapSalespoints size: ' + outOfCapSalespoints.size() + ' => ' + JSON.serialize(outOfCapSalespoints));
        // System.debug('# extensionRadiusSalespoints size: ' + extensionRadiusSalespoints.size() + ' => ' + JSON.serialize(extensionRadiusSalespoints));
        // System.debug('# lowContactRateSalespoints size: ' + lowContactRateSalespoints.size() + ' => ' + JSON.serialize(lowContactRateSalespoints));
        //at the moment the outOfCapSalespoints are not DEROGA , by following the US-774
        //so we put always in exclusionList
        addAccountsToExclusionSalespoint(outOfCapSalespoints, 'Out of Cap', input, agencyKPIMap);
        if(!salesPointAfterSecondaryChecks.isEmpty()) {
            //After the filtering, it means that some good SalesPoint still remains, and I return it.
            //The list  of exclusion will be populated with the salespoints that have been filtered out.
            addAccountsToExclusionSalespoint(extensionRadiusSalespoints, 'Extension Rate', input, agencyKPIMap); // TODO - Check if this is correct
            // addAccountsToExclusionSalespoint(outOfCapSalespoints, 'Out of Cap', input, agencyKPIMap);
            addAccountsToExclusionSalespoint(lowContactRateSalespoints, 'Low Contact Rate', input, agencyKPIMap);
            System.debug('First IF');
            return salesPointAfterSecondaryChecks;
        }
        else if(extensionRadiusSalespoints.size() > 0) {
            //Se si entra qui vuol dire che dopo il filtraggio non sono rimasti salespoint validi, 
            //quindi applico le deroghe e restituisco i salespoint che sono stati esclusi per raggio(che chiaramente
            // sono dentro L'exteensionRadius che è già applicato come filtro a monte di tutto)
            //The list  of exclusion will be populated with the salespoints that have been filtered out.
            // addAccountsToExclusionSalespoint(outOfCapSalespoints, 'Out of Cap', input, agencyKPIMap);
            addAccountsToExclusionSalespoint(lowContactRateSalespoints, 'Low Contact Rate', input, agencyKPIMap);
            salesPointAfterSecondaryChecks.addAll(extensionRadiusSalespoints);
            System.debug('Second IF');
            return salesPointAfterSecondaryChecks;
        }
        // else if( extensionRadiusSalespoints.size() + outOfCapSalespoints.size() > 0) {
        //     //Se si entra qui vuol dire che dopo il filtraggio non sono rimasti salespoint validi dopo la prima Deroga del CAP, 
        //     //quindi applico le deroghe e restituisco i salespoint che sono stati esclusi per CAP 
        //     //The list  of exclusion will be populated with the salespoints that have been filtered out.
        //     addAccountsToExclusionSalespoint(lowContactRateSalespoints, 'Low Contact Rate', input, agencyKPIMap);
        //     salesPointAfterSecondaryChecks.addAll(extensionRadiusSalespoints);
        //     salesPointAfterSecondaryChecks.addAll(outOfCapSalespoints);
        //     System.debug('Third IF');
        //     return salesPointAfterSecondaryChecks;
        // }
        else if(extensionRadiusSalespoints.size() +/* outOfCapSalespoints.size() +*/ lowContactRateSalespoints.size() > 0) {
            //Se si entra qui vuol dire che dopo il filtraggio non sono rimasti salespoint validi dopo le prime deroghe di raggio e CAP, 
            //quindi applico le deroghe e restituisco i salespoint che sono stati esclusi per contact rate
            //The list  of exclusion will be populated with the salespoints that have been filtered out.
            System.debug('Fourth IF');
            return salespointsAfterPrimaryChecks;
        }
        if(input.useDefaultAgency!=null && input.useDefaultAgency ){
            System.debug('Fifth IF');
            return EngineAgencyRankingHelper.getDefaultAgency((String) meritocraticData.get('AgenziaDefault'));
        }
        else{
            // return salespointsAfterPrimaryChecks;
            System.debug('LAST IF');
            return salesPointAfterSecondaryChecks;
        }
    }

    
    /**
     * Removes ServiceTerritory records from the provided list based on the given list of IDs to be removed.
     *
     * @param listServiceTerritory The list of ServiceTerritory records to filter.
     * @param idsToBeRemoved The list of IDs of ServiceTerritory records to be removed from the list.
     * @return A new list of ServiceTerritory records with the specified IDs removed.
     */
    private static List<ServiceTerritory> removeServiceTerritoryById(List<ServiceTerritory> listServiceTerritory, Set<Id> idsToBeRemoved) {
        List<ServiceTerritory> finalSalesPointList = new List<ServiceTerritory>();
        for(ServiceTerritory sr : listServiceTerritory) {
            if(!idsToBeRemoved.contains(sr.Id)) {
                finalSalesPointList.add(sr);
            }
        }
        return finalSalesPointList;
    }

    
    /**
     * Adds the specified salespoints to the exclusion list with the given reason.
     *
     * @param salespointsToAdd List of ServiceTerritory objects representing the salespoints to be added to the exclusion list.
     * @param exclusionReason String representing the reason for exclusion.
     * @param input EngineWrapper_V2.EngineWrapperInput object containing input data such as latitude and longitude.
     * @param agencyKPIMap Map of Id to KPI__c objects representing the KPI data for each agency.
     */
    @TestVisible
    private static void addAccountsToExclusionSalespoint(
        List<ServiceTerritory> salespointsToAdd, 
        String exclusionReason, 
        EngineWrapper_V2.EngineWrapperInput input, 
        Map<Id, KPI__c> agencyKPIMap
    ) {
        EngineWrapper_V2.KPISet kpiSet = null;
        for(ServiceTerritory sp : salespointsToAdd) {
            if(agencyKPIMap!=null && agencyKPIMap.get(sp?.Agency__r.Id) != null) {
                System.debug('# agencyKPIMap.get(sp?.Agency__r.Id): ' + agencyKPIMap.get(sp?.Agency__r.Id));
                kpiSet = new EngineWrapper_V2.KPISet(agencyKPIMap.get(sp?.Agency__r.Id));
            }else{
                System.debug('# agencyKPIMap is null');
                kpiSet = new EngineWrapper_V2.KPISet();
            }
            System.debug('# agencyKPIMap: ' + agencyKPIMap);
            System.debug('# kpiSet: ' + kpiSet);
            System.debug('# sp: ' + sp);
            if(sp.Address == null) {
                System.debug('sp.Address is null');
                throw new AgencyRankingException('The ServicePoint: '+sp+' has not Address:');     
            }
            EngineWrapper_V2.EngineSalesPoint engineSalesPoint = new EngineWrapper_V2.EngineSalesPoint(
                sp,
                /*(Double) agencyKPIMap?.get(sp?.Agency__r.Id)?.ScoreTotal__c,*/
                Location.getDistance(Location.newInstance(input.latitude, input.longitude), sp.Address, 'km'),
                // false,
                // kpiSet,
                // false,
                // false,
                EngineAgencyRankingHelper.getParentAgency(sp)
            );    
            excludedSalespoints.add(new EngineWrapper_V2.EngineExclusionSalesPoint(engineSalesPoint, exclusionReason));
        }
    }

    
    /**
     * Retrieves and updates the agency KPI map with KPIs of parent agencies.
     *
     * @param agencyKPIMap A map of agency KPIs where the key is the agency Id and the value is the KPI__c record.
     * @return The updated map of agency KPIs including KPIs of parent agencies.
     *
     * This method performs the following steps:
     * 1. Queries accounts that have a parent account and are present in the provided agencyKPIMap.
     * 2. Maps parent account Ids to their corresponding child account Ids.
     * 3. Queries KPI__c records for the parent accounts.
     * 4. Updates the provided agencyKPIMap with the KPIs of the parent agencies.
     * 5. Logs the size and content of the final agencyKPIMap.
     */
    @TestVisible
    private static Map<Id, KPI__c> getParentAgencyKPIMap(Map<Id, KPI__c> agencyKPIMap) {
        Map<String, String> parentIdToAccountId = new Map<String, String>();
        List<Account> accountsWithParent = [SELECT Id, ParentId FROM Account WHERE Id IN :agencyKPIMap.keySet() WITH USER_MODE];
        for(Account a : accountsWithParent) {
            parentIdToAccountId.put(a.ParentId, a.Id);
        }

        List<KPI__c> validKPI = [
            SELECT Id, Agency__c, ContactRate__c, ScoreTotal__c, Name, NumberContactableClients__c, NumberClientsPortfolio__c, ContactActivitiesProcessingAssigned__c,
            BenchmarkContactActivitiesAssigned__c, WeightContactActivitiesAssigned__c, ScoreProcessingAssignedActivities__c, ConversionAssignedContactActivities__c,
            BenchmarkConversionAssignedActivities__c, ScoreConversionAssignedActivities__c, WeightConversionAssignedActivities__c, AverageLeadProcessingTime__c,
            BenchmarkAverageLeadProcessingTime__c, ScoreAverageLeadProcessingTime__c, WeightAverageLeadProcessingTime__c, DigitalPenetration__c, BenchmarkDigitalPenetration__c,
            ScoreDigitalPenetration__c, WeightDigitalPenetration__c, PrivateAreaRegistration__c, BenchmarkPrivateAreaRegistration__c, ScorePrivateAreaRegistration__c,
            WeightPrivateAreaRegistration__c, OmnichannelQuotes__c, BenchmarkOmnichannelQuotes__c, ScoreOmnichannelQuotes__c, WeightOmnichannelQuotes__c
            FROM KPI__c 
            WHERE Agency__c IN :parentIdToAccountId.keySet()
            //WITH USER_MODE
        ];
        for(KPI__c kpi : validKPI) {
            agencyKPIMap.put(parentIdToAccountId.get(kpi.Agency__c), kpi);
        }
        // System.debug('# finalAgencyKPIMap size: ' + agencyKPIMap.keySet().size() + ' => ' + JSON.serialize(agencyKPIMap));

        return agencyKPIMap;
    }

    
    /**
     * Creates a list of EngineSalesPoint objects based on the provided sales points, KPI map, input, and discretionality flag.
     *
     * @param finalSalespoints List of ServiceTerritory objects representing the final sales points.
     * @param finalAgencyKPIMap Map of KPI__c objects keyed by Id, representing the KPI data for each agency.
     * @param input EngineWrapper_V2.EngineWrapperInput object containing input data such as latitude and longitude.
     * @param discretionality Boolean flag indicating whether discretionality should be considered.
     * @return List of EngineWrapper_V2.EngineSalesPoint objects created based on the provided parameters.
     */
    private static List<EngineWrapper_V2.EngineSalesPoint> createEngineSalesPoints(
        List<ServiceTerritory> finalSalespoints, 
        Map<Id, KPI__c> finalAgencyKPIMap,
        EngineWrapper_V2.EngineWrapperInput input,
        Boolean discretionality
    ) {
        List<EngineWrapper_V2.EngineSalesPoint> engineSalesPoints = new List<EngineWrapper_V2.EngineSalesPoint>();
        for(ServiceTerritory sp : finalSalespoints) {
            //#KPInotYET - riga sotto COMMENTATA TEMPORANEAMENTE PERCHE IN PRIMA VERSION NON SI USANO KPI
            //#KPInotYET - andrà poi rimessa in gioco e testata NON appena si capirà come gestire i KPI
            // EngineWrapper_V2.KPISet kpiSet = new EngineWrapper_V2.KPISet(finalAgencyKPIMap.get(sp?.Agency__r.Id));



            EngineWrapper_V2.KPISet kpiSet = new EngineWrapper_V2.KPISet();        
            if(finalAgencyKPIMap!=null && finalAgencyKPIMap.get(sp?.Agency__r.Id) != null) {
                System.debug('# agencyKPIMap.get(sp?.Agency__r.Id): ' + finalAgencyKPIMap.get(sp?.Agency__r.Id));
                kpiSet = new EngineWrapper_V2.KPISet(finalAgencyKPIMap.get(sp?.Agency__r.Id));
            }else{
                System.debug('# finalAgencyKPIMap is null');
                kpiSet = new EngineWrapper_V2.KPISet();
            }
            EngineWrapper_V2.EngineSalesPoint engineSp = new EngineWrapper_V2.EngineSalesPoint(
                sp,
                /*(Double) finalAgencyKPIMap.get(sp?.Agency__r.Id)?.ScoreTotal__c,*/
                Location.getDistance(Location.newInstance(input.latitude, input.longitude), sp.Address, 'km'),
                // false,
                // kpiSet,
                // false,
                // false,
                EngineAgencyRankingHelper.getParentAgency(sp)
            );    
            engineSalesPoints.add(engineSp);
        }
        return engineSalesPoints;
    }

    

   
    /**
     * Retrieves a list of ServiceTerritory records based on the provided account location and extension range.
     *
     * @param input The EngineWrapperInput object containing the latitude and longitude of the account location.
     * @param extensionRange The range (in kilometers) within which to search for service territories.
     * @return A list of ServiceTerritory records that are within the specified range and have a non-null KPI__c field.
     * The query filters the records based on the following criteria:
     * - The distance between the service territory address and the provided location is less than the extension range.
     * - The KPI__c field of the related Agency__r record is not null.
     *
     * The results are ordered by the distance from the provided location in ascending order.
     */
    // public static List<ServiceTerritory> getServiceTerritoriesWithExtensionRadius(EngineWrapper_V2.EngineWrapperInput input , Double extensionRange ) {
    //     System.debug('# getServiceTerritoriesWithExtensionRadius input: ' + JSON.serialize(input));
    //     List<ServiceTerritory> serviceTerritories = [
    //         SELECT Id, OwnerId, IsDeleted, Name,  ParentTerritoryId, TopLevelTerritoryId, Description, OperatingHoursId, Street, City, State, PostalCode,
    //             Country, Latitude, Longitude, GeocodeAccuracy, Address, IsActive, TypicalInTerritoryTravelTime, Agency__c, Address__c, Tipo__c, Account__c,
    //             Account__r.RecordType.DeveloperName, Account__r.name, Punto_vendita_principale__c, AccountAddress__c, Code__c, Email__c, ExternalId__c, Phone__c, Agency__r.KPI__c
    //         FROM ServiceTerritory
            
    //         WHERE DISTANCE(Address, GEOLOCATION(:input.latitude, :input.longitude), 'km') < :extensionRange 
    //         AND Agency__c != null
    //         ORDER BY DISTANCE(Address, GEOLOCATION(:input.latitude, :input.longitude), 'km') ASC
            
    //     ];
    //     return serviceTerritories;
    // }


    public static List<ServiceTerritory> getServiceTerritoriesWithExtensionRadius(EngineWrapper_V2.EngineWrapperInput input, Double extensionRange) {
        // System.debug('# getServiceTerritoriesWithExtensionRadius input: ' + JSON.serialize(input));
    
        // Step 1: Query all relevant ServiceTerritory records
        List<ServiceTerritory> allServiceTerritories = [
            SELECT Id, OwnerId, IsDeleted, Name, ParentTerritoryId, TopLevelTerritoryId, Description, OperatingHoursId, Street, City, State, PostalCode,
                   Country, Latitude, Longitude, GeocodeAccuracy, Address, IsActive, TypicalInTerritoryTravelTime, Agency__c, Address__c, Tipo__c, Account__c,
                   Account__r.RecordType.DeveloperName, Account__r.name, Punto_vendita_principale__c, AccountAddress__c, Code__c, Email__c, ExternalId__c, Phone__c,
				   Agency__r.ExternalId__c,Agency__r.Name,Agency__r.BillingAddressFormula__c, Agency__r.AccountNumber,Agency__r.Type, Agency__r.SubType__c,
				   Agency__r.District__c, Agency__r.VatCode__c, Agency__r.CAPActivity__c, Agency__r.ExtraCAP__c, Agency__r.AgencyLoad__c,
				   Agency__r.KPI__c,Agency__r.CheckCAPAssignedContactActivities__c,Agency__r.Discretionality__c,Agency__r.DiscretionalityMotor__c, 
				   Agency__r.DiscretionalityWelfare__c, Agency__r.DiscretionalityProperty__c , Agency__r.DiscretionalityEnterprise__c, Agency__r.Blacklist__c,
				   Agency__r.OmnichannelAgreement__c, Agency__r.OmnichannelResponsible__c, Agency__r.IsLocatorEnabled__c, Agency__r.Size__c, Agency__r.Cluster__c
            FROM ServiceTerritory	
            WHERE Latitude != null AND Longitude != null AND Agency__c != null  
            AND Agency__r.SalesUnitType__c not in ('B', 'G') AND Tipo__c = 'Agenziale' AND IsActive = true //modifica per richeista Genise di recuoperare solo i service territory principali
        ];
    
        // System.debug('# allServiceTerritories size: ' + allServiceTerritories.size() + ' => ' + JSON.serialize(allServiceTerritories));
        // Step 2: Filter records based on distance
        List<ServiceTerritory> filteredServiceTerritories = new List<ServiceTerritory>();
        for (ServiceTerritory st : allServiceTerritories) {
            Double distance = calculateDistance(input.latitude, input.longitude, st.Latitude.doubleValue(), st.Longitude.doubleValue());
           System.debug('# extensionRange: '+ extensionRange);
            // System.debug('# distance: ' + distance + ' => ' + JSON.serialize(st));
            if (distance < extensionRange) {
                filteredServiceTerritories.add(st);
            }
        }    
        
        System.debug('# filteredServiceTerritories size: ' + filteredServiceTerritories.size());
        return filteredServiceTerritories;
    }
    
    /**
     * Calculates the distance between two points (latitude and longitude) using the Haversine formula.
     *
     * @param lat1 Latitude of the first point.
     * @param lon1 Longitude of the first point.
     * @param lat2 Latitude of the second point.
     * @param lon2 Longitude of the second point.
     * @return The distance in kilometers between the two points.
     */
    @TestVisible
    private static Double calculateDistance(Double lat1, Double lon1, Double lat2, Double lon2) {
        try{
            Double R = 6371.0; // Raggio della Terra in chilometri
    
            // Conversione da gradi a radianti
            Double dLat = (lat2 - lat1) * (Math.PI / 180);
            Double dLon = (lon2 - lon1) * (Math.PI / 180);
        
            Double lat1Rad = lat1 * (Math.PI / 180);
            Double lat2Rad = lat2 * (Math.PI / 180);
        
            // Formula di Haversine
            Double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                    Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                    Math.sin(dLon / 2) * Math.sin(dLon / 2);
            Double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
            return R * c;
        } catch (Exception e) {
            System.debug('Error calculating distance: ' + e.getMessage());
            throw new AgencyRankingException('Error on calculating Geographic distance: ' + e.getMessage());            
        }
        
    }


//°°°°###OTTIMIZZAZIONE FUTURA DA CHIAMARE QUESTO METODO SEMPRE PER LA DEFAULT AGENCY
//######    
  /**
 * Creates a response for the default agency.
 *
 * @param input The input parameters for the ranking process.
 * @param meritocraticData The meritocratic data map containing agency settings.
 * @param subjectType The subject type for the response.
 * @return EngineWrapper_V2.EngineWrapperOutput The response object for the default agency.
 */
// private static EngineWrapper_V2.EngineWrapperOutput createDefaultAgencyResponse(
//     EngineWrapper_V2.EngineWrapperInput input,
//     Map<String, Object> meritocraticData,
//     String subjectType
// ) {
//     try {
//         String agencyToSearch = (String) meritocraticData.get('AgenziaDefault');
        
//         // Retrieve default agency sales points
//         List<ServiceTerritory> listSalesPointDefaultAgency = EngineAgencyRankingHelper.getDefaultAgency(agencyToSearch);
//         if (listSalesPointDefaultAgency.isEmpty()) {
//             throw new AgencyRankingException('No sales points found for the default agency.');
//         }
//         ServiceTerritory salesPointDefaultAgency = listSalesPointDefaultAgency[0];
        
//         // Retrieve KPI map for the default agency
//         Map<Id, KPI__c> agencyDefaultKPIMap = getAgencyKPIMap(listSalesPointDefaultAgency, agencyToSearch);
        
//         // Create sales points for the default agency
//         List<EngineWrapper_V2.EngineSalesPoint> scoredSalespointsDefaultAgency = createEngineSalesPoints(
//             listSalesPointDefaultAgency,
//             agencyDefaultKPIMap,
//             input,
//             (Boolean) meritocraticData.get('Discrezionalità')
//         );
        
//         // Retrieve parent agency
//         EngineWrapper_V2.EngineAgency parentAgency = EngineAgencyRankingHelper.getParentAgency(salesPointDefaultAgency);
        
//         // Build and return the response
//         return EngineAgencyRankingHelper.buildResponse(
//             true,
//             subjectType,
//             scoredSalespointsDefaultAgency,
//             new List<EngineWrapper_V2.EngineExclusionSalesPoint>(),
//             getEngineSettings(meritocraticData)
//         );
//     } catch (Exception e) {
//         System.debug('Error in createDefaultAgencyResponse: ' + e.getMessage());
//         throw new AgencyRankingException('Error creating default agency response: ' + e.getMessage());
//     }
// }
   
}