<aura:component implements="flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,force:lightningQuickActionWithoutHeader" 
                access="global">
    
    <!-- Attribute for Flow Name -->
    <aura:attribute name="flowName" type="String" required="true" default="Case_Handling_New_Case"/>
    
    <!-- Initialize Flow -->
    <aura:handler name="init" value="{!this}" action="{!c.init}" />

    
    <div class="slds-scrollable_y" style="max-height: 25rem">
        <div class="slds-text-longform">
            <lightning:flow aura:id="flowData" onstatuschange="{!c.handleStatusChange}" />
        </div>
    </div>
</aura:component>