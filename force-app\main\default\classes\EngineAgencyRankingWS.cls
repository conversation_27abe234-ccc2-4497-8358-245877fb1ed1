/****************************************************************************************************************************
* <AUTHOR>
* @description    Agency Engine class for the ranking of the existing agencies exposing the functionality as a webservice
* @date           2024-03-05
* @group          Agency Engine
*****************************************************************************************************************************/
@RestResource(urlMapping='/rankagencies')
global with sharing class EngineAgencyRankingWS {
    
    /********************************************************************************************************************
    * <AUTHOR>
    * @date         2024-03-05
    * @description  The method returns the relevant data of the valid scored agencies and of the excluded ones
    * @return       -
    ********************************************************************************************************************/
    @HttpPost
    global static void rankAgencies() {        
        RestRequest req = RestContext.request;
        RestResponse res = RestContext.response;
        String bodyRequest = req.requestBody.toString();

        try {
            /*
            EngineWrapper.EngineWrapperInput requestObject = (EngineWrapper.EngineWrapperInput) JSON.deserialize(bodyRequest, EngineWrapper.EngineWrapperInput.Class);

            EngineWrapper.EngineWrapperOutput responseObject = EngineAgencyRanking.rankAgencies(requestObject);
            res.responseBody = Blob.valueOf(JSON.serialize(responseObject, true));
            res.statusCode = 200;*/

            // Refactored to use the new version of the engine
            EngineWrapper_V2.EngineWrapperInput requestObject = (EngineWrapper_V2.EngineWrapperInput) JSON.deserialize(bodyRequest, EngineWrapper_V2.EngineWrapperInput.Class);

            EngineWrapper_V2.EngineWrapperOutput responseObject = EngineAgencyRanking_V2.rankAgencies(requestObject);
            res.responseBody = Blob.valueOf(JSON.serialize(responseObject, true));
            res.statusCode = 200;

        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'EngineAgencyRankingWS.rankAgencies: ' + e.getMessage());
            res.responseBody = Blob.valueOf(String.valueOf(e));
            res.statusCode = 500;
        }
    }
}
