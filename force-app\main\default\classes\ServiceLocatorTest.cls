@isTest
private class ServiceLocatorTest {

    public virtual class ExampleService {
        public virtual String sayHello() {
            return 'Hello from ExampleService';
        }
    }

    public class MockExampleService extends ExampleService {
        public override String sayHello() {
            return 'Hello from MockExampleService';
        }
    }

    @isTest
    static void testGetInstance_WithTestOverride() {
        // Registra il mock
        ServiceLocator.registerTestType(ExampleService.class, MockExampleService.class);

        // Ottieni l'istanza
        Object instance = ServiceLocator.getInstance(ExampleService.class);

        // Verifica che sia del tipo mockato
        System.assert(instance instanceof MockExampleService, 'L\'istanza dovrebbe essere di tipo MockExampleService');
    }

    @isTest
    static void testGetInstance_Default() {
        Object instance = ServiceLocator.getInstance(ExampleService.class);
        System.assert(instance instanceof ExampleService, 'L\'istanza dovrebbe essere di tipo ExampleService');
    }
}