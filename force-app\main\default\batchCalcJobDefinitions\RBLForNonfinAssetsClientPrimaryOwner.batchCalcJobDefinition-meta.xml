<?xml version="1.0" encoding="UTF-8"?>
<BatchCalcJobDefinition xmlns="http://soap.sforce.com/2006/04/metadata">
    <aggregates>
        <fields>
            <aggregateFunction>Sum</aggregateFunction>
            <alias>AmountSum</alias>
            <sourceFieldName>FinServ_Amount_c</sourceFieldName>
        </fields>
        <groupBy>FinServ_PrimaryOwner_c</groupBy>
        <label>aggregate_FinServ_Amount_c</label>
        <name>aggregate_FinServ_Amount_c</name>
        <sourceName>FinServ_AssetsAndLiabilities_c_RecordType_Filter</sourceName>
    </aggregates>
    <datasources>
        <fields>
            <alias>Id</alias>
            <dataType>Text</dataType>
            <name>Id</name>
        </fields>
        <fields>
            <alias>Name</alias>
            <dataType>Text</dataType>
            <name>Name</name>
        </fields>
        <label>Account</label>
        <name>Account</name>
        <sourceName>Account</sourceName>
        <type>StandardObject</type>
    </datasources>
    <datasources>
        <fields>
            <alias>FinServ_Amount_c</alias>
            <dataType>Numeric</dataType>
            <name>FinServ__Amount__c</name>
        </fields>
        <fields>
            <alias>FinServ_PrimaryOwner_c</alias>
            <dataType>Text</dataType>
            <name>FinServ__PrimaryOwner__c</name>
        </fields>
        <fields>
            <alias>RecordTypeId</alias>
            <dataType>Text</dataType>
            <name>RecordTypeId</name>
        </fields>
        <label>FinServ_AssetsAndLiabilities_c</label>
        <name>FinServ_AssetsAndLiabilities_c</name>
        <sourceName>FinServ__AssetsAndLiabilities__c</sourceName>
        <type>StandardObject</type>
    </datasources>
    <datasources>
        <fields>
            <alias>DeveloperName</alias>
            <dataType>Text</dataType>
            <name>DeveloperName</name>
        </fields>
        <fields>
            <alias>Id</alias>
            <dataType>Text</dataType>
            <name>Id</name>
        </fields>
        <label>RecordType</label>
        <name>RecordType</name>
        <sourceName>RecordType</sourceName>
        <type>StandardObject</type>
    </datasources>
    <description>Client-level summary of all non-financial assets where Client is Primary Owner.</description>
    <executionPlatformType>CRMA</executionPlatformType>
    <filters>
        <criteria>
            <operator>Equals</operator>
            <sequence>1</sequence>
            <sourceFieldName>DeveloperName</sourceFieldName>
            <value>NonfinancialAsset</value>
        </criteria>
        <filterCondition>1</filterCondition>
        <isDynamicFilter>false</isDynamicFilter>
        <label>FinServ_AssetsAndLiabilities_c_RecordType_Filter</label>
        <name>FinServ_AssetsAndLiabilities_c_RecordType_Filter</name>
        <sourceName>FinServ_AssetsAndLiabilities_c_RecordType</sourceName>
    </filters>
    <isTemplate>false</isTemplate>
    <joins>
        <fields>
            <alias>AmountSum</alias>
            <sourceFieldName>AmountSum</sourceFieldName>
            <sourceName>aggregate_FinServ_Amount_c</sourceName>
        </fields>
        <fields>
            <alias>Id</alias>
            <sourceFieldName>Id</sourceFieldName>
            <sourceName>Account</sourceName>
        </fields>
        <joinKeys>
            <primarySourceFieldName>Id</primarySourceFieldName>
            <secondarySourceFieldName>FinServ_PrimaryOwner_c</secondarySourceFieldName>
        </joinKeys>
        <label>Account_aggregate_FinServ_Amount_c</label>
        <name>Account_aggregate_FinServ_Amount_c</name>
        <primarySourceName>Account</primarySourceName>
        <secondarySourceName>aggregate_FinServ_Amount_c</secondarySourceName>
        <type>LeftOuter</type>
    </joins>
    <joins>
        <fields>
            <alias>DeveloperName</alias>
            <sourceFieldName>DeveloperName</sourceFieldName>
            <sourceName>RecordType</sourceName>
        </fields>
        <fields>
            <alias>FinServ_Amount_c</alias>
            <sourceFieldName>FinServ_Amount_c</sourceFieldName>
            <sourceName>FinServ_AssetsAndLiabilities_c</sourceName>
        </fields>
        <fields>
            <alias>FinServ_PrimaryOwner_c</alias>
            <sourceFieldName>FinServ_PrimaryOwner_c</sourceFieldName>
            <sourceName>FinServ_AssetsAndLiabilities_c</sourceName>
        </fields>
        <joinKeys>
            <primarySourceFieldName>RecordTypeId</primarySourceFieldName>
            <secondarySourceFieldName>Id</secondarySourceFieldName>
        </joinKeys>
        <label>FinServ_AssetsAndLiabilities_c_RecordType</label>
        <name>FinServ_AssetsAndLiabilities_c_RecordType</name>
        <primarySourceName>FinServ_AssetsAndLiabilities_c</primarySourceName>
        <secondarySourceName>RecordType</secondarySourceName>
        <type>LeftOuter</type>
    </joins>
    <label>RBLForNonfinAssetsClientPrimaryOwner</label>
    <processType>DataProcessingEngine</processType>
    <status>Inactive</status>
    <writebacks>
        <fields>
            <runtimeParameter>false</runtimeParameter>
            <sourceFieldName>Id</sourceFieldName>
            <targetFieldName>Id</targetFieldName>
        </fields>
        <fields>
            <runtimeParameter>false</runtimeParameter>
            <sourceFieldName>AmountSum</sourceFieldName>
            <targetFieldName>FinServ__TotalNonfinancialAssetsPrimaryOwner__c</targetFieldName>
        </fields>
        <isChangedRow>false</isChangedRow>
        <label>RBLForNonfinAssetsClientPrimaryOwnerExport</label>
        <name>RBLForNonfinAssetsClientPrimaryOwnerExport</name>
        <operationType>Update</operationType>
        <sourceName>Account_aggregate_FinServ_Amount_c</sourceName>
        <storageType>sObject</storageType>
        <targetObjectName>Account</targetObjectName>
        <writebackSequence>1</writebackSequence>
        <writebackUser>0057Q00000A7swHQAR</writebackUser>
    </writebacks>
</BatchCalcJobDefinition>
