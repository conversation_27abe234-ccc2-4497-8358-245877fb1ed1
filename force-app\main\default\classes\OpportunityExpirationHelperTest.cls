@isTest
public with sharing class OpportunityExpirationHelperTest {

    public static Database.DMLOptions dmlOptions {
        get{if(dmlOptions==null){
                Database.DMLOptions dmlOptions = new Database.DMLOptions();
                dmlOptions.DuplicateRuleHeader.AllowSave = true;
                return dmlOptions;
        }
            else{
                return dmlOptions;
            }
        }
        set;
    }

    public static sObject createRecord(String sObjectName, Map<String, Object> fieldsValues,  Boolean executeDML)
    {
        sObject record;
        System.debug(LoggingLevel.INFO, 'OpportunityExpirationHelperTest | createRecord | inserting this object '+ sObjectName);
        if(fieldsValues.keySet().contains('RecordTypeId')){
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelperTest | createRecord | with RecordTypeId');
             record = UTL_DynamicApex.sObjectTypesPerObjectName.get(sObjectName).newSObject((Id)fieldsValues.get('RecordTypeId'), true);
        }
        else{
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelperTest | createRecord | without RecordTypeId');
             record = UTL_DynamicApex.sObjectTypesPerObjectName.get(sObjectName).newSObject();
        }
        
        
        for(String fieldName : fieldsValues.keySet())
        {
            Object fieldValue = fieldsValues.get(fieldName);
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelperTest | createRecord | field has this value : ('+  fieldName + ') ' + fieldValue);
            record.put(fieldName, fieldValue);
        }
        
        if (executeDML)
        {
            // insert record;
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelperTest | createRecord | executing DML');
            Database.SaveResult myResult = Database.insert(record, dmlOptions);

            if(myResult.isSuccess()){
                System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelperTest | createRecord | Successfully updated Opp Container. Opp ID: ' + myResult.Id);
            }
            else{
                System.debug(LoggingLevel.ERROR, 'OpportunityExpirationHelperTest | createRecord | Problem while updating Opp Container. Opp ID: ' + myResult.Id);
                for(Database.Error error : myResult.getErrors()){
                    System.debug(LoggingLevel.ERROR, 'OpportunityExpirationHelperTest | createRecord | The following error has occurred.');                    
                    System.debug(LoggingLevel.ERROR, 'OpportunityExpirationHelperTest | createRecord | ' + error.getStatusCode() + ': ' + error.getMessage());
                    System.debug(LoggingLevel.ERROR, 'OpportunityExpirationHelperTest | createRecord | Opportunity fields that affected this error: ' + error.getFields());
                    Assert.fail('test data could not be inserted : ' + record);
                }
            }
        }
        
        return record;
    }


    /************
     * <AUTHOR> - Volodia Gounaris
     * @date            2025-05-25
     * @description     This is a unit test to assert the good behavior of methods in OpportunityExpirationHelper
     *                  Data is constructed this way
     *                      - 1 Opporunity Omnicanale or Agenziale
     *                      - 2 child Opportunity Prodotto
     *                      - with 2 quotes each (4 total)
     *
     * 
     *                  THe different behaviors should be tested : 
     *                      - if all quotes are expired --> opportunity prodotti should be closed (fuori SLA) --> opportunity agenziale or omnicanale should be closed (fuori SLA)
     *                      - if at least 1 quote is SOLD --> opportunity prodotti should be Chiuso con vendita
     *                      - if at least 1 opportunity product is "Chiuso con Vendita" -> opportunity agenziale or omnicanale is "Polizza emezza"
     *                      - quotes are expired --> information propage properly to parents
     *                      - opportunity products are expired --> closese properly --> information propagates properly to parents
     *                      - opportunity agenziale or omnicanale is expired --> closes properly
     */
    // important things to test
    // triggerByDate expiration : batch detect an expiration by SLA/expiration date being reached
    // triggerByExpiredChild expiration : if all quotes of given Product are expired --> product gets expired 
    // triggerByExpiredChild expiration : if all quotes of given Container are expired --> container gets expired
    // rollup info from Quote onto Product
    // rollup info from Quote onto Container

    //------------------unit test- not implemented---------------------

    // closeExpiringQuotesOrchestrator
    // closeExpiringOppProductsOrchestrator
    // closeExpiringOppContainerOrchestrator

    // inspectOppProductForAnticipatedExpiration
    // inspectOppContainersForAnticipatedExpiration

    // closeQuote
    // closeOppProductAndRollupFields
    // closeOppContainerAndRollupFields
    // 

    // ---------------test triggeredByExpiration----------------------

    // scenario1 - test triggerByDate expiration & no rollup - test orchestration independently
    // 1 Container activeToExpire               --> expired + emptyRollup
    // 1 Product activeToExpire                 --> expired + emptyRollup
    // 2 Quotes : 2 activeToExpire              --> expired

    // scenario2 - test triggerByDate expiration & 1 rollup - test orchestration independently
    // 1 Container activeToExpire               --> expired + 1rollup
    // 1 Product activeToExpire                 --> expired + 1rollup
    // 2 Quotes : 1 activeToExpire & 1 SOLD     --> expired

    // scenario3 - test triggerByDate expiration & 2 rollups - test orchestration independently
    // 1 Container activeToExpire               --> expired + 2rollups
    // 1 Product activeToExpire                 --> expired + 2rollups
    // 2 Quotes : 2 SOLD                        --> expired + 2rollups

    // scenario4 - test triggerByDate expiration with 2 Products - test orchestration independently
    // 1 Container activeToExpire               --> noChange
    // 2 Product activeToExpire & active        --> expired + 1rollup & noChange
    // product 1 : 1Q activeToExpire & 1Q SOLD  --> expired & SOLD
    // product 2 : 1Q active & 1Q SOLD          --> noChange

    // --------------- negative test ----------------------

    // scenario5 - test triggerByDate expiration & no rollup - test orchestration independently
    // 1 Container active                       --> nothing
    // 1 Product active                         --> nothing
    // 1 Quote : active                         --> nothing

    // ------------test triggeredByNoActiveQuote (following quote expiration)------------------------------------

    // scenario6 - test triggeredByNoActiveQuote & 1 rollup 
    // 1 Container active                       --> expired with empty rollup
    // 1 Product active                         --> expired with empty rollup
    // 2 Quotes 2 activeToExpire                --> expired

    // scenario7 - test triggeredByNoActiveQuote & 1 rollup 
    // 1 Container active                       --> expired with 1 rollup
    // 1 Product active                         --> expired with 1 rollup
    // 2 Quotes 1 SOLD & 1 activeToExpire       --> no change & expired

    // scenario8 - test triggeredByNoActiveQuote & 2 rollups
    // 1 Container active                       --> expired with 2 rollups
    // 1 Product active                         --> expired with 2 rollups
    // 3 Quotes 2 SOLD & 1 activeToExpire       --> 2x noChange & expired

    // scenario9 - test triggeredByNoActiveQuote with 2 Products
    // 1 Container active                       --> expired with rollup
    // 2 Products active                        --> 1 opp expired + rollup & other is noChange
    // product 1: 1Q activeToExpire & 1Q SOLD   --> expired
    // product 2: 1Q active & 1Q SOLD           --> noChange

    // ------------------negative tests-------------------------------------

    // scenario10 - test triggerByExpiredChild expiration - negativeTest - test orchestration independently
    // 1 Container active                       --> no change
    // 1 Product active                         --> no change
    // 2 Quotes 1 active & 1 activeToExpire     --> activeToExpired is expired & other is noChange

    // scenario11 - test triggerByExpiredChild expiration - negativeTest - test orchestration independently
    // 1 Container active                       --> no change
    // 2 Products : 2 active                    --> 1 opp is expired + rollup & other is noChange
    // Product 1 : 1 Quote active & 1 activeToExpire --> 1Q is expired & other is noChange (active)
    // Product 2 : 1 activeToExpire & 1 SOLD    --> 2Q expired


        
    public static Account myAcc  { get; set; }
    public static User myAdminUser  { get; set; }
    public static Opportunity myOppContainer1 { get; set; }
    public static Opportunity myOppProduct1 { get; set; }
    public static Opportunity myOppProduct2 { get; set; }

    // linked to myOppProduct1
    public static Quote myQuote1_1 { get; set; }
    public static Quote myQuote1_2 { get; set; }
    public static Quote myQuote1_3 { get; set; }

    // linked to myOppProduct2
    public static Quote myQuote2_1 { get; set; }
    public static Quote myQuote2_2 { get; set; }
    public static Quote myQuote2_3 { get; set; }

    // scenario1 - test triggerByDate expiration - positive - unit test of orchestrators methods
    // 1 Container activeToExpire   --> expired
    // 1 Product activeToExpire     --> expired
    // 1 Quotes activeToExpire      --> expired
    static void scenario1_createData(){

        myAdminUser = UTL_DataFactory.createUser('Admintst', '<EMAIL>', 'User', 'System Administrator', '', '<EMAIL>', true);

        Map<String, Object> myAccData = new Map<String, Object>{
            'Name' => 'testAccount',
            'RecordTypeId' => Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId() 
        };
        myAcc = (Account)createRecord('Account', myAccData, true );

        
        Map<String, Object> myOppContainerData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Agenziale').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(-10),
            'name' => 'testParentOpp',
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(-10),
            'AreaOfNeed__c' => '',
            'amount' => 0
        };
        myOppContainer1 = (Opportunity)createRecord('Opportunity', myOppContainerData, true );


        Map<String, Object> myOppProductData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Prodotto').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(-10),
            'name' => 'testOpp1_ExpiredOpp',
            'parent__c' => myOppContainer1.Id,
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(-10),
            'AreaOfNeed__c' => '',
            'amount' => 0

        };
        myOppProduct1 = (Opportunity)createRecord('Opportunity', myOppProductData, true );


        Map<String, Object> myQuote1_1data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'testQuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_ACTIVE,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Salute'

        };
        myQuote1_1 = (Quote)createRecord('Quote', myQuote1_1data, true );

        Map<String, Object> myQuote1_2data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'test2QuoteName_ExpiredQuote2',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_ACTIVE,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Casa'

        };
        myQuote1_2 = (Quote)createRecord('Quote', myQuote1_2data, true );
    }

    static void scenario2_createData(){

        myAdminUser = UTL_DataFactory.createUser('Admintst', '<EMAIL>', 'User', 'System Administrator', '', '<EMAIL>', true);

        Map<String, Object> myAccData = new Map<String, Object>{
            'Name' => 'testAccount',
            'RecordTypeId' => Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId() 
        };
        myAcc = (Account)createRecord('Account', myAccData, true );

        
        Map<String, Object> myOppContainerData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Agenziale').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(-10),
            'name' => 'testParentOpp',
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(-10),
            'AreaOfNeed__c' => '',
            'amount' => 0
        };
        myOppContainer1 = (Opportunity)createRecord('Opportunity', myOppContainerData, true );


        Map<String, Object> myOppProductData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Prodotto').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(-10),
            'name' => 'testOpp1_ExpiredOpp',
            'parent__c' => myOppContainer1.Id,
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(-10),
            'AreaOfNeed__c' => '',
            'amount' => 0

        };
        myOppProduct1 = (Opportunity)createRecord('Opportunity', myOppProductData, true );


        Map<String, Object> myQuote1_1data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'testQuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_SOLD,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Salute'

        };
        myQuote1_1 = (Quote)createRecord('Quote', myQuote1_1data, true );

        Map<String, Object> myQuote1_2data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'test2QuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_ACTIVE,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Casa'

        };
        myQuote1_2 = (Quote)createRecord('Quote', myQuote1_2data, true );
    }

    static void scenario3_createData(){

        myAdminUser = UTL_DataFactory.createUser('Admintst', '<EMAIL>', 'User', 'System Administrator', '', '<EMAIL>', true);

        Map<String, Object> myAccData = new Map<String, Object>{
            'Name' => 'testAccount',
            'RecordTypeId' => Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId() 
        };
        myAcc = (Account)createRecord('Account', myAccData, true );

        
        Map<String, Object> myOppContainerData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Agenziale').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(-10),
            'name' => 'testParentOpp',
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(-10),
            'AreaOfNeed__c' => '',
            'amount' => 0
        };
        myOppContainer1 = (Opportunity)createRecord('Opportunity', myOppContainerData, true );


        Map<String, Object> myOppProductData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Prodotto').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(-10),
            'name' => 'testOpp1_ExpiredOpp',
            'parent__c' => myOppContainer1.Id,
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(-10),
            'AreaOfNeed__c' => '',
            'amount' => 0

        };
        myOppProduct1 = (Opportunity)createRecord('Opportunity', myOppProductData, true );


        Map<String, Object> myQuote1_1data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'testQuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_SOLD,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Salute'

        };
        myQuote1_1 = (Quote)createRecord('Quote', myQuote1_1data, true );

        Map<String, Object> myQuote1_2data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'test2QuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_SOLD,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Casa'

        };
        myQuote1_2 = (Quote)createRecord('Quote', myQuote1_2data, true );
    }

    static void scenario4_createData(){

        myAdminUser = UTL_DataFactory.createUser('Admintst', '<EMAIL>', 'User', 'System Administrator', '', '<EMAIL>', true);

        Map<String, Object> myAccData = new Map<String, Object>{
            'Name' => 'testAccount',
            'RecordTypeId' => Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId() 
        };
        myAcc = (Account)createRecord('Account', myAccData, true );

        
        Map<String, Object> myOppContainerData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Agenziale').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(10),
            'name' => 'testParentOpp',
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(10),
            'AreaOfNeed__c' => '',
            'amount' => 0
        };
        myOppContainer1 = (Opportunity)createRecord('Opportunity', myOppContainerData, true );

        // ------------------ Opp Product 1----------------------

        Map<String, Object> myOppProductData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Prodotto').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(-10),
            'name' => 'myOppProduct1',
            'parent__c' => myOppContainer1.Id,
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(-10),
            'AreaOfNeed__c' => '',
            'amount' => 0

        };
        myOppProduct1 = (Opportunity)createRecord('Opportunity', myOppProductData, true );


        Map<String, Object> myQuote1_1data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'myQuote1_1',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_ACTIVE,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Salute'

        };
        myQuote1_1 = (Quote)createRecord('Quote', myQuote1_1data, true );

        Map<String, Object> myQuote1_2data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'myOppProduct2',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_SOLD,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Casa'

        };
        myQuote1_2 = (Quote)createRecord('Quote', myQuote1_2data, true );


        // ------------------ Opp Product 2----------------------
        Map<String, Object> myOppProductData2 = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Prodotto').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(10),
            'name' => 'myOppProduct2',
            'parent__c' => myOppContainer1.Id,
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(10),
            'AreaOfNeed__c' => '',
            'amount' => 0

        };
        myOppProduct2 = (Opportunity)createRecord('Opportunity', myOppProductData2, true );

        Map<String, Object> myQuote2_1data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'myQuote2_1',
            'ExpirationDate' => Date.Today().addDays(10),
            'OpportunityId' => myOppProduct2.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_ACTIVE,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Salute'

        };
        myQuote2_1 = (Quote)createRecord('Quote', myQuote2_1data, true );

        Map<String, Object> myQuote2_2data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'myQuote2_2',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct2.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_SOLD,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Salute'

        };
        myQuote2_2 = (Quote)createRecord('Quote', myQuote2_2data, true );
    }

    static void scenario5_createData(){

        myAdminUser = UTL_DataFactory.createUser('Admintst', '<EMAIL>', 'User', 'System Administrator', '', '<EMAIL>', true);

        Map<String, Object> myAccData = new Map<String, Object>{
            'Name' => 'testAccount',
            'RecordTypeId' => Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId() 
        };
        myAcc = (Account)createRecord('Account', myAccData, true );

        
        Map<String, Object> myOppContainerData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Agenziale').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(10),
            'name' => 'testParentOpp',
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(-10),
            'AreaOfNeed__c' => '',
            'amount' => 0
        };
        myOppContainer1 = (Opportunity)createRecord('Opportunity', myOppContainerData, true );

        // ------------------ Opp Product 1----------------------

        Map<String, Object> myOppProductData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Prodotto').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(10),
            'name' => 'testOppProduct1',
            'parent__c' => myOppContainer1.Id,
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(-10),
            'AreaOfNeed__c' => '',
            'amount' => 0

        };
        myOppProduct1 = (Opportunity)createRecord('Opportunity', myOppProductData, true );


        Map<String, Object> myQuote1_1data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'testQuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_SOLD,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Salute'

        };
        myQuote1_1 = (Quote)createRecord('Quote', myQuote1_1data, true );

        Map<String, Object> myQuote1_2data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'test2QuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_SOLD,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Casa'

        };
        myQuote1_2 = (Quote)createRecord('Quote', myQuote1_2data, true );
    }

    static void scenario6_createData(){

        myAdminUser = UTL_DataFactory.createUser('Admintst', '<EMAIL>', 'User', 'System Administrator', '', '<EMAIL>', true);

        Map<String, Object> myAccData = new Map<String, Object>{
            'Name' => 'testAccount',
            'RecordTypeId' => Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId() 
        };
        myAcc = (Account)createRecord('Account', myAccData, true );

        
        Map<String, Object> myOppContainerData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Agenziale').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(10),
            'name' => 'testParentOpp',
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(10),
            'AreaOfNeed__c' => '',
            'amount' => 0
        };
        myOppContainer1 = (Opportunity)createRecord('Opportunity', myOppContainerData, true );


        Map<String, Object> myOppProductData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Prodotto').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(10),
            'name' => 'testOpp1_ExpiredOpp',
            'parent__c' => myOppContainer1.Id,
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(10),
            'AreaOfNeed__c' => '',
            'amount' => 0

        };
        myOppProduct1 = (Opportunity)createRecord('Opportunity', myOppProductData, true );


        Map<String, Object> myQuote1_1data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'testQuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_ACTIVE,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Salute'

        };
        myQuote1_1 = (Quote)createRecord('Quote', myQuote1_1data, true );

        Map<String, Object> myQuote1_2data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'test2QuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_ACTIVE,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Casa'

        };
        myQuote1_2 = (Quote)createRecord('Quote', myQuote1_2data, true );
    }

    static void scenario7_createData(){

        myAdminUser = UTL_DataFactory.createUser('Admintst', '<EMAIL>', 'User', 'System Administrator', '', '<EMAIL>', true);

        Map<String, Object> myAccData = new Map<String, Object>{
            'Name' => 'testAccount',
            'RecordTypeId' => Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId() 
        };
        myAcc = (Account)createRecord('Account', myAccData, true );

        
        Map<String, Object> myOppContainerData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Agenziale').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(10),
            'name' => 'testParentOpp',
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(10),
            'AreaOfNeed__c' => '',
            'amount' => 0
        };
        myOppContainer1 = (Opportunity)createRecord('Opportunity', myOppContainerData, true );


        Map<String, Object> myOppProductData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Prodotto').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(10),
            'name' => 'testOpp1_ExpiredOpp',
            'parent__c' => myOppContainer1.Id,
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(10),
            'AreaOfNeed__c' => '',
            'amount' => 0

        };
        myOppProduct1 = (Opportunity)createRecord('Opportunity', myOppProductData, true );


        Map<String, Object> myQuote1_1data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'testQuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_SOLD,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Salute'

        };
        myQuote1_1 = (Quote)createRecord('Quote', myQuote1_1data, true );

        Map<String, Object> myQuote1_2data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'test2QuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_ACTIVE,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Casa'

        };
        myQuote1_2 = (Quote)createRecord('Quote', myQuote1_2data, true );
    }
    
    static void scenario8_createData(){

        myAdminUser = UTL_DataFactory.createUser('Admintst', '<EMAIL>', 'User', 'System Administrator', '', '<EMAIL>', true);

        Map<String, Object> myAccData = new Map<String, Object>{
            'Name' => 'testAccount',
            'RecordTypeId' => Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId() 
        };
        myAcc = (Account)createRecord('Account', myAccData, true );

        
        Map<String, Object> myOppContainerData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Agenziale').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(10),
            'name' => 'testParentOpp',
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(10),
            'AreaOfNeed__c' => '',
            'amount' => 0
        };
        myOppContainer1 = (Opportunity)createRecord('Opportunity', myOppContainerData, true );


        Map<String, Object> myOppProductData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Prodotto').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(10),
            'name' => 'testOpp1_ExpiredOpp',
            'parent__c' => myOppContainer1.Id,
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(10),
            'AreaOfNeed__c' => '',
            'amount' => 0

        };
        myOppProduct1 = (Opportunity)createRecord('Opportunity', myOppProductData, true );


        Map<String, Object> myQuote1_1data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'testQuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_SOLD,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Salute'

        };
        myQuote1_1 = (Quote)createRecord('Quote', myQuote1_1data, true );

        Map<String, Object> myQuote1_2data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'test2QuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_SOLD,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Casa'

        };
        myQuote1_2 = (Quote)createRecord('Quote', myQuote1_2data, true );

        Map<String, Object> myQuote1_3data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'test2QuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_ACTIVE,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Casa'

        };
        myQuote1_3 = (Quote)createRecord('Quote', myQuote1_3data, true );
    }

    static void scenario9_createData(){

        myAdminUser = UTL_DataFactory.createUser('Admintst', '<EMAIL>', 'User', 'System Administrator', '', '<EMAIL>', true);

        Map<String, Object> myAccData = new Map<String, Object>{
            'Name' => 'testAccount',
            'RecordTypeId' => Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId() 
        };
        myAcc = (Account)createRecord('Account', myAccData, true );

        
        Map<String, Object> myOppContainerData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Agenziale').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(10),
            'name' => 'testParentOpp',
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(10),
            'AreaOfNeed__c' => '',
            'amount' => 0
        };
        myOppContainer1 = (Opportunity)createRecord('Opportunity', myOppContainerData, true );

        // ------------------ Opp Product 1----------------------

        Map<String, Object> myOppProductData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Prodotto').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(10),
            'name' => 'testOppProduct1',
            'parent__c' => myOppContainer1.Id,
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(10),
            'AreaOfNeed__c' => '',
            'amount' => 0

        };
        myOppProduct1 = (Opportunity)createRecord('Opportunity', myOppProductData, true );


        Map<String, Object> myQuote1_1data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'testQuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_ACTIVE,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Salute'

        };
        myQuote1_1 = (Quote)createRecord('Quote', myQuote1_1data, true );

        Map<String, Object> myQuote1_2data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'test2QuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_SOLD,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Casa'

        };
        myQuote1_2 = (Quote)createRecord('Quote', myQuote1_2data, true );


        // ------------------ Opp Product 2----------------------
        Map<String, Object> myOppProductData2 = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Prodotto').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(10),
            'name' => 'testOppProduct2',
            'parent__c' => myOppContainer1.Id,
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(10),
            'AreaOfNeed__c' => '',
            'amount' => 0

        };
        myOppProduct2 = (Opportunity)createRecord('Opportunity', myOppProductData2, true );

        Map<String, Object> myQuote2_1data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'testQuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(10),
            'OpportunityId' => myOppProduct2.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_ACTIVE,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Salute'

        };
        myQuote2_1 = (Quote)createRecord('Quote', myQuote2_1data, true );

        Map<String, Object> myQuote2_2data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'test2QuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(10),
            'OpportunityId' => myOppProduct2.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_SOLD,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Casa'

        };
        myQuote2_2 = (Quote)createRecord('Quote', myQuote2_2data, true );
    }

    static void scenario10_createData(){

        myAdminUser = UTL_DataFactory.createUser('Admintst', '<EMAIL>', 'User', 'System Administrator', '', '<EMAIL>', true);

        Map<String, Object> myAccData = new Map<String, Object>{
            'Name' => 'testAccount',
            'RecordTypeId' => Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId() 
        };
        myAcc = (Account)createRecord('Account', myAccData, true );

        
        Map<String, Object> myOppContainerData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Agenziale').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(10),
            'name' => 'testParentOpp',
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(10),
            'AreaOfNeed__c' => '',
            'amount' => 0
        };
        myOppContainer1 = (Opportunity)createRecord('Opportunity', myOppContainerData, true );


        Map<String, Object> myOppProductData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Prodotto').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(10),
            'name' => 'testOpp1_ExpiredOpp',
            'parent__c' => myOppContainer1.Id,
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(10),
            'AreaOfNeed__c' => '',
            'amount' => 0

        };
        myOppProduct1 = (Opportunity)createRecord('Opportunity', myOppProductData, true );


        Map<String, Object> myQuote1_1data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'testQuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_ACTIVE,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Salute'

        };
        myQuote1_1 = (Quote)createRecord('Quote', myQuote1_1data, true );

        Map<String, Object> myQuote1_2data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'test2QuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_ACTIVE,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Casa'

        };
        myQuote1_2 = (Quote)createRecord('Quote', myQuote1_2data, true );
    }

    static void scenario11_createData(){

        myAdminUser = UTL_DataFactory.createUser('Admintst', '<EMAIL>', 'User', 'System Administrator', '', '<EMAIL>', true);

        Map<String, Object> myAccData = new Map<String, Object>{
            'Name' => 'testAccount',
            'RecordTypeId' => Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId() 
        };
        myAcc = (Account)createRecord('Account', myAccData, true );

        
        Map<String, Object> myOppContainerData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Agenziale').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(10),
            'name' => 'testParentOpp',
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(10),
            'AreaOfNeed__c' => '',
            'amount' => 0
        };
        myOppContainer1 = (Opportunity)createRecord('Opportunity', myOppContainerData, true );

        // ------------------ Opp Product 1----------------------

        Map<String, Object> myOppProductData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Prodotto').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(10),
            'name' => 'testOppProduct1',
            'parent__c' => myOppContainer1.Id,
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(10),
            'AreaOfNeed__c' => '',
            'amount' => 0

        };
        myOppProduct1 = (Opportunity)createRecord('Opportunity', myOppProductData, true );


        Map<String, Object> myQuote1_1data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'testQuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_ACTIVE,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Salute'

        };
        myQuote1_1 = (Quote)createRecord('Quote', myQuote1_1data, true );

        Map<String, Object> myQuote1_2data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'test2QuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(10),
            'OpportunityId' => myOppProduct1.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_ACTIVE,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Casa'

        };
        myQuote1_2 = (Quote)createRecord('Quote', myQuote1_2data, true );


        // ------------------ Opp Product 2----------------------
        Map<String, Object> myOppProductData2 = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Prodotto').getRecordTypeId(),
            'stageName' => 'in gestione',
            'closeDate' => Date.Today().addDays(10),
            'name' => 'testOppProduct2',
            'parent__c' => myOppContainer1.Id,
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(10),
            'AreaOfNeed__c' => '',
            'amount' => 0

        };
        myOppProduct2 = (Opportunity)createRecord('Opportunity', myOppProductData2, true );

        Map<String, Object> myQuote2_1data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'testQuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct2.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_SOLD,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Salute'

        };
        myQuote2_1 = (Quote)createRecord('Quote', myQuote2_1data, true );

        Map<String, Object> myQuote2_2data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id, // do not add otherwise it fails
            'Name' => 'test2QuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct2.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_ACTIVE,
            'QuoteAmount__c' => 100,
            'AreasOfNeed__c' => 'Casa'

        };
        myQuote2_2 = (Quote)createRecord('Quote', myQuote2_2data, true );
    }

    @isTest
    static void scenario1_test(){
        
        scenario1_createData();
        System.debug('STARTING THE TEST METHOD');

        Test.startTest();
        System.runAs(myAdminUser) {
            Database.executeBatch( new OpportunityExpirationBatch(OpportunityExpirationBatch.objectToProcess.QUOTE), 200);
            //OpportunityExpirationHelper.closeExpiringQuotesOrchestrator(new Set<Id>{myQuote1_1.Id, myQuote1_2.Id});
        }
        Test.stopTest();

        System.debug('TEST VOLO ' + myQuote1_1.Id + ' | ' + myQuote1_1);
        System.debug('TEST VOLO ' + myQuote1_2.Id + ' | ' + myQuote1_2);

        Quote myQuote1_1_AFTER = [SELECT Id, CommercialStatus__c FROM Quote WHERE Id = :myQuote1_1.Id LIMIT 1];
        Assert.areEqual(OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_EXPIRED,            myQuote1_1_AFTER.CommercialStatus__c,    'Quote is not expired');

        Quote myQuote1_2_AFTER = [SELECT Id, CommercialStatus__c FROM Quote WHERE Id = :myQuote1_2.Id LIMIT 1];
        Assert.areEqual(OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_EXPIRED,            myQuote1_2_AFTER.CommercialStatus__c,       'Quote is not expired');

        Opportunity myOppProduct1_AFTER = [SELECT Id, StageName, ClosureSubstatus__c, areaOfNeed__c, OverallAreasOfNeed__c, amount FROM Opportunity WHERE Id = :myOppProduct1.Id LIMIT 1];
        Assert.areEqual(OpportunityExpirationHelper.OPPORTUNITY_STAGENAME_CLOSE,                myOppProduct1_AFTER.StageName,              'Opportunity is not closed');
        Assert.areEqual(OpportunityExpirationHelper.OPPORTUNITY_CLOSURE_SUBSTATUS_FUORI_SLA,    myOppProduct1_AFTER.ClosureSubstatus__c,    'Opportunity is not FUORI SLA');
        Assert.areEqual(null,                                                                     myOppProduct1_AFTER.areaOfNeed__c,          'Opportunity does not have empty areaOfNeed');
        Assert.areEqual(0,                                                                      myOppProduct1_AFTER.amount,                 'Opportunity does not amount = 0');

        Opportunity myOppContainer1_AFTER = [SELECT Id, StageName, ClosureSubstatus__c, areaOfNeed__c, OverallAreasOfNeed__c, amount  FROM Opportunity WHERE Id = :myOppContainer1.Id LIMIT 1];
        Assert.areEqual(OpportunityExpirationHelper.OPPORTUNITY_STAGENAME_CLOSE,                myOppContainer1_AFTER.StageName,            'Opportunity is not closed');
        Assert.areEqual(OpportunityExpirationHelper.OPPORTUNITY_CLOSURE_SUBSTATUS_FUORI_SLA,    myOppContainer1_AFTER.ClosureSubstatus__c,  'Opportunity is not FUORI SLA');
        Assert.areEqual(null,                                                                     myOppContainer1_AFTER.areaOfNeed__c,        'Opportunity does not have empty areaOfNeed');
        Assert.areEqual(0,                                                                      myOppContainer1_AFTER.amount,               'Opportunity does not amount = 0');
    }

    @isTest
    static void scenario2_test(){
        
        scenario2_createData();
        System.debug('OpportunityExpirationHelper |  STARTING THE TEST METHOD');

        Test.startTest();
        System.runAs(myAdminUser) {
            // OpportunityExpirationHelper.closeExpiringQuotesOrchestrator(new Set<Id>{myQuote1_2.Id});
            Database.executeBatch( new OpportunityExpirationBatch(OpportunityExpirationBatch.objectToProcess.QUOTE), 200);
        }
        Test.stopTest();

        Quote myQuote1_1_AFTER = [SELECT Id, CommercialStatus__c FROM Quote WHERE Id = :myQuote1_1.Id LIMIT 1];
        Assert.areEqual(OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_SOLD,               myQuote1_1_AFTER.CommercialStatus__c,       'Quote is not SOLD');

        Quote myQuote1_2_AFTER = [SELECT Id, CommercialStatus__c FROM Quote WHERE Id = :myQuote1_2.Id LIMIT 1];
        Assert.areEqual(OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_EXPIRED,            myQuote1_2_AFTER.CommercialStatus__c,       'Quote is not expired');

        Opportunity myOppProduct1_AFTER = [SELECT Id, StageName, ClosureSubstatus__c, areaOfNeed__c, OverallAreasOfNeed__c, amount FROM Opportunity WHERE Id = :myOppProduct1.Id LIMIT 1];
        Assert.areEqual(OpportunityExpirationHelper.OPPORTUNITY_STAGENAME_CLOSE,                myOppProduct1_AFTER.StageName,              'Opportunity is not closed');
        Assert.areEqual(OpportunityExpirationHelper.OPPORTUNITY_CLOSURE_SUBSTATUS_POLIZZA_EMESSA, myOppProduct1_AFTER.ClosureSubstatus__c,  'Opportunity is not POLIZZA EMESSA');
        Assert.areEqual('Salute',                                                               myOppProduct1_AFTER.areaOfNeed__c,          'Opportunity does not have areOfNeed = Salute');
        Assert.areEqual(100,                                                                    myOppProduct1_AFTER.amount,                 'Opportunity does not amount = 100');

        Opportunity myOppContainer1_AFTER = [SELECT Id, StageName, ClosureSubstatus__c, areaOfNeed__c, OverallAreasOfNeed__c, amount  FROM Opportunity WHERE Id = :myOppContainer1.Id LIMIT 1];
        Assert.areEqual(OpportunityExpirationHelper.OPPORTUNITY_STAGENAME_CLOSE,                myOppContainer1_AFTER.StageName,            'Opportunity is not closed');
        Assert.areEqual(OpportunityExpirationHelper.OPPORTUNITY_CLOSURE_SUBSTATUS_POLIZZA_EMESSA, myOppContainer1_AFTER.ClosureSubstatus__c, 'Opportunity is POLIZZA EMESSA');
        Assert.areEqual('Salute',                                                               myOppContainer1_AFTER.areaOfNeed__c,        'Opportunity does not have areOfNeed = Salute');
        Assert.areEqual(100,                                                                    myOppContainer1_AFTER.amount,               'Opportunity does not amount = 100');
    }

    @isTest
    static void scenario3_test(){
        
        scenario3_createData();
        System.debug('OpportunityExpirationHelper |  STARTING THE TEST METHOD');

        Test.startTest();
        System.runAs(myAdminUser) {
            // OpportunityExpirationHelper.closeExpiringQuotesOrchestrator(new Set<Id>{});
            Database.executeBatch( new OpportunityExpirationBatch(OpportunityExpirationBatch.objectToProcess.QUOTE), 200);
        }
        Test.stopTest();

        Quote myQuote1_1_AFTER = [SELECT Id, CommercialStatus__c FROM Quote WHERE Id = :myQuote1_1.Id LIMIT 1];
        Assert.areEqual(OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_SOLD,                   myQuote1_1_AFTER.CommercialStatus__c,       'Quote is not SOLD');

        Quote myQuote1_2_AFTER = [SELECT Id, CommercialStatus__c FROM Quote WHERE Id = :myQuote1_2.Id LIMIT 1];
        Assert.areEqual(OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_SOLD,                   myQuote1_2_AFTER.CommercialStatus__c,       'Quote is not SOLD');

        Opportunity myOppProduct1_AFTER = [SELECT Id, StageName, ClosureSubstatus__c, areaOfNeed__c, OverallAreasOfNeed__c, amount FROM Opportunity WHERE Id = :myOppProduct1.Id LIMIT 1];
        Assert.areEqual(OpportunityExpirationHelper.OPPORTUNITY_STAGENAME_CLOSE,                    myOppProduct1_AFTER.StageName,              'Opportunity is not closed');
        Assert.areEqual(OpportunityExpirationHelper.OPPORTUNITY_CLOSURE_SUBSTATUS_POLIZZA_EMESSA,   myOppProduct1_AFTER.ClosureSubstatus__c,    'Opportunity is not POLIZZA EMESSA');
        Assert.areEqual(200,                                                                        myOppProduct1_AFTER.amount,                 'Opportunity does not amount = 100');
        Assert.isTrue(  myOppProduct1_AFTER.areaOfNeed__c.containsIgnoreCase('Salute'),                                                         'Salute area not present');
        Assert.isTrue(  myOppProduct1_AFTER.areaOfNeed__c.containsIgnoreCase('Casa'),                                                           'Casa area not present');
    
        Opportunity myOppContainer1_AFTER = [SELECT Id, StageName, ClosureSubstatus__c, areaOfNeed__c, OverallAreasOfNeed__c, amount  FROM Opportunity WHERE Id = :myOppContainer1.Id LIMIT 1];
        Assert.areEqual(OpportunityExpirationHelper.OPPORTUNITY_STAGENAME_CLOSE,                    myOppContainer1_AFTER.StageName,            'Opportunity is not closed');
        Assert.areEqual(OpportunityExpirationHelper.OPPORTUNITY_CLOSURE_SUBSTATUS_POLIZZA_EMESSA,   myOppContainer1_AFTER.ClosureSubstatus__c,  'Opportunity is POLIZZA EMESSA');
        Assert.areEqual(200,                                                                        myOppContainer1_AFTER.amount,               'Opportunity does not amount = 100');
        Assert.isTrue(  myOppContainer1_AFTER.areaOfNeed__c.containsIgnoreCase('Salute'),                                                       'Salute area not present');
        Assert.isTrue(  myOppContainer1_AFTER.areaOfNeed__c.containsIgnoreCase('Casa'),                                                         'Casa area not present');
    }

    @isTest
    static void scenario4_test(){
        
        scenario4_createData();
        System.debug('OpportunityExpirationHelper |  STARTING THE TEST METHOD');

        Test.startTest();
        System.runAs(myAdminUser) {
            // OpportunityExpirationHelper.closeExpiringQuotesOrchestrator(new Set<Id>{myQuote1_1.Id});
            Database.executeBatch( new OpportunityExpirationBatch(OpportunityExpirationBatch.objectToProcess.QUOTE), 200);
        }
        Test.stopTest();

        Quote myQuote1_1_AFTER = [SELECT Id, CommercialStatus__c FROM Quote WHERE Id = :myQuote1_1.Id LIMIT 1];
        System.debug('scenario4_test | myQuote1_1_AFTER.Id ' + myQuote1_1_AFTER.Id + ' CommercialStatus__c'  + myQuote1_1_AFTER.CommercialStatus__c);
        Assert.areEqual(OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_EXPIRED,                   myQuote1_1_AFTER.CommercialStatus__c,        'Quote is not EXPIRED');

        Quote myQuote1_2_AFTER = [SELECT Id, CommercialStatus__c FROM Quote WHERE Id = :myQuote1_2.Id LIMIT 1];
        System.debug('scenario4_test | myQuote1_2_AFTER.Id ' + myQuote1_2_AFTER.Id + ' CommercialStatus__c'  + myQuote1_2_AFTER.CommercialStatus__c);
        Assert.areEqual(OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_SOLD,                   myQuote1_2_AFTER.CommercialStatus__c,           'Quote is not SOLD');

        Quote myQuote2_1_AFTER = [SELECT Id, CommercialStatus__c FROM Quote WHERE Id = :myQuote2_1.Id LIMIT 1];
        System.debug('scenario4_test | myQuote2_1_AFTER.Id ' + myQuote2_1_AFTER.Id + ' CommercialStatus__c'  + myQuote2_1_AFTER.CommercialStatus__c);
        Assert.areEqual(OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_ACTIVE,                   myQuote2_1_AFTER.CommercialStatus__c,         'Quote is not ACTIVE');

        Quote myQuote2_2_AFTER = [SELECT Id, CommercialStatus__c FROM Quote WHERE Id = :myQuote2_2.Id LIMIT 1];
        System.debug('scenario4_test | myQuote2_2_AFTER.Id ' + myQuote2_2_AFTER.Id + ' CommercialStatus__c'  + myQuote2_2_AFTER.CommercialStatus__c);
        Assert.areEqual(OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_SOLD,                       myQuote2_2_AFTER.CommercialStatus__c,       'Quote is not SOLD');

        Opportunity myOppProduct1_AFTER = [SELECT Id, StageName, ClosureSubstatus__c, areaOfNeed__c, OverallAreasOfNeed__c, amount FROM Opportunity WHERE Id = :myOppProduct1.Id LIMIT 1];
        System.debug('scenario4_test | myOppProduct2_AFTER.Id ' + myOppProduct1_AFTER.Id + ' StageName'  + myOppProduct1_AFTER.StageName);
        Assert.areEqual(OpportunityExpirationHelper.OPPORTUNITY_STAGENAME_CLOSE,                    myOppProduct1_AFTER.StageName,              'Opportunity is not closed');
        Assert.areEqual(OpportunityExpirationHelper.OPPORTUNITY_CLOSURE_SUBSTATUS_POLIZZA_EMESSA,   myOppProduct1_AFTER.ClosureSubstatus__c,    'Opportunity is not POLIZZA EMESSA');
        Assert.areEqual(100,                                                                        myOppProduct1_AFTER.amount,                 'Opportunity does not amount = 100');
        Assert.isTrue(  myOppProduct1_AFTER.areaOfNeed__c.containsIgnoreCase('Casa'),                                                           'Casa area not present');
    
        Opportunity myOppProduct2_AFTER = [SELECT Id, StageName, ClosureSubstatus__c, areaOfNeed__c, OverallAreasOfNeed__c, amount FROM Opportunity WHERE Id = :myOppProduct2.Id LIMIT 1];
        System.debug('scenario4_test | myOppProduct2_AFTER.Id ' + myOppProduct2_AFTER.Id + ' StageName'  + myOppProduct2_AFTER.StageName);
        Assert.areNotEqual(OpportunityExpirationHelper.OPPORTUNITY_STAGENAME_CLOSE,                 myOppProduct2_AFTER.StageName,              'Opportunity is closed');
        Assert.areNotEqual(OpportunityExpirationHelper.OPPORTUNITY_CLOSURE_SUBSTATUS_POLIZZA_EMESSA,myOppProduct2_AFTER.ClosureSubstatus__c,    'Opportunity is POLIZZA EMESSA');
        Assert.areEqual(0,                                                                        myOppProduct2_AFTER.amount,                 'Opportunity does not amount = 100');
        // Assert.isTrue(  myOppProduct1_AFTER.areaOfNeed__c.containsIgnoreCase('Salute'),                                                         'Salute area not present');
    
        Opportunity myOppContainer1_AFTER = [SELECT Id, StageName, ClosureSubstatus__c, areaOfNeed__c, OverallAreasOfNeed__c, amount  FROM Opportunity WHERE Id = :myOppContainer1.Id LIMIT 1];
        Assert.areNotEqual(OpportunityExpirationHelper.OPPORTUNITY_STAGENAME_CLOSE,                    myOppContainer1_AFTER.StageName,            'Opportunity is not closed');
        Assert.areNotEqual(OpportunityExpirationHelper.OPPORTUNITY_CLOSURE_SUBSTATUS_POLIZZA_EMESSA,   myOppContainer1_AFTER.ClosureSubstatus__c,  'Opportunity is POLIZZA EMESSA');
        Assert.areEqual(0,                                                                        myOppContainer1_AFTER.amount,               'Opportunity does not amount = 100');
        // Assert.isTrue(  myOppContainer1_AFTER.areaOfNeed__c.containsIgnoreCase('Salute'),                                                       'Salute area not present');
        // Assert.isTrue(  myOppContainer1_AFTER.areaOfNeed__c.containsIgnoreCase('Casa'),                                                         'Casa area not present');
    }

    @isTest
    static void scenario9_test(){
        
        scenario9_createData();
        System.debug('OpportunityExpirationHelper |  STARTING THE TEST METHOD');

        Test.startTest();
        System.runAs(myAdminUser) {
            // OpportunityExpirationHelper.closeExpiringQuotesOrchestrator(new Set<Id>{myQuote1_1.Id});
            Database.executeBatch( new OpportunityExpirationBatch(OpportunityExpirationBatch.objectToProcess.QUOTE), 200);
        }
        Test.stopTest();

        Quote myQuote1_1_AFTER = [SELECT Id, CommercialStatus__c FROM Quote WHERE Id = :myQuote1_1.Id LIMIT 1];
        Assert.areEqual(OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_EXPIRED,                myQuote1_1_AFTER.CommercialStatus__c,     'Quote is not EXPIRED');

        Quote myQuote1_2_AFTER = [SELECT Id, CommercialStatus__c FROM Quote WHERE Id = :myQuote1_2.Id LIMIT 1];
        Assert.areEqual(OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_SOLD,                   myQuote1_2_AFTER.CommercialStatus__c,    'Quote is not SOLD');

        Quote myQuote2_1_AFTER = [SELECT Id, CommercialStatus__c FROM Quote WHERE Id = :myQuote2_1.Id LIMIT 1];
        //System.debug('myQuote2_1_AFTER Id, CommercialStatus__c -> ' + myQuote2_1_AFTER.Id + ', ' + myQuote2_1_AFTER.CommercialStatus__c);
        Assert.areEqual(OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_ACTIVE,                 myQuote2_1_AFTER.CommercialStatus__c,    'Quote is not ACTIVE');

        Quote myQuote2_2_AFTER = [SELECT Id, CommercialStatus__c FROM Quote WHERE Id = :myQuote2_2.Id LIMIT 1];
        //System.debug('myQuote2_2_AFTER Id, CommercialStatus__c -> ' + myQuote2_2_AFTER.Id + ', ' + myQuote2_2_AFTER.CommercialStatus__c);
        Assert.areEqual(OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_SOLD,                   myQuote2_2_AFTER.CommercialStatus__c,    'Quote is not SOLD');

        Opportunity myOppProduct1_AFTER = [SELECT Id, StageName, ClosureSubstatus__c, areaOfNeed__c, OverallAreasOfNeed__c, amount FROM Opportunity WHERE Id = :myOppProduct1.Id LIMIT 1];
        Assert.areEqual(OpportunityExpirationHelper.OPPORTUNITY_STAGENAME_CLOSE,                    myOppProduct1_AFTER.StageName,              'Opportunity is not closed');
        Assert.areEqual(OpportunityExpirationHelper.OPPORTUNITY_CLOSURE_SUBSTATUS_POLIZZA_EMESSA,   myOppProduct1_AFTER.ClosureSubstatus__c,    'Opportunity is not FUORI SLA');
        Assert.areEqual(100,                                                                        myOppProduct1_AFTER.amount,                 'Opportunity does not amount = 0');
        Assert.isTrue(  myOppProduct1_AFTER.areaOfNeed__c.containsIgnoreCase('Casa'),                                                           'Casa area not present');
    
        Opportunity myOppProduct2_AFTER = [SELECT Id, StageName, ClosureSubstatus__c, areaOfNeed__c, OverallAreasOfNeed__c, amount FROM Opportunity WHERE Id = :myOppProduct2.Id LIMIT 1];
        System.debug('myOppProduct2_AFTER Id, StageName, areaOfNeed__c -> ' + myOppProduct2_AFTER.Id + ', ' + myOppProduct2_AFTER.StageName + ', '+ myOppProduct2_AFTER.areaOfNeed__c);
        Assert.areNotEqual(OpportunityExpirationHelper.OPPORTUNITY_STAGENAME_CLOSE,                 myOppProduct2_AFTER.StageName,              'Opportunity is closed');
        Assert.areEqual(null,                                                                       myOppProduct2_AFTER.ClosureSubstatus__c,    'Opportunity is not null');
        Assert.areEqual(0,                                                                          myOppProduct2_AFTER.amount,                 'Opportunity does not amount = 100');
        Assert.areEqual(null,                                                                       myOppProduct2_AFTER.areaOfNeed__c,          'Quote info was rolled up but is not closed');
    
        Opportunity myOppContainer1_AFTER = [SELECT Id, StageName, ClosureSubstatus__c, areaOfNeed__c, OverallAreasOfNeed__c, amount  FROM Opportunity WHERE Id = :myOppContainer1.Id LIMIT 1];
        Assert.areNotEqual(OpportunityExpirationHelper.OPPORTUNITY_STAGENAME_CLOSE,                 myOppContainer1_AFTER.StageName,            'Opportunity is not open');
        Assert.areEqual(null,                                                                       myOppContainer1_AFTER.ClosureSubstatus__c,  'Opportunity is not null');
        Assert.areEqual(0,                                                                          myOppContainer1_AFTER.amount,                 'Opportunity does not amount = 0');
    }

    @isTest
    static void Coverage_scheduler(){

        User adminUser = UTL_DataFactory.createUser('Admintst', '<EMAIL>', 'User', 'System Administrator', '', '<EMAIL>', true);

        // Map<String, Object> myAccData = new Map<String, Object>{
        //     'Name' => 'testAccount',
        //     'RecordTypeId' => Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId() 
        // };
        Account myAcc = new Account(Name = 'testAccount', recordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId());


        Map<String, Object> myOppContainerData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Agenziale').getRecordTypeId(),
            'stageName' => 'Nuovo',
            'closeDate' => Date.Today().addDays(-10),
            'name' => 'testParentOpp',
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(-10)
        };
        Opportunity myOppContainer = (Opportunity)createRecord('Opportunity', myOppContainerData, true );

        Map<String, Object> myOppProductData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Prodotto').getRecordTypeId(),
            'stageName' => 'Nuovo',
            'closeDate' => Date.Today().addDays(-10),
            'name' => 'testOpp1_ExpiredOpp',
            'parent__c' => myOppContainer.Id,
            'WorkingSLAExpiryDate__c' => Datetime.now().addDays(-10)
        };
        Opportunity myOppProduct = (Opportunity)createRecord('Opportunity', myOppProductData, true );


        Map<String, Object> myQuote1data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id,
            'Name' => 'testQuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct.Id,
            'CommercialStatus__c' => OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_SOLD

        };
        Quote myQuote1 = (Quote)createRecord('Quote', myQuote1data, true );

        Map<String, Object> myQuote2data = new Map<String, Object>{
            // 'AccountId' => myAcc.Id,
            'Name' => 'test2QuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOppProduct.Id

        };
        Quote myQuote2 = (Quote)createRecord('Quote', myQuote2data, true );

        String CRON_EXP = '0 0 0 3 9 ? 2042';


        Test.startTest();
        System.runAs(adminUser) {
            String jobId = System.schedule('OpportunityExpirationScheduler', CRON_EXP, new OpportunityExpirationScheduler());
        }
        Test.stopTest();

    }


}