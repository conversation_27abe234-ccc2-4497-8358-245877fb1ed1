@isTest
private class TemplateSubstitutionTest {
    
    @isTest
    static void testConstructor() {
        String key = 'nome';
        String value = 'Mario';
        TemplateSubstitution ts = new TemplateSubstitution(key, value);
        
        System.assertEquals(key, ts.keyFlow, 'Il campo keyFlow dovrebbe essere valorizzato correttamente');
        System.assertEquals(value, ts.valueFlow, 'Il campo valueFlow dovrebbe essere valorizzato correttamente');
    }
    
    @isTest
    static void testParseDynamicJSON() {
        String jsonInput = '{\"nome\":\"<PERSON>\",\"cognome\":\"Rossi\"}';
        List<String> inputList = new List<String>{ jsonInput };
            
		Test.startTest();
        List<List<TemplateSubstitution>> result = TemplateSubstitution.parseDynamicJSON(inputList);
        Test.stopTest();
        
        System.assertEquals(1, result.size(), 'Dovrebbe esserci una lista esterna');
        System.assertEquals(2, result[0].size(), 'Dovrebbero esserci due TemplateSubstitution nella lista interna');
        
        Set<String> keys = new Set<String>{ 'nome', 'cognome' };
            for (TemplateSubstitution ts : result[0]) {
                System.assert(keys.contains(ts.keyFlow), 'La chiave dovrebbe essere presente nel JSON');
            }
    }
    
    @isTest
    static void testParseDynamicJSONWithEmptyList() {
        List<String> emptyList = new List<String>();
        List<List<TemplateSubstitution>> result = TemplateSubstitution.parseDynamicJSON(emptyList);
        System.assertEquals(1, result.size(), 'Dovrebbe esserci una lista esterna anche se vuota');
        System.assertEquals(0, result[0].size(), 'La lista interna dovrebbe essere vuota');
    }
    
    @isTest
    static void testParseDynamicJSONWithNull() {
        List<List<TemplateSubstitution>> result = TemplateSubstitution.parseDynamicJSON(null);
        System.assertEquals(1, result.size(), 'Dovrebbe esserci una lista esterna anche se null');
        System.assertEquals(0, result[0].size(), 'La lista interna dovrebbe essere vuota');
    }
}