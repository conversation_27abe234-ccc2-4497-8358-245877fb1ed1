/***************************************************************************************************************************************************
* <AUTHOR>
* @description    PortfolioAssignmentInvocable class handles the invocable method for portfolio assignment operations. It includes methods for 
*                 making asynchronous callouts to an external service and handling the responses. This class defines custom exceptions, request, 
*                 and response structures for the callout.
* @date           2024-04-29
****************************************************************************************************************************************************/
public with sharing class PortfolioAssignmentInvocable {
    
    // Custom exception class for handling portfolio assignment related exceptions.
    public class PortfolioAssignmentException extends Exception {}
    
    // Constant integration ID used for identifying the portfolio assignment callout.
    private final static String INTEGRATION_ID = 'PortfolioAssignment';
    
    /******************************************************************************************
    * @description  Invocable method for handling portfolio assignment requests. It processes 
    *               the requests and makes an asynchronous callout.
    * @param        requests - A list of InvocableRequest objects containing the callout parameters.
    *******************************************************************************************/
    @InvocableMethod(label='Portfolio Assignment')
    public static void call(List<InvocableRequest> requests) {
        if(requests.isEmpty()) {
            throw new PortfolioAssignmentException('No valid requests were provided to the portfolio assignment callout');
        }
        InvocableRequest request = requests[0];
        makeCalloutAsync(request.operation, request.agencyCode, request.contractId, request.cip);
    }

    /******************************************************************************************
    * @description  Asynchronous method for making the portfolio assignment callout. It constructs 
    *               and sends an HTTP request, and handles the response.
    * @param        operation - The operation to be performed.
    * @param        agencyCode - The agency code for the request.
    * @param        contractId - The contract ID for the request.
    * @param        cip - The CIP for the request.
    *******************************************************************************************/
    @future(callout=true)
    public static void makeCalloutAsync(String operation, String agencyCode, String contractId, String cip) {
        // Map<String,String> valuesByPlaceHolders = new Map<String,String> {
        //     '{operation}' => operation
        // };
        // HttpRequest httpRequest = IntegrationSettingUtility.getHttpRequest(INTEGRATION_ID, valuesByPlaceHolders);
        // httpRequest.setBody(JSON.serialize(
        //     new PortfolioAssignmentRequest(agencyCode, contractId, cip)
        // ));
        // HttpResponse httpResponse = IntegrationSettingUtility.sendRequest(httpRequest, 'PortfolioAssignmentInvocable', 'callout');
        
        // if(httpResponse.getStatusCode() == 200) {
        //     PortfolioAssignmentResponse responseBody = (PortfolioAssignmentResponse)JSON.deserialize(httpResponse.getBody(), PortfolioAssignmentResponse.class);
        // } else {
        //     PortfolioAssignmentResponseError responseBody = (PortfolioAssignmentResponseError)JSON.deserialize(httpResponse.getBody(), PortfolioAssignmentResponseError.class);
        //     throw new CalloutException('An error has occured during the portfolio assignment callout');
        // }
    }

    // Inner class representing the invocable request structure for portfolio assignment.
    public class InvocableRequest {
        @InvocableVariable
        public String operation;
        @InvocableVariable
        public String agencyCode;
        @InvocableVariable
        public String contractId;
        @InvocableVariable
        public String cip;
    }

    // Inner class representing the request body for the portfolio assignment callout.
    public class PortfolioAssignmentRequest {
        @InvocableVariable
        public String agencyCode;
        @InvocableVariable
        public String contractId;
        @InvocableVariable
        public String cip;

        public PortfolioAssignmentRequest(String agencyCode, String contractId, String cip) {
            this.agencyCode = agencyCode;
            this.contractId = contractId;
            this.cip = cip;
        }
    }

    // Inner class representing the response body for a successful portfolio assignment callout.
    public class PortfolioAssignmentResponse {
        @InvocableVariable
        public String result;
    }

    // Inner class representing the response body for an error during the portfolio assignment callout.
    public class PortfolioAssignmentResponseError {
        @InvocableVariable
        public String errorCode;
        @InvocableVariable
        public String errorDescription;
    }
}