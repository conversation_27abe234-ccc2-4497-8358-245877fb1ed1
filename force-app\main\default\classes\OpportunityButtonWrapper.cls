/***************************************************************************************************************************************************
* <AUTHOR>
* @description    OpportunityButtonWrapper class extends ButtonWrapperAbstract and provides a wrapper for handling opportunity button actions. 
*                 This class contains inner classes RecordData and Checks for managing record-specific data and validation checks respectively. 
*                 It also overrides methods to create a map of generic checks.
* @date           2024-03-07
****************************************************************************************************************************************************/
public with sharing class OpportunityButtonWrapper extends ButtonWrapperAbstract {

    /******************************************************************************************
    * @description  Inner class RecordData extends GenericRecordData and holds additional fields 
    *               specific to opportunity records such as clientType, slaRemainingHours, stage, 
    *               substatus, and omnichannelAgreement.
    *******************************************************************************************/
    public class RecordData extends GenericRecordData {
        public String clientType;
        public Integer slaRemainingHours;
        public String stage;
        public String substatus;
        public Boolean omnichannelAgreement;
        public Boolean hasChildren;
        public Boolean canBeParent;
        public Boolean canBeClosed;
    }

    /******************************************************************************************
    * @description  Inner class Checks extends GenericChecks and includes specific validation 
    *               checks for the opportunity fields clientType, slaRemainingHours, stage, 
    *               substatus, and omnichannelAgreement. It overrides the createGenericChecksMap 
    *               method to populate a map with these checks.
    *******************************************************************************************/
    public class Checks extends GenericChecks {
        public StringCheck clientType;
        public NumberCheck slaRemainingHours;
        public StringCheck stage;
        public StringCheck substatus;
        public BooleanCheck omnichannelAgreement;
        public BooleanCheck hasChildren;
        public BooleanCheck canBeParent;
        public BooleanCheck canBeClosed;

        /******************************************************************************************
        * @description  Overrides the createGenericChecksMap method to populate a map with the 
        *               specific checks for opportunity fields. It removes any null entries from 
        *               the map.
        *******************************************************************************************/
        public override void createGenericChecksMap() {
            genericChecksMap = new Map<String, GenericCheck> {
                'clientType' => this.clientType,
                'slaRemainingHours' => this.slaRemainingHours,
                'stage' => this.stage,
                'substatus' => this.substatus,
                'omnichannelAgreement' => this.omnichannelAgreement,
                'hasChildren' => this.hasChildren,
                'canBeParent' => this.canBeParent,
                'canBeClosed' => this.canBeClosed
            };
            for(String key : genericChecksMap.keySet()) {
                if(genericChecksMap.get(key) == null) {
                    genericChecksMap.remove(key);
                }
            }
        }
    }
}
