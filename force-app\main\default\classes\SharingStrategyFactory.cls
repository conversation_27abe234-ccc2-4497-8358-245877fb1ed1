public class SharingStrategyFactory {
    public static SharingStrategy getStrategy(String sobjectType) {
        switch on sobjectType {
            when 'User' { return new UserSharingStrategy(); }
            when 'ServiceResource' { return new ServiceResourceSharingStrategy(); }
            when 'NetworkUser__c' { return new NetworkUserSharingStrategy(); }
            when 'ServiceTerritory' { return new ServiceTerritorySharingStrategy(); }
            when else { throw new IllegalArgumentException('Unsupported type: ' + sobjectType); }
        }
    }
}