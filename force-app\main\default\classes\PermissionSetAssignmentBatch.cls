/**
 * PermissionSetAssignmentBatch class provides methods to add permisssionSet to User.
 * 
 * Note: Add any additional notes or warnings here.
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2023-10-01
 * @cicd_tests PermissionSetAssignmentBatchTest
 */
global with sharing class PermissionSetAssignmentBatch implements Database.Batchable<sObject>, Database.Stateful{
    
    String query;
    Boolean isInsert;
    List<Id> listUserId;
    Map<String,Id> permissionSetMap;
    List<PermissionSetAssignment> listPermissionSetAssignmentToInsert = new List<PermissionSetAssignment>();
    List<PermissionSetAssignment> listPermissionSetAssignmentToDelete = new List<PermissionSetAssignment>();
    private static final String COMPANY = 'Compagnia';
    private static final String MANDATO_UNISALUTE = 'MandatoUniSalute';
    private static final String MANDATO_UNIPOLSAI = 'MandatoUnipolSai';

    global PermissionSetAssignmentBatch(List<Id> listUserId, Boolean isInsert){
        System.debug('PermissionSetAssignmentBatch.constructor');
        this.isInsert = isInsert;
        this.listUserId = listUserId; 
        Map<String,Id> permissionSetMap = new Map<String,Id>();
        List<PermissionSet> permissionSetList = [SELECT Id, Name FROM PermissionSet WHERE Name IN (:MANDATO_UNISALUTE, :MANDATO_UNIPOLSAI)];
        for(PermissionSet p : permissionSetList){
            permissionSetMap.put(p.Name, p.Id);
        }
        
        this.permissionSetMap = permissionSetMap;
        System.debug('permissionSetMap: ' + permissionSetMap);
    }

    global Database.QueryLocator start(Database.BatchableContext BC) {
        System.debug('PermissionSetAssignmentBatch.start');
        query = 'SELECT Id, Name, IdAzienda__c FROM User WHERE Id IN :listUserId';
        return Database.getQueryLocator(query);
    }

    
    global void execute(Database.BatchableContext BC, List<User> scope) {
        try{
            System.debug('PermissionSetAssignmentBatch.execute');
            System.debug('scope: ' + scope);
            System.debug('Is Insert: ' + this.isInsert);    
            // List to store agency IDs
            List<String> agencyId = new List<String>();
            
            // Map to store the relationship between agency ID and company name
            Map<Id,List<FinServ__AccountAccountRelation__c>> mapAgencyAAR = new Map<Id,List<FinServ__AccountAccountRelation__c>>();
            
            // Collect agency IDs from the scope
            for(User u : scope){
                agencyId.add(u.IdAzienda__c);
            }

            // Query to get the relationship between agencies and companies
            List<FinServ__AccountAccountRelation__c> listAgencyCompany = [SELECT id , FinServ__Account__c, FinServ__RelatedAccount__c, FinServ__RelatedAccount__r.name, FinServ__Role__r.name, FinServ__Role__r.FinServ__InverseRole__c 
                                                                            FROM FinServ__AccountAccountRelation__c 
                                                                            WHERE FinServ__Account__c IN :agencyId AND FinServ__Role__r.FinServ__InverseRole__c = :COMPANY];

            // Map to store the relationship between agency ID and company
            for(FinServ__AccountAccountRelation__c aar : listAgencyCompany){
                if(mapAgencyAAR.get(aar.FinServ__Account__c) == null){
                    List<FinServ__AccountAccountRelation__c> companyList = new List<FinServ__AccountAccountRelation__c>();
                    companyList.add(aar);
                    mapAgencyAAR.put(aar.FinServ__Account__c, companyList);
                }else{
                    mapAgencyAAR.get(aar.FinServ__Account__c).add(aar);
                }
                
            }

            if(!this.isInsert)  {
               checkUserPermissionAssignment(scope);
            }
            
            
            // Iterate through the users in the scope
            for(User u : scope){
                
                List<FinServ__AccountAccountRelation__c> listCompanyAgency = mapAgencyAAR.get(u.IdAzienda__c);
                
                for(FinServ__AccountAccountRelation__c aar : listCompanyAgency){
                    String companyName = aar.FinServ__RelatedAccount__r.Name;
                    System.debug('companyName: ' + companyName);
                    if('UniSalute'.equalsIgnoreCase(companyName)){
                        System.debug('UniSalute');
                        
                        Boolean check = false;
                        PermissionSetAssignment psa = new PermissionSetAssignment();
                        if(!this.isInsert ){
                                psa.AssigneeId = u.Id;
                                Id permissionSetId = this.permissionSetMap.get(MANDATO_UNISALUTE);
                                psa.PermissionSetId = permissionSetId;
                                check = true;
                        }else{
                            psa.AssigneeId = u.Id;
                            Id permissionSetId = this.permissionSetMap.get(MANDATO_UNISALUTE);
                            psa.PermissionSetId = permissionSetId;
                            check = true;
                        }
                        if(check) this.listPermissionSetAssignmentToInsert.add(psa);
                        
                    } else if('UnipolSai'.equalsIgnoreCase(companyName)){
                        System.debug('UnipolSai');
                        
                        Boolean check = false;
                        PermissionSetAssignment psa = new PermissionSetAssignment();
                        if(!this.isInsert){
                                psa.AssigneeId = u.Id;
                                Id permissionSetId = this.permissionSetMap.get(MANDATO_UNIPOLSAI);
                                psa.PermissionSetId = permissionSetId;
                                check = true;
                        }else if(this.permissionSetMap != null && this.permissionSetMap.containsKey(MANDATO_UNIPOLSAI)){
                            psa.AssigneeId = u.Id;
                            Id permissionSetId = this.permissionSetMap.get(MANDATO_UNIPOLSAI);   
                            psa.PermissionSetId = permissionSetId;
                            check = true;
                        }     
                        if(check) this.listPermissionSetAssignmentToInsert.add(psa);
                    }
                }
                
                
            }
            
            // Debug log to check the list of permission set assignments to insert
            System.debug('listPermissionSetAssignmentToInsert: ' + this.listPermissionSetAssignmentToInsert);
        }catch(Exception e){
            System.debug('PermissionSetAssignmentBatch.execute - Exception: ' + e.getMessage()+' At Line : ' + e.getLineNumber());
        }
        
    }

    global void finish(Database.BatchableContext BC) {
        System.debug('PermissionSetAssignmentBatch.finish');
        try{
            System.debug('this.isInsert: ' + this.isInsert);
            // If the batch is for insertion, delete the permission set assignments
            if(!this.isInsert){
                System.debug('this.listPermissionSetAssignmentToDelete : ' + this.listPermissionSetAssignmentToDelete);
                delete this.listPermissionSetAssignmentToDelete;
            }
            
            System.debug('this.listPermissionSetAssignmentToInsert : ' + this.listPermissionSetAssignmentToInsert); 
            if(!this.listPermissionSetAssignmentToInsert.isEmpty()){
                insert this.listPermissionSetAssignmentToInsert;
            }
            
        }catch(Exception e){
            System.debug('PermissionSetAssignmentBatch.finish - Exception: ' + e.getMessage()+' At Line : ' + e.getLineNumber());
        }
    }
    
    /**
     * This method checks the existing permission set assignments for the given list of users.
     * If any permission set assignments are found for the specified permission sets, they are added to the list for deletion.
     *
     * @param userList The list of users to check for permission set assignments.
     */
    public void checkUserPermissionAssignment(List<User> userList){
        // Map to store the user ID and their corresponding permission set names
        Map<Id, List<String>> userPermissionSetMap = new Map<Id, List<String>>();
        
        // Query to get the permission set assignments for the given users and specified permission sets
        List<PermissionSetAssignment> checkListPsa = [SELECT Id, AssigneeId, PermissionSetId, PermissionSet.Name
                                                      FROM PermissionSetAssignment 
                                                      WHERE AssigneeId IN :userList AND PermissionSet.Name IN (:MANDATO_UNISALUTE, :MANDATO_UNIPOLSAI)];
        
        // Add the found permission set assignments to the list for deletion
        for(PermissionSetAssignment psa : checkListPsa){
            this.listPermissionSetAssignmentToDelete.add(psa);     
        }                                                    
    }

}