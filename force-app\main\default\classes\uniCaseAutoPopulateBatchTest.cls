@isTest
public class uniCaseAutoPopulateBatchTest {

    @testSetup
    static void setupData() {

        insert new CaseActivityInit__c(
            LeoCode__c = '10041010',NeedsCloseCallout__c = false,Source__c = 'Agente',CCEngaged__c = false,PossibleAssignemnt__c = '2-Assegnazione ad Agenzia',AssignmentRules__c = 'R08',
            LeoPriority__c = false,ShowOutcome__c = false,IsPlannable__c = true,Nature__c = 'Attività Commerciale',Area__c = 'Rinnovi',ClosedDate__c = 30,Activity__c = 'Memo Rinnovi',
            Detail__c = '<PERSON><PERSON> in copertura il titolo in scadenza (AUTO)',AreaOfNeed__c = 'AUTO',DueDateDays__c = 9,IsCallBack__c = false,IsReservedArea__c = false,RequiredPolicy__c = true,
            RequiredIncident__c = false,GetInTouch__c = false,ShowCloseManual__c = true);

        List<Case> casesToInsert = new List<Case>();
        casesToInsert.add(new Case(DrSistemaUltimaModifica__c='Leonardo_upt', DrCompagnia__c='1', DrAgenziaMadre__c='01853', DrAgenziaFiglia__c='01853', DrCfPivaCliente__c='****************', ClosedDate__c=null, DrIdAttivitaLeonardo__c='999964796785993523', ExternalId__c='', DrSistemaId__c='', DrDominio__c='1004', DrCodice__c='1010', BusinessKey__c='', ClaimNumber__c='', DrContextEntity__c='', DrAttSTTPCD__c='', DueDate__c=Date.newInstance(2025, 7, 12), DrAssegnazione__c='2', DrAssegnatario__c='1', DrAssegnatarioDesc__c='AGENZIA', DrAnnotazione__c='test', DrFlagAnnotazione__c='S', CCEngaged__c=false, isCCEnabled__c=false, DrGestioneMC__c='N', DrIdProcesso__c='', DrNumeroRicontatto__c='', DrFasciaOrarioRicontatto__c='', DrMotivazioneRichiestaRiscatto__c='', DrDataSospRiattPolizza__c=null, DrIdDocumenti__c='', DrStepRichiestaPU__c='', DrIdFolderPU__c='N', DrDataFineSospPolizza__c=null));
        casesToInsert.add(new Case(DrSistemaUltimaModifica__c='Leonardo_upt', DrCompagnia__c='1', DrAgenziaMadre__c='01853', DrAgenziaFiglia__c='01853', DrCfPivaCliente__c='****************', ClosedDate__c=null, DrIdAttivitaLeonardo__c='999964796785993528', ExternalId__c='', DrSistemaId__c='', DrDominio__c='1004', DrCodice__c='1010', BusinessKey__c='', ClaimNumber__c='', DrContextEntity__c='', DrAttSTTPCD__c='', DueDate__c=Date.newInstance(2025, 7, 12), DrAssegnazione__c='1', DrAssegnatario__c='00101', DrAssegnatarioDesc__c='00101 - B&B ASSICURAZIONI S.R.L.', DrAnnotazione__c='', DrFlagAnnotazione__c='N', CCEngaged__c=false, isCCEnabled__c=false, DrGestioneMC__c='N', DrIdProcesso__c='', DrNumeroRicontatto__c='', DrFasciaOrarioRicontatto__c='', DrMotivazioneRichiestaRiscatto__c='', DrDataSospRiattPolizza__c=null, DrIdDocumenti__c='', DrStepRichiestaPU__c='', DrIdFolderPU__c='N', DrDataFineSospPolizza__c=null));
        casesToInsert.add(new Case(DrSistemaUltimaModifica__c='Leonardo_upt', DrCompagnia__c='1', DrAgenziaMadre__c='01853', DrAgenziaFiglia__c='01853', DrCfPivaCliente__c='****************', ClosedDate__c=null, DrIdAttivitaLeonardo__c='999964796785993342', ExternalId__c='', DrSistemaId__c='', DrDominio__c='1004', DrCodice__c='1010', BusinessKey__c='', ClaimNumber__c='', DrContextEntity__c='', DrAttSTTPCD__c='', DueDate__c=Date.newInstance(2025, 6, 10), DrAssegnazione__c='1', DrAssegnatario__c='00101', DrAssegnatarioDesc__c='00101 - B&B ASSICURAZIONI S.R.L.', DrAnnotazione__c='test', DrFlagAnnotazione__c='N', CCEngaged__c=false, isCCEnabled__c=false, DrGestioneMC__c='N', DrIdProcesso__c='', DrNumeroRicontatto__c='', DrFasciaOrarioRicontatto__c='', DrMotivazioneRichiestaRiscatto__c='', DrDataSospRiattPolizza__c=null, DrIdDocumenti__c='', DrStepRichiestaPU__c='', DrIdFolderPU__c='N', DrDataFineSospPolizza__c=null));
        
        system.debug(casesToInsert.size());
        insert casesToInsert;
    }

    @isTest
    static void testBatchExecution() {
        Test.startTest();
        uniCaseAutoPopulateBatch batch = new uniCaseAutoPopulateBatch();
        Database.executeBatch(batch);
        Test.stopTest();

        Case updatedCase = [SELECT Id, LeoActivityCode__c, RecordTypeId, StartDate__c FROM Case LIMIT 1];
        /*System.assertEquals('10041010', updatedCase.LeoActivityCode__c, 'LeoActivityCode__c non valorizzato correttamente');
        System.assertNotEquals(null, updatedCase.StartDate__c, 'StartDate__c dovrebbe essere valorizzato');
        System.assertNotEquals(null, updatedCase.RecordTypeId, 'RecordTypeId dovrebbe essere valorizzato');*/
    }
}