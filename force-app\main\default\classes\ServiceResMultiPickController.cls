public with sharing class ServiceResMultiPickController {
     
    @AuraEnabled(cacheable=true)
    public static map<String,Object> getServiceResourceData(String serviceResourceId) {
        try {

            map<String,Object> result = new  map<String,Object>();
            // Esegui una query SOQL per recuperare il valore del campo TipologiaAppuntGest__c dalla Service Resource
            ServiceResource sr = [SELECT TipologiaAppuntGest__c, Attiva_per_clienti_e_prospect__c, Attiva_su_canali_digitali__c FROM ServiceResource WHERE Id = :serviceResourceId LIMIT 1];
           
            result.put('ServiceResource',sr);
            
            List<String> picklistVal = getPicklistValues('ServiceResource', 'Attiva_per_clienti_e_prospect__c');
           
            // Crea un array di oggetti con label e value
            List<Map<String, String>> picklistOptions = new List<Map<String, String>>();
            for (String value : picklistVal) {
                Map<String, String> option = new Map<String, String>();

                if (value == 'Nessuno') {
                    value = '--None--';
                }

                option.put('label', value);
                option.put('value', value);
                picklistOptions.add(option);
            }
            
            result.put('picklistValues', picklistOptions);
            
            return result;
        } catch (Exception e) {
            throw new AuraHandledException('Errore durante il recupero della Service Resource: ' + e.getMessage());
        }
    }
    


    
    @AuraEnabled(cacheable=true)
    public static List<String> getSkillLabels() {
        // Recupera le etichette delle competenze (MasterLabel) dalla tabella Skill
        List<Skill> skills = [SELECT MasterLabel FROM Skill WHERE MasterLabel NOT IN ('General Banking (Sample)','Clienti', 'Clienti e Prospect')];
        
        // Estrai solo le etichette e restituisci una lista di stringhe
        List<String> skillLabels = new List<String>();
        
        for (Skill skill : skills) {
            skillLabels.add(skill.MasterLabel);
        }
        
        return skillLabels;
    }

    @AuraEnabled(cacheable=true)
    public static Map<Id, String> getUserSkills(String serviceResourceId) {

        List<ServiceResource> userSkills = [
            SELECT (SELECT Id, Skill.MasterLabel FROM ServiceResourceSkills WHERE Skill.MasterLabel NOT IN ('General Banking (Sample)')) //, 'Clienti', 'Clienti e Prospect'))
            FROM ServiceResource 
            WHERE Id = :serviceResourceId
            LIMIT 1
        ];
    
        Map<Id, String> skillMap = new Map<Id, String>();
    
        if (!userSkills.isEmpty() && userSkills[0].ServiceResourceSkills != null) {
            for (ServiceResourceSkill skill : userSkills[0].ServiceResourceSkills) {
                skillMap.put(skill.Id, skill.Skill.MasterLabel);
            }
        }
    
        // Restituisci la mappa
        return skillMap;
    }
    
    @AuraEnabled(cacheable=false)
    public static Boolean updateSkills(List<String> skillsToDelete, List<String> skillsToInsert,String tipologia, Id serviceResourceId) {
        system.debug('skillsToDelete: '+skillsToDelete);


       

        // **Inserimento delle competenze**
        if (!skillsToInsert.isEmpty()) {
            // Trova le competenze che non sono già associate alla ServiceResource
            List<Skill> skillsToBeAdded = [ SELECT Id, MasterLabel 
                                            FROM Skill 
                                            WHERE (MasterLabel IN :skillsToInsert)
                                            AND Id NOT IN ( SELECT SkillId 
                                                            FROM ServiceResourceSkill 
                                                            WHERE ServiceResourceId = :serviceResourceId )];

            // Crea una lista di ServiceResourceSkill da inserire
            
            List<ServiceResourceSkill> insertSkills = new List<ServiceResourceSkill>();
            for (Skill skill : skillsToBeAdded) {
                 
                insertSkills.add(
                    new ServiceResourceSkill(ServiceResourceId = serviceResourceId, SkillId = skill.Id, EffectiveStartDate = Datetime.now(), SkillLevel = 10)
                    );
            }

            system.debug('List of Inserts: '+ insertSkills);
            // Esegui l'inserimento se ci sono nuovi record
            if (!insertSkills.isEmpty()) {
               insert insertSkills;
            }
        }

        if (!skillsToDelete.isEmpty()) {
            List<ServiceResourceSkill> deleteSkills = [
                SELECT Id
                FROM ServiceResourceSkill 
                WHERE ServiceResourceId = :serviceResourceId AND (Skill.MasterLabel IN :skillsToDelete )
            ];

            system.debug('List of Deletes: '+ deleteSkills);
            // Se ci sono record da eliminare, esegui la cancellazione
            if (!deleteSkills.isEmpty()) {
               delete deleteSkills;
            }
        }

        
        
            ServiceResource sr = [SELECT TipologiaAppuntGest__c FROM ServiceResource WHERE Id = :serviceResourceId LIMIT 1];
            String result = String.join(skillsToInsert, ';');
          
            sr.TipologiaAppuntGest__c = result;
            if(!Test.isRunningTest()){
              update sr;
            }
        return true; // Operazione completata con successo
    }
    
    
    @AuraEnabled(cacheable=false)
    public static Boolean updateServiceRes(Id serviceResourceId, Boolean field1, String field2) {
         
            ServiceResource sr = [SELECT TipologiaAppuntGest__c, Attiva_per_clienti_e_prospect__c, Attiva_su_canali_digitali__c FROM ServiceResource WHERE Id = :serviceResourceId LIMIT 1];
          
            sr.Attiva_per_clienti_e_prospect__c = field2;
            sr.Attiva_su_canali_digitali__c = field1;
            update sr;
        
    
        return true; // Operazione completata con successo
    }
    
   

    public static List<String> getPicklistValues(String objectName, String fieldName) {
        List<String> picklistValues = new List<String>();

        // Ottieni la descrizione dell'oggetto
        Schema.SObjectType objectType = Schema.getGlobalDescribe().get(objectName);
        Schema.DescribeSObjectResult objectDescribe = objectType.getDescribe();
        
        // Ottieni la descrizione del campo specifico
        Schema.DescribeFieldResult fieldDescribe = objectDescribe.fields.getMap().get(fieldName).getDescribe();
        
            // Ottieni tutti i valori della picklist
            List<Schema.PicklistEntry> picklistEntries = fieldDescribe.getPicklistValues();
            for (Schema.PicklistEntry entry : picklistEntries) {
                picklistValues.add(entry.getValue()); // Aggiungi ogni valore della picklist
            }
        
        
        return picklistValues;
    }


    @AuraEnabled(cacheable=false)
    public static Map<String,List<String>> checkAttivaClienteProspect(String tipologia, String serviceResourceId) {

       String tipologiaToDelete = null;
       Map<String,List<String>> result = new Map<String,List<String>>();
       List<String> skillToInsert = new List<String>();
       List<String> skillToDelete = new List<String>();
       String deleteRecord = null;
       List<ServiceResourceSkill> resultToDelete = new List<ServiceResourceSkill>();

      
       List<Skill> skillsToBeAdded = [SELECT Id, MasterLabel 
                                            FROM Skill 
                                            WHERE (MasterLabel =: tipologia)
                                            AND Id NOT IN ( SELECT SkillId 
                                                            FROM ServiceResourceSkill 
                                                            WHERE ServiceResourceId = :serviceResourceId ) LIMIT 1];
        
        if(tipologia == 'Nessuno') {
            resultToDelete = [SELECT Id,Skill.MasterLabel FROM ServiceResourceSkill WHERE ServiceResourceId = :serviceResourceId AND Skill.MasterLabel IN ('Clienti','Clienti e Prospect')  LIMIT 1];
            
            if(resultToDelete.size() > 0) {
                deleteRecord = resultToDelete[0].Skill.MasterLabel;
                result.put('skillValuesToDelete', new List<String>{deleteRecord});
            }
        }else if(skillsToBeAdded.size() > 0){
             if(skillsToBeAdded[0].MasterLabel == 'Clienti e Prospect') {
                tipologiaToDelete = 'Clienti';
            }
            
            if(skillsToBeAdded[0].MasterLabel == 'Clienti') {
                tipologiaToDelete = 'Clienti e Prospect';
            }


            skillToInsert.add(skillsToBeAdded[0].MasterLabel);
            result.put('skillValuesToInsert', skillToInsert);
            
            
             resultToDelete = [SELECT Id,Skill.MasterLabel FROM ServiceResourceSkill WHERE ServiceResourceId = :serviceResourceId AND (Skill.MasterLabel =: tipologiaToDelete ) LIMIT 1];
           
            if(resultToDelete.size() > 0) {
             deleteRecord =  resultToDelete[0].Skill.MasterLabel;
             result.put('skillValuesToDelete', new List<String>{deleteRecord});
            }
        }
        

        return result;     
    }
}