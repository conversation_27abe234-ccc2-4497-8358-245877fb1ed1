/**
 * @description       : 
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 04-25-2025
 * @last modified by  : <EMAIL>
**/
public without sharing class CaseRulesBeforeInsert {

    public static void logicCaseActivityInit(List<Case> newListCase) {
        if (!Schema.sObjectType.CaseActivityInit__c.isAccessible()) {
            return;
        }

        if (newListCase.isEmpty()) {
            return;
        }

        List<CaseActivityInit__c> caseActivityInits = getCaseActivityInits();
        if (caseActivityInits.isEmpty()) {
            return;
        }

        Boolean isTris = newListCase[0].LeoActivityCode__c == null;
        system.debug('%%%% isTris: ' + isTris);
        Map<String, CaseActivityInit__c> manualCase = new Map<String, CaseActivityInit__c>();
        Map<Integer, CaseActivityInit__c> externalCase = new Map<Integer, CaseActivityInit__c>();

        populateCaseMaps(caseActivityInits, manualCase, externalCase, isTris);

        if (isTris) {
            system.debug('%%%% manualCase: ' + manualCase);
            processManualCases(newListCase, manualCase);
        } else {
            processExternalCases(newListCase, externalCase);
        }
    }

    private static List<CaseActivityInit__c> getCaseActivityInits() {
        return [SELECT IsDeleted, Name, OwnerId,CreatedDate,CreatedById, LastModifiedDate, LastModifiedById,SystemModstamp, LastActivityDate, LeoCode__c, NeedsCloseCallout__c, Source__c, CCEngaged__c, PossibleAssignemnt__c, 
                AssignmentRules__c, LeoHeat__c, LeoPriority__c, ShowOutcome__c, IsPlannable__c, Nature__c, Area__c, ClosedDate__c, Activity__c, Detail__c, 
                AreaOfNeed__c, DueDateDays__c, IsCallBack__c, IsReservedArea__c, RequiredPolicy__c, RequiredIncident__c, GetInTouch__c, ShowCloseManual__c, 
                OverrideAgecy__c FROM CaseActivityInit__c WHERE OverrideAgecy__c = null AND LeoCode__c!= null ];
    }

    private static void populateCaseMaps(List<CaseActivityInit__c> caseActivityInits, Map<String, CaseActivityInit__c> manualCase, Map<Integer, CaseActivityInit__c> externalCase, Boolean isTris) {
        for (CaseActivityInit__c caseActInit : caseActivityInits) {
            if (isTris) {
                System.debug('&&: ' + caseActInit.Area__c + '-' + caseActInit.Activity__c + '-' + caseActInit.Detail__c);
                manualCase.put(caseActInit.Area__c + '-' + caseActInit.Activity__c + '-' + caseActInit.Detail__c, caseActInit);
            } else {
                externalCase.put(Integer.valueOf(caseActInit.LeoCode__c), caseActInit);
            }
        }
    }

    private static void processManualCases(List<Case> newListCase, Map<String, CaseActivityInit__c> manualCase) {
        for (Case c : newListCase) {
            String key = c.Area__c + '-' + c.Activity__c + '-' + c.Detail__c;
            system.debug('%%%% key: ' + key);
            system.debug('%%%% manualCase: ' + manualCase);
            system.debug('%%%% manualCase.containsKey(key): ' + manualCase.containsKey(key));
            system.debug('%%%% manualCase.get(key): ' + manualCase.get(key));
            if (manualCase.containsKey(key)) {
                CaseActivityInit__c matchedRecord = manualCase.get(key);
                populateFieldsIfEmpty(c, matchedRecord);
            }
            c.StartDate__c = System.TODAY();
            String recordTypeId = Schema.SObjectType.Case.getRecordTypeInfosByName().get('AttivitaContatto').getRecordTypeId();
            c.RecordTypeId = (c.RecordTypeId == null) ? recordTypeId : c.RecordTypeId;
        }
    }

    private static void processExternalCases(List<Case> newListCase, Map<Integer, CaseActivityInit__c> externalCase) {
        for (Case c : newListCase) {
            if (externalCase.containsKey(Integer.valueOf(c.LeoActivityCode__c))) {
                CaseActivityInit__c matchedRecord = externalCase.get(Integer.valueOf(c.LeoActivityCode__c));
                populateFieldsIfEmpty(c, matchedRecord);
            }
            c.StartDate__c = (c.StartDate__c == null) ? System.today() : c.StartDate__c;
            String recordTypeId = Schema.SObjectType.Case.getRecordTypeInfosByName().get('AttivitaContatto').getRecordTypeId();
            c.RecordTypeId = (c.RecordTypeId == null) ? recordTypeId : c.RecordTypeId;
        }
    }

    public static void populateFieldsIfEmpty(Case c, CaseActivityInit__c matchedRecord) {
        Map<String, String> fieldMapping = getFieldMapping();
        List<String> excludedFields = getExcludedFields();
        Map<String, SObjectField> caseFields = Case.SObjectType.getDescribe().fields.getMap();
        System.debug('%%%% Field: ' + CaseActivityInit__c.SObjectType.getDescribe().fields.getMap().values());
        for (SObjectField field : CaseActivityInit__c.SObjectType.getDescribe().fields.getMap().values()) {
            system.debug('%%%% Field: ' + field.getDescribe().getName());
            String fieldName = field.getDescribe().getName();
            String caseFieldName = fieldMapping.containsKey(fieldName) ? fieldMapping.get(fieldName) : fieldName;

            if (!caseFields.containsKey(caseFieldName) || excludedFields.contains(fieldName)) {
                continue;
            }

            if (handleSpecialFields(c, matchedRecord, fieldName, caseFieldName)) {
                continue;
            }

            Object fieldValue = c.get(caseFieldName);
            if ((fieldValue == null) || (fieldValue instanceof Boolean && !(Boolean) fieldValue)) {
                c.put(caseFieldName, matchedRecord.get(fieldName));
            }
        }
    }

    public static Boolean handleSpecialFields(Case c, CaseActivityInit__c matchedRecord, String fieldName, String caseFieldName) {
        if (fieldName == 'ClosedDate__c' || fieldName == 'DueDateDays__c') {
            if (c.get(caseFieldName) == null || c.get(caseFieldName) == 'DefaultValue') {
                 DateTime now = DateTime.now();
                 if (fieldName == 'ClosedDate__c') {
                    Decimal daysToAdd = (Decimal) matchedRecord.get('ClosedDate__c');
                    c.put('ClosedDate__c', now.addDays(daysToAdd.intValue()).date());
                 } else if (fieldName == 'DueDateDays__c') {
                    Decimal daysToAdd = (Decimal) matchedRecord.get('DueDateDays__c');
                    c.put('DueDate__c', now.addDays(daysToAdd.intValue()).date());
                 }
            }
            return true;
        }

        if (fieldName == 'IsCallBack__c' || fieldName == 'IsReservedArea__c') {
            Boolean isCallBack = (Boolean) matchedRecord.get('IsCallBack__c');
            Boolean isReservedArea = (Boolean) matchedRecord.get('IsReservedArea__c');
            if (Boolean.valueOf(isCallBack) || Boolean.valueOf(isReservedArea)) {
                c.put('Requestfromclient__c', true);
            }
            return true;
        }

        return false;
    }

    private static Map<String, String> getFieldMapping() {
        return new Map<String, String>{
            'PossibleAssignemnt__c' => 'TECH_PossibleAssignemnt__c',
            'RequiredPolicy__c' => 'TECH_RequiredPolicy__c',
            'RequiredIncident__c' => 'TECH_RequiredIncident__c',
            'ShowCloseManual__c' => 'TECH_ShowCloseManual__c',
            'ShowOutcome__c' => 'TECH_ShowOutcome__c',
            'IsPlannable__c' => 'TECH_IsPlannable__c',
            'LeoHeat__c' => 'TECH_LeoHeat__c',
            'LeoPriority__c' => 'TECH_LeoPriority__c',
            'NeedsCloseCallout__c' => 'TECH_NeedsCloseCallout__c',
            'IsCallBack__c'=> 'Requestfromclient__c',
            'IsReservedArea__c'=> 'Requestfromclient__c',
            'AssignmentRules__c' => 'TECH_AssignmentRules__c',
            'DueDateDays__c' => 'DueDate__c',
            'GetInTouch__c' => 'TECH_getInTouch__c'
        };
    }

    private static List<String> getExcludedFields() {
        return new List<String>{
            'CreatedDate', 'CreatedById', 'LastModifiedDate', 'LastModifiedById',
            'SystemModstamp', 'LastActivityDate', 'OwnerId', 'IsDeleted', 'Name'
        };
    }

    public static void setCasesPriority(List<Case> newListCase) {
        Map<String, Decimal> weightMap = loadWeightMap();
        Map<String, Decimal> activityScoreMap = new Map<String, Decimal>();
        Map<String, Decimal> heatScoreMap = new Map<String, Decimal>();

        loadScoreMaps(activityScoreMap, heatScoreMap);

        for (Case c : newListCase) {
            Decimal priority = calculatePriority(c, weightMap, activityScoreMap, heatScoreMap);
            assignPriority(c, priority);
        }
    }

    private static Map<String, Decimal> loadWeightMap() {
        Map<String, Decimal> weightMap = new Map<String, Decimal>();
        for (CaseActivityInit__c weight : [SELECT WeightAttribute__c, WeightValue__c FROM CaseActivityInit__c WHERE Type__c = 'Weight']) {
            weightMap.put(weight.WeightAttribute__c, weight.WeightValue__c);
        }
        return weightMap;
    }

    private static void loadScoreMaps(Map<String, Decimal> activityScoreMap, Map<String, Decimal> heatScoreMap) {
        for (CaseActivityInit__c score : [SELECT Area__c, Activity__c, LeoHeat__c, LeoPriority__c, WeightValue__c FROM CaseActivityInit__c WHERE Type__c = 'Score']) {
            if (!String.isEmpty(score.Area__c) && !String.isEmpty(score.Activity__c)) {
                activityScoreMap.put(score.Area__c + '-' + score.Activity__c, score.WeightValue__c);
            } else {
                heatScoreMap.put(score.LeoHeat__c + '-' + score.LeoPriority__c, score.WeightValue__c);
            }
        }
    }

    private static Decimal calculatePriority(Case c, Map<String, Decimal> weightMap, Map<String, Decimal> activityScoreMap, Map<String, Decimal> heatScoreMap) {
        Decimal weightActivity = weightMap.get('ATTIVITA') != null ? weightMap.get('ATTIVITA') : 0;
        Decimal weightHeat = weightMap.get('CALORE-FLAG') != null ? weightMap.get('CALORE-FLAG') : 0;
        Decimal weightDueDate = weightMap.get('SCADENZA') != null ? weightMap.get('SCADENZA') : 0;

        Decimal scoreActivity = activityScoreMap.get(c.Area__c + '-' + c.Activity__c) != null ? activityScoreMap.get(c.Area__c + '-' + c.Activity__c) : 0;

        Decimal scoreHeat = heatScoreMap.get(c.TECH_LeoHeat__c + '-' + c.TECH_LeoPriority__c) != null ? heatScoreMap.get(c.TECH_LeoHeat__c + '-' + c.TECH_LeoPriority__c) : 0;

        Decimal scoreDueDate = calculateDueDateScore(c.dueDate__c);

        return (weightActivity * scoreActivity) + (weightHeat * scoreHeat) + (weightDueDate * scoreDueDate);
    }

    private static Decimal calculateDueDateScore(Date dueDate) {
        Integer daysFromToday = Date.today().daysBetween(dueDate);
        if (daysFromToday <= 3) {
            return 1;
        } else if (daysFromToday <= 7) {
            return 0.66;
        } else if (daysFromToday <= 14) {
            return 0.33;
        } else {
            return 0;
        }
    }

    private static void assignPriority(Case c, Decimal priorityValue) {
        if (priorityValue >= 0.7 && c.Priority != 'High') {
            c.Priority = 'High';
        } else if (priorityValue >= 0.4 && c.Priority != 'Medium') {
            c.Priority = 'Medium';
        } else if (priorityValue < 0.4 && c.Priority != 'Low') {
            c.Priority = 'Low';
        }
    }

    public static void populateAgencyOnAutomaticCase(List<Case> newListCase) {
        Set<String> externalIds = extractExternalIds(newListCase);
        Set<String> cfPivaValues = extractcfPivaValues(newListCase);
        
        Map<String, Id> accountMap = fetchAccounts(externalIds, cfPivaValues);

        assignAgencyToCases(newListCase, accountMap);
    }
    
    private static Set<String> extractExternalIds(List<Case> cases) {
        Set<String> externalIds = new Set<String>();
        for (Case c : cases) {
            if (c.DrAgenziaFiglia__c != null) {
                externalIds.add('AGE_' + c.DrAgenziaFiglia__c);
            }
        }
        return externalIds;
    }

    private static Set<String> extractcfPivaValues(List<Case> cases) {
        Set<String> cfPivaValues = new Set<String>();
        for (Case c : cases) {
            if (c.DrCfPivaCliente__c != null) {
                cfPivaValues.add( c.DrCfPivaCliente__c);
            }
        }
        return cfPivaValues;
    }
    
    private static Map<String, Id> fetchAccounts(Set<String> externalIds, Set<String> cfPivaValues) {
        Map<String, Id> externalIdToAccountId = new Map<String, Id>();
        externalIds.addAll(cfPivaValues);
        if (!Schema.sObjectType.Account.isAccessible()) {
            return externalIdToAccountId;
        }

        List<Account> accounts = [SELECT Id, ExternalId__c, RecordType.DeveloperName FROM Account WHERE ExternalId__c IN :externalIds];
        for (Account acc : accounts) {
            if (acc.RecordType.DeveloperName == 'PersonAccount') {
                externalIdToAccountId.put(acc.ExternalId__c, acc.Id);
            } else if (acc.RecordType.DeveloperName == 'IndustriesBusiness') {
                externalIdToAccountId.put(acc.ExternalId__c, acc.Id);
            } else if (acc.RecordType.DeveloperName == 'Agency') {
                externalIdToAccountId.put(acc.ExternalId__c, acc.Id);
            }
        }
        return externalIdToAccountId;
    }
    
    private static void assignAgencyToCases(List<Case> cases, Map<String, Id> accountMap) {
        for (Case c : cases) {
            // Assegna Agenzia
            if (c.DrAgenziaFiglia__c != null) {
                String fullExternalId = 'AGE_' + c.DrAgenziaFiglia__c;
                if (accountMap.containsKey(fullExternalId)) {
                    c.Agency__c = accountMap.get(fullExternalId);
                }
            }
            // Assegna Account
            if(c.DrCfPivaCliente__c != null){
                if(accountMap.containsKey(c.DrCfPivaCliente__c)){
                    c.AccountId = accountMap.get(c.DrCfPivaCliente__c);
                }
            }
        }
    }

    public static void updateStatusToClosed(List<Case> cases){
        for(Case c : cases){
            if(c.ClosedDate__c == System.today()) {
                c.Status = 'Closed';
            }
        }
    }

    public static void updateStatusToNew(List<Case> cases){
        for(Case c : cases){
            if(c.dueDate__c >= System.today() && c.Status == 'Expired'){
                c.Status = 'New';
            }
        }
    }

    public static void updateStatusToExpiredDueDate(List<Case> cases){
        for(Case c : cases){
            if(c.DueDate__c != null && c.DueDate__c <= System.today().addDays(-1)) {
                c.Status = 'Expired';
            }
        }
    }

    public static void assignDefaultOwner(List<Case> cases) {
        if (cases == null || cases.isEmpty()) {
            return;
        }
        if (Schema.sObjectType.User.isAccessible()) {
            User u = [SELECT Id, Name FROM User WHERE (Name = :System.Label.OwnerId OR Name = :System.Label.OwnerId2) LIMIT 1];
            for (Case obj : cases) {
                obj.OwnerId = u.Id;
            }
        }
    }

    /*public static void checkLookupAccountField(List<Case> cases){
        List<Case> casesToDelete = new List<Case>();
        for (Case c : cases) {
            if (c.AccountId == null) {
                casesToDelete.add(c);
            }
        }
        if (!casesToDelete.isEmpty()) {
            delete casesToDelete;
        }
    }

    public static List<Case> filterCodeActivity(List<Case> cases){
        Set<String> codeActivitySet = new Set<String>();
        for (Case c : cases) {
            if (c.LeoActivityCode__c != null ) {
                codeActivitySet.add(c.LeoActivityCode__c);
            }
        }
    }*/
}