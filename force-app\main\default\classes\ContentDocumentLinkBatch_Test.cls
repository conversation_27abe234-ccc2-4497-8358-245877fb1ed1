@isTest
private class ContentDocumentLinkBatch_Test {

    @TestSetup
    static void makeData(){


        // Create RecordTypes for Agency and Society
        Id agencyRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId();
        Id societyRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId();

        Id accountSocietyRecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AccountSociety').getRecordTypeId();
        Id accountAgencyRecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AccountAgency').getRecordTypeId();

        // Creazione Account
        List<Account> accListToInsert = new List<Account>();
        Account accountCliente1 = new Account(
            FirstName = 'Cliente', LastName = '1',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('PersonAccount').getRecordTypeId(), 
            ExternalId__c = '****************'
        );
        accListToInsert.add(accountCliente1);

        Account accountSociety1 = new Account(
            Name = 'Scoietà 1', RecordTypeId = societyRecordTypeId,  
            ExternalId__c = 'SOC_1', Identifier__c = '1'
        );
        accListToInsert.add(accountSociety1);

        Account accountAgenzia1 = new Account(
            Name = 'Agenzia 1', RecordTypeId = agencyRecordTypeId,  
            ExternalId__c = 'AGE_1'
        );
        accListToInsert.add(accountAgenzia1);
        insert accListToInsert;

        // Creazione ReciprocalRole
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(Name = 'Cliente', FinServ__InverseRole__c = 'Agenzia');
        insert role;
       
        FinServ__ReciprocalRole__c roleC = new FinServ__ReciprocalRole__c(Name = 'Cliente', FinServ__InverseRole__c = 'Compagnia');
        insert roleC;

        // Creazione degli AccountAccountRel
        FinServ__AccountAccountRelation__c accAccRelClienteSociety1 = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = accountCliente1.Id,
            FinServ__RelatedAccount__c = accountSociety1.Id,
            FinServ__Role__c = role.Id,
            RecordTypeId = accountSocietyRecordTypeId
        );
        insert accAccRelClienteSociety1;

        // Creazione degli AccountAccountRel
        FinServ__AccountAccountRelation__c accAccRelClienteAgency1 = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = accountCliente1.Id,
            FinServ__RelatedAccount__c = accountAgenzia1.Id,
            FinServ__Role__c = role.Id,
            RecordTypeId = accountAgencyRecordTypeId
        );
        insert accAccRelClienteAgency1;
        
        //Creazione  AccountDetails
        List<AccountDetails__c> accDetListToInsert = new List<AccountDetails__c>();
        AccountDetails__c accDetails1 = new AccountDetails__c(
        	Relation__c = accAccRelClienteSociety1.Id, SourceSystemIdentifier__c = '000001', 
            FeaMail__c = '<EMAIL>', FeaMobile__c = '+39 **********',
            Country__c = 'ITA', FiscalCode__c = '****************', Street__c = 'Via del Campo',
            PostalCode__c = '20900', City__c = 'Firenze', State__c = 'TO', Mobile__c = '+39 *********',
            Email__c = '<EMAIL>', ExternalId__c = 'ANAG2_****************_SOC_1'
        );
        accDetListToInsert.add(accDetails1);
        insert accDetListToInsert;

        List<AccountAgencyDetails__c> accAgenDetListToInsert = new List<AccountAgencyDetails__c>();
        AccountAgencyDetails__c agencyDetails1 = new AccountAgencyDetails__c( 
            Relation__c = accAccRelClienteAgency1.Id, SubAgencyCode__c = 'SUB1',
            ExternalId__c = 'ANAG2_****************_SOC_1_AGE_1'
        );
        accAgenDetListToInsert.add(agencyDetails1);
        insert accAgenDetListToInsert;

        // Crea la ContentVersion
        ContentVersion contentVersion = new ContentVersion();
        contentVersion.Title = 'Test Document';
        contentVersion.PathOnClient = 'test_document.pdf';
        contentVersion.VersionData = EncodingUtil.base64Decode('Test file');
        contentVersion.ExternalId__c = '****************_09489_98874_SOC_1';
        insert contentVersion;
    }

    @isTest
    static void myUnitTest() {
        ContentDocument doc = [SELECT Id FROM ContentDocument LIMIT 1];
        
        Test.startTest();
        Database.executeBatch(new ContentDocumentLinkBatch(new Set<Id>{doc.Id}), 200);
        Test.stopTest();
    }
}