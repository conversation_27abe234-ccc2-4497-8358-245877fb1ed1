/*********************************************************************************
* <AUTHOR>
* @description    Agency Engine class for the apex defined object definitions
* @date           2024-03-05
* @group          Agency Engine
**********************************************************************************/

public with sharing class EngineWrapper_V2 {
    private static Map<String, String> AREAS_OF_NEED_TO_MACRO_AREAS = new Map<String, String> {
        'Cane e Gatto' => 'Property', 
        'Casa' => 'Property', 
        'Famiglia' => 'Property',
        'Infortuni' => 'Welfare',
        'Mobilità' => 'Motor',
        'Salute' => 'Welfare',
        'Veicoli' => 'Motor',
        'Viaggio' => 'Property',
        'Previdenza integrativa' => 'Welfare'
    };

    // Recuperiamo la INFO, per capire 
    //se gli ordinamenti da KPI sono da escludere al momento
    public static isKPISet__mdt isKPISetMetadata = [
            SELECT isKPISetActive__c 
            FROM isKPISet__mdt 
            LIMIT 1
    ];
       

    // Metodo pubblico per accedere al valore di isKPISetActive
    public static  Boolean getIsKPISetActive() {
        if(isKPISetMetadata == null) {
            return false; // Default value if metadata is not found
        }
        Boolean isKPISetActive = isKPISetMetadata.isKPISetActive__c;
        return isKPISetActive;
    }

    public static List<String> getMacroAreas(String areasofNeed) {
        if(String.isEmpty(areasofNeed)) {
            return new List<String>();
        }

        List<String> areasofNeedList = areasofNeed.split(';');
        Set<String> macroAreas = new Set<String>();
        for(String area : areasofNeedList) {
            if(AREAS_OF_NEED_TO_MACRO_AREAS.containsKey(area)) {
                macroAreas.add(AREAS_OF_NEED_TO_MACRO_AREAS.get(area));
            }
        }
        return new List<String>(macroAreas);
    }

    

    public class EngineWrapperInput {
        @InvocableVariable
        public String pIva;
        @InvocableVariable
        public String fiscalCode;
        @InvocableVariable
        public String source;
        @InvocableVariable
        public List<String> agenciestoExcludeIds;
        @InvocableVariable
        public Double latitude;
        @InvocableVariable
        public Double longitude;
        @InvocableVariable
        public String zipCode;
        @InvocableVariable
        public String street;
        @InvocableVariable
        public String streetNumber;
        @InvocableVariable
        public String birthDayte;
        public List<String> macroAreas;
        @InvocableVariable
        public String areasofNeed;
        @InvocableVariable
        public String company;
        @InvocableVariable
        public Boolean useDefaultAgency;

        public EngineWrapperInput(
            String source,
            List<String> agenciestoExcludeIds, 
            Double latitude,
            Double longitude,
            List<String> macroAreas,
            String areasofNeed,
            String birthDayte,
            String pIva,
            String fiscalCode, 
            String zipCode,
            String street,
            String streetNumber,
            Boolean useDefaultAgency,
            String company
        ) {
            this.source = source;
            this.company = company;
            this.agenciestoExcludeIds = agenciestoExcludeIds;
            this.latitude = latitude;
            this.longitude = longitude;
            this.macroAreas = macroAreas;
            this.areasofNeed = areasofNeed;
            this.birthDayte = birthDayte;
            this.pIva = pIva;
            this.fiscalCode = fiscalCode;
            this.zipCode = zipCode;
            this.street = street;
            this.streetNumber = streetNumber;
            this.useDefaultAgency = useDefaultAgency;            
        }

        public EngineWrapperInput() {
            // Default constructor with no specific initialization
        }
    }

    public class EngineWrapperInvocableOutput {
        @InvocableVariable
        public Boolean isSuccess;
        @InvocableVariable
        public String errorMessage;
        @InvocableVariable
        public List<ServiceTerritory> selectedAgencies;
        @InvocableVariable
        public ServiceTerritory selectedSalespoint;
        @InvocableVariable
        public Account selectedAgency;

        public EngineWrapperInvocableOutput(Boolean isSuccess, String errorMessage) {
            this.isSuccess = isSuccess;
            this.errorMessage = errorMessage;
        }

        public EngineWrapperInvocableOutput(Boolean isSuccess, String errorMessage, List<ServiceTerritory> selectedAgencies, ServiceTerritory selectedSalespoint, Account selectedAgency) {
            this.isSuccess = isSuccess;
            this.errorMessage = errorMessage;
            this.selectedAgencies = selectedAgencies;
            this.selectedSalespoint = selectedSalespoint;
            this.selectedAgency = selectedAgency;
        }
    }

    public class EngineWrapperOutput {
        //public EngineWrapperInput engineWrapperInput;
        public String subjectType;
        public EngineSettings engineSettings;
        public Boolean isSuccess;
        public EngineError engineError;
        public List<EngineSalesPoint> matchingSalespoints;
        //public EngineSalesPoint selectedSalespoint;
       // public EngineAgency selectedAgency;
        public List<EngineExclusionSalesPoint> excludedSalespoints;


        public EngineWrapperOutput(
            // EngineWrapperInput engineWrapperInput, 
            String subjectType,
            Boolean isSuccess, 
            EngineError engineError, 
            EngineSettings engineSettings
            
        ) {
            // this.engineWrapperInput = engineWrapperInput;
            this.isSuccess = isSuccess;
            this.engineError = engineError;
            this.subjectType = subjectType;
        }

        public EngineWrapperOutput(
            //EngineWrapperInput engineWrapperInput, 
            Boolean isSuccess, 
            String subjectType,
            List<EngineSalesPoint> matchingSalespoints, 
            // EngineSalesPoint selectedSalespoint,
            // EngineAgency selectedAgency,
            List<EngineExclusionSalesPoint> excludedSalespoints, 
            EngineSettings engineSettings
        ) {
            // this.engineWrapperInput = engineWrapperInput;
            this.subjectType = subjectType;
            this.isSuccess = isSuccess;
            this.subjectType = subjectType;
            this.matchingSalespoints = matchingSalespoints;
            // this.selectedSalespoint = selectedSalespoint;
            // this.selectedAgency = selectedAgency;
            this.excludedSalespoints = excludedSalespoints;
        }
    }

    public class EngineSettings {
        public Boolean discretionality;
        public Double extensionRangeKM;
        public String defaultAgency;
        public String defaultAssignment;
        public Integer maxReassignments;
        public Double centerRangeKM;
        public Double contactRate;

        public EngineSettings(
            Boolean discretionality,
            Double extensionRangeKM,
            String defaultAgency,
            String defaultAssignment,
            Integer maxReassignments,
            Double centerRangeKM,
            Double contactRate
        ) {
            this.discretionality = discretionality;
            this.extensionRangeKM = extensionRangeKM;
            this.defaultAgency = defaultAgency;
            this.defaultAssignment = defaultAssignment;
            this.maxReassignments = maxReassignments;
            this.centerRangeKM = centerRangeKM;
            this.contactRate = contactRate;
        }
    }

    public class EngineError {
        public String errorKeyword;
        public String errorDescription;

        public EngineError(String errorKeyword) {
            this.errorKeyword = errorKeyword;
        }
    }

    public class EngineExclusionSalesPoint {
        public EngineSalesPoint excludedSalesPoint;
        public String exclusionReason;

        public EngineExclusionSalesPoint(EngineSalesPoint excludedSalesPoint, String exclusionReason) {
            this.excludedSalesPoint = excludedSalesPoint;
            this.exclusionReason = exclusionReason;
        }
    }

    public class KPISet {
        public String name; // Name
        public Double contactRate; // ContactRate__c
        public Double contactableClientsNumber; // NumberContactableClients__c
        public Double portfolioClientsNumber; // NumberClientsPortfolio__c
        public Double contactActivitiesProcessingAssigned; // ContactActivitiesProcessingAssigned__c
        public Double benchmarkContactActivitiesAssigned; // BenchmarkContactActivitiesAssigned__c
        public Double weightContactActivitiesAssigned; // WeightContactActivitiesAssigned__c
        public Double scoreProcessingAssignedActivities; // ScoreProcessingAssignedActivities__c
        public Double conversionAssignedContactActivities; // ConversionAssignedContactActivities__c
        public Double benchmarkConversionAssignedActivities; // BenchmarkConversionAssignedActivities__c
        public Double scoreConversionAssignedActivities; // ScoreConversionAssignedActivities__c
        public Double weightConversionAssignedActivities; // WeightConversionAssignedActivities__c
        public Double averageLeadProcessingTime; // AverageLeadProcessingTime__c
        public Double benchmarkAverageLeadProcessingTime; // BenchmarkAverageLeadProcessingTime__c
        public Double scoreAverageLeadProcessingTime; // ScoreAverageLeadProcessingTime__c
        public Double weightAverageLeadProcessingTime; // WeightAverageLeadProcessingTime__c
        public Double digitalPenetration; // DigitalPenetration__c
        public Double benchmarkDigitalPenetration; // BenchmarkDigitalPenetration__c
        public Double scoreDigitalPenetration; // ScoreDigitalPenetration__c
        public Double weightDigitalPenetration; // WeightDigitalPenetration__c
        public Double privateAreaRegistration; // PrivateAreaRegistration__c
        public Double benchmarkPrivateAreaRegistration; // BenchmarkPrivateAreaRegistration__c
        public Double scorePrivateAreaRegistration; // ScorePrivateAreaRegistration__c
        public Double weightPrivateAreaRegistration; // WeightPrivateAreaRegistration__c
        public Double omnichannelQuotes; // OmnichannelQuotes__c
        public Double benchmarkOmnichannelQuotes; // BenchmarkOmnichannelQuotes__c
        public Double scoreOmnichannelQuotes; // ScoreOmnichannelQuotes__c
        public Double weightOmnichannelQuotes; // WeightOmnichannelQuotes__c

        public KPISet() {
            // Default constructor with no specific initialization
        }
        public KPISet(KPI__c kpi) {
            this.name = kpi.Name;
            this.contactRate = kpi.ContactRate__c;
            this.contactableClientsNumber = kpi.NumberContactableClients__c;
            this.portfolioClientsNumber = kpi.NumberClientsPortfolio__c;
            this.contactActivitiesProcessingAssigned = kpi.ContactActivitiesProcessingAssigned__c;
            this.benchmarkContactActivitiesAssigned = kpi.BenchmarkContactActivitiesAssigned__c;
            this.weightContactActivitiesAssigned = kpi.WeightContactActivitiesAssigned__c;
            this.scoreProcessingAssignedActivities = kpi.ScoreProcessingAssignedActivities__c;
            this.conversionAssignedContactActivities = kpi.ConversionAssignedContactActivities__c;
            this.benchmarkConversionAssignedActivities = kpi.BenchmarkConversionAssignedActivities__c;
            this.scoreConversionAssignedActivities = kpi.ScoreConversionAssignedActivities__c;
            this.weightConversionAssignedActivities = kpi.WeightConversionAssignedActivities__c;
            this.averageLeadProcessingTime = kpi.AverageLeadProcessingTime__c;
            this.benchmarkAverageLeadProcessingTime = kpi.BenchmarkAverageLeadProcessingTime__c;
            this.scoreAverageLeadProcessingTime = kpi.ScoreAverageLeadProcessingTime__c;
            this.weightAverageLeadProcessingTime = kpi.WeightAverageLeadProcessingTime__c;
            this.digitalPenetration = kpi.DigitalPenetration__c;
            this.benchmarkDigitalPenetration = kpi.BenchmarkDigitalPenetration__c;
            this.scoreDigitalPenetration = kpi.ScoreDigitalPenetration__c;
            this.weightDigitalPenetration = kpi.WeightDigitalPenetration__c;
            this.privateAreaRegistration = kpi.PrivateAreaRegistration__c;
            this.benchmarkPrivateAreaRegistration = kpi.BenchmarkPrivateAreaRegistration__c;
            this.scorePrivateAreaRegistration = kpi.ScorePrivateAreaRegistration__c;
            this.weightPrivateAreaRegistration = kpi.WeightPrivateAreaRegistration__c;
            this.omnichannelQuotes = kpi.OmnichannelQuotes__c;
            this.benchmarkOmnichannelQuotes = kpi.BenchmarkOmnichannelQuotes__c;
            this.scoreOmnichannelQuotes = kpi.ScoreOmnichannelQuotes__c;
            this.weightOmnichannelQuotes = kpi.WeightOmnichannelQuotes__c;
        }
    }

    public class EngineAgency implements Comparable {
        public Id sfAccountId; // Id
        public String agencyCode; // ExternalId__c
        public String name; // Name
        public String billingAddress; // BillingAddressFormula__c
        // public Double score; 
        public Double distance;
        // Transient Boolean discretionality;
        public String agencyNumber; // AccountNumber
        public String type; // Type
        public String subtype; // SubType__c
        // public String district; // District__c
        // public String vatCode; // VatCode__c
        // public Double capActivity; // CAPActivity__c
        // public Double extraCap; // ExtraCAP__c
        // public Double agencyLoad; // AgencyLoad__c
        // public Boolean discretionalityMotor; // DiscretionalityMotor__c
        // public Boolean discretionalityWelfare; // DiscretionalityWelfare__c
        // public Boolean discretionalityProperty; // DiscretionalityProperty__c
        // public Boolean discretionalityEnterprise; // DiscretionalityEnterprise__c
        // public Boolean blacklist; // Blacklist__c
        public Boolean omnichannelAgreement; // OmnichannelAgreement__c
        // public Boolean omnichannelResponsible; // OmnichannelResponsible__c
        // public Boolean isLocatorEnabled; // IsLocatorEnabled__c
        // public String size; // Size__c
        // public String cluster; // Cluster__c
        // public KPISet kpiSet;
        // Transient Boolean isSortingDiscretionalityOn = false;
        // Transient Boolean isAgency;
        
        // Imposta dinamicamente il valore di isKPISetActive
        Transient Boolean isKPISetActive = EngineWrapper_V2.isKPISetMetadata.isKPISetActive__c;


        

        public EngineAgency(
            Account agency,
            // Boolean discretionality,
            // KPISet kpiSet,
            // Boolean isSortingDiscretionalityOn,
            Boolean isAgency
        ) {
            this.sfAccountId = agency?.Id;
            this.agencyCode = isAgency ? agency?.ExternalId__c : agency?.Agency__r?.ExternalId__c;
            this.name = agency?.Name;
            this.billingAddress = isAgency ? agency?.BillingAddressFormula__c : agency?.Agency__r?.BillingAddressFormula__c;
            // this.discretionality = discretionality;
            this.agencyNumber = isAgency ? agency?.AccountNumber : agency?.Agency__r?.AccountNumber;
            this.type = isAgency ? agency?.Type : agency?.Agency__r?.Type;
            this.subtype = agency?.SubType__c;
            // this.district = isAgency ? agency?.District__c : agency?.Agency__r?.District__c;
            // this.vatCode = isAgency ? agency?.VatCode__c : agency?.Agency__r?.VatCode__c;
            // this.capActivity = isAgency ? agency?.CAPActivity__c : agency?.Agency__r?.CAPActivity__c;
            // this.extraCap = isAgency ? agency?.ExtraCAP__c : agency?.Agency__r?.ExtraCAP__c;
            // this.agencyLoad = isAgency ? agency?.AgencyLoad__c : agency?.Agency__r?.AgencyLoad__c;
            // this.discretionalityMotor = isAgency ? agency?.DiscretionalityMotor__c : agency?.Agency__r?.DiscretionalityMotor__c;
            // this.discretionalityWelfare = isAgency ? agency?.DiscretionalityWelfare__c : agency?.Agency__r?.DiscretionalityWelfare__c;
            // this.discretionalityProperty = isAgency ? agency?.DiscretionalityProperty__c : agency?.Agency__r?.DiscretionalityProperty__c;
            // this.discretionalityEnterprise = isAgency ? agency?.DiscretionalityEnterprise__c : agency?.Agency__r?.DiscretionalityEnterprise__c;
            // this.blacklist = isAgency ? agency?.Blacklist__c : agency?.Agency__r?.Blacklist__c;
            this.omnichannelAgreement = isAgency ? agency?.OmnichannelAgreement__c : agency?.Agency__r?.OmnichannelAgreement__c;
            // this.omnichannelResponsible = isAgency ? agency?.OmnichannelResponsible__c : agency?.Agency__r?.OmnichannelResponsible__c;
            // this.isLocatorEnabled = agency?.IsLocatorEnabled__c;
            // this.size = isAgency ? agency?.Size__c : agency?.Agency__r?.Size__c;
            // this.cluster = isAgency ? agency?.Cluster__c : agency?.Agency__r?.Cluster__c;
            // this.kpiSet = kpiSet;
            // this.isSortingDiscretionalityOn = isSortingDiscretionalityOn;
        }

        /********************************************************************************************************************
        * <AUTHOR>
        * @date         2024-03-05
        * @description  The method overides the standard sorting used by apex
        * @param        compareTo (Object): the object to compare against the object the sorting was called on
        * @return       Integer: returns -1 if the object the sorting was called on is ranked lower than the compareTo object
        *               1 if it is ranked higher than the compareTo object or 0 if they are ranked evenly
        ********************************************************************************************************************/

        //THIS COMPARE WILL NOT USED!!!!
        public Integer compareTo(Object compareTo) {
            EngineAgency compareToAgency = (EngineAgency) compareTo;

            // Sorting for discretionality if it's active in the lookup table            
            // if(isSortingDiscretionalityOn && (discretionality && !compareToAgency.discretionality)) {
            //     return -1;
            // }
            // else if(isSortingDiscretionalityOn && (!discretionality && compareToAgency.discretionality)) {
            //     return 1;
            // }

           

            System.debug('EngineAgency.compareTo: siamo arrivati all\'ordinamento per sola DISTANZA in quanto è settato a FALSE l\'ordinamento per KPI ->isKPISetActive = ' + isKPISetActive);
                       
            return 0;
        }
    }

    public class EngineSalesPoint implements Comparable {
        
        EngineAgency relatedEngineAgency; // Agency__r
        public Id salespoint; // Id
        public String salesPointCode; // ExternalId__c
        public String name; // Name
        public String billingAddress; // BillingAddressFormula__c
        // public Double score; 
        public Double distance;
        public String type; // Tipo__c
        @TestVisible
        // Transient Boolean discretionality;
        public String agency; // Agency__c
        //public String cluster; // Cluster__c
        // public KPISet kpiSet;
        // Transient Boolean isSortingDiscretionalityOn = false;
        // Transient Boolean isAgency;         
        // Imposta dinamicamente il valore di isKPISetActive
        Transient Boolean isKPISetActive = EngineWrapper_V2.isKPISetMetadata.isKPISetActive__c;

        public EngineSalesPoint(
            ServiceTerritory salespoint,
            //Double score, 
            Double distance,
            // Boolean discretionality,
            // KPISet kpiSet,
            // Boolean isSortingDiscretionalityOn,
            // Boolean isAgency,
            EngineAgency agency
        ) {
            this.salespoint = salespoint.Id;
            this.salesPointCode = salespoint.ExternalId__c != null ? salespoint.ExternalId__c : '';
            this.name = salespoint.Name;
            this.billingAddress = salespoint.Address__c;
           // this.score = score; 
            this.distance = distance;
            // this.discretionality = discretionality;
            this.type = salespoint.Tipo__c != null ? salespoint.Tipo__c : '';
            this.agency = salespoint.Agency__c != null ? salespoint.Agency__c : '';
            //this.cluster = isAgency ? agency?.Cluster__c : agency?.Agency__r?.Cluster__c;
            // this.kpiSet = kpiSet;
            // this.isSortingDiscretionalityOn = isSortingDiscretionalityOn;
            // this.isAgency = isAgency;
            this.relatedEngineAgency = agency;
        }

        //At the moment this method order only by distance, but it could be extended in the future to include other parameters
        //the order by KPI is disabled because it is not used in the current implementation of the engine
        public Integer compareTo(Object compareTo) {
            EngineSalesPoint compareToSalesPoint = (EngineSalesPoint) compareTo;
        
            // Initialize the result to 0 (equal by default)
            Integer result = 0;
        
            // // 1. Sorting for discretionality if it's active in the lookup table
            // if (isSortingDiscretionalityOn) {
            //     if (discretionality && !compareToSalesPoint.discretionality) {
            //         result = -1;
            //     } else if (!discretionality && compareToSalesPoint.discretionality) {
            //         result = 1;
            //     }
            // }
        
            // If result is still 0, move to the next criterion
            if (result == 0 && isKPISetActive) {
                // 2. Sorting for score
                //comment at the moment because it is not used in the current implementation of the engine
                // if (score != null && compareToSalesPoint?.score != null) {
                //     if (score > compareToSalesPoint.score) {
                //         result = -1;
                //     } else if (score < compareToSalesPoint.score) {
                //         result = 1;
                //     }
                // }
            }
        
            // If result is still 0, move to the next criterion
            if (result == 0) {
                // 3. Sorting for distance
                if (distance != null && compareToSalesPoint?.distance != null) {
                    if (distance < compareToSalesPoint.distance) {
                        result = -1;
                    } else if (distance > compareToSalesPoint.distance) {
                        result = 1;
                    }
                }
            }
        
            // Return the final result
            return result;
        }
     
        // public Integer compareTo(Object compareTo) {
        //     EngineAgency compareToAgency = (EngineAgency) compareTo;

           

        //     // Sorting for discretionality if it's active in the lookup table            
        //     if(isSortingDiscretionalityOn && (discretionality && !compareToAgency.discretionality)) {
        //         return -1;
        //     }
        //     else if(isSortingDiscretionalityOn && (!discretionality && compareToAgency.discretionality)) {
        //         return 1;
        //     }

        //     // Sorting for score
        //     if(isKPISetActive && (score != null && compareToAgency?.score != null) && (score > compareToAgency.score)) {
        //         return -1;
        //     }
        //     else if(isKPISetActive && (score != null && compareToAgency?.score != null) && (score < compareToAgency.score)) {
        //         return 1;
        //     }
        //     System.debug('EngineAgency.compareTo: siamo arrivati all\'ordinamento per sola DISTANZA in quanto è settato a FALSE l\'ordinamento per KPI ->isKPISetActive = ' + isKPISetActive);
            
        //     // Sorting for distance
        //     if((distance != null && compareToAgency?.distance != null) && (distance < compareToAgency.distance)) {
        //         return -1;
        //     }
        //     else if((distance != null && compareToAgency?.distance != null) && (distance > compareToAgency.distance)) {
        //         return 1;
        //     }
        //     return 0;
        // }
    }
}