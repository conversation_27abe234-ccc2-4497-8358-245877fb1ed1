@isTest
private class AsyncProccessIntSubscribeTest {
    
    @isTest
    static void testExecuteBatch() {
        // Simula un nome di batch da passare al metodo
        List<String> batchNames = new List<String>{'TestBatchName'};

        // Avvia il test
        Test.startTest();
        AsyncProccessIntSubscribe.ExecuteBatch(batchNames);
        Test.stopTest();

        // Non possiamo verificare direttamente EventBus.publish, ma possiamo assicurarci che il metodo non lanci eccezioni
        System.assert(true, 'Metodo ExecuteBatch eseguito senza errori');
    }
}