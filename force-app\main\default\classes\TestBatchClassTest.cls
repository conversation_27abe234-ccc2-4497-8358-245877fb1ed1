@isTest
private class TestBatchClassTest {
    @isTest
    static void testBatchExecution() {
        // Crea un Account di test per assicurarti che la query nel batch restituisca almeno un record
        Account acc = new Account(Name = 'Test Account');
        insert acc;

        // Istanzia la classe batch
        TestBatchClass batch = new TestBatchClass();

        // Esegui il batch con Test.startTest() e Test.stopTest() per simulare l'esecuzione asincrona
        Test.startTest();
        Database.executeBatch(batch, 1); // Dimensione del batch impostata a 1 per semplicità
        Test.stopTest();

        // Verifica che l'account sia stato inserito
        System.assertEquals(1, [SELECT COUNT() FROM Account WHERE Name = 'Test Account']);
    }
}