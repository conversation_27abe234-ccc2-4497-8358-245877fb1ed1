public with sharing class ControllerAgendaTipoDispCanDig {

    @AuraEnabled
    public static Map<String, List<SlotDisponibilitaDTO>> getDisponibilita() {
        Map<String, List<SlotDisponibilitaDTO>> result = new Map<String, List<SlotDisponibilitaDTO>>();
        String userId = UserInfo.getUserId();

        List<ServiceResource> srList = [SELECT Id, RelatedRecordId FROM ServiceResource WHERE RelatedRecordId = :userId LIMIT 1];
        if (srList.isEmpty()) {
            SlotDisponibilitaDTO dto = new SlotDisponibilitaDTO();
            dto.message = 'Nessun Service Resource associato all\'utente corrente';
            result.put('Error', new List<SlotDisponibilitaDTO>{dto});
            return result;
        }
        ServiceResource sr = srList[0];
        

        // Mappa giorno in italiano
        Map<String, String> dayMap = new Map<String, String>{
            'Monday' => 'Lunedì',
            'Tuesday' => 'Martedì',
            'Wednesday' => 'Mercoledì',
            'Thursday' => 'Giovedì',
            'Friday' => 'Venerdì',
            'Saturday' => 'Sabato',
            'Sunday' => 'Domenica'
        };


        // Step 1: recupero i ServiceTerritoryMember dell'agente
        List<ServiceTerritoryMember> territori = [
            SELECT ServiceTerritoryId, ServiceTerritory.Name, OperatingHoursId
            FROM ServiceTerritoryMember
            WHERE ServiceResource.RelatedRecordId = :userId and IsActive__c = true
        ];


        if(territori.isEmpty()) {
            SlotDisponibilitaDTO dto = new SlotDisponibilitaDTO();
            dto.message = 'Nessun territorio attivo associato all\'utente corrente';
            result.put('Error', new List<SlotDisponibilitaDTO>{dto});
            return result;
        }

        Set<Id> operatingHoursIds = new Set<Id>();
        Map<Id, String> territorioIdToNome = new Map<Id, String>();

        for (ServiceTerritoryMember stm : territori) {
            operatingHoursIds.add(stm.OperatingHoursId);
            territorioIdToNome.put(stm.ServiceTerritoryId, stm.ServiceTerritory.Name);
        }


        // Step 2: recupero gli slot orari associati
        List<TimeSlot> slots = [
            SELECT StartTime, EndTime, DayOfWeek, OperatingHoursId
            FROM TimeSlot
            WHERE OperatingHoursId IN :operatingHoursIds
        ];

        // Mappa OperatingHoursId > lista territori
        Map<Id, List<Id>> oreToTerritori = new Map<Id, List<Id>>();
        for (ServiceTerritoryMember stm : territori) {
            if (!oreToTerritori.containsKey(stm.OperatingHoursId)) {
                oreToTerritori.put(stm.OperatingHoursId, new List<Id>());
            }
            oreToTerritori.get(stm.OperatingHoursId).add(stm.ServiceTerritoryId);
        }


        // Step 3: costruzione mappa giorno -> slots
        for (TimeSlot ts : slots) {
            String giorno = dayMap.get(ts.DayOfWeek);
            if (giorno == null) continue;


            List<Id> territoriAssociati = oreToTerritori.get(ts.OperatingHoursId);
            for (Id territorioId : territoriAssociati) {
                SlotDisponibilitaDTO dto = new SlotDisponibilitaDTO();
                dto.puntoVenditaId = territorioId;
                dto.puntoVendita = territorioIdToNome.get(territorioId);
                dto.oraInizio = String.valueOf(ts.StartTime).removeEnd(':00.000Z');
                dto.oraFine = String.valueOf(ts.EndTime).removeEnd(':00.000Z');

                if (!result.containsKey(giorno)) {
                    result.put(giorno, new List<SlotDisponibilitaDTO>());
                }

                result.get(giorno).add(dto);
            }

        }

        System.debug('@MM result: ' + JSON.serialize(result));

        return result;
    }

    @AuraEnabled
    public static List<Map<String, Object>> getPuntiVenditaConOrari() {
        Id userId = UserInfo.getUserId();

        // 1. Recupera tutte le risorse legate all'utente
        List<ServiceResource> risorse = [
            SELECT Id FROM ServiceResource WHERE RelatedRecordId = :userId
        ];
        if (risorse.isEmpty()) return new List<Map<String, Object>>();
        Id serviceResourceId = risorse[0].Id;

        // 2. Recupera i territory assegnati alla risorsa
        List<ServiceTerritoryMember> membri = [
            SELECT ServiceTerritoryId FROM ServiceTerritoryMember
            WHERE ServiceResourceId = :serviceResourceId AND IsActive__c = true
        ];
        if (membri.isEmpty()) return new List<Map<String, Object>>();

        Set<Id> territoryIds = new Set<Id>();
        for (ServiceTerritoryMember m : membri) {
            territoryIds.add(m.ServiceTerritoryId);
        }

        // 3. Recupera i territori e gli OperatingHours collegati
        List<ServiceTerritory> territori = [
            SELECT Id, Name, OperatingHoursId
            FROM ServiceTerritory
            WHERE Id IN :territoryIds AND isActive = true
        ];

        Set<Id> operatingHoursIds = new Set<Id>();
        for (ServiceTerritory t : territori) {
            if (t.OperatingHoursId != null) operatingHoursIds.add(t.OperatingHoursId);
        }

        // 4. Recupera tutti i TimeSlot relativi agli OperatingHours
        List<TimeSlot> timeslotRecords = [
            SELECT OperatingHoursId, DayOfWeek, StartTime, EndTime
            FROM TimeSlot
            WHERE OperatingHoursId IN :operatingHoursIds
        ];

        // 5. Mappa degli orari: OperatingHoursId -> Giorno -> Lista di fasce orarie
        Map<String, String> giorniMap = new Map<String, String>{
            'Monday' => 'Lunedì',
            'Tuesday' => 'Martedì',
            'Wednesday' => 'Mercoledì',
            'Thursday' => 'Giovedì',
            'Friday' => 'Venerdì',
            'Saturday' => 'Sabato',
            'Sunday' => 'Domenica'
        };

        //Mappa per mappare ad ogni OperatingHoursId gli orari dei diversi giorni della settimana
        Map<Id, Map<String, List<Map<String, String>>>> orariPerOH = new Map<Id, Map<String, List<Map<String, String>>>>();

        for (TimeSlot slot : timeslotRecords) {
            String giorno = giorniMap.get(slot.DayOfWeek);
            if (giorno == null) continue;
    
            if (!orariPerOH.containsKey(slot.OperatingHoursId)) {
                orariPerOH.put(slot.OperatingHoursId, new Map<String, List<Map<String, String>>>());
            }
    
            Map<String, List<Map<String, String>>> giornoMap = orariPerOH.get(slot.OperatingHoursId);
    
            if (!giornoMap.containsKey(giorno)) {
                giornoMap.put(giorno, new List<Map<String, String>>());
            }
    
            giornoMap.get(giorno).add(new Map<String, String>{
                'start' => formatTime(slot.StartTime),
                'end' => formatTime(slot.EndTime)
            });
        }

        // 6. Costruzione finale da restituire al LWC
        List<Map<String, Object>> risultato = new List<Map<String, Object>>();

        for (ServiceTerritory t : territori) {
            Map<String, List<Map<String, String>>> orari = new Map<String, List<Map<String, String>>>();
            if (orariPerOH.containsKey(t.OperatingHoursId)) {
                orari = orariPerOH.get(t.OperatingHoursId);
            }

            risultato.add(new Map<String, Object>{
                'label' => t.Name,
                'value' => t.Id,
                'orari' => orari
            });
        }

        return risultato;
    }

    @AuraEnabled
    public static Map<String, Object> saveDisponibilita(String settimanaDisponibilitaInp) {

        Map<String, Object> result = new Map<String, Object>();
        Savepoint sp = Database.setSavepoint();

        try {
            List<GiornoDisponibilita> settimanaDisponibilita =
            (List<GiornoDisponibilita>) JSON.deserialize(
                settimanaDisponibilitaInp, List<GiornoDisponibilita>.class
            );

            System.debug('@MM settimanaDisponibilita:' + JSON.serialize(settimanaDisponibilita));

            Id currentUserId = UserInfo.getUserId();

            // Mappa ServiceResource associata all'utente
            List<ServiceResource> resList = [
                SELECT Id, Name, RelatedRecordId
                FROM ServiceResource
                WHERE RelatedRecordId = :currentUserId
                LIMIT 1
            ];

            ServiceResource sr = resList.get(0);

            List<ServiceTerritoryMember> stmList = [
                SELECT ServiceTerritoryId, OperatingHoursId, Id 
                FROM ServiceTerritoryMember
                WHERE ServiceResourceId = :sr.Id and IsActive__c = true
            ];

            // Costruiamo una mappa dei membri per territoryId
            Map<Id, ServiceTerritoryMember> memberByTerritory = new Map<Id, ServiceTerritoryMember>();
            for (ServiceTerritoryMember stm : stmList) {
                memberByTerritory.put(stm.ServiceTerritoryId, stm);
            }

            // Mappa nome giorno IT -> EN per campo DayOfWeek
            Map<String, String> giornoToPicklist = new Map<String, String>{
                'Lunedì' => 'Monday',
                'Martedì' => 'Tuesday',
                'Mercoledì' => 'Wednesday',
                'Giovedì' => 'Thursday',
                'Venerdì' => 'Friday',
                'Sabato' => 'Saturday',
                'Domenica' => 'Sunday'
            };

            // Precarichiamo i ServiceTerritory per i nomi
            Set<Id> territoryIds = memberByTerritory.keySet();
            Map<Id, ServiceTerritory> territories = new Map<Id, ServiceTerritory>([
                SELECT Id, Name FROM ServiceTerritory WHERE Id IN :territoryIds
            ]);
            
            List<OperatingHours> ohToInsert = new List<OperatingHours>();
            List<OperatingHours> ohToUpdate = new List<OperatingHours>();
            List<TimeSlot> timeSlotsToInsert = new List<TimeSlot>();
            List<ServiceTerritoryMember> stmToUpdate = new List<ServiceTerritoryMember>();

            Map<Id, OperatingHours> newOperatingHoursByTerritory = new Map<Id, OperatingHours>();

            // Recupera tutti gli OperatingHours usati
            Set<Id> allOperatingHoursIds = new Set<Id>();
            for (ServiceTerritoryMember stm : stmList) {
                if (stm.OperatingHoursId != null) {
                    allOperatingHoursIds.add(stm.OperatingHoursId);
                }
            }
           
            // Elimina tutti i TimeSlot collegati
            if (!allOperatingHoursIds.isEmpty()) {
                List<TimeSlot> oldSlots = [
                    SELECT Id FROM TimeSlot WHERE OperatingHoursId IN :allOperatingHoursIds
                ];
                if (!oldSlots.isEmpty()) {
                    delete oldSlots;
                }
            }

           
            System.debug('@MM settimanaDisponibilita: ' + JSON.serialize(settimanaDisponibilita));
            for (GiornoDisponibilita giorno : settimanaDisponibilita) {
                for (Disponibilita disp : giorno.disponibilita) { 
                    Id territoryId = disp.puntoVenditaId;
                    if (!memberByTerritory.containsKey(territoryId)) continue;

                    ServiceTerritoryMember stm = memberByTerritory.get(territoryId);

                    // Creazione o recupero OperatingHours
                    OperatingHours oh;
                    if (stm.OperatingHoursId != null) {
                        oh = [SELECT Id FROM OperatingHours WHERE Id = :stm.OperatingHoursId LIMIT 1];
                    } else if (newOperatingHoursByTerritory.containsKey(territoryId)) {
                        oh = newOperatingHoursByTerritory.get(territoryId);
                    } else {
                        String nomeCompleto = sr.Name;
                        String territoryName = territories.get(territoryId).Name;
                        oh = new OperatingHours(
                            Name = nomeCompleto + ' Operating Hours at ' + territoryName,
                            TimeZone = 'Europe/Rome'
                        );
                        
                        insert oh;
                        newOperatingHoursByTerritory.put(territoryId, oh);
                    }

                   
                    // Conversione date + orari a Time
                    String[] startTimeSplit = disp.oraInizio.split(':');
                    String[] endTimeSplit = disp.oraFine.split(':');

                    Time startTime = Time.newInstance(Integer.valueOf(startTimeSplit[0]) //hour
                                     ,Integer.valueOf(startTimeSplit[1]) //min
                                     ,0                                //sec
                                     ,0);  

                    Time endTime = Time.newInstance(Integer.valueOf(endTimeSplit[0]) //hour
                                     ,Integer.valueOf(endTimeSplit[1]) //min
                                     ,0                                //sec
                                     ,0); 

                    TimeSlot ts = new TimeSlot(
                        OperatingHoursId = oh.Id,
                        StartTime = startTime,
                        EndTime = endTime,
                        DayOfWeek = giornoToPicklist.get(giorno.giorno)
                    );
                    timeSlotsToInsert.add(ts);

                    // Associare OperatingHours appena creato al member se mancante
                    if (stm.OperatingHoursId == null && !stmToUpdate.contains(stm)) {
                        stm.OperatingHoursId = oh.Id;
                        stmToUpdate.add(stm);
                    }
                }
            }

            if (!stmToUpdate.isEmpty()) update stmToUpdate;
            if (!timeSlotsToInsert.isEmpty()) insert timeSlotsToInsert;

            result.put('success', true);
            result.put('message', 'Disponibilità salvate correttamente.');
        } catch (Exception e) {
            result.put('success', false);
            result.put('message', 'Errore durante il salvataggio: ' + e.getMessage());
            Database.rollback(sp);
        }

        return result;
    }


    private static String formatTime(Time t) {
        String hh = String.valueOf(t.hour());
        String mm = String.valueOf(t.minute());
        if (hh.length() == 1) hh = '0' + hh;
        if (mm.length() == 1) mm = '0' + mm;
        return hh + ':' + mm;
    }

    public class SlotDisponibilitaDTO {
        @AuraEnabled public Id puntoVenditaId;
        @AuraEnabled public String puntoVendita;
        @AuraEnabled public String oraInizio;
        @AuraEnabled public String oraFine;
        @AuraEnabled public String message;
    }

    public class Disponibilita {
        @AuraEnabled public String id;
        @AuraEnabled public String puntoVendita;
        @AuraEnabled public String puntoVenditaId;
        @AuraEnabled public String oraInizio;
        @AuraEnabled public String oraFine;

        public Disponibilita() {}
    }

    public class GiornoDisponibilita {
        @AuraEnabled public String giorno;
        @AuraEnabled public List<Disponibilita> disponibilita;

        public GiornoDisponibilita() {}
    }
}