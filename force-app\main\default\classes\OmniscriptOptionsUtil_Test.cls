@isTest
private class OmniscriptOptionsUtil_Test {
    @testSetup
    static void setup() {
        // Creazione di un utente di test
        Profile p = [SELECT Id FROM Profile WHERE Name = 'System Administrator'];
        Account a = new Account();
        User u = new User(
            Alias = 'testuser',
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8',
            LastName = 'Test',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            ProfileId = p.Id,
            TimeZoneSidKey = 'America/Los_Angeles',
            UserName = '<EMAIL>',
            FiscalCode__c = '**********',
            IdAzienda__c = a.Id
        );
        insert u;

        // Creazione di un NetworkUser di test
        NetworkUser__c nu = new NetworkUser__c(
            Agency__c = a.Id,
            FiscalCode__c = '**********',
            IsActive__c = true,
            Society__c = 'SOC_1',
            User__c = u.Id
        );
        insert nu;
        NetworkUser__c nu2 = new NetworkUser__c(
            Agency__c = a.Id,
            FiscalCode__c = '**********',
            IsActive__c = true,
            Society__c = 'SOC_4',
            User__c = u.Id
        );
        insert nu2;
        NetworkUser__c nu3 = new NetworkUser__c(
            Agency__c = a.Id,
            FiscalCode__c = '**********',
            IsActive__c = true,
            Society__c = 'SOC_X',
            User__c = u.Id
        );
        insert nu3;
    }

    @isTest
    static void testSocietyPicklistAdmin() {
        // Impostazione dell'utente come amministratore di sistema
        User u = [SELECT Id FROM User WHERE Alias = 'testuser'];
        System.runAs(u) {
            Map<String, Object> inputMap = new Map<String, Object>();
            Map<String, Object> outMap = new Map<String, Object>();
            Map<String, Object> options = new Map<String, Object>();

            OmniscriptOptionsUtil util = new OmniscriptOptionsUtil();
            util.societyPicklist(inputMap, outMap, options);

            List<Map<String, String>> optionsList = (List<Map<String, String>>)outMap.get('options');
            //System.assertEquals(2, optionsList.size());
            //System.assertEquals('unipolsai', optionsList[0].get('name'));
            //System.assertEquals('Unipol', optionsList[0].get('value'));
            //System.assertEquals('unisalute', optionsList[1].get('name'));
            //System.assertEquals('Unisalute', optionsList[1].get('value'));
        }
    }

    @isTest
    static void testSocietyPicklistNonAdmin() {
        // Impostazione dell'utente come non amministratore di sistema
        Profile p = [SELECT Id FROM Profile WHERE Name != 'System Administrator' LIMIT 1];
        Account a = new Account();
        User u = new User(
            Alias = 'testuse2',
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8',
            LastName = 'NonAdmin',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            ProfileId = p.Id,
            TimeZoneSidKey = 'America/Los_Angeles',
            UserName = '<EMAIL>',
            FiscalCode__c = '**********',
            IdAzienda__c =  a.Id
        );
        insert u;

        System.runAs(u) {
            Map<String, Object> inputMap = new Map<String, Object>();
            Map<String, Object> outMap = new Map<String, Object>();
            Map<String, Object> options = new Map<String, Object>();

            OmniscriptOptionsUtil util = new OmniscriptOptionsUtil();
            util.invokeMethod('societyPicklist',inputMap, outMap, options);

            List<Map<String, String>> optionsList = (List<Map<String, String>>)outMap.get('options');
            //System.assertEquals(2, optionsList.size());
            //System.assertEquals('unipolsai', optionsList[0].get('name'));
            //System.assertEquals('Unipol', optionsList[0].get('value'));
        }
    }
}