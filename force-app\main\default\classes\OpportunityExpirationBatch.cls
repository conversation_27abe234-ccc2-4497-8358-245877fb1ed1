/**************
 * <AUTHOR> - <PERSON><PERSON><PERSON>
 * @date                2025-05-28
 * @description         This batch executes several operations related to the expiration of Opportunity related records.
 *                      It is triggered by a scheduler every day. (manual action)
 *                      It proceeds with the 3 consecurtive batches of following object, when they are expired
 *                          1) QUOTE : changes the status of Quotes that are expired. Consequently : 
 *                                  (1) Analyze if Parent Container only expired Quotes. If yes its status is changed and info are rolled-up
 *                                  (2) Analyze if Parent Product only has expired Quotes. If yes its status is changed and info are rolled-up
 * *                        2) OPPORTUNITY of RT AGENZIALE OR OMNICANALE : changes the status and rollup info. Consequently
 *                                  (1) closes all Quotes related
 *                                  (2) closes all Product related and rollup info.
 *                          3) OPPORTUNITY of RT PRODOTTO : changes the status of Opportunity Prodotto that are expired and rollup info. Consequently :
 *                                  (1) related Quotes are set to expired.
 *                                  (2) parent Container opp is analyzed if active quote. if not is closed and info is rolled up
 * DATE                 WHO                         WHAT
 * 2025-05-28           <PERSON><PERSON><PERSON>            Creation of the feature
 * 2025-06-25           <PERSON><PERSON><PERSON>            Change the order of execution (Quote > Opp Container > Opp Product) to bypass some record trigger on Opp Product. Make several test classes
 * 
 */
public without sharing class OpportunityExpirationBatch implements Database.Batchable<SObject>, Database.Stateful {

    public enum objectToProcess {QUOTE, OPPORTUNITY_PRODUCT, OPPORTUNITY_AGENZIALE_OR_OMNICANALE}
    public objectToProcess currentTargetExpiringObject;

    public class CustomException extends Exception {}

    public OpportunityExpirationBatch(objectToProcess myObjectToProcess){
        currentTargetExpiringObject = myObjectToProcess;
    }


    /********
     * @description             returns a series of criteria to fetch Quote, Opp Product, Opp Container which have become expired the day before.
     */
    public List<SObject> start(Database.BatchableContext bc) {
        System.debug(LoggingLevel.INFO, 'OpportunityExpirationBatch | start | entering the "start" context with (objectToProcess = ' + currentTargetExpiringObject + ')');
        switch on currentTargetExpiringObject {
            when  QUOTE {
                return new List<Quote>([SELECT Id, OpportunityId, CommercialStatus__c FROM Quote WHERE (ExpirationDate <= YESTERDAY AND CommercialStatus__c = :OpportunityExpirationHelper.QUOTE_COMMERCIAL_STATUS_ACTIVE) ]);
            }
            when  OPPORTUNITY_PRODUCT {
                return new List<Opportunity>( [SELECT Id FROM Opportunity WHERE StageName != :OpportunityExpirationHelper.OPPORTUNITY_STAGENAME_CLOSE AND RecordType.DeveloperName = :OpportunityExpirationHelper.OPPORTUNITY_RECORD_TYPE_DEVELOPER_NAME_PRODOTTO AND WorkingSLAExpiryDate__c <= YESTERDAY]);
            }
            when OPPORTUNITY_AGENZIALE_OR_OMNICANALE {
                return new List<Opportunity>([SELECT Id FROM Opportunity WHERE StageName != :OpportunityExpirationHelper.OPPORTUNITY_STAGENAME_CLOSE AND  (RecordType.DeveloperName = :OpportunityExpirationHelper.OPPORTUNITY_RECORD_TYPE_DEVELOPER_NAME_OMNICANALE OR RecordType.DeveloperName = :OpportunityExpirationHelper.OPPORTUNITY_RECORD_TYPE_DEVELOPER_NAME_AGENZIALE) AND WorkingSLAExpiryDate__c <= YESTERDAY]);
            }
            when else {
                throw new CustomException('Not implemented ' + currentTargetExpiringObject);
            }
        }
    }

    public void execute(Database.BatchableContext bc,  List<sObject> myList){
        System.debug(LoggingLevel.INFO, 'OpportunityExpirationBatch | execute | entering the "execute" context with (objectToProcess = ' + currentTargetExpiringObject + ')');


        switch on currentTargetExpiringObject {
            when QUOTE {
                OpportunityExpirationHelper.closeExpiringQuotesOrchestrator((new Map<Id,SObject>(myList)).keySet());
            }
            when OPPORTUNITY_PRODUCT {
                OpportunityExpirationHelper.closeExpiringOppProductsOrchestrator((new Map<Id,SObject>(myList)).keySet());            
            }
            when OPPORTUNITY_AGENZIALE_OR_OMNICANALE {
                OpportunityExpirationHelper.closeExpiringOppContainerOrchestrator((new Map<Id,SObject>(myList)).keySet());
            }
        }
    }

    public void finish(Database.BatchableContext bc){
        System.debug(LoggingLevel.INFO, 'OpportunityExpirationBatch | finish | entering the "finish" context with (objectToProcess = ' + currentTargetExpiringObject + ')');
        // log summary

        switch on currentTargetExpiringObject {
            when QUOTE {
                Database.executeBatch( new OpportunityExpirationBatch(objectToProcess.OPPORTUNITY_AGENZIALE_OR_OMNICANALE));
            }
            when OPPORTUNITY_PRODUCT {
                System.debug(LoggingLevel.INFO, 'OpportunityExpirationBatch : last finish, no consecutive job');
            }
            when OPPORTUNITY_AGENZIALE_OR_OMNICANALE {
                Database.executeBatch( new OpportunityExpirationBatch(objectToProcess.OPPORTUNITY_PRODUCT));
            }
        }
    }
}