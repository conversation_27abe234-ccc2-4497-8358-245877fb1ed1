/**
 * @description       : TM_LC_CustomLookupTst.cls
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 03-02-2025
 * @last modified by  : <EMAIL>
**/
@IsTest
private class TM_LC_CustomLookupTst {

    private static TM_UTL_DataFactoryTst dataFactoryUtl = TM_UTL_DataFactoryTst.getInstance();
    //public static RPU_UTL_Constants constantsUtl = RPU_UTL_Constants.getInstance();

    @TestSetup
    private static void setup(){
        dataFactoryUtl.createClienti(5, true);
    }
    
    @IsTest
    static void testFetchLookupValues() {
        List<Account> accounts = new List<Account>();
            
        
        Test.startTest();
        Map<String, Object> returnObj = TM_LC_CustomLookup.fetchLookupValues(
                '', 'Account', 'Name',
                '', 'Name', '',
                '', '', '',
                '', '', '', '');
        accounts = (List<Account>)returnObj.get('returnList'); 

        System.assert(accounts.size() == 5);
        
        Test.stopTest();     

    }

    @IsTest
    static void testInitialize() {
        Account account = new Account();
            
        Account acc = [SELECT Id, Name FROM Account LIMIT 1];

        Test.startTest();
        Map<String, Object> returnObj = TM_LC_CustomLookup.initialize(
                'Account', 'Name', '', 'Name',
                '', '', '','', acc.Id, '',
                '', '');
        account = (Account)returnObj.get('initialRecord');

        System.assertEquals(acc.Name, account.Name);

        Test.stopTest();     

    }
    
}