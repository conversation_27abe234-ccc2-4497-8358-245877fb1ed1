/**
 * @description Helper per la gestione delle condivisioni (Share) e abbinamenti tra Account, <PERSON><PERSON><PERSON>, <PERSON>, Opportunity e altri oggetti custom.
 * <AUTHOR>
 * @since       16/05/2025
 */
@SuppressWarnings('PMD.AvoidDebugStatements')
public without sharing class ConiAssignmentHelper {
    private static final Id ID_ACCOUNT_AGENCY = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AccountAgency').getRecordTypeId();
    private static final Id ID_ACCOUNT_SOCIETY = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AccountSociety').getRecordTypeId();
    private static final Id ID_AGENCY_SOCIETY = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AgencySociety').getRecordTypeId();
    private static final Id ID_ATTIVITA_CONTATTO = Schema.SObjectType.Case.getRecordTypeInfosByDeveloperName().get('AttivitaContatto').getRecordTypeId();
    private static final Id ID_PU_FOLDER = Schema.SObjectType.InsurancePolicy.getRecordTypeInfosByDeveloperName().get('PU_FOLDER').getRecordTypeId();

    /**
     * @description       Elimina le condivisioni (Share) di FinServ__AccountAccountRelation__c e AccountDetailsNPI__c per gli utenti e agenzie specificati.
     * @param setOldUser  Set di Id utente da cui rimuovere la condivisione
     * @param seAccountId Set di Id Account
     * @param seAgencyId  Set di Id Agenzia
     */
    public static void deleteShareAAR(Set<String> setOldUser, Set<String> seAccountId, Set<String> seAgencyId) {
        List<FinServ__AccountAccountRelation__Share> listAccAccRelShare = ConiAssignmentUtility.getAccountAccountRelationShares(setOldUser, seAccountId, seAgencyId);
        Set<Id> idRelatedAccount = ConiAssignmentUtility.getRelatedAccountIds(seAgencyId);
        List<AccountDetailsNPI__Share> lstAccDetNPIShares = ConiAssignmentUtility.getAccountDetailsNPIshares(setOldUser, idRelatedAccount);
        List<SObject> listToDelete = new List<SObject>();
        listToDelete.addAll(listAccAccRelShare);
        listToDelete.addAll(lstAccDetNPIShares);
        delete listToDelete;
        //CrudUtility.checkCrudAndExecute(CrudUtility.DmlType.DELETE_OPERATION, listToDelete);
    }

    /**
     * @description         Crea le condivisioni Read per Case e Opportunity chiusi relativi ad account e agenzie specificati.
     * @param seAccountId   Set di Id Account
     * @param seAgencyId    Set di Id Agenzia
     * @param setActualUser Set di Id utente
     */
    public static void abbinamentoAAR(Set<String> seAccountId, Set<String> seAgencyId, Set<String> setActualUser) {
        List<FinServ__AccountAccountRelation__Share> listAARShare = new List<FinServ__AccountAccountRelation__Share>();
        List<FinServ__AccountAccountRelation__c> listAAR = ConiAssignmentUtility.getAccountAccountRelation(seAccountId, seAgencyId);
        for (FinServ__AccountAccountRelation__c aar : listAAR) {
            for (String actualUser : setActualUser) {
                FinServ__AccountAccountRelation__Share accAccRelAgencyShare = new FinServ__AccountAccountRelation__Share();
                accAccRelAgencyShare.ParentId = aar.Id;
                accAccRelAgencyShare.UserOrGroupId = actualUser;
                accAccRelAgencyShare.AccessLevel = 'Read';
                listAARShare.add(accAccRelAgencyShare);
            }
        }
        upsert listAARShare;
        //CrudUtility.checkCrudAndExecute(CrudUtility.DmlType.UPSERT_OPERATION, listAARShare);
        ConiAssignmentHelper.createShareReadForClosedAttivita(seAccountId, seAgencyId, setActualUser);
        ConiAssignmentHelper.createShareReadForAccountDetailsNPI(seAccountId, seAgencyId, setActualUser);
    }

    /**
     * @description         Crea le condivisioni Read per Case e Opportunity chiusi relativi ad account e agenzie specificati.
     * @param seAccountId   Set di Id Account
     * @param seAgencyId    Set di Id Agenzia
     * @param setActualUser Set di Id utente
     */
    public static void createShareReadForClosedAttivita(Set<String> seAccountId, Set<String> seAgencyId, Set<String> setActualUser) {
        List<Case> listCaseClosed = ConiAssignmentUtility.getCaseClosed(seAccountId, seAgencyId);
        List<Opportunity> listOpportunityClosed = ConiAssignmentUtility.getOpportunityClosed(seAccountId, seAgencyId);
        List<SObject> allShareToInsert = new List<SObject>();
        allShareToInsert.addAll(ConiAssignmentUtility.buildCaseShares(listCaseClosed, setActualUser));
        allShareToInsert.addAll(ConiAssignmentUtility.buildOpportunityShares(listOpportunityClosed, setActualUser));
        insert allShareToInsert;
        //CrudUtility.checkCrudAndExecute(CrudUtility.DmlType.INSERT_OPERATION, allShareToInsert);
    }

    /**
     * @description         Crea le condivisioni Read su AccountDetailsNPI__c per le agenzie e utenti specificati.
     * @param seAgencyId    Set di Id Agenzia
     * @param setActualUser Set di Id utente
     */
    public static void createShareReadForAccountDetailsNPI(Set<String> seAccountId, Set<String> seAgencyId, Set<String> setActualUser) {
        Set<Id> idRelatedAccount = new Set<Id>();
        //Recupero mandati di Agenzia
        List<FinServ__AccountAccountRelation__c> listAAR = [
            SELECT FinServ__RelatedAccount__c
            FROM FinServ__AccountAccountRelation__c
            WHERE RecordTypeId = :ID_AGENCY_SOCIETY AND FinServ__Account__c IN :seAgencyId AND (FinServ__StartDate__c = NULL OR FinServ__StartDate__c < TODAY) AND (FinServ__EndDate__c = NULL OR FinServ__EndDate__c > TODAY)
        ];
        for (FinServ__AccountAccountRelation__c aar : listAAR) {
            idRelatedAccount.add(aar.FinServ__RelatedAccount__c);
        }

        //Recuper Relazioni tra Cliente e Società di Mandato
        List<FinServ__AccountAccountRelation__c> listAARClientSociety = [
            SELECT Id
            FROM FinServ__AccountAccountRelation__c
            WHERE RecordTypeId = :ID_ACCOUNT_SOCIETY AND FinServ__Account__c IN :seAccountId AND FinServ__RelatedAccount__c IN :idRelatedAccount
        ];
        Set<Id> setIdClienteSocietaAAR = new Map<Id, FinServ__AccountAccountRelation__c>(listAARClientSociety).keySet();

        //Si condivide in lettura con il gruppo/utente il record di AccountDetailsNPI__c
        List<AccountDetails__c> listAccDetails = [SELECT AccountDetailsNPI__c FROM AccountDetails__c WHERE Relation__c IN :setIdClienteSocietaAAR AND AccountDetailsNPI__c != NULL];
        List<AccountDetailsNPI__Share> lstAccDetNPIShares = new List<AccountDetailsNPI__Share>();
        for (AccountDetails__c accDet : listAccDetails) {
            for (String idUser : setActualUser) {
                AccountDetailsNPI__Share accDetNPIShare = new AccountDetailsNPI__Share();
                accDetNPIShare.ParentId = accDet.AccountDetailsNPI__c;
                accDetNPIShare.UserOrGroupId = idUser;
                accDetNPIShare.AccessLevel = 'Read';
                lstAccDetNPIShares.add(accDetNPIShare);
            }
        }
        insert lstAccDetNPIShares;
        //CrudUtility.checkCrudAndExecute(CrudUtility.DmlType.INSERT_OPERATION, lstAccDetNPIShares);
    }

    /**
     * @description                 Gestisce la cancellazione e creazione delle condivisioni in caso di cambio agenzia su attività.
     * @param listAttivitaNewAgency Lista attività con nuova agenzia
     * @param mapAttivitaOldAgency  Mappa attività con vecchia agenzia
     */
    public static void deleteForChangeAgencyShareAAR(List<SObject> listAttivitaNewAgency, Map<Id, SObject> mapAttivitaOldAgency) {
        Set<Id> setOldAgencyId = ConiAssignmentUtility.extractOldAgencyIds(mapAttivitaOldAgency);
        Set<String> setOldExternalId = ConiAssignmentUtility.extractOldExternalIds(setOldAgencyId);
        Set<Id> setIdGroupResp = ConiAssignmentUtility.extractGroupIdsByDeveloperName(setOldExternalId);

        Map<Id, Id> setIdCaseWithNewAgency = new Map<Id, Id>();
        Map<Id, Id> setIdOpportunityWithNewAgency = new Map<Id, Id>();
        Set<Id> setIdNewAgency = new Set<Id>();
        ConiAssignmentUtility.extractNewAgencyMaps(listAttivitaNewAgency, setIdCaseWithNewAgency, setIdOpportunityWithNewAgency, setIdNewAgency);

        Map<Id, String> mapAgencyIdWithExternalId = ConiAssignmentUtility.extractAgencyIdWithExternalId(setIdNewAgency);
        Map<String, Id> mapGroupDeveloperNameWithId = ConiAssignmentUtility.extractGroupDeveloperNameWithId(mapAgencyIdWithExternalId);

        List<SObject> listShareToInsert = new List<SObject>();
        List<SObject> listShareToDelete = new List<SObject>();
        if (!setIdCaseWithNewAgency.isEmpty()) {
            listShareToDelete.addAll(ConiAssignmentUtility.getCaseSharesToDelete(setIdCaseWithNewAgency, setIdGroupResp));
            listShareToInsert.addAll(ConiAssignmentUtility.buildCaseSharesForAgencyChange(setIdCaseWithNewAgency, mapAgencyIdWithExternalId, mapGroupDeveloperNameWithId));
        }
        if (!setIdOpportunityWithNewAgency.isEmpty()) {
            listShareToDelete.addAll(ConiAssignmentUtility.getOpportunitySharesToDelete(setIdOpportunityWithNewAgency, setIdGroupResp));
            listShareToInsert.addAll(ConiAssignmentUtility.buildOpportunitySharesForAgencyChange(setIdCaseWithNewAgency, mapAgencyIdWithExternalId, mapGroupDeveloperNameWithId));
        }
        delete listShareToDelete;
        insert listShareToInsert;
        //CrudUtility.checkCrudAndExecute(CrudUtility.DmlType.DELETE_OPERATION, listShareToDelete);
        //CrudUtility.checkCrudAndExecute(CrudUtility.DmlType.INSERT_OPERATION, listShareToInsert);
    }

    /**
     * @description   Verifica se un record Case o Opportunity è stato chiuso.
     * @param sObj    Record nuovo
     * @param sObjOld Record vecchio
     * @return        `Boolean` -> true se lo stato è passato a chiuso, false altrimenti
     */
    public static Boolean checkIsClosed(SObject sObj, SObject sObjOld) {
        if (Trigger.isInsert) {
            return false;
        }
        if ('Opportunity'.equalsIgnoreCase(sObj.getSObjectType().getDescribe().getName())) {
            return String.valueOf(sObj.get('StageName')) != String.valueOf(sObjOld.get('StageName')) && 'Chiuso'.equalsIgnoreCase(String.valueOf(sObj.get('StageName')));
        } else if ('Case'.equalsIgnoreCase(sObj.getSObjectType().getDescribe().getName())) {
            return String.valueOf(sObj.get('Status')) != String.valueOf(sObjOld.get('Status')) && 'Closed'.equalsIgnoreCase(String.valueOf(sObj.get('Status')));
        }
        return false;
    }

    /**
     * @description   Verifica se è cambiata l'agenzia su un record.
     * @param sObj    Record nuovo
     * @param sObjOld Record vecchio
     * @return        `Boolean` -> true se l'agenzia è cambiata, false altrimenti
     */
    public static Boolean checkIfAgencyChanged(SObject sObj, SObject sObjOld) {
        if (Trigger.isInsert) {
            return false;
        }
        return sObjOld != null && sObj.get('Agency__c') != sObjOld.get('Agency__c') && sObjOld.get('Agency__c') != null;
    }

    /**
     * @description        Aggiorna le condivisioni di Case e Opportunity da Edit a Read per i record chiusi.
     * @param setObjectId  Set di Id oggetti (Case/Opportunity)
     * @param setAccountId Set di Id Account
     * @param setAgenziaId Set di Id Agenzia
     */
    public static void updateShareInRead(Set<Id> setObjectId, Set<Id> setAccountId, Set<Id> setAgenziaId) {
        List<SObject> listShareToUpdate = new List<SObject>();
        List<OpportunityShare> listShareOpp = [
            SELECT Id
            FROM OpportunityShare
            WHERE OpportunityId IN :setObjectId AND Opportunity.AccountId IN :setAccountId AND Opportunity.Agency__c IN :setAgenziaId AND RowCause = 'Manual' AND OpportunityAccessLevel = 'Edit' AND Opportunity.StageName = 'Chiuso'
        ];
        List<CaseShare> listShareCase = [
            SELECT Id
            FROM CaseShare
            WHERE CaseId = :setObjectId AND Case.AccountId IN :setAccountId AND Case.Agency__c IN :setAgenziaId AND RowCause = 'Manual' AND CaseAccessLevel = 'Edit' AND Case.Status = 'Closed' /*AND Case.RecordTypeId = :ID_ATTIVITA_CONTATTO*/
        ];
        for (OpportunityShare share : listShareOpp) {
            share.OpportunityAccessLevel = 'Read';
            listShareToUpdate.add(share);
        }
        for (CaseShare share : listShareCase) {
            share.CaseAccessLevel = 'Read';
            listShareToUpdate.add(share);
        }
        update listShareToUpdate;
        //CrudUtility.checkCrudAndExecute(CrudUtility.DmlType.UPDATE_OPERATION, listShareToUpdate);
    }

    /**
     * @description   Verifica se esistono attività o polizze attive per determinati account, agenzie e assegnatari.
     * @param wrapper Wrapper con parametri di verifica
     * @return        `Boolean` -> true se non esistono attività o polizze attive, false altrimenti
     */
    public static Boolean verificaAbbinamento(Set<Id> setAccountId, Set<Id> setAgenziaId, Set<Id> setIdAssigned, Set<String> setGroupNameCIP, Set<Id> setGroupSocietyCIP) {
        List<Case> listCase = [
            SELECT Id
            FROM Case
            WHERE AccountId IN :setAccountId AND Agency__c IN :setAgenziaId AND (AssignedTo__c IN :setIdAssigned OR AssignedGroup__c IN :setIdAssigned) /*AND Case.RecordTypeId = :ID_ATTIVITA_CONTATTO*/ AND Status != 'Closed'
        ];
        List<Opportunity> listOpportunity = [
            SELECT Id
            FROM Opportunity
            WHERE AccountId IN :setAccountId AND Agency__c IN :setAgenziaId AND (AssignedTo__c IN :setIdAssigned OR AssignedGroup__c IN :setIdAssigned) AND StageName != 'Chiuso'
        ];
        List<SObject> listTotalAttivita = new List<SObject>();
        listTotalAttivita.addAll(listCase);
        listTotalAttivita.addAll(listOpportunity);

        if (!setGroupNameCIP.isEmpty()) {
            List<InsurancePolicy> listInsurancePolicy = [
                SELECT Id
                FROM InsurancePolicy
                WHERE NameInsuredId IN :setAccountId AND Society__c IN :setGroupSocietyCIP AND Agency__c IN :setAgenziaId AND CIP__c IN :setGroupNameCIP AND RecordTypeId != :ID_PU_FOLDER AND (ActiveDate__c = NULL OR ActiveDate__c > TODAY)
                LIMIT 1
            ];
            return listInsurancePolicy.isEmpty() && listTotalAttivita.isEmpty();
        }
        return listTotalAttivita.isEmpty();
    }

    /**
     * @description         Elimina le condivisioni manuali di Case, Opportunity e ServiceAppointment per account, agenzie e assegnatari specificati.
     * @param setAccountId  Set di Id Account
     * @param setAgenziaId  Set di Id Agenzia
     * @param setIdAssigned Set di Id utente/gruppo assegnatario
     */
    public static void deleteRecordShare(Set<Id> setAccountId, Set<Id> setAgenziaId, Set<Id> setIdAssigned) {
        List<CaseShare> listCaseShareToDelete = [
            SELECT Id
            FROM CaseShare
            WHERE Case.AccountId IN :setAccountId AND Case.Agency__c IN :setAgenziaId AND UserOrGroupId IN :setIdAssigned AND RowCause = 'Manual'
        ];
        List<OpportunityShare> listOpportunityShareToDelete = [
            SELECT Id
            FROM OpportunityShare
            WHERE Opportunity.AccountId IN :setAccountId AND Opportunity.Agency__c IN :setAgenziaId AND UserOrGroupId IN :setIdAssigned AND RowCause = 'Manual'
        ];
        List<ServiceAppointmentShare> listServiceAppointmentShareToDelete = [
            SELECT Id
            FROM ServiceAppointmentShare
            WHERE Parent.AccountId IN :setAccountId AND Parent.ServiceTerritory.Agency__c IN :setAgenziaId AND UserOrGroupId IN :setIdAssigned AND RowCause = 'Manual'
        ];
        List<SObject> listShareToDelete = new List<SObject>();
        listShareToDelete.addAll(listCaseShareToDelete);
        listShareToDelete.addAll(listOpportunityShareToDelete);
        listShareToDelete.addAll(listServiceAppointmentShareToDelete);
        delete listShareToDelete;
        //CrudUtility.checkCrudAndExecute(CrudUtility.DmlType.DELETE_OPERATION, listShareToDelete);
    }

    /**
     * @description Converte un Set<Id> in Set<String> tramite serializzazione/deserializzazione JSON.
     * @param setId setId  Set di Id
     * @return      `Set<String>` -> Set di String corrispondenti agli Id
     */
    public static Set<String> deserializeSetIdInSetString(Set<Id> setId) {
        return (Set<String>) JSON.deserialize(JSON.serialize(setId), Set<String>.class);
    }

    /**
     * @description   Verifica se uno status è stato riaperto (da Closed/Chiuso a diverso).
     * @param sObj    Record nuovo
     * @param sObjOld Record vecchio
     * @return        `Boolean` -> true se riaperto, false altrimenti
     */
    public static Boolean checkIfStatusReopen(SObject sObj, SObject sObjOld) {
        if ('Case'.equalsIgnoreCase(sObj.getSObjectType().getDescribe().getName())) {
            return sObj.get('Status') != 'Closed' && sObjOld.get('Status') == 'Closed';
        } else if ('Opportunity'.equalsIgnoreCase(sObj.getSObjectType().getDescribe().getName())) {
            return sObj.get('StageName') != 'Chiuso' && sObjOld.get('StageName') == 'Chiuso';
        }
        return false;
    }

    /**
     * @description Verifica i permessi CRUD sugli oggetti principali gestiti dalla classe.
     * @return      `Boolean` -> true se mancano permessi, false altrimenti
     */
    @TestVisible
    private static Boolean checkPermissionCRUD() {
        return (!Schema.sObjectType.Case.isAccessible() || !Schema.sObjectType.Opportunity.isAccessible() || !Schema.sObjectType.CaseShare.isAccessible() || !Schema.sObjectType.OpportunityShare.isAccessible() || !Schema.sObjectType.CaseShare.isDeletable() || !Schema.sObjectType.OpportunityShare.isDeletable() || !Schema.sObjectType.User.isAccessible() || !Schema.sObjectType.FinServ__AccountAccountRelation__c.isAccessible() || !Schema.sObjectType.FinServ__AccountAccountRelation__c.isDeletable());
    }

    /**
     * @description
     * Wrapper per la verifica abbinamento.
     */
    public class WrapperVerificaAbbinamento {
        public Set<Id> setAccountId;
        public Set<Id> setAgenziaId;
        public Set<Id> setIdAssigned;
        public Set<String> setGroupNameCIP;
        public Set<Id> setGroupSocietyCIP;
    }
}
