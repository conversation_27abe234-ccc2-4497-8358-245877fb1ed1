@isTest
public class UserInterfaceAPIUtility_Test {

    @isTest
    static void getSObjectPicklistValuesByRecordTypeTest() {
        Test.setMock(HttpCalloutMock.class, new PicklistValuesByRecordType_Mock());

        Id recordTypeId = Schema.SObjectType.Opportunity.getRecordTypeInfosByName().get('Interest Show').getRecordTypeId();
        Test.startTest();
        Map<String, Object> response = UserInterfaceAPIUtility.getSObjectPicklistValuesByRecordType('Opportunity', recordTypeId);
        Test.stopTest();

        System.debug('# response: ' + response);
        Map<String, Object> picklistFieldValuesObject = (Map<String, Object>) response?.get('picklistFieldValues');
        Map<String, Object> targetPicklistObject = (Map<String, Object>) picklistFieldValuesObject?.get('StageName');
        List<Object> picklistValueObjects = (List<Object>) targetPicklistObject?.get('values');
        
        Assert.areEqual(2, picklistValueObjects.size(), 'There should be two StageName picklist values');
    }

    @isTest
    static void getPickListValuesTest() {
        Test.setMock(HttpCalloutMock.class, new PicklistValuesByRecordType_Mock());

        Id recordTypeId = Schema.SObjectType.Opportunity.getRecordTypeInfosByName().get('Interest Show').getRecordTypeId();
        Test.startTest();
        Map<String, Object> response = UserInterfaceAPIUtility.getSObjectPicklistValuesByRecordType('Opportunity', recordTypeId);
        List<String> picklistOptions = UserInterfaceAPIUtility.getPickListValues(response, 'StageName');
        Test.stopTest();
        
        Assert.areEqual(2, picklistOptions.size(), 'There should be two StageName picklist values');
    }
    
    public with sharing class PicklistValuesByRecordType_Mock implements HttpCalloutMock {
        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');

            res.setBody('{"picklistFieldValues":{"StageName":{"values":[{"label":"Nuovo","value":"Nuovo"},{"label":"Assegnato","value":"Assegnato"}]}}}');
            res.setStatusCode(200);
            return res;
        }
    }
}
