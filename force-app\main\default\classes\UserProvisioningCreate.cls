/*
* @description This class is used to update a User after SSO Login
* @cicd_tests UserProvisioningHandler_Test
*/
public without sharing class UserProvisioningCreate {
    
    /*
    * @description This is method is call when it is creating a new User via SSO
    * @param User u instance of the new User
    * @param Map<String, String> attributes Map Key-->Value saml request attriute --> value of the attribute
    */
    public static void handleUser(User u, Map<String, String> attributes) {
        
        List<QueueableUserProvisioning.InputWrapper> queueableInputs = new List<QueueableUserProvisioning.InputWrapper>();
        
        try{
            
            System.debug('user id: '+u.Id);
            System.debug('user: '+u);

            String fiscalCode = attributes.get('CF');
            System.debug('UserProvisioningCreate.fiscalCode: '+fiscalCode);

            System.debug('attributes: '+attributes);
            
        }catch(Exception ex){
            System.debug('UserProvisioningCreate Exception message: '+ex.getMessage());
            System.debug('UserProvisioningCreate Exception stack trace: '+ex.getStackTraceString());
            throw ex;
        }
        
    }

}
