/***************************************************************************************************************************************************
* <AUTHOR>
* @description    FilterSectionController class for handling filter logic related to Uniview. This class contains methods for fetching, saving,
*                 and manipulating filter data, including metadata-driven filters and user preferences. It integrates with the Salesforce 
*                 metadata API and User Interface API for dynamic filter creation and supports AuraEnabled methods for Lightning components.
* @date           2024-01-29
****************************************************************************************************************************************************/
public without sharing class FilterSectionController {
    
    // Custom exception class for handling specific exceptions in the FilterSectionController class.
    public class FilterSectionControllerException extends Exception {}
    
    // A static map of default values used in filter logic, currently includes the current user's name.
    public final static Map<String, String> DEFAULT_VALUES = new Map<String, String> {
        '{!CurrentUserName}' => UserInfo.getName()
    };

    // Static variable to store the Uniview key.
    public static String univiewKey { get; set; }

    // Static property to get and set filter metadata from UniviewFilter__mdt, filtered by UniviewKey__c and IsActive__c.
   @testVisible static List<UniviewFilter__mdt> filterMetadata { 
        get {
            if ( filterMetadata == null ){
                filterMetadata = [
                SELECT FieldName__c, FieldOrder__c, FilterOperatorList__c, IsActive__c, IsModifiable__c,
                FieldType__c, FilterInputType__c, FilterLabel__c, FilterName__c, FilterOptions__c,
                FilterType__c, FilterValue__c, Object__c, FilterClass__c, FilterOperator__c, IsNullable__c
                FROM UniviewFilter__mdt 
                WHERE UniviewKey__c =: univiewKey AND IsActive__c = true
                ORDER BY FilterLabel__c ASC
                ];
            }
            return filterMetadata;
        } set; 
    }

    // Static property to get and set default filters from UniviewFilter__mdt, filtered by UniviewKey__c, IsActive__c, and IsModifiable__c.
    @testVisible static List<UniviewFilter__mdt> getDefaultFiltersFromMD { 
        get {
            if ( getDefaultFiltersFromMD == null ){
                getDefaultFiltersFromMD = [
                SELECT FieldName__c, FieldOrder__c, FilterOperatorList__c, IsActive__c, IsModifiable__c,
                FieldType__c, FilterInputType__c, FilterLabel__c, FilterName__c, FilterOptions__c,
                FilterType__c, FilterValue__c, Object__c, FilterClass__c, FilterOperator__c, IsNullable__c
                FROM UniviewFilter__mdt 
                WHERE UniviewKey__c =: univiewKey AND IsActive__c = true AND IsModifiable__c = false
                ORDER BY FieldOrder__c ASC
                ];
            }
            return getDefaultFiltersFromMD;
        } set; 
    }


    
    /******************************************************************************************
    * @description  Fetches filters from metadata and constructs filter objects for UI.
    * @param        univiewKey - The Uniview key to filter metadata.
    * @param        recordtypesEnabled - The record types enabled for the filters.
    * @param        objectName - The name of the object to fetch filters for.
    * @return       List of filters constructed from metadata.
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static List<Filter> getFiltersFromMD(String univiewKey, String recordtypesEnabled, String objectName) {
        try {

            FilterSectionController.univiewKey=univiewKey;
            List<UniviewFilter__mdt> filterMetadata = filterMetadata;
            Set<Id> recordtypeIdsEnabled = getRecordtypeIdsEnabled(recordtypesEnabled, objectName);
            List<Filter> filters = createFilterObjectsFromMD(filterMetadata, recordtypeIdsEnabled, objectName);

            return filters;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    /******************************************************************************************
    * @description  Saves user preferences for filters.
    * @param        maximumUserPreferences - Maximum number of user preferences allowed.
    * @param        request - The request object containing filter data.
    * @param        userPreferenceName - The name of the user preference.
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static void saveFilters(Integer maximumUserPreferences, DataTableController.QueryRequest request, String userPreferenceName) {
        try {
            if(
                !Schema.sObjectType.UniViewUserPreference__c.fields.UniviewKey__c.isCreateable() ||
                !Schema.sObjectType.UniViewUserPreference__c.fields.Query__c.isCreateable() ||
                !Schema.sObjectType.UniViewUserPreference__c.fields.QueryParameters__c.isCreateable() ||
                !Schema.sObjectType.UniViewUserPreference__c.fields.OwnerId.isCreateable() ||
                !Schema.sObjectType.UniViewUserPreference__c.fields.RecordTypeId.isCreateable() ||                
                !Schema.sObjectType.UniViewUserPreference__c.fields.UserPreferenceName__c.isCreateable() ||
                !Schema.sObjectType.UniViewUserPreference__c.fields.IsPublic__c.isCreateable()            
            ) {
                return;
            }

            UniViewUserPreference__c newQuery = new UniViewUserPreference__c(
                UniviewKey__c = request.univiewKey,
                Query__c = DataTableController.createQuery(request, DataTableController.getFieldsFromMD(request.univiewKey)),
                QueryParameters__c = JSON.serialize(request.filters),
                OwnerId = UserInfo.getUserId(),
                RecordTypeId = getRecodType('UniViewUserPreference__c','Custom Report'),
                UserPreferenceName__c = userPreferenceName,
                IsPublic__c=false
            );
            insert newQuery;

            List<UniViewUserPreference__c> latestQueries = getOwnedLatestQueries(request.univiewKey);
            if(latestQueries.size() > maximumUserPreferences) {
                delete latestQueries[latestQueries.size() - 1];
            }

        } catch (Exception e) {
            System.debug(e.getMessage() + ' ' + e.getLineNumber() + ' ' + e.getStackTraceString());
            throw new AuraHandledException(e.getMessage());
        }
    }

    /******************************************************************************************
    * @description  Fetches user preferences from custom object based on Uniview key.
    * @param        univiewKey - The Uniview key to filter user preferences.
    * @return       List of user preferences.
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static List<UniViewUserPreference__c> getFiltersFromCustomObject(String univiewKey) {
        try {
            return getLatestQueries(univiewKey);
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    /******************************************************************************************
    * @description  Fetches the latest user preferences owned by the current user.
    * @param        univiewKey - The Uniview key to filter user preferences.
    * @return       List of user preferences owned by the current user.
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static List<UniViewUserPreference__c> getOwnedLatestQueries(String univiewKey) {
        try{
            return [
            SELECT UniviewKey__c, Query__c, CreatedDate, QueryParameters__c, Name, UserPreferenceName__c,id
            FROM UniViewUserPreference__c 
            WHERE OwnerId =: UserInfo.getUserId() AND UniviewKey__c =: univiewKey AND RecordTypeId =: getRecodType('UniViewUserPreference__c','Custom Report') AND IsPublic__c = false
            WITH USER_MODE
            ORDER BY CreatedDate DESC
            ];
        }
        catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
        
    }

    /******************************************************************************************
    * @description  Fetches the latest user preferences, including public ones.
    * @param        univiewKey - The Uniview key to filter user preferences.
    * @return       List of latest user preferences including public ones.
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static List<UniViewUserPreference__c> getLatestQueries(String univiewKey) {
        try{
            return [
            SELECT UniviewKey__c, Query__c, CreatedDate, QueryParameters__c, Name, UserPreferenceName__c,id,IsPublic__c
            FROM UniViewUserPreference__c 
            WHERE ((OwnerId =: UserInfo.getUserId() AND UniviewKey__c =: univiewKey AND RecordTypeId =: getRecodType('UniViewUserPreference__c','Custom Report')) OR IsPublic__c = true)
            WITH USER_MODE
            ORDER BY IsPublic__c DESC,CreatedDate DESC
            ];
        }
        catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
        
    }

    /******************************************************************************************
    * @description  Deletes a user preference record by ID.
    * @param        recordId - The ID of the user preference record to delete.
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static void  deleteUniViewUserPreference(Id recordId) {
        Id recordtype = Schema.SObjectType.UniViewUserPreference__c.getRecordTypeInfosByName().get('Custom Report').getRecordTypeId();
        try {
            delete [SELECT Id FROM UniViewUserPreference__c WHERE Id = :recordId AND RecordTypeId =: getRecodType('UniViewUserPreference__c','Custom Report')];
        } catch (Exception e) {
            throw new AuraHandledException('Error deleting record: ' + e.getMessage());
        }
    }

    /******************************************************************************************
    * @description  Retrieves the record type ID for a given object and record type name.
    * @param        obj - The API name of the object.
    * @param        name - The name of the record type.
    * @return       The record type ID.
    *******************************************************************************************/
    private static Id getRecodType(String obj, String name){
        try{
            return Schema.getGlobalDescribe().get(obj).getDescribe().getRecordTypeInfosByName().get(name).getRecordTypeId();
        }
        catch (Exception e) {
            throw new FilterSectionControllerException(e.getMessage());
        }
        
    }

    /******************************************************************************************
    * @description  Retrieves UI API responses for record types enabled.
    * @param        recordtypeIdsEnabled - Set of record type IDs enabled.
    * @param        objectName - The API name of the object.
    * @return       List of UI API responses.
    *******************************************************************************************/
    private static List<Map<String, Object>> getUIAPIResponses(Set<Id> recordtypeIdsEnabled, String objectName) {
        List<Map<String, Object>> uiAPIResponses = new List<Map<String, Object>>();
        for(Id i : recordtypeIdsEnabled) {
            Map<String, Object> uiAPIResponse = UserInterfaceAPIUtility.getSObjectPicklistValuesByRecordType(
                objectName,
                i
            );
            uiAPIResponses.add(uiAPIResponse);
        }
        return uiAPIResponses;
    }

    /******************************************************************************************
    * @description  Creates filter objects from metadata.
    * @param        filterMetadata - List of UniviewFilter__mdt records.
    * @param        recordtypeIdsEnabled - Set of record type IDs enabled.
    * @param        objectName - The API name of the object.
    * @return       List of constructed filter objects.
    *******************************************************************************************/
    private static List<Filter> createFilterObjectsFromMD(List<UniviewFilter__mdt> filterMetadata, Set<Id> recordtypeIdsEnabled, String objectName) {
        try{
            List<Map<String, Object>> uiAPIResponses = getUIAPIResponses(recordtypeIdsEnabled, objectName);
            System.debug('# uiAPIResponses: ' + json.serialize(uiAPIResponses));
            List<Filter> filters = new List<Filter>();
            for(UniviewFilter__mdt filter : filterMetadata) {
                // Dynamically create the filter options for Picklists and Multiselects
                List<Option> filterOptionList = createFilterOptions(filter.FilterOptions__c, filter.Object__c, filter.FieldName__c, uiAPIResponses);
                // Dynamically create the values of default filters
                String filterValue = !filter.IsModifiable__c &&  DEFAULT_VALUES.containsKey(filter.FilterValue__c)? DEFAULT_VALUES.get(filter.FilterValue__c) : filter.FilterValue__c;
                filters.add(new Filter(
                    filter.FilterLabel__c,
                    filter.FilterName__c,
                    filter.FilterType__c,
                    filter.Object__c,
                    filter.FieldName__c,
                    filter.FieldType__c,
                    filter.FilterOperatorList__c,
                    filter.FilterInputType__c,
                    filter.FilterLabel__c,
                    filter.FilterName__c,
                    filterValue,
                    filter.FilterOperator__c,
                    filterOptionList,
                    filter.FilterClass__c,
                    filter.IsActive__c,
                    filter.IsModifiable__c,
                    (Integer) filter.FieldOrder__c,
                    filter.IsNullable__c
                ));
            }
            return filters;
        }
        catch (Exception e) {
            throw new FilterSectionControllerException(e.getMessage());
        }

    }

    /******************************************************************************************
    * @description  Creates filter options based on filter metadata and UI API responses.
    * @param        filterOptions - Options string from metadata.
    * @param        objectName - The API name of the object.
    * @param        fieldName - The API name of the field.
    * @param        uiAPIResponses - List of UI API responses.
    * @return       List of constructed filter options.
    *******************************************************************************************/
    private static List<Option> createFilterOptions(
        String filterOptions, 
        String objectName, 
        String fieldName,
        List<Map<String, Object>> uiAPIResponses
    ) {
        try{
            List<Option> filterOptionList = new List<Option>();

            // If the options field is equal to "API" => Get the picklist values from the User Interface API
            if(String.isNotBlank(filterOptions) && filterOptions == 'API') {
                Set<String> optionStrings = new Set<String>();
                for(Map<String, Object> reponse : uiAPIResponses) {
                    optionStrings.addAll(UserInterfaceAPIUtility.getPickListValues(reponse, fieldName));
                }
                for(String option : optionStrings) {
                    List<String> optionValues = option.split(';');
                    filterOptionList.add(new Option(optionValues[0], optionValues[1]));
                }
                return filterOptionList;
            }

            // If the options field is equal to "QUERY" => Get the picklist values by quering the values of the filter field
            if(String.isNotBlank(filterOptions) && filterOptions == 'QUERY') {
                return getOptionsByField(objectName, fieldName);
            }

            // Get the picklist values manually added in the metadata by spliting the string
            if(String.isNotBlank(filterOptions)) {
                for(String option : filterOptions.split(';')) {
                    filterOptionList.add(new Option(option));
                }
                return filterOptionList;
            }

            return filterOptionList;
        }
        catch (Exception e) {
            throw new FilterSectionControllerException(e.getMessage());
        }

    }

    @AuraEnabled(cacheable=false)
    public static List<Filter> getDefaultFiltersFromMD(String univiewKey, String recordtypesEnabled, String objectName) {
        try {       

            FilterSectionController.univiewKey=univiewKey;
            List<UniviewFilter__mdt> getDefaultFiltersFromMD = getDefaultFiltersFromMD;
            Set<Id> recordtypeIdsEnabled = getRecordtypeIdsEnabled(recordtypesEnabled, objectName);
            List<Filter> filters = createFilterObjectsFromMD(getDefaultFiltersFromMD, recordtypeIdsEnabled, objectName);

            return filters;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    private static Set<Id> getRecordtypeIdsEnabled(String recordtypesEnabled, String objectName) {
        try{
            Set<Id> recordtypeIdsEnabled = new Set<Id>();
            for(RecordType rt : [
                SELECT Id, DeveloperName, SobjectType 
                FROM RecordType 
                WHERE SobjectType =: objectName AND DeveloperName IN :recordtypesEnabled.split(';') WITH USER_MODE]) {  
                recordtypeIdsEnabled.add(rt.Id);
            }
            return recordtypeIdsEnabled;
        }
        catch (Exception e) {
            throw new FilterSectionControllerException(e.getMessage());
        }

    }

    // Filters to be persisted to the database
    public class FilterPersistence {
        @AuraEnabled
        public String filterName {get;set;}
        @AuraEnabled
        public String filterValue {get;set;}
        @AuraEnabled
        public String filterType {get;set;}
        @AuraEnabled
        public String filterOperator {get;set;}
        @AuraEnabled
        public String fieldName {get;set;}
        @AuraEnabled
        public String fieldType {get;set;}
        @AuraEnabled
        public Boolean isNullable {get;set;}
    }

    // Filter definitions to be ruturned to the GUI constructed by
    // the Uniview Filter Metadata
    public class Filter {
        @AuraEnabled
        public String label {get;set;}
        @AuraEnabled
        public String value {get;set;}
        @AuraEnabled
        public String filterType {get;set;}
        @AuraEnabled
        public String objectType {get;set;}
        @AuraEnabled
        public String fieldName {get;set;}
        @AuraEnabled
        public String fieldType {get;set;}
        @AuraEnabled
        public List<Option> filterOperatorList {get;set;}
        @AuraEnabled
        public String filterInputType {get;set;}
        @AuraEnabled
        public String filterLabel {get;set;}
        @AuraEnabled
        public String filterName {get;set;}
        @AuraEnabled
        public String filterValue {get;set;}
        @AuraEnabled
        public String filterOperator {get;set;}
        @AuraEnabled
        public List<Option> filterOptions {get;set;}
        @AuraEnabled
        public String filterClass {get;set;}
        @AuraEnabled
        public Boolean isActive {get;set;}
        @AuraEnabled
        public Boolean isModifiable {get;set;}
        @AuraEnabled
        public Boolean isNullable {get;set;}
        @AuraEnabled
        public Integer fieldOrder {get;set;}
        @AuraEnabled
        public Boolean isInput {get;set;}
        @AuraEnabled
        public Boolean isBooleanInput {get;set;}
        @AuraEnabled
        public Boolean isPicklist {get;set;}
        @AuraEnabled
        public Boolean isMultiselect {get;set;}
        @AuraEnabled
        public Boolean isPicklistWithSuggestions {get;set;}
        @AuraEnabled
        public Boolean isMultiselectWithSuggestions {get;set;}

        public Filter(
            String label,
            String value,
            String filterType,
            String objectType,
            String fieldName,
            String fieldType,
            String filterOperatorList,
            String filterInputType,
            String filterLabel,
            String filterName,
            String filterValue,
            String filterOperator,
            List<Option> filterOptions,
            String filterClass,
            Boolean isActive,
            Boolean isModifiable,
            Integer fieldOrder,
            Boolean isNullable
        ) {
            this.label = label;
            this.value = value;
            this.filterType = filterType;
            this.objectType = objectType;
            this.fieldName = fieldName;
            this.fieldType = fieldType;
            this.filterInputType = filterInputType;
            this.filterLabel = filterLabel;
            this.filterName = filterName;
            this.filterValue = filterValue;
            this.filterOperator = filterOperator;
            this.filterOptions = filterOptions;
            this.filterClass = filterClass;
            this.isActive = isActive;
            this.isModifiable = isModifiable;
            this.isNullable = isNullable;
            this.fieldOrder = fieldOrder;
            
            // Constructing the Option objects
            this.filterOperatorList = new List<Option>();
            for(String operator : filterOperatorList.split(';')) {
                this.filterOperatorList.add(new Option(operator));
            }

            // Constructing the flags for the filters' conditional rendering
            this.isBooleanInput = (filterType == 'Input' && filterInputType == 'boolean');
            this.isInput = (filterType == 'Input' && !this.isBooleanInput);
            this.isPicklist = (filterType == 'Picklist');
            this.isMultiselect = (filterType == 'Multiselect');
            this.isPicklistWithSuggestions = (filterType == 'Picklist With Suggestions');
            this.isMultiselectWithSuggestions = (filterType == 'Multiselect with Suggestions');
        }
    }

    public class Option {
        @AuraEnabled
        public String label {get;set;}
        @AuraEnabled
        public String value {get;set;}
        @AuraEnabled
        public Boolean selected {get;set;}

        public Option(String label, String value, Boolean selected) {
            this.label = label;
            this.value = value;
            this.selected = selected;
        }

        public Option(String value) {
            this.label = value;
            this.value = value;
            this.selected = false;
        }

        public Option(String label, String value) {
            this.label = label;
            this.value = value;
            this.selected = false;
        }
    }

    /******************************************************************************************
    * @description  Retrieves options for a field by querying the object's records.
    * @param        objectName - The API name of the object.
    * @param        fieldName - The API name of the field.
    * @return       List of options based on field values.
    *******************************************************************************************/
    public static List<Option> getOptionsByField(String objectName, String fieldName) {
        try{
            Set<String> uniqueFieldValues = new Set<String>();
            if(fieldName.contains('.')) {
                List<String> relationshipTraversals = fieldName.split('\\.');
                fieldName = relationshipTraversals[relationshipTraversals.size() - 1];
            }
            String query = 'SELECT Id, ' + String.escapeSingleQuotes(fieldName) + ' FROM ' + String.escapeSingleQuotes(objectName) + ' ORDER BY ' + String.escapeSingleQuotes(fieldName) + ' ASC';
            for(SObject s : Database.query(query)) {
                uniqueFieldValues.add((String) s.get(fieldName));
            }

            List<Option> filterOptionList = new List<Option>();
            for(String currentValue : uniqueFieldValues) {
                if(String.isNotBlank(currentValue)) {
                    filterOptionList.add(new Option(currentValue));
                }
            }
            return filterOptionList;
        }
        catch (Exception e) {
            throw new FilterSectionControllerException(e.getMessage());
        }
    }
}