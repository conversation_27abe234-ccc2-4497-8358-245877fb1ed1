/**
 * @description       : TM_SRV_CustomLookup.cls
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 03-02-2025
 * @last modified by  : <EMAIL>
 * @cicd_tests TM_SRV_CustomLookupTst
**/
public with sharing class TM_SRV_CustomLookup {

    public static TM_SRV_CustomLookup getInstance(){
        return new TM_SRV_CustomLookup();
    }

    public Map<String, Object> initialize (String objectApiName, String queryFields, String queryWhere,
                                           String filterSearchKeywordField, String filterFields, String primaryDisplayField,
                                           String secondaryDisplayField, String orderByField, String initialSelectedId,
                                           String queryClass, String queryClassMethod, String queryClassParams) {

        Map<String, Object> returnValue = new Map<String, Object>();
        returnValue.put('error', false);
        
        try {
            if(!String.isBlank(queryClass)){
                TM_IApexAction ri = (TM_IApexAction) Type.forName(queryClass).newInstance();
                List<SObject> returnList = (List<SObject>)ri.execute(
                    	new Map<String, Object> {
                            'method' => queryClassMethod,
                            'searchKeyword' => '',
                            'objectApiName' => objectApiName,
                            'queryFields' => queryFields,
                            'queryWhere' => queryWhere,
                            'filterSearchKeywordField' => filterSearchKeywordField,
                            'filterFields' => filterFields,
                            'primaryDisplayField' => primaryDisplayField,
                            'secondaryDisplayField' => secondaryDisplayField,
                            'orderByField' => orderByField,
                            'initialSelectedId' => initialSelectedId,
                            'additionalParams' => queryClassParams
                        });      
                returnValue.put('initialRecord', returnList[0]);
            }else{
                Map<String, Object> returnedQuery = TM_QR_CustomLookup.buildQuery(
                        '', objectApiName, queryFields, queryWhere, filterSearchKeywordField,
                        filterFields, primaryDisplayField, secondaryDisplayField, orderByField,
                        '', initialSelectedId);
                String query = '';
                if (returnedQuery.get('error') == false) {
                    query = (String) returnedQuery.get('query');
                } else {
                    throw new MyException('Unable to build query');
                }
                returnValue.put('initialize query', query);
                sObject initialRecord = Database.query(query);
                returnValue.put('initialRecord', initialRecord);
            }

            
        } catch (Exception e) {
            System.debug('e.getMessage() ----> ' + e.getMessage());
            System.debug('errorStackTraceString -----> ' + e.getStackTraceString());
            returnValue.put('error', true);
            returnValue.put('errorMessage', e.getMessage());
            returnValue.put('errorStackTraceString', e.getStackTraceString());
        }
        return returnValue;
    }


    public Map<String, Object> fetchLookupValues (String searchKeyword, String objectApiName, String queryFields, String queryWhere,
                                                  String filterSearchKeywordField, String filterFields, String primaryDisplayField,
                                                  String secondaryDisplayField, String orderByField, String recordNumberLimit,
                                                  String queryClass, String queryClassMethod, String queryClassParams) {
        Map<String, Object> returnValue = new Map<String, Object> ();
        returnValue.put('error', false);
        try {

            if (!String.isBlank(queryClass)){
                TM_IApexAction ri = (TM_IApexAction) Type.forName(queryClass).newInstance();
                List<Object> returnList = (List<Object>)ri.execute(
                    	new Map<String, Object> {
                            'method' => queryClassMethod,
                            'searchKeyword' => searchKeyword,
                            'objectApiName' => objectApiName,
                            'queryFields' => queryFields,
                            'queryWhere' => queryWhere,
                            'filterSearchKeywordField' => filterSearchKeywordField,
                            'filterFields' => filterFields,
                            'primaryDisplayField' => primaryDisplayField,
                            'secondaryDisplayField' => secondaryDisplayField,
                            'orderByField' => orderByField,
                            'recordNumberLimit' => recordNumberLimit,
                            'additionalParams' => queryClassParams
                        });      
                returnValue.put('returnList', returnList);

            }else{
                String query = '';
                List<sObject> returnList = new List<sObject> ();
                Map<String, Object> returnedQuery = TM_QR_CustomLookup.buildQuery(
                        searchKeyword, objectApiName, queryFields, queryWhere, filterSearchKeywordField,
                        filterFields, primaryDisplayField, secondaryDisplayField, orderByField,
                        recordNumberLimit, '');
                System.debug('query: '+returnedQuery.get('query'));
                if (returnedQuery.get('error') == false) {

                   query = (String) returnedQuery.get('query');
                } else {

                  throw new MyException('Unable to build query');
            }
            System.debug('query' + query);
            returnValue.put('query', query);
            returnList = Database.query(query);

            returnValue.put('returnList', returnList);
            }

            
        } catch (Exception e) {

            returnValue.put('error', true);
            returnValue.put('errorMessage', e.getMessage());
            returnValue.put('errorStackTraceString', e.getStackTraceString());
            returnValue.put('queryClass', queryClass);
            returnValue.put('payload', queryClassParams);
        }
        return returnValue;
    }

    public class MyException extends Exception {}
}