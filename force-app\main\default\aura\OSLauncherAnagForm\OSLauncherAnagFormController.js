({
    init: function(component, event, helper) {
        /* this[NavigationMixin.Navigate]({
            type: 'standard__component',
            attributes: {
                componentName: 'omnistudio__vlocityLWCOmniWrapper'
            },
            state: {
                c__target: 'c:anagFormFormItalian',
                c__layout: 'lightning', // or can be 'newport'
                c__tabIcon: 'custom:custom18',
                c__tabLabel: 'AnagForm',
            }
        }) */

        var navService = component.find("navService");
        var pageReference = {
            type: 'standard__component',
            attributes: {
                componentName: 'omnistudio__vlocityLWCOmniWrapper'
            },
            state: {
                c__target: 'c:anagFormFormItalian',
                c__layout: 'lightning', // or can be 'newport'
                c__tabIcon: 'custom:custom18',
                c__tabLabel: 'Nuova Anagrafica',
            }
        }
        navService.navigate(pageReference);
    },
})