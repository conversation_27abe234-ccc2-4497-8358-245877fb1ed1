@isTest
private class BatchAccountAccountRelationAgency_Test {

    @testSetup
    static void setup() {
        Id rt = SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId();
        Id rtAAR = SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AgencySociety').getRecordTypeId();

        // Crea un gruppo corrispondente a ExternalId__c
        Group g1 = new Group(DeveloperName = 'EXT123', Name = 'Group EXT123', Type = 'Regular');
        Group g2 = new Group(DeveloperName = 'EXT999', Name = 'Group EXT999', Type = 'Regular');
        insert new List<Group>{g1, g2};

        // Account con condivisione già presente
        Account acc1 = new Account(Name = 'Acc 1', RecordTypeId = rt, ExternalId__c = 'EXT123');
        // Account con condivisione da creare
        Account acc3 = new Account(Name = 'Acc 3', RecordTypeId = rt, ExternalId__c = 'EXT999');
        insert new List<Account>{acc1, acc3};

        Account account3 = new Account(Name = 'Test Society 2', ExternalId__c = 'Test3', RecordTypeId = [SELECT Id FROM RecordType WHERE DeveloperName = 'IndustriesBusiness' AND SObjectType = 'Account' LIMIT 1].Id);
        insert account3;

        // Crea un record di ruolo (supponendo che l'oggetto sia FinServ__ReciprocalRole__c)
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(Name = 'Cliente', FinServ__InverseRole__c = 'Agenzia');
        insert role;

        // Crea una relazione esistente tra Agenzia e Compagnia
        FinServ__AccountAccountRelation__c existingRelation = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = account3.Id,
            FinServ__RelatedAccount__c = acc1.Id,
            FinServ__Role__c = role.Id,
            RecordTypeId = rtAAR
        );
        insert existingRelation;
    }

    @isTest
    static void noParamsTest() {
        Test.startTest();
        BatchAccountAccountRelationAgency batch = new BatchAccountAccountRelationAgency();
        Database.executeBatch(batch, 50);
        Test.stopTest();
    }

    @isTest
    static void withParamsTest() {
        Test.startTest();
        BatchAccountAccountRelationAgency batch = new BatchAccountAccountRelationAgency(null, '2025-05-25T08:52:55Z');
        Database.executeBatch(batch, 50);
        Test.stopTest();
    }
}