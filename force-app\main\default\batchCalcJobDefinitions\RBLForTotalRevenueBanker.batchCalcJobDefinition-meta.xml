<?xml version="1.0" encoding="UTF-8"?>
<BatchCalcJobDefinition xmlns="http://soap.sforce.com/2006/04/metadata">
    <aggregates>
        <fields>
            <aggregateFunction>Sum</aggregateFunction>
            <alias>AmountSum</alias>
            <sourceFieldName>FinServ_Amount_c</sourceFieldName>
        </fields>
        <groupBy>FinServ_Account_c</groupBy>
        <label>aggregate_FinServ_Amount_c</label>
        <name>aggregate_FinServ_Amount_c</name>
        <sourceName>FinServ_Revenue_c</sourceName>
    </aggregates>
    <datasources>
        <fields>
            <alias>Id</alias>
            <dataType>Text</dataType>
            <name>Id</name>
        </fields>
        <fields>
            <alias>Name</alias>
            <dataType>Text</dataType>
            <name>Name</name>
        </fields>
        <label>Account</label>
        <name>Account</name>
        <sourceName>Account</sourceName>
        <type>StandardObject</type>
    </datasources>
    <datasources>
        <fields>
            <alias>FinServ_Account_c</alias>
            <dataType>Text</dataType>
            <name>FinServ__Account__c</name>
        </fields>
        <fields>
            <alias>FinServ_Amount_c</alias>
            <dataType>Numeric</dataType>
            <name>FinServ__Amount__c</name>
        </fields>
        <label>FinServ_Revenue_c</label>
        <name>FinServ_Revenue_c</name>
        <sourceName>FinServ__Revenue__c</sourceName>
        <type>StandardObject</type>
    </datasources>
    <description>Rollup By Lookup Rule</description>
    <executionPlatformType>CRMA</executionPlatformType>
    <isTemplate>false</isTemplate>
    <joins>
        <fields>
            <alias>AmountSum</alias>
            <sourceFieldName>AmountSum</sourceFieldName>
            <sourceName>aggregate_FinServ_Amount_c</sourceName>
        </fields>
        <fields>
            <alias>Id</alias>
            <sourceFieldName>Id</sourceFieldName>
            <sourceName>Account</sourceName>
        </fields>
        <joinKeys>
            <primarySourceFieldName>Id</primarySourceFieldName>
            <secondarySourceFieldName>FinServ_Account_c</secondarySourceFieldName>
        </joinKeys>
        <label>Account_aggregate_FinServ_Amount_c</label>
        <name>Account_aggregate_FinServ_Amount_c</name>
        <primarySourceName>Account</primarySourceName>
        <secondarySourceName>aggregate_FinServ_Amount_c</secondarySourceName>
        <type>LeftOuter</type>
    </joins>
    <label>RBLForTotalRevenueBanker</label>
    <processType>DataProcessingEngine</processType>
    <status>Inactive</status>
    <writebacks>
        <fields>
            <runtimeParameter>false</runtimeParameter>
            <sourceFieldName>Id</sourceFieldName>
            <targetFieldName>Id</targetFieldName>
        </fields>
        <fields>
            <runtimeParameter>false</runtimeParameter>
            <sourceFieldName>AmountSum</sourceFieldName>
            <targetFieldName>FinServ__TotalRevenue__c</targetFieldName>
        </fields>
        <isChangedRow>false</isChangedRow>
        <label>RBLForTotalRevenueBankerExport</label>
        <name>RBLForTotalRevenueBankerExport</name>
        <operationType>Update</operationType>
        <sourceName>Account_aggregate_FinServ_Amount_c</sourceName>
        <storageType>sObject</storageType>
        <targetObjectName>Account</targetObjectName>
        <writebackSequence>1</writebackSequence>
        <writebackUser>0057Q00000A7swHQAR</writebackUser>
    </writebacks>
</BatchCalcJobDefinition>
