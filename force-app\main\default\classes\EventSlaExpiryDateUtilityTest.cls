@isTest
public class EventSlaExpiryDateUtilityTest {
	@TestSetup
    static void makeData(){
        Account account1 = new Account(
            Name = 'Test Agency 1',
            RecordTypeId = [
                SELECT Id
                FROM RecordType
                WHERE DeveloperName = 'Agency' AND SObjectType = 'Account'
                LIMIT 1
            ]
            .Id
        );
        insert account1;

        Account account2 = new Account(
            Name = 'Test Society 1',
            RecordTypeId = [
                SELECT Id
                FROM RecordType
                WHERE DeveloperName = 'Society' AND SObjectType = 'Account'
                LIMIT 1
            ]
            .Id,
            ExternalId__c = 'PippoGroup'
        );
        insert account2;
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(
            Name = 'Agenzia',
            FinServ__InverseRole__c = 'Compagnia'
        );
        insert role;
        
        FinServ__AccountAccountRelation__c existingRelation = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = account1.Id,
            FinServ__RelatedAccount__c = account2.Id,
            FinServ__Role__c = role.Id
        );
        insert existingRelation;
        Asset ast = new Asset(Name='ass1',AccountId=account1.Id,Price=100, MasterRecordId__c=existingRelation.id);
        insert ast;
    }
    @isTest
    static void test1() {
        List<string> idAccountAccountRelation = new List<string>();
        FinServ__AccountAccountRelation__c ar=[SELECT Id FROM FinServ__AccountAccountRelation__c limit 1];
        idAccountAccountRelation.add(ar.Id);
        EventSlaExpiryDateUtility.getTipoSoggetto(idAccountAccountRelation);
    }
}