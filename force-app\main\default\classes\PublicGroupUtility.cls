// PoC of an utility to handle public groups to manage visibility
public class PublicGroupUtility {

    // Parameters
    public static final String AGENCY_CODE_FIELD = 'ExternalId__c';
    public static final String AGENCY_TYPE_FIELD = 'Type';
    public static final String AGENCY_PREFIX = 'AG_';
    public static final String SUBAGENCY_PREFIX = 'SUBAG_';
    public static final String CIP_PREFIX = 'CIP_';
    
    /**
     * Get Agency accounts with needed fields
     */ 
    public static List<Account> getAgencies(String whereCond){
        String qTemplate = 'SELECT Id, {0}, ParentId, parent.{1}, {2} FROM Account WHERE RecordType.Name = \'Agency\' {3} {4}';
        List<Object> parameters = new List<Object> {AGENCY_CODE_FIELD, AGENCY_CODE_FIELD, AGENCY_TYPE_FIELD};
		// Add optional where condition input parameter
        parameters.add(String.isNotBlank(whereCond) ? ('AND ' + whereCond) : '');
        parameters.add('ORDER BY ParentId');
        
        // Build the query string
        String q = String.format(qTemplate, parameters);
		
        // Execute the query
        return (List<Account>)Database.query(q);
    }
    
    
    /**
     * Create public groups for Agency accounts, if not existing. Nest parent agencies into sub-agency, to model visibility
     * (sharing with sub-agency group will enable visibility on parent agency public group members)
     **/ 
    public static void createAgencyGroups(List<Account> agencyList){
        If(agencyList == null) return;
        
        // Get all configured groups
        List<Group> grpList = [
            SELECT Id, Name, DeveloperName, Type
        	FROM Group
        	WHERE Type = 'Regular'];
        
        // Build the Map of configured groups by developer name
        Map<String, Group> grpByDevNameMap = new Map<String, Group>();
        for(Group grp : grpList){
            grpByDevNameMap.put(grp.DeveloperName, grp);
        }
        
        List<Group> grpToAddList = new List<Group>();
        Map<String, String> parentGrpNameMap = new Map<String, String>();
        
        // For each agency
        for(Account a : agencyList){
            Group existingGrp = null;
            
            // Get parent agency name (or its expected name, if a parent)
            String parentAgencyGroupName = buildParentAgencyGroupName(a);
            
            if(String.isNotBlank(parentAgencyGroupName)){
                // Get existing group having same name             
                existingGrp = grpByDevNameMap.get(parentAgencyGroupName);
                
                // Group needs to be created
                if(existingGrp == null){
                    Group newParGrp = createGroupInstance(parentAgencyGroupName, false);
                    grpToAddList.add(newParGrp);
                    grpByDevNameMap.put(parentAgencyGroupName, newParGrp);
                }
            }
                        
            // The current agency is a children
            if(a.ParentId != null){
                // Get child agency name
                String childAgencyGroupName = buildAgencyGroupName(a);
                
                if(String.isBlank(childAgencyGroupName)) continue;
                
                // Get existing child group having same name   
                existingGrp = grpByDevNameMap.get(childAgencyGroupName);
                if(existingGrp == null){
                    Group newChldGrp = createGroupInstance(childAgencyGroupName, false);
                    grpToAddList.add(newChldGrp);
                    grpByDevNameMap.put(childAgencyGroupName, newChldGrp);
                    parentGrpNameMap.put(childAgencyGroupName, parentAgencyGroupName);
                }
            }
        }
        
        // Insert new public groups
        if(grpToAddList.size() > 0){
            insert grpToAddList;
        }
        
        // Nest agency group into sub-agency ones
        List<GroupMember> grpMemberToAddList = new List<GroupMember>();
        for(String chldGrpName : parentGrpNameMap.keySet()){
            Group parGroup = grpByDevNameMap.get(parentGrpNameMap.get(chldGrpName));
            Group childGroup = grpByDevNameMap.get(chldGrpName);
            if(parGroup != null && childGroup != null && parGroup.Id != null && childGroup.Id != null){
                grpMemberToAddList.add(createGroupMemberInstance(childGroup.Id, parGroup.Id));
            }
        }
        if(grpMemberToAddList.size() > 0){
            insert grpMemberToAddList;
        }
    }
    
    
    /**
     * Get the developer name of the public group for the input Agency
     **/
    public static String buildAgencyGroupName(Account agency){
        if(agency == null) return '';
        
        // Account is a parent agency
        if(agency.ParentId == null){
        	return buildParentAgencyGroupName(agency);    
        }
        
        // Sub-Agency
        String grpDevNameTemplate = '{0}{1}{2}{3}{4}';
        List<Object> parList = new List<Object>{SUBAGENCY_PREFIX};
       
        String agencyCode = (String)agency.get(AGENCY_CODE_FIELD);
        
        if(String.isBlank(agencyCode)) return ''; // No identifier
        
        if(agencyCode.length() + SUBAGENCY_PREFIX.length() > 40){
            agencyCode = agencyCode.right(40-SUBAGENCY_PREFIX.length());
        }
       
        parList.add(agencyCode);
        
        // There is space for parent agency info as well
        Integer usedLength = SUBAGENCY_PREFIX.length() + agencyCode.length() + 1 + AGENCY_PREFIX.length();
        if(usedLength < 40){
            String parAgencyCode = (String)agency.getSobject('Parent').get(AGENCY_CODE_FIELD);
            if(parAgencyCode.length() + usedLength > 40){
                parAgencyCode.right(40 - usedLength);
            }
            parList.add('_');  // The +1 of usedLength
            parList.add(AGENCY_PREFIX);
            parList.add(parAgencyCode);
        }
        else{
            parList.add('');
            parList.add('');
        }
       
        return String.format(grpDevNameTemplate, parList);
    }
    
    
    /**
     *  Get the developer name of the public group for the parent agency (can be called on both parent or child agency)
     **/
    public static String buildParentAgencyGroupName(Account agency){
        if(agency == null) return '';
        String grpDevNameTemplate = '{0}{1}';
        List<Object> parList = new List<Object>{AGENCY_PREFIX};
        // If a parent, use its code, otherwise the one of the parent agency
        String parAgencyCode = (agency.ParentId == null) ? (String)agency.get(AGENCY_CODE_FIELD) : (String)agency.getSobject('Parent').get(AGENCY_CODE_FIELD);
        
        if(String.isBlank(parAgencyCode)) return ''; // No identifier
        
        // Group names limited to 40 chars
        if((parAgencyCode.length() + AGENCY_PREFIX.length()) > 40){
            parAgencyCode = parAgencyCode.right(40-AGENCY_PREFIX.length());
        }
        parList.add(parAgencyCode);
        return String.format(grpDevNameTemplate, parList);
    }
    
    
    /**
     * Build the CIP group name for an Agency or a sub-agency
     **/
    public static String buildAgencyCipGroupName(Account agency, String cipIdentifier){
        if(agency == null || String.isBlank(cipIdentifier)) return '';
        
        // Get agency code
        String agencyCode = (String)agency.get(AGENCY_CODE_FIELD);
        
        if(String.isBlank(agencyCode)) return '';
        
        // E.g. [AG_]/[SUBAG_] + Agency Identifier + '_' + CIP_ + CIP Identifier
        String grpDevNameTemplate = '{3}{4}{0}{1}{2}';
        List<Object> parList = new List<Object>{'_', CIP_PREFIX, cipIdentifier};
        
        // Get agency or sub-agency prefix
        String agPrefix = (agency.ParentId == null) ? AGENCY_PREFIX : SUBAGENCY_PREFIX;
        parList.add(agPrefix);
        
        // Get the num of chars burned by other info
        Integer usedLength = agPrefix.length() + CIP_PREFIX.length() + cipIdentifier.length() + 1;
        
        // Group names limited to 40 chars
        if((usedLength + agencyCode.length()) > 40){
            agencyCode = agencyCode.right(40 - usedLength);
        }
        parList.add(agencyCode);
        
        return String.format(grpDevNameTemplate, parList);
    }
    
    
    /**
     * Create Group instance
     **/
    private static Group createGroupInstance(String name, Boolean hierarchyShare){
        Group g = new Group();
        g.Name = name;
        g.DeveloperName = name;
        g.DoesIncludeBosses = hierarchyShare;
        g.DoesSendEmailToMembers = false;
        g.Type = 'Regular';
        return g;
    }
    
    /**
     * Create Group Member instance
     **/
    private static GroupMember createGroupMemberInstance(Id groupId, Id memberId){
        GroupMember gm = new GroupMember();
        gm.GroupId = groupId;
        gm.UserOrGroupId = memberId;
        return gm;
    }
}
