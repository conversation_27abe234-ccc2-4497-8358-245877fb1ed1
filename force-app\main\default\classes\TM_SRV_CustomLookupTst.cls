/**
 * @description       : TM_SRV_CustomLookupTst.cls
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 03-02-2025
 * @last modified by  : <EMAIL>
**/
@IsTest
public with sharing class TM_SRV_CustomLookupTst {
    private static TM_UTL_DataFactoryTst dataFactoryUtl = TM_UTL_DataFactoryTst.getInstance();
    //public static RPU_UTL_Constants constantsUtl = RPU_UTL_Constants.getInstance();
    public static TM_SRV_CustomLookup customLookupSrv = TM_SRV_CustomLookup.getInstance();

    @TestSetup
    private static void setup(){

        List<Account> clienti = dataFactoryUtl.createClienti(1, true);
    }
    
    @IsTest
    static void testFetchLookupValues() {
        List<Account> accounts = new List<Account>();            
        
        Test.startTest();

        customLookupSrv.fetchLookupValues(
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                'AddressApexAction',
                'GetComuni',
                '{"codiceBelfiore": "","descrizione": "", "limiteOccorrenze": "99", "siglaProvincia": "RM", "soloAttivi": "true"}');
        

        Map<String, Object> returnObj = customLookupSrv.fetchLookupValues(
                'C',
                'Account',
                'Name',
                'Id != null',
                'Name',
                '',
                '',
                '',
                '',
                '',
                'RPU_SRV_Account',
                'getClientsByCodiceVenditore',
                '');

        accounts = (List<Account>)returnObj.get('returnList'); 

        System.assert(accounts == null, 'Assert TestFetchLookupValues');

        
        Test.stopTest();     

    }

    @IsTest
    static void testInitialize() {

        Account account = new Account();
        Account acc = [SELECT Id, Name FROM Account LIMIT 1];

        Test.startTest();
        Map<String, Object> returnObj = customLookupSrv.initialize(
                'Account',
                'Name',
                'Id != null',
                'Name',
                '',
                '',
                '',
                '', acc.Id,
                'RPU_SRV_Account',
                'getClientsByCodiceVenditore',
                '');
        account = (Account)returnObj.get('initialRecord');

        System.assert(account == null, 'Assert TestInitialize');

        Test.stopTest();     

    }
}