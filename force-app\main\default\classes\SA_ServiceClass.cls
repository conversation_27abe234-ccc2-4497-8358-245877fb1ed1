global with sharing class SA_ServiceClass implements omnistudio.VlocityOpenInterface{
   
    global Boolean invokeMethod(String methodName, Map<String,Object> inputMap, Map<String,Object> outMap, Map<String,Object> options){
        
        if(methodName == 'getRelatedServiceResources'){
            getRelatedServiceResources ('getRelatedServiceResources', inputMap, outMap, options);
        }
       
        if(methodName == 'getRelatedServiceTerritories'){
            getRelatedServiceTerritories ('getRelatedServiceTerritories', inputMap, outMap, options);
        }
        return true;
    }
    
   
    private void getRelatedServiceResources(String methodName, Map<String,Object> inputMap, Map<String,Object> outMap, Map<String,Object> options) {
         Map<String,Object> errorResponse = New Map<String,Object>();
        Id currentUserId = UserInfo.getUserId();


        List<ServiceResource> currentResources = [ 
            SELECT Id 
            FROM ServiceResource 
            WHERE RelatedRecordId =: currentUserId AND IsActive = true
            LIMIT 1 ];

        if (currentResources.isEmpty()) {
            errorResponse.put('Error', True);
            errorResponse.put('Message', '0001');
            outMap.put('serviceResources', errorResponse);
        }

        List<ServiceTerritoryMember> currentTerritoryMembers = [
            SELECT ServiceTerritoryId
            FROM ServiceTerritoryMember
            WHERE IsActive__c = true AND ServiceResourceId =: currentResources[0].Id 
        ];

        Set<Id> territoryIds = new Set<Id>();
        for (ServiceTerritoryMember stm : currentTerritoryMembers) {
            territoryIds.add(stm.ServiceTerritoryId);
        }

        if (territoryIds.isEmpty()) {
                errorResponse.put('Error', True);
                errorResponse.put('Message', '0002');
                outMap.put('serviceResources', errorResponse);
        }

        List<ServiceTerritoryMember> sharedMembers = [
            SELECT ServiceResourceId
            FROM ServiceTerritoryMember
            WHERE IsActive__c = true AND (ServiceTerritoryId IN :territoryIds OR ServiceTerritory.Punto_vendita_principale__c IN: territoryIds)
        ];


        Set<Id> relatedResourceIds = new Set<Id>();
        for (ServiceTerritoryMember member : sharedMembers) {
            relatedResourceIds.add(member.ServiceResourceId);
        }

         // Step 5: Ritorna la lista di ServiceResource con i campi richiesti
         List<ServiceResource> relatedResources = [
            SELECT Id, Name, Attiva_su_canali_digitali__c, Attiva_per_clienti_e_prospect__c, TipologiaAppuntGest__c
            FROM ServiceResource
            WHERE IsActive = true AND Id IN :relatedResourceIds
        ];

        // Crea una mappa per restituire i dati
        List<Map<String, Object>> serviceResourcesList = new List<Map<String, Object>>();
        for (ServiceResource sr : relatedResources) {

            Map<String, Object> resourceData = new Map<String, Object>();
            resourceData.put('Id', sr.Id);
            resourceData.put('Name', sr.Name);
            resourceData.put('Attiva_su_canali_digitali__c', sr.Attiva_su_canali_digitali__c);
            resourceData.put('Attiva_per_clienti_e_prospect__c', sr.Attiva_per_clienti_e_prospect__c);
            resourceData.put('TipologiaAppuntGest__c', sr.TipologiaAppuntGest__c);
            resourceData.put('Error', False);
            serviceResourcesList.add(resourceData);
        }
        
        if(!serviceResourcesList.isEmpty()){
          outMap.put('serviceResources', serviceResourcesList);
        }
    }

    private void getRelatedServiceTerritories(String methodName, Map<String,Object> inputMap, Map<String,Object> outMap, Map<String,Object> options) {
        
        Id currentUserId = UserInfo.getUserId();
        Map<String,Object> errorResponse = New Map<String,Object>();

        List<ServiceResource> currentResources = [ 
            SELECT Id 
            FROM ServiceResource 
            WHERE RelatedRecordId =: currentUserId AND IsActive = true
            LIMIT 1 ];

        if (currentResources.isEmpty()) {
            errorResponse.put('Error', True);
            errorResponse.put('Message', '0001');
            outMap.put('serviceTerritories', errorResponse);
        }

        List<ServiceTerritoryMember> currentTerritoryMembers = [
            SELECT ServiceTerritoryId
            FROM ServiceTerritoryMember
            WHERE IsActive__c = true AND ServiceResourceId =: currentResources[0].Id
        ];

        Set<Id> territoryIds = new Set<Id>();
        for (ServiceTerritoryMember stm : currentTerritoryMembers) {
            territoryIds.add(stm.ServiceTerritoryId);
        }

        
        if (territoryIds.isEmpty()) {
                errorResponse.put('Error', True);
                errorResponse.put('Message', '0002');
                outMap.put('serviceTerritories', errorResponse);
        }

        List<ServiceTerritory> sharedTeritory = [
            SELECT id, name, Address__c ,Tipo__c from ServiceTerritory WHERE IsActive = true AND (id IN :territoryIds OR Punto_vendita_principale__c IN :territoryIds)
        ];

        // Crea una mappa per restituire i dati
        List<Map<String, Object>> serviceTerritoriesList = new List<Map<String, Object>>();
        for (ServiceTerritory st : sharedTeritory) {

            Map<String, Object> resourceData = new Map<String, Object>();
            resourceData.put('Id', st.Id);
            resourceData.put('Name', st.Name);
            resourceData.put('Address__c', st.Address__c);
            resourceData.put('Tipo__c', st.Tipo__c);
            resourceData.put('Error', False);
            serviceTerritoriesList.add(resourceData);
        }
        
        if(!serviceTerritoriesList.isEmpty()){
           outMap.put('serviceTerritories', serviceTerritoriesList);
        }
      
    }
    
}