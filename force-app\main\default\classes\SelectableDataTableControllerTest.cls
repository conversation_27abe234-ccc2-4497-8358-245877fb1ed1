@isTest
private class SelectableDataTableControllerTest {

    @testSetup
    static void setupTestData() {
        // Crea dati di test
        List<Account> accounts = new List<Account>();
        for (Integer i = 0; i < 5; i++) {
            accounts.add(new Account(Name = 'Test Account ' + i));
        }
        insert accounts;
    }

    @isTest
    static void testRetrieveData_ValidQuery() {
        String query = 'SELECT Id, Name FROM Account WHERE Name LIKE \'Test Account%\'';

        Test.startTest();
        List<SObject> results = SelectableDataTableController.retrieveData(query);
        Test.stopTest();

        System.assertNotEquals(null, results, 'Il risultato non dovrebbe essere null');
        System.assertEquals(5, results.size(), 'Dovrebbero essere restituiti 5 account');
    }

    @isTest
    static void testRetrieveData_InvalidQuery() {
        String invalidQuery = 'SELECT NonExistentField FROM Account';

        Test.startTest();
        List<SObject> results = SelectableDataTableController.retrieveData(invalidQuery);
        Test.stopTest();

        System.assertEquals(null, results, 'Il risultato dovrebbe essere null per una query non valida');
    }
}