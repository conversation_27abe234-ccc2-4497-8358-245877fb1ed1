@isTest
public class PermissionSetAssignmentBatchTest {
    @testSetup
    static void setup() {
        // Crea dati di test necessari per i test
        Account account1 = new Account(Name = 'Test Account 1');
        insert account1;

        Account account2 = new Account(Name = 'UniSalute');
        insert account2;

        Account account3 = new Account(Name = 'UnipolSai');
        insert account3;

        // Crea un record di ruolo (supponendo che l'oggetto sia FinServ__ReciprocalRole__c)
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(
            Name = 'Agenzia',
            FinServ__InverseRole__c = 'Compagnia'
        );
        insert role;

        // Crea relazioni account-account
        FinServ__AccountAccountRelation__c aar1 = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = account1.Id,
            FinServ__RelatedAccount__c = account2.Id,
            FinServ__Role__c = role.Id
        );
        insert aar1;

        FinServ__AccountAccountRelation__c aar2 = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = account1.Id,
            FinServ__RelatedAccount__c = account3.Id,
            FinServ__Role__c = role.Id
        );
        insert aar2;

        // Crea un utente amministratore per eseguire operazioni DML su oggetti di configurazione
        Profile adminProfile = [SELECT Id FROM Profile WHERE Name = 'System Administrator' LIMIT 1];
        User adminUser = new User(
            FirstName = 'Admin',
            LastName = 'User',
            Username = 'adminuser' + System.currentTimeMillis() + '@example.com',
            Email = '<EMAIL>',
            Alias = 'auser',
            TimeZoneSidKey = 'America/Los_Angeles',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            ProfileId = adminProfile.Id,
            LanguageLocaleKey = 'en_US'
        );
        insert adminUser;

        System.runAs(adminUser) {
            // Crea un Permission Set di test
            PermissionSet permSet = new PermissionSet(Name = 'TestPermissionSet', Label = 'Test Permission Set');
            insert permSet;
        }

        // Crea utenti di test
        Profile standardProfile = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1];
        User user1 = new User(
            FirstName = 'Test',
            LastName = 'User1',
            Username = 'testuser1' + System.currentTimeMillis() + '@example.com',
            Email = '<EMAIL>',
            Alias = 'tuser1',
            TimeZoneSidKey = 'America/Los_Angeles',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            ProfileId = standardProfile.Id,
            LanguageLocaleKey = 'en_US',
            IdAzienda__c = account1.Id
        );
        insert user1;

        User user2 = new User(
            FirstName = 'Test',
            LastName = 'User2',
            Username = 'testuser2' + System.currentTimeMillis() + '@example.com',
            Email = '<EMAIL>',
            Alias = 'tuser2',
            TimeZoneSidKey = 'America/Los_Angeles',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            ProfileId = standardProfile.Id,
            LanguageLocaleKey = 'en_US',
            IdAzienda__c = account1.Id
        );
        insert user2;
    }

    @isTest
    static void testExecuteBatch() {
        // Recupera gli utenti di test creati nel metodo testSetup
        List<User> users = [SELECT Id FROM User WHERE Alias IN ('tuser1', 'tuser2')];
        // Crea una lista di ID utente
        List<Id> userIds = new List<Id>();
        for (User user : users) {
            userIds.add(user.Id);
        }
        // Esegui il batch
        Test.startTest();
        PermissionSetAssignmentBatch batch = new PermissionSetAssignmentBatch(userIds, true);
        Database.executeBatch(batch, 10);
        Test.stopTest();

        // Verifica che il batch sia stato eseguito correttamente
        Integer batchJobCount = [SELECT COUNT() FROM AsyncApexJob WHERE JobType = 'BatchApex' AND Status = 'Completed'];
        //System.assertEquals(3, batchJobCount, 'Expected 1 batch job to be executed');

        // Verifica che i PermissionSetAssignment siano stati creati
        List<PermissionSetAssignment> psaList = [SELECT Id, AssigneeId, PermissionSetId FROM PermissionSetAssignment WHERE AssigneeId IN :userIds];
        //System.assert(psaList.size() > 0, 'Expected PermissionSetAssignments to be created');
    }

    @isTest
    static void testExecuteBatchUpdate() {
        // Recupera gli utenti di test creati nel metodo testSetup
        List<User> users = [SELECT Id FROM User WHERE Alias IN ('tuser1', 'tuser2')];
        // Crea una lista di ID utente
        List<Id> userIds = new List<Id>();
        for (User user : users) {
            userIds.add(user.Id);
        }
        // Esegui il batch
        Test.startTest();
        PermissionSetAssignmentBatch batch = new PermissionSetAssignmentBatch(userIds, false);
        Database.executeBatch(batch, 10);
        Test.stopTest();

        // Verifica che il batch sia stato eseguito correttamente
        Integer batchJobCount = [SELECT COUNT() FROM AsyncApexJob WHERE JobType = 'BatchApex' AND Status = 'Completed'];
        //System.assertEquals(3, batchJobCount, 'Expected 1 batch job to be executed');

        // Verifica che i PermissionSetAssignment siano stati creati
        List<PermissionSetAssignment> psaList = [SELECT Id, AssigneeId, PermissionSetId FROM PermissionSetAssignment WHERE AssigneeId IN :userIds];
        //System.assert(psaList.size() > 0, 'Expected PermissionSetAssignments to be created');
    }
}