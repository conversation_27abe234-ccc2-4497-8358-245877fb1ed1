@isTest
private class TimelineController_Test {

    @isTest
    static void testGetRecentActivities() {
        // Esegue il metodo statico della classe da testare
        Test.startTest();
        List<TimelineController.ActivityWrapper> result = TimelineController.getRecentActivities();
        Test.stopTest();

        // Verifica che il risultato non sia nullo e contenga attività
        System.assertNotEquals(null, result, 'Il risultato non dovrebbe essere null');
        System.assert(result.size() > 0, 'Dovrebbero essere restituite almeno alcune attività');

        // Verifica che i campi principali siano valorizzati correttamente
        for (TimelineController.ActivityWrapper activity : result) {
            System.assertNotEquals(null, activity.id, 'L\'ID non dovrebbe essere null');
            System.assertNotEquals(null, activity.type, 'Il tipo non dovrebbe essere null');
            System.assertNotEquals(null, activity.company, 'La società non dovrebbe essere null');
            System.assertNotEquals(null, activity.detail, 'Il dettaglio non dovrebbe essere null');
            System.assertNotEquals(null, activity.eventDate, 'La data non dovrebbe essere null');
        }
    }
}