/**
 * Test class: GroupAssignPublicGroupBatch_Test
 */
global class GroupAssignPublicGroupBatch implements Database.Batchable<SObject>, Database.Stateful {
    
    //private String recordType;
    private Set<Id> idsSet;

    private Id idValueFrom;
    private Id idValueTo;
    
    private String dateType;
    private DateTime dateFrom;
    private DateTime dateTo;

    private Boolean concatenate = false;

    /*global GroupAssignPublicGroupBatch(String recordType) {
        this(recordType);
    }*/

    global GroupAssignPublicGroupBatch() {
        this(false);
    }

    global GroupAssignPublicGroupBatch(Set<Id> idsSet) {
        this(idsSet, false);
    }

    global GroupAssignPublicGroupBatch(Id idValueFrom, Id idValueTo) {
        this(idValueFrom, idValueTo, false);
    }

    global GroupAssignPublicGroupBatch(DateTime dateFrom, DateTime dateTo, String dateType) {
        this(dateFrom, dateTo, dateType, false);
    }

    /*global GroupAssignPublicGroupBatch(String recordType, Boolean useLike) {
        this.recordType = recordType;
        this.useLike = useLike;
    }*/

    global GroupAssignPublicGroupBatch(Boolean concatenate) {
        this.concatenate = concatenate;
    }

    global GroupAssignPublicGroupBatch(Set<Id> idsSet, Boolean concatenate) {
        this.idsSet = idsSet;
        this.concatenate = concatenate;
    }

    global GroupAssignPublicGroupBatch(Id idValueFrom, Id idValueTo, Boolean concatenate) {
        this.idValueFrom = idValueFrom;
        this.idValueTo = idValueTo;
        this.concatenate = concatenate;
    }

    global GroupAssignPublicGroupBatch(DateTime dateFrom, DateTime dateTo, String dateType, Boolean concatenate) {
        this.dateFrom = dateFrom;
        this.dateTo = dateTo;
        this.dateType = dateType;
        this.concatenate = concatenate;
    }

    public class BatchException extends Exception {}

    global Database.QueryLocator start(Database.BatchableContext BC) {
        
        String query = 'SELECT Id, Name, PublicGroupId__c FROM Group__c';
        
        if(idsSet != null) {
            query += ' WHERE Id IN :idsSet';
        }

        if(idValueFrom != null && idValueTo != null) {
            query += ' WHERE Id >= :idValueFrom AND Id <= :idValueTo';
        }

        if(dateFrom != null && dateTo != null && dateType != null) {
            if(dateType == 'LastModifiedDate') {

                query += ' WHERE LastModifiedDate >= :dateFrom AND LastModifiedDate <= :dateTo';
            } else if(dateType == 'CreatedDate') {

                query += ' WHERE CreatedDate >= :dateFrom AND CreatedDate <= :dateTo';
            } else {
                throw new BatchException('Invalid dataType');
            }
        }
        
        return Database.getQueryLocator(query);
    }

    global void execute(Database.BatchableContext BC, List<Group__c> scope) {
        
        //List<Group__c> groupList = (Group__c) scope;
        Map<String, Id> groupNameIdMap = new Map<String, Id>();
        for(Group__c grp : scope) {
            groupNameIdMap.put(grp.Name, grp.Id);
        }

        Map<String, Id> publicGroupMap = new  Map<String, Id>();
        for(Group grp : [SELECT Id, Name FROM Group WHERE Name IN :groupNameIdMap.keySet()]) {
            publicGroupMap.put(grp.Name, grp.Id);
        }

        List<Group__c> groupListToUpdate = new List<Group__c>();

        for(String grpName : groupNameIdMap.keySet()) {
            
            if(!publicGroupMap.containsKey(grpName)) continue;

            groupListToUpdate.add(new Group__c(
                Id = groupNameIdMap.get(grpName),
                PublicGroupId__c = publicGroupMap.get(grpName)
            ));
        }

        if(!groupListToUpdate.isEmpty()) {
            Database.update(groupListToUpdate, false);
        }
    }

    global void finish(Database.BatchableContext BC) {
        System.debug('Batch Process Finished.');

        if(concatenate) {
            Database.executeBatch(new BatchGroupAssignment(concatenate), 200);
        }
    }
}