@isTest(SeeAllData=true)
private class OpportunityAgencyReassignmentBatchTest {

    @isTest
    static void testOpportunityAgencyReassignmentBatch() {
        // Create Account
        Account acc = new Account(
            Name = 'Test Account OpportunityAgencyReassignmentBatch',
            BillingPostalCode = '26100',
            ExternalId__c = 'TESTBATCH_01853',
            BillingLatitude = 45.0703,
            BillingLongitude = 7.6869,
            RecordTypeId = SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId(),
            IsRolledOut__c = true
        );
        insert acc;

        Account acc2 = new Account(
            Name = 'IRMA',
            BillingPostalCode = '26100',
            BillingLatitude = 45.0705,
            BillingLongitude = 7.6870,
            ExternalId__c = 'AG-SOGEINT',
            RecordTypeId = SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId()
        );
        insert acc2;

        Account personAccount = new Account(FirstName = 'OpportunityAgencyReassignmentBatchTest', LastName = 'Cli', BillingLatitude = 45.0703, BillingLongitude = 7.6869, ExternalId__c = '12345', BillingPostalCode = '10100', RecordTypeId = SObjectType.Account.getRecordTypeInfosByDeveloperName().get('PersonAccount').getRecordTypeId(),OmnichannelAgreement__c = true);
        insert personAccount;

        OperatingHours testOperatingHours = new OperatingHours(Name = 'Test OpportunityAgencyReassignmentBatch');
        insert testOperatingHours;

        OperatingHours testOperatingHours2 = new OperatingHours(Name = 'Testing OpportunityAgencyReassignmentBatch');
        insert testOperatingHours2;

        ServiceTerritory testTerritory = new ServiceTerritory(
            Name = 'Test SalesPoint OpportunityAgencyReassignmentBatch',
            Tipo__c = 'Agenziale',
            OperatingHoursId = testOperatingHours.Id,
            Agency__c = acc.Id,
            Street = 'Test Street',
            City = 'Test City',
            State = 'MI',
            PostalCode = '26100',
            Country = 'Italy',
            Latitude = 45.0703,
            Longitude = 7.6869
        );
        insert testTerritory;

        ServiceTerritory testTerritory2 = new ServiceTerritory(
            Name = 'Test SalesPoint 2 OpportunityAgencyReassignmentBatch',
            Tipo__c = 'Agenziale',
            OperatingHoursId = testOperatingHours2.Id,
            Agency__c = acc2.Id,
            Street = 'Test Street',
            City = 'Test City',
            State = 'MI',
            PostalCode = '26100',
            Country = 'Italy',
            Latitude = 45.0703,
            Longitude = 7.6869
        );
        insert testTerritory2;
        
        // Create Opportunity
        Opportunity opp = new Opportunity(
            Name = 'Test Opportunity OpportunityAgencyReassignmentBatch',
            StageName = 'Assegnato',
            CloseDate = Date.today().addDays(30),
            RecordTypeId = SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Omnicanale').getRecordTypeId(),
            TakenInChargeSLAExpiryDate__c = Date.today().addDays(-1),
            WorkingSLAExpiryDate__c = Date.today().addDays(+1),
            Agency__c = acc.Id,
            Channel__c = 'Preventivatore digitale Unica',
            AreaOfNeed__c = 'Casa',
            AccountId = personAccount.Id,
            AssignmentCounter__c = 1,
            Rating__c = 'Calda'
           
        );
        insert opp;

        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(Name = 'Cliente', FinServ__InverseRole__c = 'Agenzia');
        insert role;

        FinServ__AccountAccountRelation__c existingRelation = new FinServ__AccountAccountRelation__c(
           FinServ__Account__c = acc2.Id,
           FinServ__RelatedAccount__c = acc.Id,
           FinServ__Role__c = role.Id
       );

        List<CalculationMatrixRow> calculationMatrixRows = [SELECT Id, InputData, OutputData FROM CalculationMatrixRow WHERE CalculationMatrixVersion.CalculationMatrix.UniqueName = 'Parametri_Motore'];

        EngineAgencyRanking.testingMatrixRows = new List<CalculationMatrixRow>{
            new CalculationMatrixRow(
                InputData = '{"Cap": "26100"}',
                OutputData = '{"Longitude":7.6869,"Latitude":"45.0703"}'
            )
        };

        Test.startTest();
        OpportunityAgencyReassignmentBatch batch = new OpportunityAgencyReassignmentBatch();
        Database.executeBatch(batch);
        Test.stopTest();

        // Assert updates
        Opportunity updatedOpp = [SELECT Id, TakenInChargeSLAExpiryDate__c, Agency__c FROM Opportunity WHERE Id = :opp.Id];
        //System.assertNotEquals(null, updatedOpp.TakenInChargeSLAExpiryDate__c, 'SLA Date should be updated');
    }
}