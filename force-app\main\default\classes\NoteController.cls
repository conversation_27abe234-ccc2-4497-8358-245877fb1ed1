public without sharing class Note<PERSON><PERSON>roller {

    @AuraEnabled
    public static void deleteNoteCase(String recordId) {
        try {
            ContentNote noteToDelete = [SELECT Id FROM ContentNote WHERE Id = :recordId LIMIT 1];
            if(noteToDelete!= null){
                delete noteToDelete;
            }
        } catch (Exception e) {
            throw new AuraHandledException('Errore durante la cancellazione della nota: ' + e.getMessage());
        }
    }
    
    @AuraEnabled
    public static void deleteNote(String recordId) {
        try {
            Note noteToDelete = [SELECT Id FROM Note WHERE Id = :recordId LIMIT 1];
            if(noteToDelete!= null){
                delete noteToDelete;
            }
        } catch (Exception e) {
            throw new AuraHandledException('Errore durante la cancellazione della nota: ' + e.getMessage());
        }
    }

    @AuraEnabled
    public static void updateCaseNote(String noteId, String title, String body) {
        try {
            ContentNote noteToUpdate = [SELECT Id, Title, Content  FROM ContentNote WHERE Id = :noteId LIMIT 1];
            if (noteToUpdate != null) {
                noteToUpdate.Title = title;
                noteToUpdate.Content = Blob.valueOf(body);
                update noteToUpdate;
            }
        } catch (Exception e) {
            throw new AuraHandledException('Errore durante l\'aggiornamento della nota: ' + e.getMessage());
        }
    }
    

    @AuraEnabled
    public static void updateNote(String noteId, String title, String body) {
        try {
            Note noteToUpdate = [SELECT Id, Title, Body FROM Note WHERE Id = :noteId LIMIT 1];
            if (noteToUpdate != null) {
                noteToUpdate.Title = title;
                noteToUpdate.Body = body;
                update noteToUpdate;
            }
        } catch (Exception e) {
            throw new AuraHandledException('Errore durante l\'aggiornamento della nota: ' + e.getMessage());
        }
    }


        @AuraEnabled
        public static String getNotesJson(String recordId) {
            List<ContentDocumentLink> contentDocumentLinks = [SELECT Id, LinkedEntityId, ContentDocumentId FROM ContentDocumentLink WHERE LinkedEntityId = :recordId];
    
            if (!contentDocumentLinks.isEmpty()) {
                Set<Id> contentDocumentIds = new Set<Id>();
                for (ContentDocumentLink link : contentDocumentLinks) {
                    contentDocumentIds.add(link.ContentDocumentId);
                }
    
                List<ContentDocument> contentDocuments = [SELECT Id, CreatedById, CreatedBy.Name, CreatedDate, LastModifiedById, LastModifiedBy.Name, LastModifiedDate, Title, LatestPublishedVersionId, 
                LatestPublishedVersion.TextPreview FROM ContentDocument WHERE Id IN :contentDocumentIds order by CreatedDate DESC];
    
                if (!contentDocuments.isEmpty()) {
                    List<Map<String, Object>> notes = new List<Map<String, Object>>();
                    for (ContentDocument doc : contentDocuments) {
                        Map<String, Object> note = new Map<String, Object>();
                        note.put('IdCreated', doc.CreatedById);
                        note.put('Body', doc.LatestPublishedVersion.TextPreview);
                        note.put('Id', doc.Id);
                        note.put('Title', doc.Title);
                        note.put('IdModified', doc.LastModifiedById);
                        note.put('CreatedBy', doc.CreatedBy.Name);
                        note.put('LastModifiedBy', doc.LastModifiedBy.Name);
                        note.put('CreatedDate', String.valueOf(doc.CreatedDate));
                        note.put('LastModifiedDate', String.valueOf(doc.LastModifiedDate));
                        notes.add(note);
                    }
                    return JSON.serialize(notes);
                } else {
                    return JSON.serialize(new List<Map<String, Object>>());
                }
            } else {
                return JSON.serialize(new List<Map<String, Object>>());
            }
        }

        @AuraEnabled(cacheable=true)
        public static Boolean hasPermissionSet() {
            return [SELECT Id FROM PermissionSetAssignment WHERE AssigneeId = :UserInfo.getUserId() AND PermissionSet.Name = 'OperativitaAttivitaNonAssegnate' LIMIT 1].size() > 0;
        }
    }