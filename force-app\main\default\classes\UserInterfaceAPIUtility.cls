/***************************************************************************************************************************************************
* <AUTHOR>
* @description    UserInterfaceAPIUtility class provides utility methods for interacting with the Salesforce UI API to retrieve picklist values 
*                 for specific SObject types and record types. This class handles callouts to the Salesforce API, processes responses, and extracts 
*                 relevant picklist values.
* @date           2024-02-06
****************************************************************************************************************************************************/
public with sharing class UserInterfaceAPIUtility {

    // Custom exception class for handling exceptions related to UserInterfaceAPIUtility operations.
    public class UserInterfaceAPIUtilityException extends Exception {}

    // Holds the Salesforce domain URL. This is initialized with the organization's domain URL.
    private static String SFDC_URL = URL.getOrgDomainURL().toExternalForm();
    
    /******************************************************************************************
    * @description  Retrieves picklist values for a specified SObject type and record type ID by 
    *               making a callout to the Salesforce UI API.
    * @param        sObjectType - The API name of the SObject type.
    * @param        recordTypeId - The ID of the record type for which to retrieve picklist values.
    * @return       Map<String, Object> - A map containing the picklist values for the specified SObject type and record type.
    * @throws       CalloutException - If an error occurs during the API call.
    *******************************************************************************************/
    public static Map<String, Object> getSObjectPicklistValuesByRecordType(String sObjectType, String recordTypeId) {
        String picklistValuesURL = SFDC_URL + '/services/data/v58.0/ui-api/object-info/' + sObjectType + '/picklist-values/' + recordTypeId + '/';   
        Map<String, Object> response;
        HttpResponse httpResponse = call(picklistValuesURL, 'GET');
        if(httpResponse.getStatusCode() == 200) {
            response = (Map<String, Object>) JSON.deserializeUntyped(httpResponse.getBody()); 
        } else {
            throw new CalloutException('Si è verificato un errore durante il recupero dei valori del filtro');
        } 
        return response; 
    }

    /******************************************************************************************
    * @description  Makes an HTTP call to a specified endpoint using the provided HTTP method.
    * @param        endpoint - The URL to which the HTTP request is sent.
    * @param        httpMethod - The HTTP method (e.g., 'GET', 'POST') to use for the request.
    * @return       HttpResponse - The response received from the HTTP call.
    *******************************************************************************************/
    private static HttpResponse call(String endpoint, String httpMethod) {
        Http http = new Http();
        HttpRequest request = new HttpRequest();
        String token = !Test.isRunningTest() ? Page.sessionIdGathererPage.getContent().toString().trim() : '';
        request.setHeader('Authorization', 'OAuth ' + token);
        request.setMethod(httpMethod);
        request.setEndpoint(endpoint);
        return http.send(request);
    }

    /******************************************************************************************
    * @description  Extracts picklist values from the API response for a specified picklist field.
    * @param        response - The API response containing picklist field values.
    * @param        picklistAPIName - The API name of the picklist field for which to retrieve values.
    * @return       List<String> - A list of picklist values in the format 'label;value'.
    *******************************************************************************************/
    public static List<String> getPickListValues(Map<String, Object> response, String picklistAPIName) {
        Map<String, Object> picklistFieldValuesObject = (Map<String, Object>) response?.get('picklistFieldValues');
        Map<String, Object> targetPicklistObject = (Map<String, Object>) picklistFieldValuesObject?.get(picklistAPIName);
        List<Object> picklistValueObjects = (List<Object>) targetPicklistObject?.get('values');
        List<String> picklistValues = new List<String>();
        for(Object o : picklistValueObjects) {
            Map<String, Object> currentObjectMap = (Map<String, Object>) o;
            picklistValues.add((String) currentObjectMap?.get('label') + ';' + (String) currentObjectMap?.get('value'));
        }
        return picklistValues;
    }
}
