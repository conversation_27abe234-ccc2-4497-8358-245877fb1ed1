@isTest
public without sharing class OpportunityFetchExpiredOpportunitiesTest {
    public static Database.DMLOptions dmlOptions {
        get {
            if (dmlOptions == null) {
                Database.DMLOptions dmlOptions = new Database.DMLOptions();
                dmlOptions.DuplicateRuleHeader.AllowSave = true;
                return dmlOptions;
            } else {
                return dmlOptions;
            }
        }
        set;
    }

    public static sObject createRecord(String sObjectName, Map<String, Object> fieldsValues, Boolean executeDML) {
        sObject record;
        System.debug(LoggingLevel.INFO, 'inserting this object ' + sObjectName);
        if (fieldsValues.keySet().contains('RecordTypeId')) {
            System.debug(LoggingLevel.DEBUG, 'with RecordTypeId');
            record = UTL_DynamicApex.sObjectTypesPerObjectName.get(sObjectName)
                .newSObject((Id) fieldsValues.get('RecordTypeId'), true);
        } else {
            System.debug(LoggingLevel.DEBUG, 'without RecordTypeId');
            record = UTL_DynamicApex.sObjectTypesPerObjectName.get(sObjectName).newSObject();
        }

        for (String fieldName : fieldsValues.keySet()) {
            Object fieldValue = fieldsValues.get(fieldName);
            System.debug(LoggingLevel.DEBUG, 'field has this value : (' + fieldName + ') ' + fieldValue);
            record.put(fieldName, fieldValue);
        }

        if (executeDML) {
            // insert record;
            System.debug(LoggingLevel.DEBUG, 'executing DML');
            Database.insert(record, dmlOptions);
        }

        return record;
    }

    /************
     * <AUTHOR> - Volodia Gounaris
     * @date            2025-04-29
     * @description     This is a unit test to assert the good behavior of method OpportunityFetchExpiredOpportunities.OpportunityFetchExpiredOpportunities
     *                  This scenario has 2 opportunities :
     *                    - 1 opp should be expired
     *                              - 2 expired quote
     *                    - 1 opp is valid
     *                              - 1 expired quote
     *                              - 1 valid quote
     *                  The OpportunityFetchExpiredOpportunities should return only 1 opportunity : the one with only expired Quote
     */
    @IsTest
    static void Scenario1_unitTest_OpportunityFetchExpiredOpportunities() {
        User adminUser = UTL_DataFactory.createUser(
            'Admintst',
            '<EMAIL>',
            'User',
            'System Administrator',
            '',
            '<EMAIL>',
            true
        );

        Map<String, Object> myAccData = new Map<String, Object>{
            'Name' => 'testAccount',
            'RecordTypeId' => Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName()
                .get('Society')
                .getRecordTypeId()
        };

        Account myAcc = (Account) createRecord('Account', myAccData, true);

        // Map<String, Object> myParentOppData = new Map<String, Object>{
        //     'AccountId' => myAcc.Id,
        //     'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Agenziale').getRecordTypeId(),
        //     'stageName' => 'Nuovo',
        //     'closeDate' => Date.Today().addDays(30),
        //     'name' => 'testParentOpp'
        // };
        // Opportunity myParentOpp = (Opportunity)createRecord('Opportunity', myParentOppData, true );

        Map<String, Object> myOpp1Data_ExpiredOpp = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName()
                .get('Prodotto')
                .getRecordTypeId(),
            'stageName' => 'Nuovo',
            'closeDate' => Date.Today().addDays(30),
            'name' => 'testOpp1_ExpiredOpp'
        };
        Opportunity myOpp1_ExpiredOpp = (Opportunity) createRecord('Opportunity', myOpp1Data_ExpiredOpp, true);

        Map<String, Object> myQuote1DataExpiredQuote = new Map<String, Object>{
            // 'AccountId' => myAcc.Id,
            'Name' => 'testQuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOpp1_ExpiredOpp.Id
            // 'StageName' => 'Needs Review'
        };
        Quote myQuote1 = (Quote) createRecord('Quote', myQuote1DataExpiredQuote, true);

        Map<String, Object> myQuote2DataExpiredQuote = new Map<String, Object>{
            // 'AccountId' => myAcc.Id,
            'Name' => 'test2QuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-15),
            'OpportunityId' => myOpp1_ExpiredOpp.Id
            // 'StageName' => 'Needs Review'
        };
        Quote myQuote2 = (Quote) createRecord('Quote', myQuote2DataExpiredQuote, true);

        Map<String, Object> myOpp2Data_ValidOpp = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName()
                .get('Prodotto')
                .getRecordTypeId(),
            'stageName' => 'Nuovo',
            'closeDate' => Date.Today().addDays(30),
            'name' => 'testOpp2_ValidOpp'
        };
        Opportunity myOpp2_ValidOpp = (Opportunity) createRecord('Opportunity', myOpp2Data_ValidOpp, true);

        Map<String, Object> myQuote3Data_ExpiredQuote = new Map<String, Object>{
            // 'AccountId' => myAcc.Id,
            'Name' => 'test3QuoteName_ExpiredQuote',
            'ExpirationDate' => Date.Today().addDays(-15),
            'OpportunityId' => myOpp2_ValidOpp.Id
            // 'StageName' => 'Needs Review'
        };
        Quote myQuote3_ExpiredQuote = (Quote) createRecord('Quote', myQuote3Data_ExpiredQuote, true);

        Map<String, Object> myQuote4Data_ValidQuote = new Map<String, Object>{
            // 'AccountId' => myAcc.Id,
            'Name' => 'test3QuoteName_ValidQuote',
            'ExpirationDate' => Date.Today().addDays(15),
            'OpportunityId' => myOpp2_ValidOpp.Id
            // 'StageName' => 'Needs Review'
        };
        Quote myQuote4_ValidQuote = (Quote) createRecord('Quote', myQuote4Data_ValidQuote, true);

        List<List<Id>> myListofExpiredOpps_BEFORE = OpportunityFetchExpiredOpportunities.OpportunityFetchExpiredOpportunities();
        Assert.isTrue(
            myListofExpiredOpps_BEFORE[0].size() == 1,
            'There should be 1 expired Opp before the scheduled flow (and 1 non-expired Opp). Currently is ' +
            myListofExpiredOpps_BEFORE[0].size()
        );
    }

    /************
     * <AUTHOR> - Volodia Gounaris
     * @date            2025-04-29
     * @description     This is a feature test to test the schedule flow OpportunityClosedIfExpired
     *                  It tests that the opp with expired quotes is closed, as well as its parent
     */
    @IsTest
    static void Scenario2_featureTest_ScheduledFlow() {
        User adminUser = UTL_DataFactory.createUser(
            'Admintst',
            '<EMAIL>',
            'User',
            'System Administrator',
            '',
            '<EMAIL>',
            true
        );

        Map<String, Object> myAccData = new Map<String, Object>{
            'Name' => 'testAccount',
            'RecordTypeId' => Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName()
                .get('Society')
                .getRecordTypeId()
        };

        Account myAcc = (Account) createRecord('Account', myAccData, true);

        Map<String, Object> myParentOppData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName()
                .get('Agenziale')
                .getRecordTypeId(),
            'stageName' => 'Nuovo',
            'closeDate' => Date.Today().addDays(30),
            'name' => 'testParentOpp'
        };
        Opportunity myParentOpp = (Opportunity) createRecord('Opportunity', myParentOppData, true);

        Map<String, Object> myOppData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName()
                .get('Prodotto')
                .getRecordTypeId(),
            'stageName' => 'Nuovo',
            'closeDate' => Date.Today().addDays(30),
            'name' => 'testOpp',
            'parent__c' => myParentOpp.Id
        };
        Opportunity myOpp = (Opportunity) createRecord('Opportunity', myOppData, true);

        Map<String, Object> myQuote1DataExpiredQuote = new Map<String, Object>{
            // 'AccountId' => myAcc.Id,
            'Name' => 'testQuoteName',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOpp.Id
            // 'StageName' => 'Needs Review'
        };
        Quote myQuote1 = (Quote) createRecord('Quote', myQuote1DataExpiredQuote, true);

        Map<String, Object> myQuote2DataExpiredQuote = new Map<String, Object>{
            // 'AccountId' => myAcc.Id,
            'Name' => 'test2QuoteName',
            'ExpirationDate' => Date.Today().addDays(-15),
            'OpportunityId' => myOpp.Id
            // 'StageName' => 'Needs Review'
        };
        Quote myQuote2 = (Quote) createRecord('Quote', myQuote2DataExpiredQuote, true);

        List<List<Id>> myListofExpiredOpps_BEFORE = OpportunityFetchExpiredOpportunities.OpportunityFetchExpiredOpportunities();
        Assert.isTrue(
            myListofExpiredOpps_BEFORE[0].size() == 1,
            'THere should be 1 expired opp before the scheduled flow. Currently is ' +
            myListofExpiredOpps_BEFORE[0].size()
        );

        Test.startTest();
        System.runAs(adminUser) {
            Map<String, Object> params = new Map<String, Object>();
            Flow.Interview.OpportunityClosedIfExpired myScheduledFlow = new Flow.Interview.OpportunityClosedIfExpired(
                params
            );
            myScheduledFlow.start();
        }
        Test.stopTest();

        List<List<Id>> myListofExpiredOpps_AFTER = OpportunityFetchExpiredOpportunities.OpportunityFetchExpiredOpportunities();
        Assert.isTrue(
            myListofExpiredOpps_AFTER[0].size() == 0,
            'THere should be 1 expired opp after the scheduled flow.Currently is ' +
            myListofExpiredOpps_BEFORE[0].size()
        );

        Opportunity oppExpired = [SELECT Id, isClosed FROM Opportunity WHERE Id = :myOpp.Id LIMIT 1];
        Assert.isTrue(oppExpired.isClosed, 'opp with all Quotes expired is well closed by logic');

        Opportunity oppParentExpired = [SELECT Id, isClosed FROM Opportunity WHERE Id = :myParentOpp.Id LIMIT 1];
        Assert.isTrue(oppParentExpired.isClosed, 'opp parent should also be closed by logic');
    }

    /************
     * <AUTHOR> - Volodia Gounaris
     * @date            2025-04-29
     * @description     This is a feature test to test the schedule flow OpportunityClosedIfExpired
     *                  It tests that the opp with expired quotes is closed.
     *                  The opp does not have parent opp
     */
    @IsTest
    static void Scenario3_featureTest_ScheduledFlow_OppParentDoesNotExist() {
        User adminUser = UTL_DataFactory.createUser(
            'Admintst',
            '<EMAIL>',
            'User',
            'System Administrator',
            '',
            '<EMAIL>',
            true
        );

        Map<String, Object> myAccData = new Map<String, Object>{
            'Name' => 'testAccount',
            'RecordTypeId' => Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName()
                .get('Society')
                .getRecordTypeId()
        };

        Account myAcc = (Account) createRecord('Account', myAccData, true);

        // Map<String, Object> myParentOppData = new Map<String, Object>{
        //     'AccountId' => myAcc.Id,
        //     'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Agenziale').getRecordTypeId(),
        //     'stageName' => 'Nuovo',
        //     'closeDate' => Date.Today().addDays(30),
        //     'name' => 'testParentOpp'
        // };
        // Opportunity myParentOpp = (Opportunity)createRecord('Opportunity', myParentOppData, true );

        Map<String, Object> myOppData = new Map<String, Object>{
            'AccountId' => myAcc.Id,
            'recordTypeId' => Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName()
                .get('Prodotto')
                .getRecordTypeId(),
            'stageName' => 'Nuovo',
            'closeDate' => Date.Today().addDays(30),
            'name' => 'testOpp'
        };
        Opportunity myOpp = (Opportunity) createRecord('Opportunity', myOppData, true);

        Map<String, Object> myQuote1DataExpiredQuote = new Map<String, Object>{
            // 'AccountId' => myAcc.Id,
            'Name' => 'testQuoteName',
            'ExpirationDate' => Date.Today().addDays(-10),
            'OpportunityId' => myOpp.Id
            // 'StageName' => 'Needs Review'
        };
        Quote myQuote1 = (Quote) createRecord('Quote', myQuote1DataExpiredQuote, true);

        Map<String, Object> myQuote2DataExpiredQuote = new Map<String, Object>{
            // 'AccountId' => myAcc.Id,
            'Name' => 'test2QuoteName',
            'ExpirationDate' => Date.Today().addDays(-15),
            'OpportunityId' => myOpp.Id
            // 'StageName' => 'Needs Review'
        };
        Quote myQuote2 = (Quote) createRecord('Quote', myQuote2DataExpiredQuote, true);

        List<List<Id>> myListofExpiredOpps_BEFORE = OpportunityFetchExpiredOpportunities.OpportunityFetchExpiredOpportunities();
        Assert.isTrue(
            myListofExpiredOpps_BEFORE[0].size() == 1,
            'THere should be 1 expired opp before the scheduled flow. Currently is ' +
            myListofExpiredOpps_BEFORE[0].size()
        );

        Test.startTest();
        System.runAs(adminUser) {
            Map<String, Object> params = new Map<String, Object>();
            Flow.Interview.OpportunityClosedIfExpired myScheduledFlow = new Flow.Interview.OpportunityClosedIfExpired(
                params
            );
            myScheduledFlow.start();
        }
        Test.stopTest();

        List<List<Id>> myListofExpiredOpps_AFTER = OpportunityFetchExpiredOpportunities.OpportunityFetchExpiredOpportunities();
        Assert.isTrue(
            myListofExpiredOpps_AFTER[0].size() == 0,
            'THere should be 1 expired opp after the scheduled flow.Currently is ' +
            myListofExpiredOpps_BEFORE[0].size()
        );

        Opportunity oppExpired = [SELECT Id, isClosed FROM Opportunity WHERE Id = :myOpp.Id LIMIT 1];
        Assert.isTrue(oppExpired.isClosed, 'opp with all Quotes expired is well closed by logic');
    }
}
