/**
 * @description       : TM_LC_CustomLookup.cls
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 03-02-2025
 * @last modified by  : <EMAIL>
 * @cicd_tests TM_LC_CustomLookupTst
**/
public with sharing class TM_LC_CustomLookup {

    private static TM_SRV_CustomLookup srvCustomLookup = TM_SRV_CustomLookup.getInstance();

    @AuraEnabled
    public static Map<String, Object> fetchLookupValues (String searchKeyword, String objectApiName, String queryFields, String queryWhere,
                                                         String filterSearchKeywordField, String filterFields, String primaryDisplayField,
                                                         String secondaryDisplayField, String orderByField, String recordNumberLimit,
                                                         String queryClass, String queryClassMethod, String queryClassParams) {
        System.debug('---> ASD:' + objectApiName);
        System.debug('---> ASD:' + queryFields);
        System.debug('---> ASD:' + queryWhere);
        System.debug('---> ASD:' + filterSearchKeywordField);
        System.debug('---> ASD:' + filterFields);
        System.debug('---> ASD:' + primaryDisplayField);
        System.debug('---> ASD:' + secondaryDisplayField);
        System.debug('---> ASD:' + orderByField);
        System.debug('---> ASD:' + queryClass);
        System.debug('---> ASD:' + queryClassMethod);
        System.debug('---> ASD:' + queryClassParams);
        
        return srvCustomLookup.fetchLookupValues(searchKeyword, objectApiName, queryFields, queryWhere, filterSearchKeywordField, 
                                                 filterFields, primaryDisplayField, secondaryDisplayField, orderByField, recordNumberLimit, queryClass, queryClassMethod, queryClassParams);
        /*
        Map<String, Object> ret = new Map<String, Object>();
        String procedureName = 'RestGet_AtecoBreve';
        Map<String, Object> ipInput = new Map<String, Object> ();
        List<Object> ipOutput = new List<Object> ();
        Map<String, Object> ipOptions = new Map<String, Object> ();

        ipOutput = (List<Object>) omnistudio.IntegrationProcedureService.runIntegrationService(procedureName, ipInput, ipOptions);

        return ret;
        */
    }

    @AuraEnabled
    public static Map<String, Object> initialize (String objectApiName, String queryFields, String queryWhere,
                                                  String filterSearchKeywordField, String filterFields, String primaryDisplayField,
                                                  String secondaryDisplayField, String orderByField, String initialSelectedId,
                                                  String queryClass, String queryClassMethod, String queryClassParams) {
        System.debug('---> ASD:' + objectApiName);
        System.debug('---> ASD:' + queryFields);
        System.debug('---> ASD:' + queryWhere);
        System.debug('---> ASD:' + filterSearchKeywordField);
        System.debug('---> ASD:' + filterFields);
        System.debug('---> ASD:' + primaryDisplayField);
        System.debug('---> ASD:' + secondaryDisplayField);
        System.debug('---> ASD:' + orderByField);
        System.debug('---> ASD:' + initialSelectedId);
        System.debug('---> ASD:' + queryClass);
        System.debug('---> ASD:' + queryClassMethod);
        System.debug('---> ASD:' + queryClassParams);

        return srvCustomLookup.initialize(objectApiName, queryFields, queryWhere, filterSearchKeywordField, filterFields, primaryDisplayField, 
                                          secondaryDisplayField, orderByField, initialSelectedId, queryClass, queryClassMethod, queryClassParams);
        
    }

    
}