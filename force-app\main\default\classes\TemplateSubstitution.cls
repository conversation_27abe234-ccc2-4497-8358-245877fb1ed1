public class TemplateSubstitution {
    /* @AuraEnabled
    public String key;
    
    @AuraEnabled
    public String value; */

    @AuraEnabled
    @InvocableVariable(label='Key')
    public String keyFlow;
    
    @AuraEnabled
    @InvocableVariable(label='value')
    public String valueFlow;

    public TemplateSubstitution(String key, String value) {
        this.keyFlow = key;
        this.valueFlow = value;
    }

    @InvocableMethod(label='Parse Dynamic JSON' description='Parse dynamic JSON and return a list of TemplateSubstitution objects')
    public static List<List<TemplateSubstitution>> parseDynamicJSON(List<String> jsonStringList) {
        List<TemplateSubstitution> results = new List<TemplateSubstitution>();
        if (jsonStringList != null && jsonStringList.size() == 1) {
            // Deserialize into a Map<String, Object>
            String jsonString = jsonStringList[0];
            Map<String, Object> jsonMap = (Map<String, Object>) JSON.deserializeUntyped(jsonString);
            // Iterate over the map and create a new TemplateSubstitution object for each key/value pair
            for (String key : jsonMap.keySet()) {
                String val = String.valueOf(jsonMap.get(key));
                TemplateSubstitution result = new TemplateSubstitution(key, val);
                results.add(result);
            }
        }
        return new List<List<TemplateSubstitution>> {results};
    }
}