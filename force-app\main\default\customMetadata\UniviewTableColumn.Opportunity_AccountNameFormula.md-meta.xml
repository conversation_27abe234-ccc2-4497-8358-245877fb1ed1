<?xml version="1.0" encoding="UTF-8"?>
<CustomMetadata xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <label>Opportunity_AccountNameFormula</label>
    <protected>false</protected>
    <values>
        <field>ColumnCurrencyCode__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>ColumnDayFormat__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>ColumnHourFormat__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>ColumnLabel__c</field>
        <value xsi:type="xsd:string">Soggetto</value>
    </values>
    <values>
        <field>ColumnMinuteFormat__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>ColumnMonthFormat__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>ColumnType__c</field>
        <value xsi:type="xsd:string">text</value>
    </values>
    <values>
        <field>ColumnVariant__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>ColumnYearFormat__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>DefaultSortDirection__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>DefaultSortOrder__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>FieldName__c</field>
        <value xsi:type="xsd:string">AccountNameFormula__c</value>
    </values>
    <values>
        <field>FieldOrder__c</field>
        <value xsi:type="xsd:double">8.0</value>
    </values>
    <values>
        <field>IsActive__c</field>
        <value xsi:type="xsd:boolean">true</value>
    </values>
    <values>
        <field>IsSortable__c</field>
        <value xsi:type="xsd:boolean">true</value>
    </values>
    <values>
        <field>IsSortedByDefault__c</field>
        <value xsi:type="xsd:boolean">false</value>
    </values>
    <values>
        <field>Object__c</field>
        <value xsi:type="xsd:string">Opportunity</value>
    </values>
    <values>
        <field>SearchField__c</field>
        <value xsi:type="xsd:string">AccountNameFormula__c</value>
    </values>
    <values>
        <field>SortingField__c</field>
        <value xsi:type="xsd:string">Account.Name</value>
    </values>
    <values>
        <field>UniviewKey__c</field>
        <value xsi:type="xsd:string">OpportunityReport</value>
    </values>
</CustomMetadata>
