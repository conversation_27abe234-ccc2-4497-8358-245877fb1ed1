public class OpportunityStageUpdateBatch implements Database.Batchable<sObject>{
    
	public Database.QueryLocator start(Database.BatchableContext BC){
        String query = 'SELECT Id, RecordType.Name, WorkingSLAExpiryDate__c, StageName ' +
               'FROM Opportunity ' +
               'WHERE (RecordType.Name = \'Interest Show\' ' +
               'AND StageName <> \'Chiuso\' ' +
               'AND WorkingSLAExpiryDate__c < TODAY)';
       return Database.getQueryLocator(query);
    }
    
    public void execute(Database.BatchableContext BC, List<Opportunity> opportunities){
        for(Opportunity opp : opportunities){
            opp.StageName= 'Chiuso';
            opp.ClosureSubstatus__c= 'Fuori SLA';
        }
        update opportunities;
    }
    public void finish(Database.BatchableContext BC) {
        
    }
}
