@isTest
private with sharing class UniLoggerTest {

    // for runAs
    // User adminUser = UTL_DataFactory.createUser('Admintst', '<EMAIL>', 'User', 'System Administrator', '', '<EMAIL>', true);


    @isTest
    public static void SimpleLogGeneric() {

        String myPayload = 'myPayload';

        Account myAcc = new Account(Name = 'this is a test account');
        insert myAcc;

        Test.startTest();
        Unilogger.writeDEBUG('test message', myPayload);
        Unilogger.writeINFO('test message', myPayload);
        Unilogger.writeWARN('test message', myPayload);
        Unilogger.writeERROR('test message', myPayload);
        Unilogger.writeDEBUG('test message', myPayload, myAcc.Id);
        Unilogger.writeINFO('test message', myPayload, myAcc.Id);
        Unilogger.writeWARN('test message', myPayload, myAcc.Id);
        Unilogger.writeERROR('test message', myPayload, myAcc.Id);
        test.stopTest();

        List<Custom_Log__c> myLog1s = [SELECT Id, Message__c FROM Custom_Log__c WHERE Record_of_Interest__c = :myAcc.Id ];
        System.debug('SimpleLogGeneric | myLogs.size ' + myLog1s.size());
        Assert.areEqual(4, myLog1s.size());
        List<Custom_Log__c> myLog2s = [SELECT Id FROM Custom_Log__c];
        System.debug('SimpleLogGeneric | myLogs.size ' + myLog2s.size());
        Assert.areEqual(8, myLog2s.size());

        Assert.areEqual('test message', myLog1s[0].Message__c, 'the field Message__c should be populated by "test message". value : ' +  myLog1s[0].Message__c);

        System.debug('SimpleLogGeneric | placeholder');

    }


    @isTest
    public static void SimpleLogException() {

        String myPayload = 'myPayload';

        Account myAcc = new Account(Name = 'this is a test account');
        insert myAcc;

        List<Account> myAccs = [SELECT Id FROM Account];
        
        Test.startTest();
        try{
            Account failToFetch = myAccs[10];
        }
        catch(Exception exc){
            Unilogger.writeERROR('test exception message', exc);

        }
        Test.stopTest();


        List<Custom_Log__c> myLogs = [SELECT Id, Err_Exception_Type_Name__c  FROM Custom_Log__c];
        System.debug('SimpleLogGeneric | myLogs.size ' + myLogs.size());
        Assert.areEqual(1, myLogs.size(), 'there should be 1 and only 1 record created. Number of record created : ' + myLogs.size());
        Assert.isNotNull(!String.isBlank(myLogs[0].Err_Exception_Type_Name__c), 'the field Err_Exception_Type_Name__c should be populated but it is empty');
    }


    @isTest
    public static void SimpleLogIntegration() {

        Test.setMock(HttpCalloutMock.class, new UniLoggerTestIntegrationMock());

        HttpRequest req = new HttpRequest();
        req.setEndpoint('http://www.unilol.com');
        req.setMethod('GET');
        
        Blob headerValue = Blob.valueOf('myUsername' + ':' + 'myPassword');
        String authorizationHeader = 'Basic ' +
        EncodingUtil.base64Encode(headerValue);
        req.setHeader('Authorization', authorizationHeader);
    
        Http http = new Http();
        HTTPResponse res = http.send(req);

        Test.startTest();

            UniLogger logEvent = new Unilogger('integration framework happy flow', req, res, 'myIntegrationId');
            logEvent
                .setlogSource(Unilogger.logSource.Apex)
                .setLoggingLevel(LoggingLevel.INFO)
                .publishLog();

        Test.stopTest();

        
        List<Custom_Log__c> myLogs = [SELECT Id, Call_Response_Body__c  FROM Custom_Log__c];

        Assert.areEqual(1, myLogs.size(), 'there should be 1 and only 1 record created. Number of record created : ' + myLogs.size());
        Assert.isNotNull(!String.isBlank(myLogs[0].Call_Response_Body__c), 'the field Call_Response_Body__c should be populated but it is empty');
    }


        @isTest
    public static void testFlowSubmit() {

		//instatiate the error
		UniLogFlowSubmit.FlowLog log = new UniLogFlowSubmit.FlowLog();
		log.actionName = 'Test Action';
		log.message = 'Test Error';
		log.flowName = 'Test Flow';
		log.refId = '12312331';

		List<UniLogFlowSubmit.FlowLog> logList = new List<UniLogFlowSubmit.FlowLog>{ log };

		//start the test and publish the log
		Test.startTest();
        
        UniLogFlowSubmit.submitFlowLog(logList);

		Test.stopTest();

		//Assert response
        List<Custom_Log__c> myLogs = [SELECT Id, Context__c  FROM Custom_Log__c];
        Assert.areEqual(1, myLogs.size());
        Assert.areEqual('Flow', myLogs[0].Context__c, 'context field on log should be "Flow"');

    }

    @isTest
    public static void testDeleteBatch() {

        Custom_Log__c myLog = new Custom_Log__c(
            Message__c = 'test message',
            Expiration_Date__c = Date.today().addDays(-2)
        );

        insert myLog;
        List<AggregateResult> countBeforeList = [SELECT Count(Id) recordCount FROM Custom_Log__c];
        Integer countBefore = (Integer)countBeforeList[0].get('recordCount');
        Assert.areEqual(1, countBefore, 'should be 1 record in the database before batch');

        Test.startTest();
        
        Database.executeBatch( new UniLogCleanLogBatch(), 200);

		Test.stopTest();

        List<AggregateResult> countAfterList = [SELECT Count(Id) recordCount FROM Custom_Log__c];
        Integer countAfter = (Integer)countAfterList[0].get('recordCount');
        Assert.areEqual(0, countAfter, 'should be 0 record in the database after batch');

    }

    public static void testDeleteScheduler(){
        
        String CRON_EXP = '0 0 0 3 9 ? 2042';


        Test.startTest();

        String jobId = System.schedule('UniLogCleanLogBatch', CRON_EXP, new UniLogCleanLogBatch());
        
        Test.stopTest();
    }


}