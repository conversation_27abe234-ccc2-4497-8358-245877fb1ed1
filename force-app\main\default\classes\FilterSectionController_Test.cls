@isTest
public class FilterSectionController_Test {
    @TestSetup
    static void setupTestData() {
        List<Opportunity> opportunities = new List<Opportunity>();
        Account newAccount = new Account(FirstName = 'John', LastName = 'Doe', Valus__c = '5');

        insert newAccount;

        Account insertedAccount = [SELECT Id, FirstName, LastName FROM Account WHERE Id = :newAccount.Id];

        Id recordTypeId = Schema.SObjectType.Opportunity.getRecordTypeInfosByName().get('Interest Show').getRecordTypeId();

        for (Integer i = 0; i < 5; i++) {
            opportunities.add(
                new Opportunity(
                    Name = 'TestOpportunity' + (i + 1),
                    StageName = 'Assegnato',
                    Rating__c = 'Caldissima',
                    HasCallMeBack__c = true,
                    Amount = 10000,
                    AccountId = insertedAccount.Id,
                    AreaOfNeed__c = 'Cane e Gatto',
                    RecordTypeId = recordTypeId,
                    CloseDate = Date.today().addDays(30)
                )
            );
        }
        insert opportunities;

        UniViewUserPreference__c newQuery = new UniViewUserPreference__c(
            UniviewKey__c = 'OpportunityReport',
            Query__c = 'SELECT TemperatureFormula__c, Rating__c, TakenInChargeSLARemainingHours__c, Name,  AccountNameFormula__c, AccountValusFormula__c, JourneyStep__c, HasCallMeBackFormula__c, AreasOfNeedFormula__c, Amount, CreatedDate, WorkingSLAExpiryDate__c FROM Opportunity ORDER BY Rating__c DESC NULLS LAST, TakenInChargeSLARemainingHours__c ASC NULLS LAST',
            QueryParameters__c = '[]',
            OwnerId = UserInfo.getUserId(),
            UserPreferenceName__c = 'Generic'
        );

        insert newQuery;
    }

    @isTest
    static void getFiltersFromMDTest() {
        Test.setMock(HttpCalloutMock.class, new UIResponse_Mock());
        FilterSectionController.univiewKey = 'OpportunityReport';
        UniviewController.testUniviewDefinitionMetadata = (List<UniviewDefinition__mdt>) JSON.deserialize(
            '[{"attributes":{"type":"UniviewDefinition__mdt","url":"/services/data/v60.0/sobjects/UniviewDefinition__mdt/m0P1x0000001G0lEAE"},"MaximumUserPreferences__c":5,"RefreshInterval__c":300,"Object__c":"Opportunity","PageSize__c":10,"ObjectPlural__c":"Trattative","ScopingRule__c":true,"RecordTypes__c":"InterestShow","RefreshonFocus__c":true,"ShowPreferencesButtons__c":false,"Id":"m0P1x0000001G0lEAE"}]',
            List<UniviewDefinition__mdt>.class
        );
        FilterSectionController.filterMetadata = (List<UniviewFilter__mdt>) JSON.deserialize(
            '[{"Object__c":"Opportunity","FieldName__c":"Rating__c","FieldOrder__c":"1","FilterOperatorList__c":"uguale a","IsActive__c":"true","IsModifiable__c":"true","FieldType__c":"Picklist","FilterInputType__c":"","FilterLabel__c":"Temperatura","FilterName__c":"temperature","FilterOptions__c":"API","FilterType__c":"Multiselect","FilterValue__c":"","FilterClass__c":"dropdown-content","FilterOperator__c":"","IsNullable__c":"false"},{"Object__c":"Opportunity","FieldName__c":"Name","FieldOrder__c":"1","FilterOperatorList__c":"uguale a","IsActive__c":"true","IsModifiable__c":"true","FieldType__c":"Text","FilterInputType__c":"text","FilterLabel__c":"Nome trattativa","FilterName__c":"Name","FilterOptions__c":"QUERY","FilterType__c":"Picklist With Suggestions","FilterValue__c":"","FilterClass__c":"Opportunity","FilterOperator__c":"","IsNullable__c":"false"}]',
            List<UniviewFilter__mdt>.class
        );

        Test.startTest();
        List<FilterSectionController.Filter> filters = FilterSectionController.getFiltersFromMD(FilterSectionController.univiewKey, 'InterestShow', 'Opportunity');
        Test.stopTest();
    }

    @isTest
    static void getFiltersFromMDManualOptionTest() {
        FilterSectionController.univiewKey = 'OpportunityReport';
        FilterSectionController.filterMetadata = (List<UniviewFilter__mdt>) JSON.deserialize(
            '[{"Object__c":"Opportunity","FieldName__c":"Rating__c","FieldOrder__c":"1","FilterOperatorList__c":"uguale a","IsActive__c":"true","IsModifiable__c":"true","FieldType__c":"Picklist","FilterInputType__c":"","FilterLabel__c":"Temperatura","FilterName__c":"temperature","FilterOptions__c":"Calda;Tiepida","FilterType__c":"Multiselect","FilterValue__c":"","FilterClass__c":"dropdown-content","FilterOperator__c":"","IsNullable__c":"false"},{"Object__c":"Opportunity","FieldName__c":"Name","FieldOrder__c":"1","FilterOperatorList__c":"uguale a","IsActive__c":"true","IsModifiable__c":"true","FieldType__c":"Text","FilterInputType__c":"text","FilterLabel__c":"Nome trattativa","FilterName__c":"Name","FilterOptions__c":"QUERY","FilterType__c":"Picklist With Suggestions","FilterValue__c":"","FilterClass__c":"Opportunity","FilterOperator__c":"","IsNullable__c":"false"}]',
            List<UniviewFilter__mdt>.class
        );

        Test.startTest();
        List<FilterSectionController.Filter> filters = FilterSectionController.getFiltersFromMD(FilterSectionController.univiewKey, 'Opportunity', 'InterestShow');
        Test.stopTest();
    }

    @isTest
    static void getOptionsByFieldTest() {
        Test.startTest();
        List<FilterSectionController.Option> options = FilterSectionController.getOptionsByField('Opportunity', 'Rating__c');
        Test.stopTest();
        System.debug('# options: ' + options);

    }

    @isTest
    static void getFiltersFromCustomObjectTest() {
        DataTableController.testColumnMetadata = (List<UniviewTableColumn__mdt>) JSON.deserialize(
            '[{"attributes":{"type":"UniviewTableColumn__mdt","url":"/services/data/v60.0/sobjects/UniviewTableColumn__mdt/m0R1x000000ALZCEA4"},"Id":"m0R1x000000ALZCEA4","Object__c":"Opportunity","FieldName__c":"Name","SearchField__c":"Name","ColumnLabel__c":"Nome trattativa","ColumnType__c":"buttona","FieldOrder__c":1,"IsActive__c":true,"IsSortable__c":true,"ColumnVariant__c":"Base","IsSortedByDefault__c":false,"SortingField__c":"Name"}]',
            List<UniviewTableColumn__mdt>.class
        );

        Test.startTest();
        Integer maximumUserPreferences = 5;
        String userPreferenceName = 'AAA';

        List<FilterSectionController.FilterPersistence> filters = new List<FilterSectionController.FilterPersistence>();
        FilterSectionController.FilterPersistence filter = new FilterSectionController.FilterPersistence();
        filter.filterName = 'temperature';
        filter.filterValue = 'Fredda,Tiepida';
        filter.filterType = 'Multiselect';
        filter.filterOperator = 'uguale a';
        filter.fieldName = 'Rating__c';
        filter.fieldType = 'Picklist';
        filter.isNullable = false;
        filters.add(filter);

        DataTableController.QueryRequest a = new DataTableController.QueryRequest();
        a.univiewKey = 'OpportunityReport';
        a.objectType = 'InterestShow';
        a.filters = filters;

        FilterSectionController.univiewKey = 'OpportunityReport';
        FilterSectionController.saveFilters(maximumUserPreferences, a, userPreferenceName);
        Test.stopTest();
        List<UniViewUserPreference__c> filtersa;
        FilterSectionController.univiewKey = 'OpportunityReport';
        filtersa = FilterSectionController.getFiltersFromCustomObject(FilterSectionController.univiewKey);

    }

    @isTest
    static void saveFiltersTest() {
        DataTableController.testColumnMetadata = (List<UniviewTableColumn__mdt>) JSON.deserialize(
            '[{"attributes":{"type":"UniviewTableColumn__mdt","url":"/services/data/v60.0/sobjects/UniviewTableColumn__mdt/m0R1x000000ALZCEA4"},"Id":"m0R1x000000ALZCEA4","Object__c":"Opportunity","FieldName__c":"Name","SearchField__c":"Name","ColumnLabel__c":"Nome trattativa","ColumnType__c":"buttona","FieldOrder__c":1,"IsActive__c":true,"IsSortable__c":true,"ColumnVariant__c":"Base","IsSortedByDefault__c":false,"SortingField__c":"Name"}]',
            List<UniviewTableColumn__mdt>.class
        );

        Test.startTest();
        Integer maximumUserPreferences = 5;
        String UserPreferenceName = 'AAA';

        List<FilterSectionController.FilterPersistence> filters = new List<FilterSectionController.FilterPersistence>();
        FilterSectionController.FilterPersistence filter = new FilterSectionController.FilterPersistence();
        filter.filterName = 'temperature';
        filter.filterValue = 'Fredda,Tiepida';
        filter.filterType = 'Multiselect';
        filter.filterOperator = 'uguale a';
        filter.fieldName = 'Rating__c';
        filter.fieldType = 'Picklist';
        filter.isNullable = false;
        filters.add(filter);

        DataTableController.QueryRequest a = new DataTableController.QueryRequest();
        a.univiewKey = 'OpportunityReport';
        a.objectType = 'InterestShow';
        a.filters = filters;

        FilterSectionController.univiewKey = 'OpportunityReport';
        FilterSectionController.saveFilters(maximumUserPreferences, a, UserPreferenceName);
        Test.stopTest();

        List<UniViewUserPreference__c> filtersa;
        FilterSectionController.univiewKey = 'OpportunityReport';
        filtersa = FilterSectionController.getFiltersFromCustomObject(FilterSectionController.univiewKey);
    }

    @isTest
    static void checkFilterPersistenceTest() {
        FilterSectionController.FilterPersistence filter = new FilterSectionController.FilterPersistence();
        filter.fieldName = 'StageName';
        filter.fieldType = 'Picklist';
        filter.filterName = 'stage';
        filter.filterOperator = 'uguale a';
        filter.filterType = 'Multiselect';
        filter.filterValue = 'Assegnato';
        filter.isNullable = false;
    }

    @isTest
    static void getDefaultFiltersFromMDTest() {
        Test.setMock(HttpCalloutMock.class, new UIResponse_Mock());
        FilterSectionController.univiewKey = 'OpportunityReport';
        FilterSectionController.getDefaultFiltersFromMD = (List<UniviewFilter__mdt>) JSON.deserialize(
            '[{"Object__c":"Opportunity","FieldName__c":"Rating__c","FieldOrder__c":"1","FilterOperatorList__c":"uguale a","IsActive__c":"true","IsModifiable__c":"false","FieldType__c":"Picklist","FilterInputType__c":"","FilterLabel__c":"Temperatura","FilterName__c":"temperature","FilterOptions__c":"API","FilterType__c":"Multiselect","FilterValue__c":"","FilterClass__c":"dropdown-content","FilterOperator__c":"","IsNullable__c":"false"},{"Object__c":"Opportunity","FieldName__c":"Name","FieldOrder__c":"1","FilterOperatorList__c":"uguale a","IsActive__c":"true","IsModifiable__c":"false","FieldType__c":"Text","FilterInputType__c":"text","FilterLabel__c":"Nome trattativa","FilterName__c":"Name","FilterOptions__c":"QUERY","FilterType__c":"Picklist With Suggestions","FilterValue__c":"","FilterClass__c":"Opportunity","FilterOperator__c":"","IsNullable__c":"false"}]',
            List<UniviewFilter__mdt>.class
        );

        Test.startTest();
        List<FilterSectionController.Filter> filters = FilterSectionController.getDefaultFiltersFromMD(FilterSectionController.univiewKey, 'InterestShow', 'Opportunity');
        Test.stopTest();
    }

    @isTest
    static void deleteUniViewUserPreferenceTest() {
        List<UniViewUserPreference__c> uvup = new List<UniViewUserPreference__c>();
        uvup = [
            SELECT Id, UniviewKey__c, UserPreferenceName__c, OwnerId
            FROM UniViewUserPreference__c
            WHERE UserPreferenceName__c = 'Generic'
            LIMIT 1
        ];
        Id testId = uvup[0].Id;
        Test.startTest();
        FilterSectionController.deleteUniViewUserPreference(testId);
        Test.stopTest();
        List<UniViewUserPreference__c> uvupAfterTest = new List<UniViewUserPreference__c>();
        uvupAfterTest = [
            SELECT Id, UniviewKey__c, UserPreferenceName__c, OwnerId
            FROM UniViewUserPreference__c
            WHERE UserPreferenceName__c = 'Generic'
            LIMIT 1
        ];
    }

    @isTest
    static void optionCreationTest() {
        Test.startTest();
        FilterSectionController.Option currentOption = new FilterSectionController.Option('label', 'value', true);
        Test.stopTest();

    }

    public with sharing class UIResponse_Mock implements HttpCalloutMock {
        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');

            res.setBody(
                '{"picklistFieldValues":{ "Rating__c":{"values":[{"value":"Fredda","validFor":[ ],"label":"Fredda","attributes":null},{"value":"Tiepida","validFor":[],"label":"Tiepida","attributes":null},{"value":"Calda","validFor":[],"label":"Calda","attributes":null},{"value":"Caldissima","validFor":[],"label":"Caldissima","attributes":null},{"value":"Bollente","validFor":[],"label":"Bollente","attributes":null}],"url":"/services/data/v58.0/ui-api/object-info/Opportunity/picklist-values/0121x000005gISNAA2/Rating__c","eTag":"43936798bd12b36e0b565945c050dc69","defaultValue":null,"controllerValues":{}}},"eTag":"83a534a0277cc1fbab0570d5a72d47ba"}'
            );
            res.setStatusCode(200);
            return res;
        }
    }
}
