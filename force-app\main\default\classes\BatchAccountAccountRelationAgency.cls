/*
 * @cicd_tests BatchAccountAccountRelationAgency_Test
*/
public class BatchAccountAccountRelationAgency implements Database.Batchable<SObject>, Database.Stateful {
    
    public String query;
    private Boolean concatenate = false;
    
    public BatchAccountAccountRelationAgency(){
        this(false);
    }
    
    public BatchAccountAccountRelationAgency(Boolean concatenate){
        this.concatenate = concatenate;
        query = 'SELECT Id, FinServ__AccountAccountRelation__c.FinServ__Account__r.ExternalId__c FROM FinServ__AccountAccountRelation__c WHERE RecordType.DeveloperName = \'AgencySociety\'';
    }
    
    public BatchAccountAccountRelationAgency(String agency, String lastModifiedDate){

        query = 'SELECT Id, FinServ__AccountAccountRelation__c.FinServ__Account__r.ExternalId__c FROM FinServ__AccountAccountRelation__c WHERE RecordType.DeveloperName = \'AgencySociety\' ';
        
        if(String.isNotBlank(agency)){
            query += 'AND FinServ__Account__r.ExternalId__c = \''+agency+'\' ';
        }
        
        if(String.isNotBlank(lastModifiedDate)){
            query += 'AND LastModifiedDate >= '+lastModifiedDate;
        }

    }
    
    public Database.QueryLocator start(Database.BatchableContext BC) {
        System.debug('query: '+query);
        return Database.getQueryLocator(query);
    }
    
    public void execute(Database.BatchableContext bc, List<FinServ__AccountAccountRelation__c> scope) {
        
        Set<String> agencyCode = new Set<String>();

        for(FinServ__AccountAccountRelation__c aar : scope){

            if(String.isNotBlank(aar.FinServ__Account__r.ExternalId__c)){
                agencyCode.add(aar.FinServ__Account__r.ExternalId__c);
            }

        }

        System.debug('agencyCode: '+agencyCode);

        List<Group> groupList = [SELECT Id, DeveloperName FROM Group WHERE DeveloperName IN :agencyCode];
        Map<String, Id> mapGroupId = new Map<String, Id>();
        for(Group g : groupList){
            mapGroupId.put(g.DeveloperName, g.Id);
        }

        System.debug('Groups: '+mapGroupId);

        List<FinServ__AccountAccountRelation__Share> shareToInsert = new List<FinServ__AccountAccountRelation__Share>();

        for(FinServ__AccountAccountRelation__c aar : scope){
            FinServ__AccountAccountRelation__Share newShare = new FinServ__AccountAccountRelation__Share();
            newShare.ParentId = aar.Id;
            newShare.UserOrGroupId = mapGroupId.get(aar.FinServ__Account__r.ExternalId__c);
            newShare.AccessLevel = 'Read';
            newShare.RowCause = Schema.AccountShare.RowCause.Manual;

            shareToInsert.add(newShare);
        }

        System.debug('shareToInsert: '+shareToInsert);

        if(!shareToInsert.isEmpty()) Database.insert(shareToInsert, false);
        
    }
    
    public void finish(Database.BatchableContext BC) {
        System.debug('Batch Process Finished.');
        if(concatenate) {
            Database.executeBatch(new GroupAssignPublicGroupBatch(concatenate), 200);
        }
    }
}