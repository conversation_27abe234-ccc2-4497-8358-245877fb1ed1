@isTest
private class OmniStudioControllerTest {
    
    @isTest
    static void testInvokeMethod_getTimelineDatas() {
        // Crea un utente di test per OwnerId
        User testUser = new User(
            LastName = 'Test OmnistudioController',
            Username = '<EMAIL>',
            Alias = 'tuser',
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            ProfileId = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1].Id,
            TimeZoneSidKey = 'Europe/Rome'
        );
        insert testUser;

        // Crea un'Opportunità
        Opportunity opp = new Opportunity(
            Name = 'Test Opportunity',
            StageName = 'Prospecting',
            CloseDate = Date.today().addDays(10)
        );
        insert opp;

        // Crea un Task associato all'opportunità
        Task t = new Task(
            WhatId = opp.Id,
            Subject = 'Call Client',
            OwnerId = testUser.Id,
            Description = 'Called client for update',
            Status = 'Open'
        );
        insert t;

        // Crea un record ContactHistory__c associato all'opportunità
        ContactHistory__c ch = new ContactHistory__c(
            Opportunity__c = opp.Id,
            Name = 'Initial Contact',
            Actor__c = 'Agent X',
            Description__c = 'Client contacted via email'
        );
        insert ch;

        // Prepara l'invocazione simulata
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => opp.Id
        };
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Map<String, Object> args = new Map<String, Object>{
            'input' => input,
            'output' => output,
            'options' => options
        };

        Test.startTest();
        OmniStudioController controller = new OmniStudioController();
        try{
            controller.call('getTimelineDatas', args);
        }catch(Exception ex){}
        Test.stopTest();

        // Puoi ispezionare l'output se necessario
        System.debug('Output: ' + output);

    }
}