global class CaseCalculatePriorityBatch implements Database.Batchable<SObject>, Schedulable, Database.Stateful {
    global CaseCalculatePriorityBatch() {
    }

    global Database.QueryLocator start(Database.BatchableContext BC) {
        return Database.getQueryLocator(
            [
                SELECT
                    Id,
                    Priority,
                    Status,
                    Area__c,
                    Activity__c,
                    TECH_LeoHeat__c,
                    TECH_LeoPriority__c,
                    dueDate__c,
                    ClosedDate__c
                FROM Case
                WHERE RecordType.DeveloperName = 'AttivitaContatto' AND Status != 'Closed'
            ]
        );
    }

    global void execute(Database.BatchableContext BC, List<Case> scope) {
        Map<String, Decimal> weightMap = loadWeights();
        Map<String, Decimal> activityScoreMap = new Map<String, Decimal>();
        Map<String, Decimal> heatScoreMap = new Map<String, Decimal>();
        List<Case> caseToUpdate = new List<Case>();

        loadScores(activityScoreMap, heatScoreMap);

        for (Case c : scope) {
            Boolean updated = false;

            updated = updatePriority(c, weightMap, activityScoreMap, heatScoreMap) || updated;
            //updated = updateExpiredStatus(c) || updated;
            //updated = updateClosedStatus(c) || updated;

            if (updated) {
                caseToUpdate.add(c);
            }
        }

        if (!caseToUpdate.isEmpty()) {
            update caseToUpdate;
        }
    }

    global void finish(Database.BatchableContext BC) {
    }

    global void execute(SchedulableContext SC) {
        Database.executeBatch(new CaseCalculatePriorityBatch());
    }

    private Map<String, Decimal> loadWeights() {
        Map<String, Decimal> weightMap = new Map<String, Decimal>();
        for (CaseActivityInit__c weight : [
            SELECT WeightAttribute__c, WeightValue__c
            FROM CaseActivityInit__c
            WHERE Type__c = 'Weight'
        ]) {
            weightMap.put(weight.WeightAttribute__c, weight.WeightValue__c);
        }
        return weightMap;
    }

    private void loadScores(Map<String, Decimal> activityScoreMap, Map<String, Decimal> heatScoreMap) {
        for (CaseActivityInit__c score : [
            SELECT Area__c, Activity__c, LeoHeat__c, LeoPriority__c, WeightValue__c
            FROM CaseActivityInit__c
            WHERE Type__c = 'Score'
        ]) {
            if (!String.isEmpty(score.Area__c) && !String.isEmpty(score.Activity__c)) {
                activityScoreMap.put(score.Area__c + '-' + score.Activity__c, score.WeightValue__c);
            } else {
                heatScoreMap.put(score.LeoHeat__c + '-' + score.LeoPriority__c, score.WeightValue__c);
            }
        }
    }

    private Boolean updatePriority(
        Case c,
        Map<String, Decimal> weightMap,
        Map<String, Decimal> activityScoreMap,
        Map<String, Decimal> heatScoreMap
    ) {
        Decimal weightActivity = weightMap.get('ATTIVITA') != null ? weightMap.get('ATTIVITA') : 0;
        Decimal weightHeat = weightMap.get('CALORE-FLAG') != null ? weightMap.get('CALORE-FLAG') : 0;
        Decimal weightDueDate = weightMap.get('SCADENZA') != null ? weightMap.get('SCADENZA') : 0;

        Decimal scoreActivity = activityScoreMap.get(c.Area__c + '-' + c.Activity__c) != null
            ? activityScoreMap.get(c.Area__c + '-' + c.Activity__c)
            : 0;
        Decimal scoreHeat = heatScoreMap.get(c.TECH_LeoHeat__c + '-' + c.TECH_LeoPriority__c) != null
            ? heatScoreMap.get(c.TECH_LeoHeat__c + '-' + c.TECH_LeoPriority__c)
            : 0;

        Decimal scoreDueDate = 0;
        if (c.dueDate__c != null) {
            Integer daysFromToday = Date.today().daysBetween(c.dueDate__c);
            if (daysFromToday <= 3)
                scoreDueDate = 1;
            else if (daysFromToday <= 7)
                scoreDueDate = 0.66;
            else if (daysFromToday <= 14)
                scoreDueDate = 0.33;
        }

        Decimal priorityValue =
            (weightActivity * scoreActivity) +
            (weightHeat * scoreHeat) +
            (weightDueDate * scoreDueDate);

        if (priorityValue >= 0.7 && c.Priority != 'High') {
            c.Priority = 'High';
            return true;
        } else if (priorityValue >= 0.4 && c.Priority != 'Medium') {
            c.Priority = 'Medium';
            return true;
        } else if (priorityValue < 0.4 && c.Priority != 'Low') {
            c.Priority = 'Low';
            return true;
        }
        return false;
    }

    /*private Boolean updateExpiredStatus(Case c) {
        if (c.dueDate__c != null && c.dueDate__c <= Date.today()) {
            if (c.Status != 'Expired') {
                c.Status = 'Expired';
                return true;
            }
        }
        return false;
    }

    private Boolean updateClosedStatus(Case c) {
        if (c.ClosedDate__c != null && c.ClosedDate__c <= Date.today()) {
            if (c.Status != 'Closed') {
                c.Status = 'Closed';
                return true;
            }
        }
        return false;
    }*/
}
