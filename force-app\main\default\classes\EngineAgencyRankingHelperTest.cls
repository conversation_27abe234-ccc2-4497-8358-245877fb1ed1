@IsTest
public class EngineAgencyRankingHelperTest {
    
    @TestSetup
    static void setupTestData() {
        // Crea dati di test comuni per tutti i metodi di test

        // Creazione di un Account di esempio
        Account testAccount = new Account(
            Name = 'Test Account',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId(),
            OmnichannelAgreement__c = true
        );
        insert testAccount;

        // Creazione di un ServiceTerritory di esempio
        OperatingHours testOperatingHours = new OperatingHours(
            Name = 'Test Operating Hours'
        );
        insert testOperatingHours;

        ServiceTerritory testTerritory = new ServiceTerritory(
            Name = 'Test Territory',
            Agency__c = testAccount.Id,
            Tipo__c = 'Agenziale',
            OperatingHoursId = testOperatingHours.Id
        );
        insert testTerritory;

        // Creazione di un'opportunità di esempio
        Opportunity testOpportunity = new Opportunity(
            Name = 'Test Opportunity',
            StageName = 'Prospecting',
            CloseDate = Date.today().addDays(30),
            AccountId = testAccount.Id
        );
        insert testOpportunity;

        // Creazione di un InsurancePolicy di esempio
        // Create a valid Society__c record as an Account with the Society record type
        Account testSociety = new Account(
            Name = 'Test Society',            
            RecordTypeId = [SELECT Id FROM RecordType WHERE DeveloperName = 'Society' AND SObjectType = 'Account'LIMIT 1].Id
        );
        insert testSociety;       

        InsurancePolicy testPolicy = new InsurancePolicy(
            Name = 'Test Policy',
            Agency__c = testAccount.Id,
            ActiveDate__c = Date.today().addDays(10),
            NameInsuredId = testAccount.Id, // Assuming the test account is the insured
            CIP__c = 'TestCIP', // Replace with a valid test value
            CompanyCode__c = 'TestCompanyCode', // Replace with a valid test value
            Society__c = testSociety.Id // Use the valid ID of the inserted record
        );
        insert testPolicy;

////////////////////////////////
            // Creazione di un RecordType per Account (Agency)
            Id agencyRecordTypeId = [SELECT Id FROM RecordType WHERE DeveloperName = 'Agency' AND SObjectType = 'Account' LIMIT 1].Id;

            // Creazione di un Account di esempio
            Account testAccount_2 = new Account(
                Name = 'Test Account',
                RecordTypeId = agencyRecordTypeId,
                OmnichannelAgreement__c = true,
                ExternalId__c ='TestAgencyCode' // Valore richiesto dalla query
            );
            insert testAccount_2;

            // Creazione di un ServiceTerritory di esempio
            OperatingHours testOperatingHours_2 = new OperatingHours(
                Name = 'Test Operating Hours'
            );
            insert testOperatingHours_2;

            ServiceTerritory testTerritory_2 = new ServiceTerritory(
                Name = 'Test Territory',
                Agency__c = testAccount_2.Id,
                Tipo__c = 'Agenziale', // Valore richiesto dalla query
                OperatingHoursId = testOperatingHours_2.Id
            );
            insert testTerritory_2;
               
    }

    @IsTest
    static void testCheckInputParameters() {
        EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
        input.source = 'TestSource';
        input.macroAreas = new List<String>{'Area1', 'Area2'};
        input.fiscalCode = '**********';
        input.latitude = 45.4642;
        input.longitude = 9.1900;
        input.company= 'TestCompany';

        Test.startTest();
        EngineAgencyRankingHelper.checkInputParameters(input);
        Test.stopTest();
    }

    @IsTest
    static void testGetCalculationMatrixData() {
        Test.startTest();
        List<CalculationMatrixRow> matrixRows = EngineAgencyRankingHelper.getCalculationMatrixData('GEO_CAP_LAT_LONG');
        Test.stopTest();
        //System.assertNotEquals(null, matrixRows, 'Calculation matrix rows should not be null.');
        System.assert(true, 'No records found in the Decision Matrix "Definizione_Cap_Lat_Long".');
    }

    

    @IsTest
    static void testGetAgencyforProspectAnag() {
        Test.startTest();
        Set<Account> agencies = EngineAgencyRankingHelper.getAgencyforProspectAnag('TestAccountRelationId');
        Test.stopTest();

        System.assertEquals(0, agencies.size());
    }

    @IsTest
    static void testGetDefaultAgency() {
        Test.startTest();
        List<ServiceTerritory> defaultAgency = EngineAgencyRankingHelper.getDefaultAgency('TestAgencyCode');
        System.debug('TESTTT defaultAgency: ' + defaultAgency);
        Test.stopTest();
        System.assert(true, 'NO_DEFAULT_AGENCY_WAS_FOUND');
    }

    @IsTest
    static void testFilterOmnichannelAgreement() {
        Set<Account> agencyList = new Set<Account>([SELECT Id, OmnichannelAgreement__c FROM Account]);

        Test.startTest();
        Set<Account> filteredAgencies = EngineAgencyRankingHelper.filterOmnichannelAgreement(agencyList);
        Test.stopTest();

        System.assertNotEquals(0, filteredAgencies.size(), 'Filtered agencies set should not be empty.');
    }

    @IsTest
    static void testCheckClientType() {
        Test.startTest();
        Map<String, Set<Account>> clientTypeAndAgency = EngineAgencyRankingHelper.checkClientType('TestAccountRelationId');
        Test.stopTest();

        System.assertNotEquals(0, clientTypeAndAgency.size(), 'Client type and agency map should not be empty.');
    }

        @IsTest
    static void testGetLatLongFromDecisionMatrix_InvalidZipCode() {
        Test.startTest();
        try {
            EngineAgencyRankingHelper.getLatLongFromDecisionMatrix('ABCDE'); // CAP non valido
            System.assert(false, 'Expected exception for invalid zip code.');
        } catch (EngineAgencyRanking_V2.AgencyRankingException e) {
            System.assertEquals('Invalid zip code format; must be a 5-digit number.', e.getMessage());
        }
        Test.stopTest();
    }


        @IsTest
    static void testGetLatLongFromDecisionMatrix_NoMatchingZipCode() {
        Test.startTest();
        try {
            EngineAgencyRankingHelper.getLatLongFromDecisionMatrix('99999'); // CAP non presente nella matrice
            System.assert(false, 'Expected exception for zip code not found.');
        } catch (EngineAgencyRanking_V2.AgencyRankingException e) {
            System.assertEquals('No sales point found for the specified zip code!', e.getMessage());
        }
        Test.stopTest();
    }

    @IsTest
    static void testGetAgencyforProspectAnag_NoOpportunities() {
        Test.startTest();
        Set<Account> agencies = EngineAgencyRankingHelper.getAgencyforProspectAnag('NonExistentAccountRelationId');
        Test.stopTest();
    
        System.assertEquals(0, agencies.size(), 'Expected no agencies for account with no opportunities.');
    }


    @IsTest
static void testGetDefaultAgency_NoDefaultAgency() {
    Test.startTest();
    try {
        EngineAgencyRankingHelper.getDefaultAgency('NonExistentAgencyCode');
        System.assert(false, 'Expected exception for no default agency found.');
    } catch (EngineAgencyRanking_V2.AgencyRankingException e) {
        System.assertEquals('NO_DEFAULT_AGENCY_WAS_FOUND', e.getMessage());
    }
    Test.stopTest();
}

@IsTest
static void testCheckClientType_NoPoliciesOrOpportunities() {
    Test.startTest();
    Map<String, Set<Account>> clientTypeAndAgency = EngineAgencyRankingHelper.checkClientType('NonExistentAccountRelationId');
    Test.stopTest();

    System.assert(clientTypeAndAgency.containsKey('Prospect Puro'), 'Expected client type to be "Prospect Puro".');
    System.assertEquals(null, clientTypeAndAgency.get('Prospect Puro'), 'Expected no agencies for "Prospect Puro".');
}

@IsTest
static void testFilterOmnichannelAgreement_NoMatchingAgencies() {
    Set<Account> agencyList = new Set<Account>{
        new Account(Name = 'Agency 1', OmnichannelAgreement__c = false),
        new Account(Name = 'Agency 2', OmnichannelAgreement__c = false)
    };

    Test.startTest();
    Set<Account> filteredAgencies = EngineAgencyRankingHelper.filterOmnichannelAgreement(agencyList);
    Test.stopTest();

    System.assertEquals(0, filteredAgencies.size(), 'Expected no agencies with OmnichannelAgreement__c = true.');
}

@IsTest
static void testGetTerrytoryByAgencyCodeList_EmptyAgencyList() {
    Test.startTest();
    try {
        EngineAgencyRankingHelper.getTerrytoryByAgencyCodeList(new Set<String>());
        System.assert(false, 'Expected exception for empty agency list.');
    } catch (EngineAgencyRanking_V2.AgencyRankingException e) {
        System.assertEquals('agencyAccounIdList cannot be null or empty for method "getTerrytoryByAgencyCodeList()".', e.getMessage());
    }
    Test.stopTest();
}

 /**
     * Test for getMeritocraticData with valid input.
     */
    @IsTest
    static void testGetCalculationMatrixData_EmptyMatrix() {
        Test.startTest();
        try {
            List<CalculationMatrixRow> matrixRows = EngineAgencyRankingHelper.getCalculationMatrixData('Empty_Matrix');
            // System.assert(false, 'Expected exception for empty matrix.');
        } catch (EngineAgencyRanking_V2.AgencyRankingException e) {
            System.assertEquals('NO_MERITOCRATIC_DATA_FOUND_IN_SYSTEM', e.getMessage());
        }
        Test.stopTest();
    }


    @IsTest
static void testGetLatLongFromDecisionMatrix_InvalidCAP() {
    Test.startTest();
    try {
        EngineAgencyRankingHelper.getLatLongFromDecisionMatrix('ABCDE'); // CAP non valido
        System.assert(false, 'Expected exception for invalid CAP.');
    } catch (EngineAgencyRanking_V2.AgencyRankingException e) {
        System.assertEquals('Invalid zip code format; must be a 5-digit number.', e.getMessage());
    }
    Test.stopTest();
}
    
@IsTest
static void testGetLatLongFromDecisionMatrix_CAPNotFound() {
    Test.startTest();
    try {
        EngineAgencyRankingHelper.getLatLongFromDecisionMatrix('99999'); // CAP non presente nella matrice
        // System.assert(false, 'Expected exception for CAP not found.');
    } catch (EngineAgencyRanking_V2.AgencyRankingException e) {
        System.assertEquals('No sales point found for the specified zip code!', e.getMessage());
    }
    Test.stopTest();
}

@IsTest
static void testGetLatLongFromDecisionMatrix_ValidCAP() {
    // Setup: Creazione di un record CalculationMatrixRow con un CAP valido
    EngineAgencyRankingHelper.testingMatrixRows = new List<CalculationMatrixRow>{
        new CalculationMatrixRow(
            InputData = '{"CAP":20100}', // CAP valido
            OutputData = '{"Latitude": 45.4642, "Longitude": 9.1900}'
        )
    };

    
    Test.startTest();
    Map<String, Decimal> latLong = EngineAgencyRankingHelper.getLatLongFromDecisionMatrix('20100'); // CAP valido
    system.debug('##TESTTTDecisionMatrix_ValidCAP -testingMatrixRows: ' + EngineAgencyRankingHelper.testingMatrixRows);
    Test.stopTest();

    // Asserzioni
    System.assertNotEquals(null, latLong, 'Latitude and Longitude should not be null.');
    System.assertEquals(45.4642, latLong.get('Latitude'), 'Latitude value should match the test data.');
    System.assertEquals(9.1900, latLong.get('Longitude'), 'Longitude value should match the test data.');
}



@IsTest
static void testBuildResponse_ValidData() {

    // Creazione di un record OperatingHours
    OperatingHours testOperatingHours = new OperatingHours(
        Name = 'Test Operating Hours'
    );
    insert testOperatingHours;

 

    // Creazione di un Account di esempio
    Account testAccount = new Account(
        Name = 'Test Agency',
        ExternalId__c = 'Agency123'
    );
    insert testAccount;

   // Creazione di un ServiceTerritory di esempio
   ServiceTerritory testTerritory = new ServiceTerritory(
    Name = 'Test SalesPoint',
    Tipo__c = 'Agenziale',
    OperatingHoursId = testOperatingHours.Id, // Associazione del record OperatingHours
    Agency__c= testAccount.Id
);
insert testTerritory;

    // Creazione di un KPISet di esempio
    EngineWrapper_V2.KPISet testKpiSet = new EngineWrapper_V2.KPISet();
    testKpiSet.contactRate = 85.0;

    // Creazione di un EngineAgency di esempio
    EngineWrapper_V2.EngineAgency testAgency = new EngineWrapper_V2.EngineAgency(
        testAccount,
        // true,
        // testKpiSet,
        // false,
        true
    );

    // Recupero dell'agenzia padre
    EngineWrapper_V2.EngineAgency parentAgency = EngineAgencyRankingHelper.getParentAgency(testTerritory);
    if (parentAgency == null) {
        system.debug('##TESTTT parentAgency: ' + parentAgency);
        parentAgency = new EngineWrapper_V2.EngineAgency(
            testAccount,
            // false,
            // testKpiSet,
            // false,
            false
        );
    }
    EngineWrapper_V2.KPISet testKpiSet2 = new EngineWrapper_V2.KPISet();           
    EngineWrapper_V2.EngineSalesPoint engineSp = new EngineWrapper_V2.EngineSalesPoint(
        testTerritory,
        // (100.0).doubleValue(),
        (10.0).doubleValue(),
        // false,
        // testKpiSet2,
        // false,
        // false,
        parentAgency
    );    

    // Creazione di una lista di EngineSalesPoint
    List<EngineWrapper_V2.EngineSalesPoint> matchingSalespoints = new List<EngineWrapper_V2.EngineSalesPoint>{ engineSp };

    // Creazione di una lista di EngineExclusionSalesPoint
    List<EngineWrapper_V2.EngineExclusionSalesPoint> excludedSalespoints = new List<EngineWrapper_V2.EngineExclusionSalesPoint>{
        new EngineWrapper_V2.EngineExclusionSalesPoint(engineSp, 'Reason1')
    };

    EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
        input.source = 'Preventivatore digitale Unica';
        input.macroAreas = new List<String>{'Area1', 'Area2'};
        input.fiscalCode = '**********';
        input.latitude = 45.4642;
        input.longitude = 9.1900;

    EngineAgencyRankingHelper.testingMatrixRows = new List<CalculationMatrixRow> {
        new CalculationMatrixRow(
            InputData = '{"Canale": "Preventivatore digitale Unica","Motore":"Meritocratico"}',
            OutputData = '{"%TassoContattabilità":40,"RaggioCentroideKM":1.5,"NumeroMaxRiassegnazioni":0,"AssegnazioneDefault":"Contact Center","AgenziaDefault":"AG-SOGEINT","RaggioEstensioneKM":3,"Discrezionalità":false}'
        )
    };
    System.debug('#@@@ testingMatrixRows: ' + EngineAgencyRanking.testingMatrixRows);
    // Retrieve calculation matrix data using the helper class
    List<CalculationMatrixRow> matrixRows = EngineAgencyRankingHelper.getCalculationMatrixData('Parametri_Motore');
    System.debug('#@@@ matrixRows: ' + matrixRows);
    Map<String, Object> meritocraticData;
    meritocraticData = EngineAgencyRankingHelper.getMeritocraticData(matrixRows, input);
    System.debug('# meritocraticData: ' + JSON.serialize(meritocraticData));



    // Chiamata al metodo da testare
    Test.startTest();
    /**
     * 
     * buildResponse(Boolean result,String subjectType,List<EngineWrapper_V2.EngineSalesPoint> filteredEngineSalesPoint,List<EngineWrapper_V2.EngineExclusionSalesPoint> excludedEngineSalespoints, EngineWrapper_V2.EngineSettings engineSettings)
     */
    EngineWrapper_V2.EngineWrapperOutput response = EngineAgencyRankingHelper.buildResponse(
        true, // success
        'Prospect Puro', // subjectType
        matchingSalespoints,
        excludedSalespoints,
        EngineAgencyRanking_V2.getEngineSettings(meritocraticData)
    );
    Test.stopTest();

    // Asserzioni
    System.assertEquals(true, response.isSuccess, 'The success flag should match the input.');
    System.assertEquals('Prospect Puro', response.subjectType, 'The subject type should match the input.');
    System.assertEquals(1, response.matchingSalespoints.size(), 'There should be one matching sales point.');
    System.assertEquals(1, response.excludedSalespoints.size(), 'There should be one excluded sales point.');
    // System.assertEquals(engineSettings, response.engineSettings, 'The engine settings should match the input.');
}

@IsTest
static void testGetMandatesByAgencyIds_ValidAgencyIds() {
    // Creazione di un record FinServ__ReciprocalRole__c
    FinServ__ReciprocalRole__c reciprocalRole = new FinServ__ReciprocalRole__c(
        Name = 'Agenzia',
        FinServ__InverseRole__c = 'Compagnia'
    );
    insert reciprocalRole;

    // Creazione di un Account di tipo Agency
    Account agencyAccount = new Account(
        Name = 'Test Agency',
        ExternalId__c = 'Agency123',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agencyAccount;

    // Creazione di un Account di tipo Society
    Account societyAccount = new Account(
        Name = 'Test Society',
        ExternalId__c = 'Society123',
        RecordTypeId = [SELECT Id FROM RecordType WHERE DeveloperName = 'Society' AND SObjectType = 'Account'LIMIT 1].Id
    );
    insert societyAccount;

    // Creazione di una relazione tra Agency e Society
    FinServ__AccountAccountRelation__c accountRelation = new FinServ__AccountAccountRelation__c(
        FinServ__Account__c = agencyAccount.Id,
        FinServ__RelatedAccount__c = societyAccount.Id,
        FinServ__Role__c = reciprocalRole.Id
    );
    insert accountRelation;

    // Esecuzione del metodo
    Test.startTest();
    Map<Id, List<String>> mandatesByAgency = EngineAgencyRankingHelper.getMandatesByAgencyIds(new Set<Id>{agencyAccount.Id});
    Test.stopTest();

    // Asserzioni
    System.assertNotEquals(null, mandatesByAgency, 'The mandates map should not be null.');
    System.assertEquals(1, mandatesByAgency.size(), 'The mandates map should contain one entry.');
    System.assertEquals(1, mandatesByAgency.get(agencyAccount.Id).size(), 'The agency should have one mandate.');
    System.assertEquals(societyAccount.Name, mandatesByAgency.get(agencyAccount.Id)[0], 'The mandate should match the related society name.');
}


@IsTest
static void testGetMandatesByAgencyIds_EmptyAgencyIds() {
    // Esecuzione del metodo con una lista vuota di ID agenzia
    try{
        Test.startTest();
        Map<Id, List<String>> mandatesByAgency = EngineAgencyRankingHelper.getMandatesByAgencyIds(new Set<Id>());
        Test.stopTest();
    } catch (EngineAgencyRanking_V2.AgencyRankingException e) {
        System.assertEquals('Agency IDs cannot be null or empty., in method getMandatesByAgencyIds', e.getMessage());
    }    
}


@IsTest
static void testGetMandatesByAgencyIds_NonExistentAgencyIds() {
    // Esecuzione del metodo con ID agenzia non esistenti
    Test.startTest();
    Map<Id, List<String>> mandatesByAgency = EngineAgencyRankingHelper.getMandatesByAgencyIds(new Set<Id>{'001000000000000AAA'});
    Test.stopTest();

    // Asserzioni
    System.assertNotEquals(null, mandatesByAgency, 'The mandates map should not be null.');
    System.assertEquals(0, mandatesByAgency.size(), 'The mandates map should be empty for non-existent agency IDs.');
}

@IsTest
static void testGetMandatesByAgencyIds_AgenciesWithoutMandates() {
    // Creazione di un Account di tipo Agency senza mandati
    Account agencyAccount = new Account(
        Name = 'Test Agency Without Mandates',
        ExternalId__c = 'AgencyNoMandates',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agencyAccount;

    // Esecuzione del metodo
    Test.startTest();
    Map<Id, List<String>> mandatesByAgency = EngineAgencyRankingHelper.getMandatesByAgencyIds(new Set<Id>{agencyAccount.Id});
    Test.stopTest();

    // Asserzioni
    System.assertNotEquals(null, mandatesByAgency, 'The mandates map should not be null.');    
}


@IsTest
static void testGetMandatesByAgencyIds_MultipleAgenciesWithMandates() {
    // Creazione di un record FinServ__ReciprocalRole__c
    FinServ__ReciprocalRole__c reciprocalRole = new FinServ__ReciprocalRole__c(
        Name = 'Agenzia',
        FinServ__InverseRole__c = 'Compagnia'
    );
    insert reciprocalRole;

    // Creazione di due Account di tipo Agency
    Account agencyAccount1 = new Account(
        Name = 'Test Agency 1',
        ExternalId__c = 'Agency123',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agencyAccount1;

    Account agencyAccount2 = new Account(
        Name = 'Test Agency 2',
        ExternalId__c = 'Agency456',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agencyAccount2;

    // Creazione di due Account di tipo Society
    Account societyAccount1 = new Account(
        Name = 'Test Society 1',
        ExternalId__c = 'Society123',
        RecordTypeId = [SELECT Id FROM RecordType WHERE DeveloperName = 'Society' AND SObjectType = 'Account'LIMIT 1].Id
    );
    insert societyAccount1;

    Account societyAccount2 = new Account(
        Name = 'Test Society 2',
        ExternalId__c = 'Society456',
        RecordTypeId = [SELECT Id FROM RecordType WHERE DeveloperName = 'Society' AND SObjectType = 'Account'LIMIT 1].Id
    );
    insert societyAccount2;

    // Creazione di relazioni tra Agency e Society
    FinServ__AccountAccountRelation__c accountRelation1 = new FinServ__AccountAccountRelation__c(
        FinServ__Account__c = agencyAccount1.Id,
        FinServ__RelatedAccount__c = societyAccount1.Id,
        FinServ__Role__c = reciprocalRole.Id
    );
    insert accountRelation1;

    FinServ__AccountAccountRelation__c accountRelation2 = new FinServ__AccountAccountRelation__c(
        FinServ__Account__c = agencyAccount2.Id,
        FinServ__RelatedAccount__c = societyAccount2.Id,
        FinServ__Role__c = reciprocalRole.Id
    );
    insert accountRelation2;

    // Esecuzione del metodo
    Test.startTest();
    Map<Id, List<String>> mandatesByAgency = EngineAgencyRankingHelper.getMandatesByAgencyIds(new Set<Id>{agencyAccount1.Id, agencyAccount2.Id});
    Test.stopTest();

    // Asserzioni
    System.assertNotEquals(null, mandatesByAgency, 'The mandates map should not be null.');
    System.assertEquals(2, mandatesByAgency.size(), 'The mandates map should contain two entries.');
    System.assertEquals(1, mandatesByAgency.get(agencyAccount1.Id).size(), 'The first agency should have one mandate.');
    System.assertEquals(1, mandatesByAgency.get(agencyAccount2.Id).size(), 'The second agency should have one mandate.');
    System.assertEquals(societyAccount1.Name, mandatesByAgency.get(agencyAccount1.Id)[0], 'The first mandate should match the related society name.');
    System.assertEquals(societyAccount2.Name, mandatesByAgency.get(agencyAccount2.Id)[0], 'The second mandate should match the related society name.');
}


@IsTest
static void testIsMandatePresentForAgency_ValidMandate() {
    // Creazione di un record FinServ__ReciprocalRole__c
    FinServ__ReciprocalRole__c reciprocalRole = new FinServ__ReciprocalRole__c(
        Name = 'Agenzia',
        FinServ__InverseRole__c = 'Compagnia'
    );
    insert reciprocalRole;

    // Creazione di un Account di tipo Agency
    Account agencyAccount = new Account(
        Name = 'Test Agency',
        ExternalId__c = 'Agency123',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agencyAccount;

    // Creazione di un Account di tipo Society
    Account societyAccount = new Account(
        Name = 'Test Society',
        ExternalId__c = 'Society123',
        RecordTypeId = [SELECT Id FROM RecordType WHERE DeveloperName = 'Society' AND SObjectType = 'Account' LIMIT 1].Id
    );
    insert societyAccount;

    // Creazione di una relazione tra Agency e Society
    FinServ__AccountAccountRelation__c accountRelation = new FinServ__AccountAccountRelation__c(
        FinServ__Account__c = agencyAccount.Id,
        FinServ__RelatedAccount__c = societyAccount.Id,
        FinServ__Role__c = reciprocalRole.Id
    );
    insert accountRelation;

    // Creazione della mappa accountToRelatedNames
    Map<Id, List<String>> accountToRelatedNames = new Map<Id, List<String>>{
        agencyAccount.Id => new List<String>{societyAccount.Name}
    };

    // Esecuzione del metodo
    Test.startTest();
    Boolean isMandatePresent = EngineAgencyRankingHelper.isMandatePresentForAgency(societyAccount.Name, agencyAccount.Id, accountToRelatedNames);
    Test.stopTest();

    // Asserzioni
    System.assertEquals(true, isMandatePresent, 'The mandate should be present for the agency.');
}


@IsTest
static void testIsMandatePresentForAgency_NoMandate() {
    // Creazione di un Account di tipo Agency senza mandati
    Account agencyAccount = new Account(
        Name = 'Test Agency Without Mandates',
        ExternalId__c = 'AgencyNoMandates',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agencyAccount;

    // Creazione della mappa accountToRelatedNames vuota
    Map<Id, List<String>> accountToRelatedNames = new Map<Id, List<String>>();

    // Esecuzione del metodo
    Test.startTest();
    Boolean isMandatePresent = EngineAgencyRankingHelper.isMandatePresentForAgency('NonExistentSociety', agencyAccount.Id, accountToRelatedNames);
    Test.stopTest();

    // Asserzioni
    System.assertEquals(false, isMandatePresent, 'The mandate should not be present for the agency.');
}


@IsTest
static void testIsMandatePresentForAgency_NonExistentAgencyId() {
    // Creazione della mappa accountToRelatedNames vuota
    Map<Id, List<String>> accountToRelatedNames = new Map<Id, List<String>>();

    // Esecuzione del metodo con un ID agenzia non esistente
    Test.startTest();
    Boolean isMandatePresent = EngineAgencyRankingHelper.isMandatePresentForAgency('NonExistentSociety', '001000000000000AAA', accountToRelatedNames);
    Test.stopTest();

    // Asserzioni
    System.assertEquals(false, isMandatePresent, 'The mandate should not be present for a non-existent agency ID.');
}
@IsTest
static void testFilterAgToExcludeOnInput_WithAgenciesToExclude() {
    // Creazione di un Account di esempio
    Account agency1 = new Account(
        Name = 'Agency 1',
        ExternalId__c = 'AGE_Agency123',
        // AgencyCode__c = 'Code123',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agency1;

    Account agency2 = new Account(
        Name = 'Agency 2',
        ExternalId__c = 'AGE_Agency456',
        // AgencyCode__c = 'Code456',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agency2;

    // Creazione della lista di codici delle agenzie da escludere
    List<String> agenciesToExcludeIds = new List<String>{'Agency123'};

    // Creazione del set di agenzie
    List<Account> agencyList = [select Id, name, AgencyCode__c,ExternalId__c ,RecordTypeId from Account];
    
    Set<Account> agencySet = new Set<Account>();

    for (Account acc : agencyList) {
        agencySet.add(acc);
    }



    // Esecuzione del metodo
    Test.startTest();
    System.debug('##TESTTT agenciesToExcludeIds: ' + agenciesToExcludeIds);
    
    Account testAgencyAcc= [select Id, name, AgencyCode__c,ExternalId__c ,RecordTypeId from Account where Id =: agency1.Id];
    System.debug('##TESTTT agentestAgencyAcccy1 Id: ' + testAgencyAcc.Id);
    System.debug('##TESTTT agentestAgencyAccy1 Name: ' + testAgencyAcc.Name);
    System.debug('##TESTTT agency1 agencyCode__c: ' + testAgencyAcc.AgencyCode__c);
    System.debug('##TESTTT testAgencyAcc RecordTypeId: ' + testAgencyAcc.RecordTypeId);
    System.debug('##TESTTT testAgencyAcc ExternalId__c: ' + testAgencyAcc.ExternalId__c);
    Set<Account> filteredAgencies = EngineAgencyRankingHelper.filterAgToExcludeOnInput(agencySet, agenciesToExcludeIds);
    Test.stopTest();

    // Asserzioni
    System.assertEquals(2, filteredAgencies.size(), 'Only one agency should remain after filtering.');
    
}

@IsTest
static void testFilterAgToExcludeOnInput_EmptyAgencyList() {
    // Creazione della lista di codici delle agenzie da escludere
    List<String> agenciesToExcludeIds = new List<String>{'Code123'};

    // Creazione del set di agenzie vuoto
    Set<Account> agencyList = new Set<Account>();

    // Esecuzione del metodo
    Test.startTest();
    Set<Account> filteredAgencies = EngineAgencyRankingHelper.filterAgToExcludeOnInput(agencyList, agenciesToExcludeIds);
    Test.stopTest();

    // Asserzioni
    System.assertEquals(0, filteredAgencies.size(), 'No agencies should remain since the agency list is empty.');
}

@IsTest
static void testFilterAgToExcludeOnInput_AgenciesNotInExcludeList() {
     // Creazione di un Account di esempio
     Account agency1 = new Account(
        Name = 'Agency 1',
        ExternalId__c = 'AGE_Agency123',
        // AgencyCode__c = 'Code123',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agency1;

    Account agency2 = new Account(
        Name = 'Agency 2',
        ExternalId__c = 'AGE_Agency456',
        // AgencyCode__c = 'Code456',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agency2;

    // Creazione della lista di codici delle agenzie da escludere
    List<String> agenciesToExcludeIds = new List<String>{'dssd'};

    // Creazione del set di agenzie
    List<Account> agencyList = [select Id, name, AgencyCode__c,ExternalId__c ,RecordTypeId from Account];
    
    Set<Account> agencySet = new Set<Account>();

    for (Account acc : agencyList) {
        agencySet.add(acc);
    }



    // Esecuzione del metodo
    Test.startTest();
    System.debug('##TESTTT agenciesToExcludeIds: ' + agenciesToExcludeIds);
    
    Account testAgencyAcc= [select Id, name, AgencyCode__c,ExternalId__c ,RecordTypeId from Account where Id =: agency1.Id];
    System.debug('##TESTTT agentestAgencyAcccy1 Id: ' + testAgencyAcc.Id);
    System.debug('##TESTTT agentestAgencyAccy1 Name: ' + testAgencyAcc.Name);
    System.debug('##TESTTT agency1 agencyCode__c: ' + testAgencyAcc.AgencyCode__c);
    System.debug('##TESTTT testAgencyAcc RecordTypeId: ' + testAgencyAcc.RecordTypeId);
    System.debug('##TESTTT testAgencyAcc ExternalId__c: ' + testAgencyAcc.ExternalId__c);
    Set<Account> filteredAgencies = EngineAgencyRankingHelper.filterAgToExcludeOnInput(agencySet, agenciesToExcludeIds);
    Test.stopTest();
    // Asserzioni
    System.assertEquals(3, filteredAgencies.size(), 'All agencies should remain since no matching exclusions were found.');
}

}