@isTest
public class CollaborativoControllerTest {
    @testSetup
    static void setupTestData() {

        // Recupera il RecordTypeId per 'Agency' utilizzando Schema.SObjectType
        Id agencyRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId();

        // Crea un Account di test
        Account testAccount = new Account(
            Name = 'Test Account',
            RecordTypeId = agencyRecordTypeId);

        insert testAccount;

        // Crea un User di test
        User testUser = new User(
            FirstName = 'Test',
            LastName = 'User',
            Alias = 'tuser',
            Email = '<EMAIL>',
            Username = '<EMAIL>.' + System.currentTimeMillis(),
            CommunityNickname = 'testuser',
            ProfileId = [SELECT Id FROM Profile WHERE Name = 'System Administrator' LIMIT 1].Id,
            TimeZoneSidKey = 'America/Los_Angeles',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            IdAzienda__c = testAccount.Id
        );
        insert testUser;

        // Crea un utente con profilo Unipol Standard User
        User unipolStandardUser = new User(
            FirstName = 'Unipol',
            LastName = 'StandardUser',
            Alias = 'ustduser',
            Email = '<EMAIL>',
            Username = '<EMAIL>.' + System.currentTimeMillis(),
            CommunityNickname = 'unipolstandarduser',
            ProfileId = [SELECT Id FROM Profile WHERE Name = 'Unipol Standard User' LIMIT 1].Id,
            TimeZoneSidKey = 'Europe/Rome',
            LocaleSidKey = 'it_IT',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'it',
            IdAzienda__c = testAccount.Id
        );
        insert unipolStandardUser;

      

        // Dichiarazione di testGroupObject
        Group testGroupObject = new Group(
            Name = 'Test Group Object',
            DeveloperName = 'Test_Group_Object',
            Type = 'Regular'
        );

        insert testGroupObject;

        System.runAs(testUser) {
            // Crea un GroupMember di test
            GroupMember testGroupMember = new GroupMember(
                GroupId = testGroupObject.Id,
                UserOrGroupId = unipolStandardUser.Id
            );
            insert testGroupMember;
        }

          // Crea un Group__c di test
          Group__c testGroup = new Group__c(
            Name = 'Test Group',
            Agenzia__c = testAccount.Id,
            RecordTypeId = Schema.SObjectType.Group__c.getRecordTypeInfosByDeveloperName().get('UserGroup').getRecordTypeId(),
            PublicGroupId__c = testGroupObject.Id
        );
        insert testGroup;
    }

    @isTest
    static void testLoadData() {
        User testUser = [SELECT Id, IdAzienda__c FROM User WHERE Profile.Name = 'Unipol Standard User' AND Email = '<EMAIL>' LIMIT 1];
        System.debug('Test User: ' + testUser);
        Map<String, Object> inputMap = new Map<String, Object>{ 'UserId' => testUser.Id };
        Map<String, Object> outMap = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        CollaborativoController.loadData('loadData', inputMap, outMap, options);


    }

    @isTest
    static void testPreAddGroup() {
        User testUser = [SELECT Id FROM User LIMIT 1];
        Map<String, Object> inputMap = new Map<String, Object>{ 'UserId' => testUser.Id };
        Map<String, Object> outMap = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        CollaborativoController.preAddGroup('preAddGroup', inputMap, outMap, options);


    }

    @isTest
    static void testInsertNewGroup() {
        // Create a new active user for runAs
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        Profile p = [SELECT Id FROM Profile WHERE Name = 'Unipol Standard User' LIMIT 1];
        User runAsUser = new User(
            FirstName = 'RunAs',
            LastName = 'User',
            Alias = 'runas',
            Email = '<EMAIL>',
            Username = '<EMAIL>.' + System.currentTimeMillis(),
            CommunityNickname = 'runasuser',
            ProfileId = p.Id,
            TimeZoneSidKey = 'Europe/Rome',
            LocaleSidKey = 'it_IT',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'it',
            IdAzienda__c = testAccount.Id
        );
        insert runAsUser;

        Map<String, Object> inputMap = new Map<String, Object>{
            'UserId' => runAsUser.Id,
            'GroupName' => 'New Test Group',
            'Descrizione' => 'Test Description',
            'ListUser' => new List<Map<String, Object>>{
                new Map<String, Object>{ 'Id' => runAsUser.Id, 'Name' => 'Test User' }
            }
        };
        Map<String, Object> outMap = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        System.runAs(runAsUser) {
            // Assicurati che l'utente abbia i permessi necessari
            CollaborativoController.insertNewgroup('insertNewgroup', inputMap, outMap, options);
        }
        Test.stopTest();

    }

    @isTest
    static void testInsertNewGroupAll() {
        // Create a new active user for runAs
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        Profile p = [SELECT Id FROM Profile WHERE Name = 'Unipol Standard User' LIMIT 1];
        User runAsUser = new User(
            FirstName = 'RunAs',
            LastName = 'User',
            Alias = 'runas',
            Email = '<EMAIL>',
            Username = '<EMAIL>.' + System.currentTimeMillis(),
            CommunityNickname = 'runasuser',
            ProfileId = p.Id,
            TimeZoneSidKey = 'Europe/Rome',
            LocaleSidKey = 'it_IT',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'it',
            IdAzienda__c = testAccount.Id
        );
        insert runAsUser;

        Map<String, Object> inputMap = new Map<String, Object>{
            'UserId' => runAsUser.Id,
            'GroupName' => 'New Test Group',
            'Descrizione' => 'Test Description',
            'ListUser' => new List<Map<String, Object>>{
                new Map<String, Object>{ 'Id' => runAsUser.Id, 'Name' => 'RunAs User' }
            },
            'SelectAll' => true
        };
        Map<String, Object> outMap = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Test.startTest();
        System.runAs(runAsUser) {
            // Assicurati che l'utente abbia i permessi necessari
            CollaborativoController.insertNewgroup('insertNewgroup', inputMap, outMap, options);
        }
        
        Test.stopTest();

    }

    @isTest
    static void testModifyGroup() {
        Group__c testGroup = [SELECT Id, PublicGroupId__c FROM Group__c LIMIT 1];
        User testUser = [SELECT Id FROM User LIMIT 1];
        Map<String, Object> inputMap = new Map<String, Object>{
            'GroupId' => testGroup.Id,
            'GroupName' => 'Modified Test Group',
            'Description' => 'Modified Description',
            'ListUser' => new List<String>{ testUser.Id }
        };
        Map<String, Object> outMap = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        CollaborativoController.modifyGroup('modifyGroup', inputMap, outMap, options);

    }

    @isTest
    static void testDeleteGroup() {
        Group__c testGroup = [SELECT Id, PublicGroupId__c FROM Group__c LIMIT 1];
        Map<String, Object> inputMap = new Map<String, Object>{
            'GroupId' => testGroup.Id,
            'PublicGroupId' => testGroup.PublicGroupId__c
        };
        Map<String, Object> outMap = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        CollaborativoController.deleteGroup('deleteGroup', inputMap, outMap, options);

    }

    @isTest
    static void testGetListUserAdded() {
        Group__c testGroup = [SELECT Id FROM Group__c LIMIT 1];
        User testUser = [SELECT Id FROM User LIMIT 1];
        List<CollaborativoController.UserAdded> result = CollaborativoController.getListUserAdded(testGroup.Id, testUser.Id);

    }

    @isTest
    static void testCreateCustomGroupAsync() {
        // Recupera i dati di test
        Account testAccount = [SELECT Id, Name FROM Account LIMIT 1];
        Group testGroup = [SELECT Id FROM Group LIMIT 1];

        // Imposta i parametri per il metodo
        String groupName = 'Test Group Name';
        String descrizione = 'Test Description';
        String externalId = 'TestExternalId';
        Id accountId = testAccount.Id;
        Id publicGroupId = testGroup.Id;

        // Chiamata al metodo asincrono
        Test.startTest();
        CollaborativoController.createCustomGroupAsync(groupName, descrizione, externalId, accountId, publicGroupId);
        Test.stopTest();
    }

    @isTest
    static void testCallAndInvokeMethod() {
        // Setup dei dati di test
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        User testUser = [SELECT Id FROM User LIMIT 1];
        Group__c testGroup = [SELECT Id , PublicGroupId__c FROM Group__c LIMIT 1];

        // Test del metodo 'loadData'
        Map<String, Object> inputMap = new Map<String, Object>{ 'UserId' => testUser.Id };
        Map<String, Object> outputMap = new Map<String, Object>();
        Map<String, Object> optionsMap = new Map<String, Object>();

        Map<String, Object> args = new Map<String, Object>{
            'input' => inputMap,
            'output' => outputMap,
            'options' => optionsMap
        };

        Test.startTest();
        Object result = new CollaborativoController().call('loadData', args);
        


        // Test del metodo 'insertNewgroup'
        inputMap.put('GroupName', 'Test Group');
        inputMap.put('Descrizione', 'Test Description');
        inputMap.put('ListUser', new List<String>{ testUser.Id });

        args.put('input', inputMap);

        
        result = new CollaborativoController().call('insertNewgroup', args);
        


        // Test del metodo 'modifyGroup'
        inputMap.put('GroupId', testGroup.Id);
        inputMap.put('GroupName', 'Modified Test Group');
        inputMap.put('Description', 'Modified Description');
        inputMap.put('ListUser', new List<String>{ testUser.Id });

        args.put('input', inputMap);

        
        result = new CollaborativoController().call('modifyGroup', args);
        


        // Test del metodo 'deleteGroup'
        inputMap.put('GroupId', testGroup.Id);
        inputMap.put('PublicGroupId', testGroup.PublicGroupId__c);

        args.put('input', inputMap);

        
        result = new CollaborativoController().call('deleteGroup', args);
        Test.stopTest();

    }

    @isTest
	static void testNestedClasses() {
        CollaborativoController.GroupInfo groupInfo = new CollaborativoController.GroupInfo();
        groupInfo.NomeGruppo = 'TestGroupId';
        groupInfo.Descrizione = 'Test Group Name';
        groupInfo.NumeroMembri = 2;
        groupInfo.PublicGroupId = 'AAAA';

        CollaborativoController.GroupInfo groupInfo2 = new CollaborativoController.GroupInfo('00112233','TestGroupId', 'Test Group Name', 2, Date.today() ,'AAAA');
        
        

        CollaborativoController.UserAdded userAdded = new CollaborativoController.UserAdded();
        userAdded.Id = 'TestUserId';
        userAdded.Nome = 'Test User Name';

        CollaborativoController.UserWrapper userWrapper = new CollaborativoController.UserWrapper();
        userWrapper.Id = 'TestUserId';
        userWrapper.Name = 'Test User Name';

        CollaborativoController.UserWrapper userWrapper2 = new CollaborativoController.UserWrapper('112233', 'TestName');
    }
}