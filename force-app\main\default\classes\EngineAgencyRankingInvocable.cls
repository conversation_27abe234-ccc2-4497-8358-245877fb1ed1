/******************************************************************************************************
* <AUTHOR>
* @description    Agency Engine class for the ranking of the existing agencies enabled for flow actions
* @date           2024-03-05
* @group          Agency Engine
*******************************************************************************************************/
public without sharing class EngineAgencyRankingInvocable {
    
    /********************************************************************************************************************
    * <AUTHOR>
    * @date         2024-03-05
    * @description  The method returns the valid agencies to the flow
    * @param        requests (List<EngineWrapper.EngineWrapperInput>): the ranking's request input parameters
    * @return       List<EngineWrapper.EngineWrapperInvocableOutput>: the response containing the valid agencies to the flow
    ********************************************************************************************************************/
    /*@InvocableMethod(label='Rank Agencies')
    public static List<EngineWrapper.EngineWrapperInvocableOutput> rankAgencies(List<EngineWrapper.EngineWrapperInput> requests) {
        if(requests.isEmpty()) {
            return new List<EngineWrapper.EngineWrapperInvocableOutput> { new EngineWrapper.EngineWrapperInvocableOutput(false, 'NO_REQUEST') };
        }

        EngineWrapper.EngineWrapperInput request = requests[0];
        request.macroAreas = EngineWrapper.getMacroAreas(request.areasofNeed);

        EngineWrapper.EngineWrapperOutput result;
        result = EngineAgencyRanking.rankAgencies(requests[0]);
        return new List<EngineWrapper.EngineWrapperInvocableOutput> { getSelectedAccounts(result) };
    }*/

    /********************************************************************************************************************
    * <AUTHOR>
    * @date         2024-03-05
    * @description  The method converts the response of the ranking to the format that the flow uses
    * @param        result (EngineWrapper.EngineWrapperOutput): the ranking's response that contains the valid agencied scored
    * @return       List<EngineWrapper.EngineWrapperInvocableOutput>: the response containing the valid agencies to the flow
    ********************************************************************************************************************/
    /*
    private static EngineWrapper.EngineWrapperInvocableOutput getSelectedAccounts(EngineWrapper.EngineWrapperOutput result) {
        if(result.matchingSalespoints == null || result.matchingSalespoints.isEmpty()) {
            return new EngineWrapper.EngineWrapperInvocableOutput(result?.isSuccess, result?.engineError?.errorKeyword);
        }

        List<Id> selectedAccountIds = new List<Id>();
        for(EngineWrapper.EngineAgency agency : result.matchingSalespoints) {
            selectedAccountIds.add(agency.sfAccountId);
        }

        Map<Id, Account> selectedAccountsMap = new Map<Id, Account>([SELECT Id, Name, Agency__c FROM Account WHERE Id IN :selectedAccountIds]);
        List<Account> orderedSelectedAccoounts = new List<Account>();
        for(Id i : selectedAccountIds) {
            orderedSelectedAccoounts.add(selectedAccountsMap.get(i));
        }

        // Get selected Agency
        List<Account> parentAgencyList;
        if(!orderedSelectedAccoounts.isEmpty() && orderedSelectedAccoounts[0].Agency__c != null) {
            parentAgencyList = [SELECT Id, Name FROM Account WHERE Id =: orderedSelectedAccoounts[0].Agency__c];
        }
        return new EngineWrapper.EngineWrapperInvocableOutput(
            result?.isSuccess, 
            result?.engineError?.errorKeyword, 
            orderedSelectedAccoounts, 
            orderedSelectedAccoounts[0], 
            !parentAgencyList.isEmpty() ? parentAgencyList[0] : null
        );
    }*/    

    /**
     * @description Invocable method to rank agencies based on the provided input requests.
     * 
     * @param {List<EngineWrapper_V2.EngineWrapperInput>} requests - A list of input requests containing the data needed for ranking agencies.
     * 
     * @return {List<EngineWrapper_V2.EngineWrapperInvocableOutput>} - A list containing the output of the ranking process. 
     * If the input requests list is empty, it returns a list with a single output indicating 'NO_REQUEST'.
     * 
     * @invocableMethod
     * @label Rank Agencies
     */
    @InvocableMethod(label='Rank Agencies')
    public static List<EngineWrapper_V2.EngineWrapperInvocableOutput> rankAgencies(List<EngineWrapper_V2.EngineWrapperInput> requests) {
        if(requests.isEmpty()) {
            return new List<EngineWrapper_V2.EngineWrapperInvocableOutput> { new EngineWrapper_V2.EngineWrapperInvocableOutput(false, 'NO_REQUEST') };
        }

        EngineWrapper_V2.EngineWrapperInput request = requests[0];
        request.macroAreas = EngineWrapper_V2.getMacroAreas(request.areasofNeed);

        EngineWrapper_V2.EngineWrapperOutput result;
        result = EngineAgencyRanking_V2.rankAgencies(requests[0]);
        return new List<EngineWrapper_V2.EngineWrapperInvocableOutput> { getSelectedAccounts(result) };
    }


    /**
     * Retrieves selected accounts based on the provided EngineWrapper output.
     *
     * @param result The EngineWrapper_V2.EngineWrapperOutput object containing the matching salespoints and other details.
     * @return EngineWrapper_V2.EngineWrapperInvocableOutput object containing the success status, error keyword, 
     *         ordered list of selected ServiceTerritories, the first selected ServiceTerritory, and the parent agency Account.
     */
    @testVisible
    private static EngineWrapper_V2.EngineWrapperInvocableOutput getSelectedAccounts(EngineWrapper_V2.EngineWrapperOutput result) {
        if(result.matchingSalespoints == null || result.matchingSalespoints.isEmpty()) {
            return new EngineWrapper_V2.EngineWrapperInvocableOutput(result?.isSuccess, result?.engineError?.errorKeyword);
        }

        List<Id> selectedSalesPointIds = new List<Id>();
        for(EngineWrapper_V2.EngineSalesPoint salespoint : result.matchingSalespoints) {
            selectedSalesPointIds.add(salespoint.salespoint);
        }

        Map<Id, ServiceTerritory> selectedSpMap = new Map<Id, ServiceTerritory>([SELECT Id, Name, Agency__c FROM ServiceTerritory WHERE Id IN :selectedSalesPointIds]);
        List<ServiceTerritory> orderedSelectedSp = new List<ServiceTerritory>();
        System.debug('init orderedSelectedSp : '+orderedSelectedSp);
        System.debug('selectedSalesPointIds : '+selectedSalesPointIds);
        for(Id i : selectedSalesPointIds) {
            System.debug('selectedSpMap.get(i) : '+selectedSpMap.get(i));
            if(selectedSpMap.get(i) != null){
                orderedSelectedSp.add(selectedSpMap.get(i));
            }
        }

        // Get selected Agency
        List<Account> parentAgencyList;
        System.debug('orderedSelectedSp : '+orderedSelectedSp);
        if(!orderedSelectedSp.isEmpty() && orderedSelectedSp[0].Agency__c != null) {
            parentAgencyList = [SELECT Id, Name FROM Account WHERE Id =: orderedSelectedSp[0].Agency__c];
        }
        return new EngineWrapper_V2.EngineWrapperInvocableOutput(
            result?.isSuccess, 
            result?.engineError?.errorKeyword, 
            orderedSelectedSp, 
            !orderedSelectedSp.isEmpty()? orderedSelectedSp[0] : null, 
            !parentAgencyList.isEmpty() ? parentAgencyList[0] : null
        );
    }
}