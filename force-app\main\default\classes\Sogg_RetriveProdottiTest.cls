@isTest
public class Sogg_RetriveProdottiTest {
   
    @TestSetup
    static void setupTestData() {
        
        Id recordTypePros = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Prospect').getRecordTypeId();
        Id recordTypeAgency = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId();
        Id recordTypeSoc = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId();
        
        List<Account> accToInsert = new List<Account>();

        Account newAccount = new Account(
            FirstName = 'John',
            LastName = 'Doe',
            RecordTypeId = recordTypePros
        );
        accToInsert.add(newAccount) ;
		insert newAccount;
        
        Account agency = new Account(
            
            Name = 'TestAgency',
            RecordTypeId = recordTypeAgency
        );
        accToInsert.add(agency) ;
		insert agency;
        
         Account society = new Account(
            
            Name = 'societa',
            RecordTypeId = recordTypeSoc
        );
        accToInsert.add(society) ;
        insert society;
        
        List<InsurancePolicy> policyToInsert = new List<InsurancePolicy>();

        Id recordTypeUnica =  SObjectType.InsurancePolicy.getRecordTypeInfosByDeveloperName().get('UNICA').getRecordTypeId();
        InsurancePolicy pol1 = new InsurancePolicy(
            Name = 'TestName1',
            PremiumAmount = 280,
            Agency__c = [SELECT Id FROM Account WHERE RecordTypeId =:recordTypeAgency ].Id,
            Society__c = society.Id,
            RecordTypeId = recordTypeUnica,
            AreaOfNeed__c = 'Salute',
            NameInsuredId = [SELECT Id FROM Account WHERE RecordTypeId =:recordTypePros ].Id,
            CIP__c = 'test',
            CompanyCode__c = 'Test Company',
            Company__c = 'TestCompany'
        );
        policyToInsert.add(pol1);

        InsurancePolicy pol2 = new InsurancePolicy(
            Name = 'TestName2',
            PremiumAmount = 600,
            Agency__c = [SELECT Id FROM Account WHERE RecordTypeId =:recordTypeAgency ].Id,
            Society__c = society.Id,
            RecordTypeId = recordTypeUnica,
            AreaOfNeed__c = 'Salute',
            NameInsuredId = [SELECT Id FROM Account WHERE RecordTypeId =:recordTypePros ].Id,
            CIP__c = 'test',
            CompanyCode__c = 'Test Company',
            Company__c = 'TestCompany'
        );
        policyToInsert.add(pol2);
        insert policyToInsert;
   	}

    @isTest
    static void parentCardCountProdotti() {
       
        Test.startTest();
        Sogg_RetriveProdotti controller = new Sogg_RetriveProdotti();
        Id rtPros = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Prospect').getRecordTypeId();
        Id rtAcency = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId();

        Id idTestAcc = [SELECT Id FROM Account WHERE RecordTypeId =:rtPros].Id;
        Id idAzienda = [SELECT Id FROM Account WHERE RecordTypeId =:rtAcency].Id;
		String username = String.valueOf(Date.today()) + '<EMAIL>';
        Profile p = [SELECT Id FROM Profile WHERE Name='System Administrator']; 
        User us = new User(Alias = 'standt', Email='<EMAIL>', 
            EmailEncodingKey='UTF-8', LastName='Testing', LanguageLocaleKey='en_US', 
            LocaleSidKey='en_US', ProfileId = p.Id, IdAzienda__c = idAzienda,
            TimeZoneSidKey='America/Los_Angeles', UserName=username);

        insert us;    

        
        Map<String,Object> inputMap = new Map<String,Object>();
        Map<String,Object> outMap = new Map<String,Object>();
        Map<String,Object> options = new Map<String,Object>();


        inputMap.put('AccountId', idTestAcc);
        inputMap.put('UserId', us.Id);
        System.runas(us){
            controller.invokeMethod('parentCardCountProdotti',inputMap,outMap,options);
        }
        
		Test.stopTest();

       
    }
}