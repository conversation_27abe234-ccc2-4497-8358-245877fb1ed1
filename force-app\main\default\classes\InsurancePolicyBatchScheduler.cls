/**
 * @File Name         : InsurancePolicyBatchScheduler.cls
 * @Description       : Schedulable class to execute the InsurancePolicyBatch
 * <AUTHOR> <EMAIL>
 * @Group             : 
 * @Last Modified On  : 23-01-2025
 * @Last Modified By  : <EMAIL>
**/
public class InsurancePolicyBatchScheduler implements Schedulable {
    public void execute(SchedulableContext sc) {
        // Execute the batch
        InsurancePolicyBatch batch = new InsurancePolicyBatch();
        Database.executeBatch(batch);
    }
}