/**
 * @description       : TM_UTL_DataFactoryTst.cls
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 03-02-2025
 * @last modified by  : <EMAIL>
**/

@IsTest
public class TM_UTL_DataFactoryTst {

    //public static RPU_UTL_Constants constantsUtl = RPU_UTL_Constants.getInstance();

    public static TM_UTL_DataFactoryTst getInstance(){

        return new TM_UTL_DataFactoryTst();
    }

    /*
    public static Id internalUserProfileId {
        get {
            if(internalUserProfileId == null) {
                internalUserProfileId =
                [SELECT Id FROM Profile WHERE Name = :constantsUtl.UTENTE_INTERNO_PROFILE_NAME].Id;
            }
            return internalUserProfileId;
        }
        set;
    }

     */

    public User createCRMUser(String codiceVenditore, Boolean doInsert) {

        User crmUser = new User(
            Alias = 'crmuser',
            FirstName = 'FirstName',
            LastName = 'LastName',
            // CodiceVenditore__c = 'CRM' + DateTime.now().millisecond(), 
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'it',
            LocaleSidKey = 'it',
            //ProfileId = internalUserProfileId,
            TimeZoneSidKey = 'Europe/Rome',
            UserName = '<EMAIL>' + DateTime.now().getTime()
        );
        if(doInsert) {

            insert crmUser;
        }
        return crmUser;
    }
    public Pricebook2 createPricebook(Boolean doUpdate) {

        Pricebook2 pricebook = new Pricebook2(
                Id = Test.getStandardPricebookId(),
                Name = 'Standard Price Book',
                IsActive = true
        );
        if(doUpdate) {

            update pricebook;
        }
        return pricebook;
    }
    public PricebookEntry createPricebookEntry(Id pricebookId, Id product2Id, Boolean doInsert) {

        PricebookEntry pricebookEntry = new PricebookEntry(
                Pricebook2Id = pricebookId,
                Product2Id = product2Id,
                IsActive = true,
                UnitPrice = 100.0
        );
        if(doInsert) {

            insert pricebookEntry;
        }
        return pricebookEntry;
    }
    public Product2 createProduct(Integer numeroProdotto, Boolean doInsert) {

        Product2 prodotto = new Product2(
                Name = 'Prodotto' + numeroProdotto,
                ProductCode = 'CodiceProdotto' + numeroProdotto,
                IsActive = true
        );
        if(doInsert) {
            insert prodotto;
        }
        return prodotto;
    }

    /*
    public PermissionSetAssignment assignPermissionSet(User u, String permissionSet, Boolean doInsert) {

        PermissionSetAssignment psa = new PermissionSetAssignment(PermissionSetId = getPermissionSetId(permissionSet), AssigneeId = u.Id);

        if(doInsert) {

            insert psa;

        }

        return psa;

    }
     */

    public List<Account> createClienti(Integer clientiNumber, Boolean doInsert) {

        List<Account> clienti = new List<Account>();
        for(Integer i = 0; i < clientiNumber; i++) {

            clienti.add(
                new Account(
                    Name = 'Cliente' + String.valueOf(i).leftPad(6, '0')
                )
            );
        }
        if(doInsert) {

            insert clienti;
        }
        return clienti;
    }

    public Opportunity createOpportunity(){
        Opportunity opp = new Opportunity();
        opp.Name = 'Opp test';
        opp.StageName = 'Quote';
        opp.CloseDate = Date.newInstance(2023, 12, 9);
        insert opp;
        return opp;
    }

    public Quote createQuote(Id opportunityId){
        Quote quote = new Quote();
        quote.OpportunityId = opportunityId;
        quote.Name = 'Test';
        quote.Pricebook2Id = Test.getStandardPricebookId();
        insert quote;
        return quote;
    }

    public List<QuoteLineItem> createQuoteLineItems(Integer quoteLineItemNumber, Id quoteId, Id productId, Boolean doInsert){
        List<QuoteLineItem> quoteLineItems = new List<QuoteLineItem>();
        Pricebook2 pricebook2 = createPricebook(true);
        PricebookEntry pricebookEntry = createPricebookEntry(pricebook2.Id, productId, true);
        for(Integer i = 0; i < quoteLineItemNumber; i++) {
            QuoteLineItem quoteLineItem = new QuoteLineItem();
            quoteLineItem.QuoteId = quoteId;
            quoteLineItem.PricebookEntryId = pricebookEntry.Id;
            quoteLineItem.Quantity = 1;
            quoteLineItem.UnitPrice = 1;
            quoteLineItem.Product2Id = productId;
            quoteLineItems.add(quoteLineItem);
        }
        if(doInsert) {
            insert quoteLineItems;
        }
        return quoteLineItems;
    }

    public List<Product2> createProducts(Integer productNumber, Boolean doInsert){
        List<Product2> products = new List<Product2>();
        for(Integer i = 0; i < productNumber; i++) {
            products.add(createProduct(i, false));
        }
        if(doInsert) {
            insert products;
        }
        return products;
    }

    public void createDummyFile(Id accountId){

        Blob bodyBlob = Blob.valueOf('[{"example":"test","ARCODIVA":"22","ARDESART":"Siringa","ARCODART":"ABC","ARUNMIS1":"PZ","ARCODFAM":"Fam"}]');

        ContentVersion contentVersion = new ContentVersion(
                Title = 'TestDocument',
                PathOnClient = 'TestDocument.json',
                VersionData = bodyBlob,
                Origin = 'H'
        );
        insert contentVersion;

        ContentVersion contentVersionRetrieved = [SELECT Id, Title, ContentDocumentId FROM ContentVersion WHERE Id =: contentVersion.Id LIMIT 1];
        ContentDocumentLink contentDocumentLink = new ContentDocumentLink(
                LinkedEntityId = accountId,
                ContentDocumentId = contentVersionRetrieved.ContentDocumentId,
                ShareType = 'V'
        );
        insert contentDocumentLink;
    }
}