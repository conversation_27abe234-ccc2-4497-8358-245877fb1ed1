public without sharing class CtrlUserDefaultRACF {
    
    public class UserWrapper{
        @AuraEnabled public UserData userData = new UserData();
        @AuraEnabled public List<NetworkUserData> nuData = new List<NetworkUserData>();
    }

    public class UserData{
        @AuraEnabled public String userName                         {get; set;}
        @AuraEnabled public String userFederationIdentifier         {get; set;}
    }

    public class NetworkUserData{
        @AuraEnabled public String networkUserId                    {get; set;}
        @AuraEnabled public String networkUser                      {get; set;}
        @AuraEnabled public Boolean preferred                       {get; set;}
        @AuraEnabled public String agencyExternalId                 {get; set;}
        @AuraEnabled public String society                          {get; set;}
        @AuraEnabled public String societyName                      {get; set;}
    }

    @AuraEnabled(cacheable=true)
    public static UserWrapper getData(){

        UserWrapper uw = new UserWrapper();

        User currentUser = [SELECT Id, Name, FederationIdentifier FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];

        UserData ud = new UserData();
        ud.userName = currentUser.Name;
        ud.userFederationIdentifier = currentUser.FederationIdentifier;
        
        uw.userData = ud;

        Id recordTypeSociety = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId();
        List<Account> accountSociety = [SELECT Id, ExternalId__c, Name FROM Account WHERE RecordTypeId = :recordTypeSociety];
        Map<String, String> mapSociety = new Map<String, String>();
        for(Account acc : accountSociety){
            mapSociety.put(acc.ExternalId__c, acc.Name);
        }

        List<NetworkUser__c> nuList = [SELECT Id, NetworkUser__c, Preferred__c, Society__c, Agency__r.ExternalId__c FROM NetworkUser__c WHERE FiscalCode__c = :currentUser.FederationIdentifier AND IsActive__c = true];
        for(NetworkUser__c nu : nuList){

            NetworkUserData nud = new NetworkUserData();
            nud.networkUserId = nu.Id;
            nud.networkUser = nu.NetworkUser__c;
            nud.preferred = nu.Preferred__c;
            nud.agencyExternalId = nu.Agency__r.ExternalId__c;
            nud.society = nu.Society__c;
            nud.societyName = mapSociety.get(nu.Society__c);

            uw.nuData.add(nud);

        }

        return uw;

    }

    @AuraEnabled
    public static void setPreferredNetworkUser(String nuId, String society){

        System.debug('nuId '+nuId);
        System.debug('society '+society);

        if(String.isBlank(nuId) || String.isBlank(society)) return;

        User currentUser = [SELECT Id, Name, FederationIdentifier FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];
        List<NetworkUser__c> nuList = [SELECT Id, 
                                            NetworkUser__c, 
                                            Preferred__c, 
                                            Society__c, 
                                            Agency__r.ExternalId__c 
                                        FROM NetworkUser__c 
                                        WHERE FiscalCode__c = :currentUser.FederationIdentifier 
                                        AND Society__c = :society
                                        AND IsActive__c = true];

        for(NetworkUser__c nu : nuList){

            if(nu.Id == nuId){
                nu.Preferred__c = true;
            }else{
                nu.Preferred__c = false;
            }

        }

        update nuList;

    }

}