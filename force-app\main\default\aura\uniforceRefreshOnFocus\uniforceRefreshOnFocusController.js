({
    doInit : function(component, event, helper) {
        console.log("Component has been initialized!");        
        helper.executeLogic(component);
        /////
        console.log('BEFORE Scripts for eventListner EVENTOFEA');
        //EVENTO CHE RISALE DA FEICONTAINER
        window.addEventListener("message", evt => {
            console.log('EVENTOFEA IN REFRESH AURA CMP --> ',evt);
            // if(evt.data.type == "feiFlowFinished"){
            //     console.log('EVENTOFEA DATA IN REFRESH AURA CMP --> ',evt.data);
            //     // Qui la tua logica (chiusura tab, refresh, ecc.)  
            // }

            if(evt.data.type == "feiFlowFinished"){
                console.log('EVENTOFEA DATA IN REFRESH AURA CMP --> ',evt.data);
                //Qui la tua logica (chiusura tab, refresh, ecc.)  
                // Logic to close FEI TAB
                console.log('event ok feiContainer FROM uniforceRefreshOnFocusController');
                var workspaceAPI = component.find("workspace");
                workspaceAPI.getAllTabInfo().then(function(tabInfo) {
                    var foundTabId = null;
                    var foundTabTitle = null;

                    // Check main tabs
                    tabInfo.forEach(function(tab) {
                        if (tab.title && tab.title.startsWith("FEI")) {
                            foundTabId = tab.tabId;
                        }
                        // Check subtabs
                        if (tab.subtabs && tab.subtabs.length > 0) {
                            tab.subtabs.forEach(function(subtab) {
                                if (subtab.title && subtab.title.startsWith("FEI")) {
                                    foundTabId = subtab.tabId;
                                    foundTabTitle = subtab.title;
                                }
                            });
                        }
                    });
                    // If you find a tab with a title that starts with "FEI", close it
                    if (foundTabId) {       
                        console.log('Found FEI tab TO CLOSE with ID:', foundTabId);
                        console.log('Found FEI tab TO CLOSE with TITLE:', foundTabTitle);
                        workspaceAPI.closeTab({ tabId: foundTabId })
                            .then(function() {
                                console.log('FEI tab closed successfully');
                            })
                            .catch(function(error) {
                                console.error('Error closing FEI tab:', error);
                            });
                    } 
                });
                
                // Execute logic of refresh salesforce internal tab
                helper.executeLogicForInternalTab(component);
                // Perform actions, e.g. close a modal, update data, etc.
                 // Close all open Quick Action modals
                // $A.get("e.force:closeQuickAction").fire();
                helper.closeModalFEI(component);
                console.log('AFTER CLOSE MODAL fire feiContainer FROM uniforceRefreshOnFocusController');
                // Trigger the event to close the container
                pubsub.fire("feiContainerEvent", "closeFeiContainer", { /* payload */ });
            }
         });

        // window.addEventListener('feiFlowFinishedCustom', function(evt) {
        //     var detail = evt.detail;
        //     console.log('Ricevuto evento feiFlowFinishedCustom via Static Resource', detail);
        //     // Qui la tua logica (chiusura tab, refresh, ecc.)
        //     var workspaceAPI = component.find("workspace");
        //     workspaceAPI.getAllTabInfo().then(function(tabInfo) {
        //         var foundTabId = null;
        //         var foundTabTitle = null;
        //         // Check main tabs
        //         tabInfo.forEach(function(tab) {
        //             if (tab.title && tab.title.startsWith("FEI")) {
        //                 foundTabId = tab.tabId;
        //             }
        //             // Check subtabs
        //             if (tab.subtabs && tab.subtabs.length > 0) {
        //                 tab.subtabs.forEach(function(subtab) {
        //                     if (subtab.title && subtab.title.startsWith("FEI")) {
        //                         foundTabId = subtab.tabId;
        //                         foundTabTitle = subtab.title;
        //                     }
        //                 });
        //             }
        //         });
        //         // If you find a tab with a title that starts with "FEI", close it
        //         if (foundTabId) {       
        //             console.log('Found FEI tab TO CLOSE with ID:', foundTabId);
        //             console.log('Found FEI tab TO CLOSE with TITLE:', foundTabTitle);
        //             workspaceAPI.closeTab({ tabId: foundTabId })
        //                 .then(function() {
        //                     console.log('FEI tab closed successfully');
        //                 })
        //                 .catch(function(error) {
        //                     console.error('Error closing FEI tab:', error);
        //                 });
        //         } 
        //     });
            
        //     // Execute logic of refresh salesforce internal tab
        //     helper.executeLogicForInternalTab(component);
        //     // Clean storage
        //     localStorage.removeItem('feiFlowFinished');
        //     // Trigger the event to close the container
        //     pubsub.fire("feiContainerEvent", "closeFeiContainer", { /* payload */ });
        // });
        console.log('AFTER Scripts for eventListner Custom  called');
        /////
    },

    
    onTabFocused : function(component, event, helper) {
        console.log('onTabFocused called');
        var focusedTabId = event.getParam('currentTabId');
        var workspaceAPI = component.find("workspace");
        // console.log('onTabFocused called with focusedTabId: ' + focusedTabId);
        // console.log('workspaceAPI: ', workspaceAPI);
        // console.log('chiamata helper.executeLogicForInternalTab');
        helper.executeLogicForInternalTab(component);
    },

    onScriptsLoaded : function(component, event, helper) {
        console.log('onScriptsLoaded called');
        window.addEventListener('feiFlowFinishedCustom', function(evt) {
            var detail = evt.detail;
            console.log('Ricevuto evento feiFlowFinishedCustom via Static Resource', detail);
            // Qui la tua logica (chiusura tab, refresh, ecc.)
             console.log("Cached event feiFlowFinishedCustom in uniforceRefreshOnFocusController");
            // Check if the event is of type "feiFlowFinished"
            // if (evt && evt.data && evt.data.type === "feiFlowFinished") {
            // if(evt.key === 'feiFlowFinished') {
            if(evt.data.type == "feiFlowFinished"){
                // var data = JSON.parse(evt.newValue);
                // console.log('chached event feiFlowFinished via localStorage FROM uniforceRefreshOnFocusController', data);
                // Logic to close FEI TAB
                console.log('event ok feiContainer FROM uniforceRefreshOnFocusController');
                var workspaceAPI = component.find("workspace");
                workspaceAPI.getAllTabInfo().then(function(tabInfo) {
                    var foundTabId = null;
                    var foundTabTitle = null;

                    // Check main tabs
                    tabInfo.forEach(function(tab) {
                        if (tab.title && tab.title.startsWith("FEI")) {
                            foundTabId = tab.tabId;
                        }
                        // Check subtabs
                        if (tab.subtabs && tab.subtabs.length > 0) {
                            tab.subtabs.forEach(function(subtab) {
                                if (subtab.title && subtab.title.startsWith("FEI")) {
                                    foundTabId = subtab.tabId;
                                    foundTabTitle = subtab.title;
                                }
                            });
                        }
                    });
                    // If you find a tab with a title that starts with "FEI", close it
                    if (foundTabId) {       
                        console.log('Found FEI tab TO CLOSE with ID:', foundTabId);
                        console.log('Found FEI tab TO CLOSE with TITLE:', foundTabTitle);
                        workspaceAPI.closeTab({ tabId: foundTabId })
                            .then(function() {
                                console.log('FEI tab closed successfully');
                            })
                            .catch(function(error) {
                                console.error('Error closing FEI tab:', error);
                            });
                    } 
                });
                
                // Execute logic of refresh salesforce internal tab
                helper.executeLogicForInternalTab(component);
                // Clean storage
                localStorage.removeItem('feiFlowFinished');
                // Perform actions, e.g. close a modal, update data, etc.
                // Trigger the event to close the container
                pubsub.fire("feiContainerEvent", "closeFeiContainer", { /* payload */ });
            }
        });
    },     


    
//     manageCustomEventfeiFlowFinished: function(component, event, helper) {
//     console.log('onScriptsLoaded called');
//     window.addEventListener('feiFlowFinishedCustom', function(evt) {
//         var detail = evt.detail;
//         console.log('Ricevuto evento feiFlowFinishedCustom via Static Resource', detail);
//         // Qui la tua logica (chiusura tab, refresh, ecc.)
//         var workspaceAPI = component.find("workspace");
//         workspaceAPI.getAllTabInfo().then(function(tabInfo) {
//             var foundTabId = null;
//             var foundTabTitle = null;

//             // Check main tabs
//             tabInfo.forEach(function(tab) {
//                 if (tab.title && tab.title.startsWith("FEI")) {
//                     foundTabId = tab.tabId;
//                 }
//                 // Check subtabs
//                 if (tab.subtabs && tab.subtabs.length > 0) {
//                     tab.subtabs.forEach(function(subtab) {
//                         if (subtab.title && subtab.title.startsWith("FEI")) {
//                             foundTabId = subtab.tabId;
//                             foundTabTitle = subtab.title;
//                         }
//                     });
//                 }
//             });
//             // If you find a tab with a title that starts with "FEI", close it
//             if (foundTabId) {       
//                 console.log('Found FEI tab TO CLOSE with ID:', foundTabId);
//                 console.log('Found FEI tab TO CLOSE with TITLE:', foundTabTitle);
//                 workspaceAPI.closeTab({ tabId: foundTabId })
//                     .then(function() {
//                         console.log('FEI tab closed successfully');
//                     })
//                     .catch(function(error) {
//                         console.error('Error closing FEI tab:', error);
//                     });
//             } 
//         });
        
//         // Execute logic of refresh salesforce internal tab
//         helper.executeLogicForInternalTab(component);
//         // Clean storage
//         localStorage.removeItem('feiFlowFinished');
//         // Trigger the event to close the container
//         pubsub.fire("feiContainerEvent", "closeFeiContainer", { /* payload */ });
//     });
// }

    })