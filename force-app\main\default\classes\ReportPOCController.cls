public with sharing class ReportPOCController {

    public class ResponseObject {
        @AuraEnabled
        public List<Opportunity> opportunities {get;set;}
        @AuraEnabled
        public String latestQuery {get;set;}
        @AuraEnabled
        public Integer numberOfPages {get;set;}

        public ResponseObject(List<Opportunity> opportunities, String latestQuery, Integer numberOfPages) {
            this.opportunities = opportunities;
            this.latestQuery = latestQuery;
            this.numberOfPages = numberOfPages;
        }
    }
    
    @AuraEnabled(cacheable=false)
    public static ResponseObject getOpportunities(Integer numberOfRecordsPerPage, Integer offset, String sortedBy, String sortedDirection, String latestQuery, String searchKey, List<String> searchFields) {
        try {
            String baseQuery;
            String countQuery = 'SELECT COUNT() FROM Opportunity ';
            if(String.isBlank(latestQuery)) {
                baseQuery = 'SELECT Name, Amount, Rating__c, CreatedDate FROM Opportunity ';
            }
            else if(latestQuery.contains('WHERE')) {
                baseQuery = latestQuery.split('WHERE')[0];
            }
            else if(latestQuery.contains('ORDER BY')) {
                baseQuery = latestQuery.split('ORDER BY')[0];
            }
            else if(latestQuery.contains('LIMIT')) {
                baseQuery = latestQuery.split('LIMIT')[0];
            }

            // Create the where statement for the search
            if(String.isNotBlank(searchKey)) {
                String searchFilterQuery = 'WHERE (';
                for (String searchField : searchFields) {
                    searchFilterQuery += searchField + ' LIKE \'%' + searchKey + '%\' OR ';
                }
                searchFilterQuery = searchFilterQuery.removeEnd(' OR ') + ') ';
                
                baseQuery += searchFilterQuery;
                countQuery += searchFilterQuery;
            }
            if(String.isNotBlank(sortedBy) && String.isNotBlank(sortedDirection)) {
                baseQuery += 'ORDER BY ' + sortedBy + ' ' + sortedDirection + ' NULLS LAST';
            }
            String paginationQueryPart = ' LIMIT ' + numberOfRecordsPerPage + ' OFFSET ' + offset;
            System.debug('# baseQuery + paginationQueryPart: ' + (baseQuery + paginationQueryPart));

            String finalQuery = baseQuery + paginationQueryPart;
            List<Opportunity> recordsToReturn = Database.query(finalQuery);
            Integer recordsNumber = Database.countQuery(countQuery);

            return new ResponseObject(recordsToReturn, finalQuery, (Integer) Math.ceil((Decimal) recordsNumber / (Decimal) numberOfRecordsPerPage));
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    // @AuraEnabled(cacheable=false)
    // public static ResponseObject getOpportunities(Integer numberOfRecordsPerPage, Integer offset, String sortedBy, String sortedDirection, String latestQuery, String searchKey) {
    //     try {
    //         String baseQuery;
    //         String countQuery = 'SELECT COUNT() FROM Opportunity ';
    //         if(String.isBlank(latestQuery)) {
    //             baseQuery = 'SELECT Name, Amount, Rating__c, CreatedDate FROM Opportunity ';
    //         }
    //         else if(latestQuery.contains('WHERE')) {
    //             baseQuery = latestQuery.split('WHERE')[0];
    //         }
    //         else if(latestQuery.contains('ORDER BY')) {
    //             baseQuery = latestQuery.split('ORDER BY')[0];
    //         }
    //         else if(latestQuery.contains('LIMIT')) {
    //             baseQuery = latestQuery.split('LIMIT')[0];
    //         }
    //         baseQuery = String.isNotBlank(searchKey) ? baseQuery + 'WHERE Name LIKE \'%' + searchKey + '%\' ' : baseQuery;
    //         countQuery = String.isNotBlank(searchKey) ? countQuery + 'WHERE Name LIKE \'%' + searchKey + '%\' ' : countQuery;
    //         if(String.isNotBlank(sortedBy) && String.isNotBlank(sortedDirection)) {
    //             baseQuery += 'ORDER BY ' + sortedBy + ' ' + sortedDirection + ' NULLS LAST';
    //         }
    //         String paginationQueryPart = ' LIMIT ' + numberOfRecordsPerPage + ' OFFSET ' + offset;
    //         System.debug('# baseQuery + paginationQueryPart: ' + (baseQuery + paginationQueryPart));

    //         String finalQuery = baseQuery + paginationQueryPart;
    //         List<Opportunity> recordsToReturn = Database.query(finalQuery);
    //         Integer recordsNumber = Database.countQuery(countQuery);

    //         return new ResponseObject(recordsToReturn, finalQuery, (Integer) Math.ceil((Decimal) recordsNumber / (Decimal) numberOfRecordsPerPage));
    //     } catch (Exception e) {
    //         throw new AuraHandledException(e.getMessage());
    //     }
    // }

    @AuraEnabled(cacheable=false)
    public static Integer getNumberOfPages(Integer numberOfRecordsPerPage) {
        try {
            Integer recordsNumber = [SELECT COUNT() FROM Opportunity];


            return (Integer) Math.ceil((Decimal) recordsNumber / (Decimal) numberOfRecordsPerPage);
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
}
