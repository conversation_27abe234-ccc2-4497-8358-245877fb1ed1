global without sharing class IntegrationUtilityAction implements Callable
{
    public Object call(String action, Map<String, Object> args)
    {
        Map<String, Object> input = (Map<String, Object>)args.get('input');
        Map<String, Object> output = (Map<String, Object>)args.get('output');
        
        String integrationId = (String)input.get('integrationId');
        String body = (String)input.get('body');
        String params = (String)input.get('params');
        IntegrationSettingUtility.HttpResponseWrapper response = IntegrationSettingUtility.executeHttpRequest(integrationId, body, params);
        output.put('result', response.body);
        output.put('statusCode', response.statusCode);
        output.put('status', response.status);
        return response;
    }
}