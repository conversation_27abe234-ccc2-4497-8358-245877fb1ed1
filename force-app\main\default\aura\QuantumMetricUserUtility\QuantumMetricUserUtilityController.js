({
    handleUserRecordUpdate: function (component, event, helper) {
        if (event.getParams().changeType == 'LOADED') {
            let userRecord = component.get("v.userRecord");
            if (!window.QuantumMetricUser) window.QuantumMetricUser = {};
            window.QuantumMetricUser.currentUserName = userRecord["Username"];
            window.QuantumMetricUser.currentName = userRecord["Name"];
            window.QuantumMetricUser.currentEmail = userRecord["Email"];
            window.QuantumMetricUser.currentUserId = userRecord["Id"];
            window.QuantumMetricUser.currentUserFiscalCode = userRecord["FederationIdentifier"];
        }
    }
})