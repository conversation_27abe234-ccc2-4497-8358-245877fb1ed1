@IsTest
public without sharing class SinistriControllerTest {
 
    @TestSetup
    static void makeData(){
    Account a = new Account(
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('PersonAccount').getRecordTypeId(),
            FirstName = 'First',
            LastName = 'Last'
        );
    insert a;
    }

    @IsTest
    static void getSinistriTest(){
        Account a = [SELECT id FROM ACCOUNT LIMIT 1];
        
        Test.startTest();
            SinistriController.getSinistriFromIntegrationProcedure(a.Id);
        Test.stopTest();
    }
}