/***********************************************************************************
* <AUTHOR>
* @description    Batchable class for bulk deletion of Communication__c records
* @date           2024-10-05
* @group          Batchable
************************************************************************************/
global class DeleteCommunicationBatch implements Database.Batchable<sObject> {  
    private static final Integer N_DAYS = 60;
    
    global Database.QueryLocator start(Database.BatchableContext BC) {
        String query = 'SELECT Id,Name FROM Communication__c WHERE CampaignId__r.Status IN (\'Approvato\',\'Rifiutato\') AND CampaignId__r.EndDate < LAST_N_DAYS:' + N_DAYS;
        return Database.getQueryLocator(query);
    }
    
    global void execute(Database.BatchableContext BC, List<Communication__c> communicationRecords) {
        delete communicationRecords;
    }
    
    global void finish(Database.BatchableContext BC) {
    }
}
