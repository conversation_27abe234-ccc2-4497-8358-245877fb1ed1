@isTest
public class CtrlFeiTabExtensionTest {

    @isTest
    static void test_coverage(){

        Test.startTest();

        FEI_Environment__c fei = new FEI_Environment__c();
        fei.SetupOwnerId = UserInfo.getUserId();
        fei.Environment__c = 'EURO';
        fei.Chrome_Extension_URL__c = 'test';
        fei.Safari_Extension_URL__c = 'test';
        fei.Extension_Required__c = false;
        fei.NOPROD_KeepAlive_Timeout_ms__c = '1000';
        fei.PROD_KeepAlive_Timeout_ms__c = '1000';
        fei.Skip_Extension__c = true;
        fei.KeepAlive_URL__c = 'test';

        insert fei;

        try{
            CtrlFeiTabExtension.getExtensionUrl();
        }catch(Exception ex){}

        Test.stopTest();

    }

}