@isTest
private class ReportQueryControllerTest {

    @isTest
    static void testCallWithGetReportOpportunities() {
        // Crea dati di test
        Opportunity opp = new Opportunity(
            Name = 'Test Opportunity',
            StageName = 'Assegnato',
            CloseDate = Date.today().addDays(30),
            Amount = 10000
        );
        insert opp;

        // Prepara input e output
        Map<String, Object> input = new Map<String, Object>{
            'amount' => String.valueOf(opp.Amount),
            'clientType' => 'Business' // anche se non usato nel filtro attivo
        };
        Map<String, Object> output = new Map<String, Object>();

        Map<String, Object> args = new Map<String, Object>{
            'input' => input,
            'output' => output
        };

        // Esegui il metodo call
        ReportQueryController controller = new ReportQueryController();
        Object result = controller.call('getReportOpportunities', args);

        // Verifica che il risultato non sia nullo e contenga l'opportunità
        System.assertNotEquals(null, result, 'Il risultato non dovrebbe essere nullo');
        List<Opportunity> opportunities = (List<Opportunity>) result;
        System.assert(!opportunities.isEmpty(), 'Dovrebbe esserci almeno un\'opportunità');
        System.assertEquals(opp.Id, opportunities[0].Id, 'L\'opportunità restituita dovrebbe corrispondere a quella inserita');
    }

    @isTest
    static void testCallWithUnknownAction() {
        Map<String, Object> args = new Map<String, Object>{
            'input' => new Map<String, Object>(),
            'output' => new Map<String, Object>()
        };

        ReportQueryController controller = new ReportQueryController();
        Object result = controller.call('unknownAction', args);

        System.assertEquals(null, result, 'Il risultato dovrebbe essere nullo per azioni non riconosciute');
    }
}