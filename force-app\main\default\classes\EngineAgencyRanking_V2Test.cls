@IsTest
public class EngineAgencyRanking_V2Test {

    @TestSetup
    static void setupTestData() {
        // Creazione di un RecordType per Account (Agency)
        Id agencyRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId();
        
    
        // Creazione di un Account di esempio
        Account testAccount = new Account(
            Name = 'Test Agency',
            RecordTypeId = agencyRecordTypeId,
            OmnichannelAgreement__c = true,
            ExternalId__c = 'Agency123',
            Size__c= 'Grande'
        );
        insert testAccount;    
    
        // Creazione di un KPI__c di esempio con il campo obbligatorio "Dimensione__c"
        KPI__c testKPI = new KPI__c(
            Agency__c = testAccount.Id,
            ContactRate__c = 85.0,
            ScoreTotal__c = 100.0
        );
        insert testKPI;
    
        //Crea un record di ruolo (supponendo che l'oggetto sia FinServ__ReciprocalRole__c)
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(Name = 'Cliente', FinServ__InverseRole__c = 'Agenzia');
        insert role;
       
        // Crea un record di ruolo (supponendo che l'oggetto sia FinServ__ReciprocalRole__c)
        FinServ__ReciprocalRole__c roleC = new FinServ__ReciprocalRole__c(Name = 'Cliente', FinServ__InverseRole__c = 'Compagnia');
        insert roleC;

       // Crea dati di test necessari per i test
       Account account1 = new Account(Name = 'Test Agency 1', Size__c= 'Grande', ExternalId__c = 'Test1', OmnichannelAgreement__c = true, RecordTypeId = [SELECT Id FROM RecordType WHERE DeveloperName = 'Agency' AND SObjectType = 'Account' LIMIT 1].Id);
       insert account1;

       Account account2 = new Account(Name = 'Test Society 1', ExternalId__c = 'Test2' ,RecordTypeId = [SELECT Id FROM RecordType WHERE DeveloperName = 'Society' AND SObjectType = 'Account' LIMIT 1].Id);
       insert account2;

       Account account3 = new Account(Name = 'Test Society 2', ExternalId__c = 'Test3', RecordTypeId = [SELECT Id FROM RecordType WHERE DeveloperName = 'IndustriesBusiness' AND SObjectType = 'Account' LIMIT 1].Id);
       insert account3;
    
       // Creazione di un KPI__c di esempio con il campo obbligatorio "Dimensione__c"
       KPI__c testKPI2 = new KPI__c(
        Agency__c = account1.Id,
        ContactRate__c = 85.0,
        ScoreTotal__c = 100.0
        // Dimensione__c = 'Large' // Valore obbligatorio per il campo Dimensione
    );
    insert testKPI2;
       
        // Creazione di un OperatingHours di esempio
        OperatingHours testOperatingHours = new OperatingHours(
            Name = 'Test Operating Hours'
        );
        insert testOperatingHours;
    
        // Creazione di un ServiceTerritory di esempio
        ServiceTerritory testTerritory = new ServiceTerritory(
            Name = 'Test SalesPoint',
            Tipo__c = 'Agenziale',
            OperatingHoursId = testOperatingHours.Id,
            Agency__c = account1.Id,
            Street = 'Test Street',
            City = 'Test City',
            State = 'MI',
            PostalCode = '20100',
            Country = 'Italy'
        );
        insert testTerritory;


       FinServ__AccountAccountRelation__c existingRelation = new FinServ__AccountAccountRelation__c(
           FinServ__Account__c = account3.Id,
           FinServ__RelatedAccount__c = account1.Id,
           FinServ__Role__c = role.Id
       );
       insert existingRelation;

       // Creazione di opportunità associate all'account principale
       Opportunity openOpportunity = new Opportunity(
           Name = 'Open Opportunity',
           AccountId = account3.Id,
           StageName = 'In Progress',
           CloseDate = Date.today().addDays(30),
           Agency__c = account1.Id
       );
       insert openOpportunity;

             // Creazione di un AccountDetails__c di esempio
             AccountDetails__c testAccountDetails = new AccountDetails__c(
                FiscalCode__c = '**********',
                Relation__c = existingRelation.Id
            );
            insert testAccountDetails;
     
    
        
        

    }
    
  
    
    @IsTest
    static void testRankAgencies_ValidInput() {
        // Creazione di un input valido
        EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
        input.source = 'Preventivatore digitale Unica';
        input.macroAreas = new List<String>{'Area1', 'Area2'};
        input.fiscalCode = '**********';
        input.latitude = 45.4642;
        input.longitude = 9.1900;
        input.zipCode = '20100';
        input.useDefaultAgency = false;
        input.agenciestoExcludeIds = new List<Id>();
        input.company= 'UniploSai';
        system.debug('EngineAgencyRanking_V2.mockedResponse: '+ EngineAgencyRanking_V2.mockedResponse);
        Test.startTest();
        EngineWrapper_V2.EngineWrapperOutput response = EngineAgencyRanking_V2.rankAgencies(input);
        Test.stopTest();        
        // System.debug('TESTSSSSS: '+response.matchingSalespoints.size());
        System.debug('TESTSSSSS: '+response);
    }

    @IsTest
    static void testRankAgencies_NoSalesPoints() {
        // Creazione di un input con un CAP non valido
        EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
        input.source = 'Preventivatore digitale Unica';
        input.macroAreas = new List<String>{'Area1', 'Area2'};
        input.fiscalCode = '**********';
        input.latitude =null; // Non necessario per questo test
        input.longitude = 9.1900;
        input.zipCode = '99999'; // CAP non valido
        input.useDefaultAgency = false;
        input.company= 'UniploSai';

        Test.startTest();
        EngineWrapper_V2.EngineWrapperOutput response = EngineAgencyRanking_V2.rankAgencies(input);
        Test.stopTest();
        system.debug('TESTSSSSS response: '+response);
            
     
    }

    @IsTest
    static void testRankAgencies_UseDefaultAgency_NotFound() {
        // Creazione di un input che forza l'uso dell'agenzia predefinita
        EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
        input.source = 'Preventivatore digitale Unica';
        input.macroAreas = new List<String>{'Area1', 'Area2'};
        input.fiscalCode = '**********';
        input.latitude = null;
        input.longitude = 9.1900;
        input.zipCode = '99999'; // CAP non valido
        input.useDefaultAgency = true;
        input.Company= 'UnipolSai';

        Test.startTest();
        EngineWrapper_V2.EngineWrapperOutput response = EngineAgencyRanking_V2.rankAgencies(input);
        Test.stopTest();
        system.debug('TESTSSSSS response: '+response);
    }

    @IsTest
    static void testRankAgencies_NotValidCap() {
        // Creazione di un input che forza l'uso dell'agenzia predefinita
        EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
        input.source = 'Preventivatore digitale Unica';
        input.macroAreas = new List<String>{'Area1', 'Area2'};
        input.fiscalCode = '**********';
        input.latitude = null;
        input.longitude = 9.1900;
        input.zipCode = '99999'; // CAP non valido
        input.useDefaultAgency = true;
        input.company= 'UnipolSai';
        // Creazione di un OperatingHours di esempio
        OperatingHours testOperatingHours5 = new OperatingHours(
            Name = 'Test Operating Hours'
        );
        insert testOperatingHours5;

         // Crea dati di test necessari per i test
       Account account4 = new Account(Name = 'Test Agency 4',ExternalId__c='AG-SOGEINT', OmnichannelAgreement__c = true, RecordTypeId = [SELECT Id FROM RecordType WHERE DeveloperName = 'Agency' AND SObjectType = 'Account' LIMIT 1].Id);
       insert account4;


        // Creazione di un ServiceTerritory di Default
        ServiceTerritory testTerritory5 = new ServiceTerritory(
        Name = 'Test SalesPoint Default',
        Tipo__c = 'Agenziale',
        OperatingHoursId = testOperatingHours5.Id,
        Agency__c = account4.Id,
        Street = 'Test Street Default',
        City = 'Test City',
        State = 'MI',
        PostalCode = '20100',
        Country = 'Italy'
    );
    insert testTerritory5;
    System.debug('account4.Agencycode__c: '+account4.Agencycode__c);

        Test.startTest();
        EngineWrapper_V2.EngineWrapperOutput response = EngineAgencyRanking_V2.rankAgencies(input);
        Test.stopTest();
        system.debug('TESTSSSSS response: '+response);
    }


    // @IsTest
    // static void testRankAgencies_UseDefaultAgency() {
    //     // Creazione di un input che forza l'uso dell'agenzia predefinita
    //     EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
    //     input.source = 'Preventivatore digitale Unica';
    //     input.macroAreas = new List<String>{'Area1', 'Area2'};
    //     input.fiscalCode = '**********';
    //     input.latitude = null;
    //     input.longitude = 9.1900;
    //     input.zipCode = '22200';//ZipCode non presente in considerazione
    //     input.useDefaultAgency = true;
    //     // Creazione di un OperatingHours di esempio
    //     OperatingHours testOperatingHours = new OperatingHours(
    //         Name = 'Test Operating Hours'
    //     );
    //     insert testOperatingHours;

    //      // Crea dati di test necessari per i test
    //    Account account4 = new Account(Name = 'Test Agency 4',ExternalId__c='AG-SOGEINT', OmnichannelAgreement__c = true, RecordTypeId = [SELECT Id FROM RecordType WHERE DeveloperName = 'Agency' AND SObjectType = 'Account' LIMIT 1].Id);
    //    insert account4;


    //     // Creazione di un ServiceTerritory di Default
    //     ServiceTerritory testTerritory = new ServiceTerritory(
    //     Name = 'Test SalesPoint Default',
    //     Tipo__c = 'Agenziale',
    //     OperatingHoursId = testOperatingHours.Id,
    //     Agency__c = account4.Id,
    //     Street = 'Test Street Default',
    //     City = 'Test City',
    //     State = 'MI',
    //     PostalCode = '10200',
    //     Country = 'Italy'
    // );
    // insert testTerritory;
    // System.debug('account4.Agencycode__c: '+account4.Agencycode__c);

    //     Test.startTest();
    //     EngineWrapper_V2.EngineWrapperOutput response = EngineAgencyRanking_V2.rankAgencies(input);
    //     Test.stopTest();
    //     system.debug('TESTSSSSS response: '+response);
    // }

    @IsTest
    static void testCheckSubjectType_ProspectPuro() {
        Test.startTest();
        String subjectType = EngineAgencyRanking_V2.checkSubjectType('12345dd890', null);
        Test.stopTest();
    }

    @IsTest
    static void testCheckSubjectType_ClientExCliente() {                

        Test.startTest();
        String subjectType = EngineAgencyRanking_V2.checkSubjectType('**********', null);
        Test.stopTest();

    }

    @IsTest
    static void testRankAgencies_InvalidInput() {
        // Creazione di un input non valido
        EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
        input.source = null; // Input non valido

        Test.startTest();
        try {
            EngineWrapper_V2.EngineWrapperOutput response = EngineAgencyRanking_V2.rankAgencies(input);
        } catch (EngineAgencyRanking_V2.AgencyRankingException e) {
        }
        Test.stopTest();
    }

    @IsTest
    static void testRankAgencies_ProspectAnagrafato() {
        // Creazione di un input con subjectType "PROSPECT_ANAGRAFATO"
        EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
        input.source = 'Preventivatore digitale Unica';
        input.macroAreas = new List<String>{'Area1', 'Area2'};
        input.fiscalCode = '**********';
        input.latitude = 45.4642;
        input.longitude = 9.1900;
        input.zipCode = '20100';
        input.useDefaultAgency = false;  
        input.company= 'UnipolSai';              

        // Mock del subjectType
        Test.startTest();
        String subjectType = EngineAgencyRanking_V2.checkSubjectType('**********', null);
        EngineWrapper_V2.EngineWrapperOutput response = EngineAgencyRanking_V2.rankAgencies(input);
        Test.stopTest();
    
    }

    @IsTest
static void testRankAgencies_ClientExCliente2Anni() {
    // Creazione di un input con subjectType "CLIENT_EX_CLIENTE_2_ANNI"
    EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
    input.source = 'Preventivatore digitale Unica';
    input.macroAreas = new List<String>{'Area1', 'Area2'};
    input.fiscalCode = '***********';
    input.latitude = 45.4642;
    input.longitude = 9.1900;
    input.zipCode = '20100';
    input.useDefaultAgency = false;
    input.Company ='UniploSai';

    // Creazione di un Account di esempio
    Account testAccount6 = new Account(
        Name = 'Test Client Account 6',
        ExternalId__c = 'Client123',
        RecordTypeId = [SELECT Id FROM RecordType WHERE DeveloperName = 'IndustriesBusiness' AND SObjectType = 'Account' LIMIT 1].Id
    );
    insert testAccount6;

      // Crea dati di test necessari per i test
      Account account6 = new Account(Name = 'Test Agency 6', ExternalId__c = 'Test6', OmnichannelAgreement__c = true, RecordTypeId = [SELECT Id FROM RecordType WHERE DeveloperName = 'Agency' AND SObjectType = 'Account' LIMIT 1].Id);
      insert account6;

      Account account7 = new Account(Name = 'Test Society 7', ExternalId__c = 'Test7' ,RecordTypeId = [SELECT Id FROM RecordType WHERE DeveloperName = 'Society' AND SObjectType = 'Account' LIMIT 1].Id);
       insert account7;

       //Crea un record di ruolo (supponendo che l'oggetto sia FinServ__ReciprocalRole__c)
       FinServ__ReciprocalRole__c role2 = new FinServ__ReciprocalRole__c(Name = 'Cliente', FinServ__InverseRole__c = 'Agenzia');
       insert role2;


        FinServ__AccountAccountRelation__c existingRelation2 = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = testAccount6.Id,
            FinServ__RelatedAccount__c = account6.Id,
            FinServ__Role__c = role2.Id
        );
        insert existingRelation2;

        // Creazione di un AccountDetails__c per simulare un cliente esistente o ex cliente
    AccountDetails__c accountDetails2 = new AccountDetails__c(
        FiscalCode__c = '***********',
        Relation__c = existingRelation2.Id // Relazione con l'account esistente
    );
    insert accountDetails2;


 
       Id recordTypeUnica =  SObjectType.InsurancePolicy.getRecordTypeInfosByDeveloperName().get('UNICA').getRecordTypeId();

        // Creazione di un record InsurancePolicy__c
        InsurancePolicy testPolicy = new InsurancePolicy(
            Name = 'TestName1',
            PremiumAmount = 280,
            Agency__c = account6.Id,
            Society__c = account7.Id,
            RecordTypeId = recordTypeUnica,
            AreaOfNeed__c = 'Salute',
            NameInsuredId = testAccount6.Id,
            CIP__c = 'test',
            CompanyCode__c = 'Test Company',
            Company__c = 'TestCompany'
        );
        insert testPolicy;

        // Creazione di un OperatingHours di esempio
        OperatingHours testOperatingHours6 = new OperatingHours(
            Name = 'Test Operating Hours'
        );
        insert testOperatingHours6;

        // Creazione di un ServiceTerritory di Default
        ServiceTerritory testTerritory = new ServiceTerritory(
        Name = 'Test SalesPoint Default other',
        Tipo__c = 'Agenziale',
        OperatingHoursId = testOperatingHours6.Id,
        Agency__c = account6.Id,
        Street = 'Test Street Default',
        City = 'Test City',
        State = 'MI',
        PostalCode = '10200',
        Country = 'Italy'
    );
    insert testTerritory;


    // Mock del subjectType
    Test.startTest();
    String subjectType = EngineAgencyRanking_V2.checkSubjectType('***********', null);
    EngineWrapper_V2.EngineWrapperOutput response = EngineAgencyRanking_V2.rankAgencies(input);
    Test.stopTest();

}

@IsTest
static void testApplyBlackOmniExcludeAgeFilter() {
    // Creazione di un Account con OmnichannelAgreement attivo
    Account agencyWithOmnichannel = new Account(
        Name = 'Agency With Omnichannel',
        OmnichannelAgreement__c = true,
        Blacklist__c = false,
        Size__c= 'Grande',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agencyWithOmnichannel;

    // Creazione di un Account senza OmnichannelAgreement
    Account agencyWithoutOmnichannel = new Account(
        Name = 'Agency Without Omnichannel',
        OmnichannelAgreement__c = false,
        Blacklist__c = false,
        Size__c= 'Grande',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agencyWithoutOmnichannel;

    // Creazione di un OperatingHours di esempio
    OperatingHours testOperatingHours3 = new OperatingHours(
        Name = 'Test Operating Hours'
    );
    insert testOperatingHours3;

    // Creazione di ServiceTerritory associati agli account
    ServiceTerritory serviceTerritoryWithOmnichannel = new ServiceTerritory(
        Name = 'Service Territory With Omnichannel',
        Agency__c = agencyWithOmnichannel.Id,
        OperatingHoursId = testOperatingHours3.Id,
        Tipo__c = 'Agenziale',
        IsActive = true,
        Street = 'Test Street',
        City = 'Test City',
        State = 'MI',
        PostalCode = '20100',
        Country = 'Italy'
    );
    insert serviceTerritoryWithOmnichannel;

    ServiceTerritory serviceTerritoryWithoutOmnichannel = new ServiceTerritory(
        Name = 'Service Territory Without Omnichannel',
        Agency__c = agencyWithoutOmnichannel.Id,
        OperatingHoursId = testOperatingHours3.Id,
        Tipo__c = 'Agenziale',
        IsActive = true,
        Street = 'Test Street',
        City = 'Test City',
        State = 'MI',
        PostalCode = '20100',
        Country = 'Italy'
    );
    insert serviceTerritoryWithoutOmnichannel;

    // Creazione di KPI associati agli account
    KPI__c kpiWithOmnichannel = new KPI__c(
        Agency__c = agencyWithOmnichannel.Id,
        ContactRate__c = 85.0,
        ScoreTotal__c = 100.0
    );
    insert kpiWithOmnichannel;

    KPI__c kpiWithoutOmnichannel = new KPI__c(
        Agency__c = agencyWithoutOmnichannel.Id,
        ContactRate__c = 70.0,
        ScoreTotal__c = 90.0
    );
    insert kpiWithoutOmnichannel;

    // Creazione della mappa KPI
    Map<Id, KPI__c> agencyKPIMap = new Map<Id, KPI__c>{
        agencyWithOmnichannel.Id => kpiWithOmnichannel,
        agencyWithoutOmnichannel.Id => kpiWithoutOmnichannel
    };

    // Lista di ServiceTerritory da filtrare
    List<ServiceTerritory> serviceTerritories = new List<ServiceTerritory>{
        serviceTerritoryWithOmnichannel,
        serviceTerritoryWithoutOmnichannel
    };

    List<ServiceTerritory> terrytoryList = [
            SELECT Id, OwnerId, IsDeleted, Name, Address,Agency__c,ExternalId__c,Tipo__c,Address__c,Agency__r.Id,Agency__r.ExternalId__c
            FROM ServiceTerritory 
            WHERE id IN :serviceTerritories
        ]; 

        for(ServiceTerritory st : terrytoryList) {
            System.debug('TEST ServiceTerritory: ' + st);
            System.debug('TEST ServiceTerritory Address: ' + st.Address);
        }



    // Input per il metodo
    EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
    input.latitude = 45.4642;
    input.longitude = 9.1900;

    // Esecuzione del metodo
    Test.startTest();
    List<ServiceTerritory> filteredServiceTerritories = EngineAgencyRanking_V2.applyBlackOmniExcludeAgeFilter(
        terrytoryList,
        input,
        agencyKPIMap
    );
    Test.stopTest();
}

@IsTest
static void testApplyCapRayContactFilter_ExcludeByCAP() {
    // Creazione di un Account con CheckCAPAssignedContactActivities impostato su "Oltre il CAP"
    Account agencyAccount = new Account(
        Name = 'Agency Out of CAP',
        OmnichannelAgreement__c = true,
        CAPActivity__c =10,
        AgencyLoad__c = 11,
        // CheckCAPAssignedContactActivities__c = 'Oltre il CAP',
        RecordTypeId =Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agencyAccount;
   

    List<Account> acList = [
        SELECT Id, AgencyLoad__c,CheckCAPAssignedContactActivities__c
        FROM Account 
        WHERE id = :agencyAccount.Id
    ]; 

         // CheckCAPAssignedContactActivities__c = 'Oltre il CAP',
         System.debug('agencyAccount.size: '+acList.size());
         System.debug('agencyAccount.CheckCAPAssignedContactActivities__c: '+acList[0].CheckCAPAssignedContactActivities__c);
         System.debug('agencyAccount.AgencyLoad__c: '+acList[0].AgencyLoad__c);

     // Creazione di un OperatingHours di esempio
     OperatingHours testOperatingHours3 = new OperatingHours(
        Name = 'Test Operating Hours'
    );
    insert testOperatingHours3;

    // Creazione di un ServiceTerritory associato all'account
    ServiceTerritory serviceTerritory = new ServiceTerritory(
        Name = 'Service Territory Out of CAP',
        Agency__c = agencyAccount.Id,
        Latitude = 45.4642,
        Longitude = 9.1900,
        IsActive = true,
        OperatingHoursId = testOperatingHours3.Id,
        Tipo__c = 'Agenziale',
        Street = 'Test Street',
        City = 'Test City',
        State = 'MI',
        PostalCode = '20100',
        Country = 'Italy'
    );
    insert serviceTerritory;

    // Creazione della mappa KPI
    Map<Id, KPI__c> agencyKPIMap = new Map<Id, KPI__c>();

    // Creazione di un input per il test
    EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
    input.latitude = 45.4642;
    input.longitude = 9.1900;

    // Creazione di dati meritocratici
    Map<String, Object> meritocraticData = new Map<String, Object>{
        'RaggioCentroideKM' => 5.0, // Raggio di estensione
        '%TassoContattabilità' => 40.0 // Soglia di tasso di contattabilità
    };

    // Lista di ServiceTerritory da filtrare
    List<ServiceTerritory> serviceTerritories = new List<ServiceTerritory>{ serviceTerritory };    
    List<ServiceTerritory> terrytoryList = [
            SELECT Id, OwnerId, IsDeleted, Name, Address,Agency__c,ExternalId__c,Tipo__c,Address__c,Agency__r.CheckCAPAssignedContactActivities__c,Agency__r.Id,Agency__r.ExternalId__c
            FROM ServiceTerritory 
            WHERE id IN :serviceTerritories
        ]; 

    // Esecuzione del metodo
    Test.startTest();
    List<ServiceTerritory> filteredServiceTerritories = EngineAgencyRanking_V2.applyCapRayContactFilter(
        terrytoryList,
        input,
        meritocraticData,
        agencyKPIMap
    );
    Test.stopTest();

}

@IsTest
static void testApplyCapRayContactFilter_ExcludeByRadius() {
    // Creazione di un Account
    Account agencyAccount = new Account(
        Name = 'Agency Out of Radius',
        OmnichannelAgreement__c = true,
        CAPActivity__c =10,
        AgencyLoad__c = 9,
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agencyAccount;

     // Creazione di un OperatingHours di esempio
     OperatingHours testOperatingHours3 = new OperatingHours(
        Name = 'Test Operating Hours'
    );
    insert testOperatingHours3;

    // Creazione di un ServiceTerritory associato all'account
    ServiceTerritory serviceTerritory = new ServiceTerritory(
        Name = 'Service Territory Out of Radius',
        Agency__c = agencyAccount.Id,
        Latitude = 47.4642,
        Longitude = 10.1900,
        IsActive = true,
        OperatingHoursId = testOperatingHours3.Id,
        Tipo__c = 'Agenziale',
        Street = 'Test Street',
        City = 'Test City',
        State = 'MI',
        PostalCode = '20100',
        Country = 'Italy'
    );
    insert serviceTerritory;


    // Creazione della mappa KPI
    Map<Id, KPI__c> agencyKPIMap = new Map<Id, KPI__c>();

    // Creazione di un input per il test
    EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
    input.latitude = 45.4642;
    input.longitude = 9.1900;

    // Creazione di dati meritocratici
    Map<String, Object> meritocraticData = new Map<String, Object>{
        'RaggioCentroideKM' => 5.0, // Raggio di estensione
        '%TassoContattabilità' => 40.0 // Soglia di tasso di contattabilità
    };

     // Lista di ServiceTerritory da filtrare
     List<ServiceTerritory> serviceTerritories = new List<ServiceTerritory>{ serviceTerritory };    
     List<ServiceTerritory> terrytoryList = [
             SELECT Id, OwnerId, IsDeleted, Name, Address,Agency__c,ExternalId__c,Tipo__c,Address__c,Agency__r.CheckCAPAssignedContactActivities__c,Agency__r.Id,Agency__r.ExternalId__c
             FROM ServiceTerritory 
             WHERE id IN :serviceTerritories
         ]; 
 

    // Esecuzione del metodo
    Test.startTest();
    List<ServiceTerritory> filteredServiceTerritories = EngineAgencyRanking_V2.applyCapRayContactFilter(
        terrytoryList,
        input,
        meritocraticData,
        agencyKPIMap
    );
    Test.stopTest();

}


@IsTest
static void testApplyCapRayContactFilter_ExcludeByContactRate() {
    // Creazione di un Account
    Account agencyAccount = new Account(
        Name = 'Agency Low Contact Rate',
        OmnichannelAgreement__c = true,
        CAPActivity__c =10,
        AgencyLoad__c = 4,
        Size__c= 'Grande',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agencyAccount;


     // Creazione di un OperatingHours di esempio
     OperatingHours testOperatingHours3 = new OperatingHours(
        Name = 'Test Operating Hours'
    );
    insert testOperatingHours3;

    List<Account> acList = [
        SELECT Id, AgencyLoad__c,CheckCAPAssignedContactActivities__c,CAPActivity__c
        FROM Account 
        WHERE id = :agencyAccount.Id
    ]; 

         // CheckCAPAssignedContactActivities__c = 'Oltre il CAP',
         System.debug('agencyAccount.size: '+acList.size());
         System.debug('agencyAccount.CheckCAPAssignedContactActivities__c: '+acList[0].CheckCAPAssignedContactActivities__c);
         System.debug('agencyAccount.CAPActivity__c: '+acList[0].CAPActivity__c);
         System.debug('agencyAccount.AgencyLoad__c: '+acList[0].AgencyLoad__c);

    

    // Creazione di un KPI con tasso di contattabilità basso
    KPI__c kpi = new KPI__c(
        Agency__c = agencyAccount.Id,
        ContactRate__c = 30.0 // Inferiore alla soglia
    );
    insert kpi;

    // Creazione della mappa KPI
    Map<Id, KPI__c> agencyKPIMap = new Map<Id, KPI__c>{
        agencyAccount.Id => kpi
    };

    // Creazione di un input per il test
    EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
    input.latitude = 45.4642;
    input.longitude = 9.1900;

    // Creazione di dati meritocratici
    Map<String, Object> meritocraticData = new Map<String, Object>{
        'RaggioCentroideKM' => 5.0, // Raggio di estensione
        '%TassoContattabilità' => 40.0 // Soglia di tasso di contattabilità
    };

    
         
    List<Account> acList2 = [
        SELECT Id, AgencyLoad__c,CheckCAPAssignedContactActivities__c,CAPActivity__c
        FROM Account 
        WHERE id = :agencyAccount.Id
    ]; 

         // CheckCAPAssignedContactActivities__c = 'Oltre il CAP',
         System.debug('agencyAccount.size: '+acList2.size());
         System.debug('agencyAccount.CheckCAPAssignedContactActivities__c: '+acList2[0].CheckCAPAssignedContactActivities__c);
         System.debug('agencyAccount.CAPActivity__c: '+acList2[0].CAPActivity__c);
         System.debug('agencyAccount.AgencyLoad__c: '+acList2[0].AgencyLoad__c);

         Account agencyAccountToUpdate = new Account(
            Name = 'Agency Low Contact Rate',
            OmnichannelAgreement__c = true,
            CAPActivity__c =10,
            AgencyLoad__c = 4,
            Size__c= 'Grande',
            id=acList2[0].Id
        );
        update agencyAccount;


        List<Account> acList3 = [
        SELECT Id, AgencyLoad__c,CheckCAPAssignedContactActivities__c,CAPActivity__c
        FROM Account 
        WHERE id = :agencyAccount.Id
    ]; 

         // CheckCAPAssignedContactActivities__c = 'Oltre il CAP',
         System.debug('agencyAccount.size: '+acList3.size());
         System.debug('agencyAccount.CheckCAPAssignedContactActivities__c: '+acList3[0].CheckCAPAssignedContactActivities__c);
         System.debug('agencyAccount.CAPActivity__c: '+acList3[0].CAPActivity__c);
         System.debug('agencyAccount.AgencyLoad__c: '+acList3[0].AgencyLoad__c);

         // Creazione di un ServiceTerritory associato all'account
    ServiceTerritory serviceTerritory = new ServiceTerritory(
        Name = 'Service Territory Low Contact Rate',
        Agency__c = acList[0].Id,
        Latitude = 45.4642,
        Longitude = 9.1900,
        IsActive = true,
        OperatingHoursId = testOperatingHours3.Id,
        Tipo__c = 'Agenziale',
        Street = 'Test Street',
        City = 'Test City',
        State = 'MI',
        PostalCode = '20100',
        Country = 'Italy'
    );
    insert serviceTerritory;

     // Lista di ServiceTerritory da filtrare
     List<ServiceTerritory> serviceTerritories = new List<ServiceTerritory>{ serviceTerritory };    
     List<ServiceTerritory> terrytoryList = [
             SELECT Id, OwnerId, IsDeleted, Name, Address,Agency__c,ExternalId__c,Tipo__c,Address__c,Agency__r.CheckCAPAssignedContactActivities__c,Agency__r.Id,Agency__r.ExternalId__c
             FROM ServiceTerritory 
             WHERE id IN :serviceTerritories
         ]; 
 

    // Esecuzione del metodo
    Test.startTest();
    List<ServiceTerritory> filteredServiceTerritories = EngineAgencyRanking_V2.applyCapRayContactFilter(
        terrytoryList,
        input,
        meritocraticData,
        agencyKPIMap
    );
    Test.stopTest();

}


@IsTest
static void testApplyCapRayContactFilter_IncludeDefaultAgency() {
    // Creazione di un input con useDefaultAgency impostato su true
    EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
    input.latitude = 45.4642;
    input.longitude = 9.1900;
    input.useDefaultAgency = true;

    // Creazione di dati meritocratici
    Map<String, Object> meritocraticData = new Map<String, Object>{
        'AgenziaDefault' => 'DefaultAgency123'
    };

      // Crea dati di test necessari per i test
       Account agencyDefault = new Account(Name = 'Test Agency 4',ExternalId__c='DefaultAgency123', OmnichannelAgreement__c = true, RecordTypeId = [SELECT Id FROM RecordType WHERE DeveloperName = 'Agency' AND SObjectType = 'Account' LIMIT 1].Id);
       insert agencyDefault;

         // Creazione di un OperatingHours di esempio
     OperatingHours testOperatingHours3 = new OperatingHours(
        Name = 'Test Operating Hours'
    );
    insert testOperatingHours3;

        // Creazione di un ServiceTerritory di Default
        ServiceTerritory testTerritory = new ServiceTerritory(
        Name = 'Test SalesPoint Default',
        Tipo__c = 'Agenziale',
        OperatingHoursId = testOperatingHours3.Id,
        Agency__c = agencyDefault.Id,
        Street = 'Test Street Default',
        City = 'Test City',
        State = 'MI',
        PostalCode = '10200',
        Country = 'Italy'
    );
    insert testTerritory;
    
    List<Account> acList = [
        SELECT Id, AgencyLoad__c,Agencycode__c, ExternalId__c
        FROM Account 
        WHERE id = :agencyDefault.Id
    ]; 


    System.debug('TEST acList: '+acList);
    System.debug('TEST agencyDefault.Agencycode__c: '+acList[0].Agencycode__c);

    // Esecuzione del metodo
    Test.startTest();
    List<ServiceTerritory> filteredServiceTerritories = EngineAgencyRanking_V2.applyCapRayContactFilter(
        new List<ServiceTerritory>(),
        input,
        meritocraticData,
        new Map<Id, KPI__c>()
    );
    Test.stopTest();

}


@IsTest
static void testCalculateDistance_ValidInput() {
    // Input validi
    Double lat1 = 45.4642;
    Double lon1 = 9.1900;
    Double lat2 = 46.0667;
    Double lon2 = 11.1500;

    // Esecuzione del metodo
    Test.startTest();
    Double distance = EngineAgencyRanking_V2.calculateDistance(lat1, lon1, lat2, lon2);
    Test.stopTest();
}

@IsTest
static void testCalculateDistance_SamePoints() {
    // Input identici
    Double lat1 = 45.4642;
    Double lon1 = 9.1900;
    Double lat2 = 45.4642;
    Double lon2 = 9.1900;

    // Esecuzione del metodo
    Test.startTest();
    Double distance = EngineAgencyRanking_V2.calculateDistance(lat1, lon1, lat2, lon2);
    Test.stopTest();
}

@IsTest
static void testCalculateDistance_NullInput() {
    try {
        // Input nulli
        Double lat1 = null;
        Double lon1 = 9.1900;
        Double lat2 = 46.0667;
        Double lon2 = 11.1500;

        // Esecuzione del metodo
        Test.startTest();
        EngineAgencyRanking_V2.calculateDistance(lat1, lon1, lat2, lon2);
        Test.stopTest();

    } catch (EngineAgencyRanking_V2.AgencyRankingException e) {

    }
}

@IsTest
static void testCalculateDistance_ExtremeValues() {
    // Input con valori estremi
    Double lat1 = -90.0; // Latitudine minima
    Double lon1 = -180.0; // Longitudine minima
    Double lat2 = 90.0; // Latitudine massima
    Double lon2 = 180.0; // Longitudine massima

    // Esecuzione del metodo
    Test.startTest();
    Double distance = EngineAgencyRanking_V2.calculateDistance(lat1, lon1, lat2, lon2);
    Test.stopTest();
}


@IsTest
static void testGetParentAgency_ValidInput() {
    // Creazione di un Account di esempio
    Account parentAgency = new Account(
        Name = 'Parent Agency',
        ExternalId__c = 'Parent123',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert parentAgency;

    // Creazione di un OperatingHours di esempio
    OperatingHours testOperatingHours = new OperatingHours(
        Name = 'Test Operating Hours'
    );
    insert testOperatingHours;

    // Creazione di un ServiceTerritory associato all'agenzia padre
    ServiceTerritory testTerritory = new ServiceTerritory(
        Name = 'Test SalesPoint Default',
        Tipo__c = 'Agenziale',
        OperatingHoursId = testOperatingHours.Id,
        Agency__c = parentAgency.Id,
        Street = 'Test Street Default',
        City = 'Test City',
        State = 'MI',
        PostalCode = '10200',
        Country = 'Italy'
    );
    insert testTerritory;

    // Esecuzione del metodo
    Test.startTest();
    EngineWrapper_V2.EngineAgency parentAgencyResult = EngineAgencyRankingHelper.getParentAgency(testTerritory);
    Test.stopTest();
}

@IsTest
static void testGetParentAgency_NoAgency() {
    // Creazione di un OperatingHours di esempio
    OperatingHours testOperatingHours = new OperatingHours(
        Name = 'Test Operating Hours'
    );
    insert testOperatingHours;

    // Creazione di un ServiceTerritory senza agenzia associata
    ServiceTerritory testTerritory = new ServiceTerritory(
        Name = 'Test SalesPoint Default',
        Tipo__c = 'Agenziale',
        OperatingHoursId = testOperatingHours.Id,
        Street = 'Test Street Default',
        City = 'Test City',
        State = 'MI',
        PostalCode = '10200',
        Country = 'Italy'
    );
    insert testTerritory;

    // Esecuzione del metodo
    Test.startTest();
    EngineWrapper_V2.EngineAgency parentAgencyResult = EngineAgencyRankingHelper.getParentAgency(testTerritory);
    Test.stopTest();
}

@IsTest
static void testAddAccountsToExclusionSalespoint_ValidServiceTerritories() {
    // Creazione di un Account di esempio
    Account agencyAccount = new Account(
        Name = 'Test Agency',
        ExternalId__c = 'Agency1234',
        Size__c= 'Grande',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agencyAccount;

    // Creazione di un OperatingHours di esempio
    OperatingHours testOperatingHours = new OperatingHours(
        Name = 'Test Operating Hours'
    );
    insert testOperatingHours;

    // Creazione di un ServiceTerritory associato all'account
    ServiceTerritory serviceTerritory = new ServiceTerritory(
        Name = 'Test SalesPoint',
        Tipo__c = 'Agenziale',
        OperatingHoursId = testOperatingHours.Id,
        Agency__c = agencyAccount.Id,
        Street = 'Test Street',
        City = 'Test City',
        State = 'MI',
        PostalCode = '10200',
        Country = 'Italy'
    );
    insert serviceTerritory;

    // Recupero del ServiceTerritory tramite query
    List<ServiceTerritory> serviceTerritoriesToExclude = [
        SELECT id,  Name,  ParentTerritoryId, TopLevelTerritoryId, Description, OperatingHoursId, Street, City, State, PostalCode,
        Country, Latitude, Longitude, GeocodeAccuracy, Address, IsActive, TypicalInTerritoryTravelTime, Agency__c, Address__c, Tipo__c, Account__c,
        Account__r.RecordType.DeveloperName, Account__r.name, Punto_vendita_principale__c, AccountAddress__c, Code__c, Email__c, ExternalId__c, Phone__c, Agency__r.KPI__c, Agency__r.ExternalId__c,Agency__r.DiscretionalityMotor__c,
        Agency__r.CheckCAPAssignedContactActivities__c,Agency__r.Discretionality__c, Agency__r.DiscretionalityWelfare__c, Agency__r.DiscretionalityProperty__c , Agency__r.DiscretionalityEnterprise__c
        FROM ServiceTerritory
        WHERE Agency__c = :agencyAccount.Id
    ];

    // Creazione di una mappa KPI
    KPI__c kpi = new KPI__c(
        Agency__c = agencyAccount.Id,
        ContactRate__c = 85.0,
        ScoreTotal__c = 100.0
    );
    insert kpi;

    Map<Id, KPI__c> agencyKPIMap = new Map<Id, KPI__c>{ agencyAccount.Id => kpi };

    // Creazione di un input per il metodo
    EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
    input.latitude = 45.4642;
    input.longitude = 9.1900;

    // Esecuzione del metodo
    Test.startTest();
    EngineAgencyRanking_V2.addAccountsToExclusionSalespoint(serviceTerritoriesToExclude, 'Test Reason', input, agencyKPIMap);
    Test.stopTest();
}

@IsTest
static void testAddAccountsToExclusionSalespoint_EmptyList() {
    // Creazione di un input per il metodo
    EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
    input.latitude = 45.4642;
    input.longitude = 9.1900;

    // Lista vuota di ServiceTerritory
    List<ServiceTerritory> serviceTerritoriesToExclude = new List<ServiceTerritory>();

    // Esecuzione del metodo
    Test.startTest();
    EngineAgencyRanking_V2.addAccountsToExclusionSalespoint(serviceTerritoriesToExclude, 'Test Reason', input, null);
    Test.stopTest();
}

@IsTest
static void testAddAccountsToExclusionSalespoint_NullAddress() {
    // Creazione di un Account di esempio
    Account agencyAccount = new Account(
        Name = 'Test Agency',
        ExternalId__c = 'Agency1234',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agencyAccount;

     // Creazione di un OperatingHours di esempio
     OperatingHours testOperatingHours = new OperatingHours(
        Name = 'Test Operating Hours'
    );
    insert testOperatingHours;

    // Creazione di un ServiceTerritory senza indirizzo
    ServiceTerritory serviceTerritory = new ServiceTerritory(
        Name = 'Test SalesPoint',
        Tipo__c = 'Agenziale',
        OperatingHoursId = testOperatingHours.Id,
        Agency__c = agencyAccount.Id
    );
    insert serviceTerritory;

    // Recupero del ServiceTerritory tramite query
    List<ServiceTerritory> serviceTerritoriesToExclude = [
        SELECT id,  Name,  ParentTerritoryId, TopLevelTerritoryId, Description, OperatingHoursId, Street, City, State, PostalCode,
        Country, Latitude, Longitude, GeocodeAccuracy, Address, IsActive, TypicalInTerritoryTravelTime, Agency__c, Address__c, Tipo__c, Account__c,
        Account__r.RecordType.DeveloperName, Account__r.name, Punto_vendita_principale__c, AccountAddress__c, Code__c, Email__c, ExternalId__c, Phone__c, Agency__r.KPI__c, Agency__r.ExternalId__c,Agency__r.DiscretionalityMotor__c,
        Agency__r.CheckCAPAssignedContactActivities__c,Agency__r.Discretionality__c, Agency__r.DiscretionalityWelfare__c, Agency__r.DiscretionalityProperty__c , Agency__r.DiscretionalityEnterprise__c
        FROM ServiceTerritory
        WHERE Agency__c = :agencyAccount.Id
    ];

    // Creazione di un input per il metodo
    EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
    input.latitude = 45.4642;
    input.longitude = 9.1900;

    // Esecuzione del metodo e gestione dell'eccezione
    try {
        Test.startTest();
        EngineAgencyRanking_V2.addAccountsToExclusionSalespoint(serviceTerritoriesToExclude, 'Test Reason', input, null);
        Test.stopTest();
    } catch (EngineAgencyRanking_V2.AgencyRankingException e) {
        // Asserzione per verificare il messaggio dell'eccezione
    }
}

@IsTest
static void testGetParentAgencyKPIMap_ValidParentKPI() {
    // Creazione di un'agenzia genitrice
    Account parentAgency = new Account(
        Name = 'Parent Agency',
        ExternalId__c = 'Parent123',
        Size__c= 'Grande',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert parentAgency;

    // Creazione di un'agenzia figlia
    Account childAgency = new Account(
        Name = 'Child Agency',
        ParentId = parentAgency.Id,
        ExternalId__c = 'Child123',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert childAgency;

    // Creazione di un KPI per l'agenzia genitrice
    KPI__c parentKPI = new KPI__c(
        Agency__c = parentAgency.Id,
        ContactRate__c = 90.0,
        ScoreTotal__c = 95.0
    );
    insert parentKPI;

    // Creazione della mappa KPI iniziale
    Map<Id, KPI__c> agencyKPIMap = new Map<Id, KPI__c>{
        childAgency.Id => null // L'agenzia figlia non ha KPI
    };

    // Esecuzione del metodo
    Test.startTest();
    Map<Id, KPI__c> updatedKPIMap = EngineAgencyRanking_V2.getParentAgencyKPIMap(agencyKPIMap);
    Test.stopTest();
}


@IsTest
static void testGetParentAgencyKPIMap_NoParent() {
    // Creazione di un'agenzia senza genitore
    Account agency = new Account(
        Name = 'Standalone Agency',
        ExternalId__c = 'Standalone123',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agency;

    // Creazione della mappa KPI iniziale
    Map<Id, KPI__c> agencyKPIMap = new Map<Id, KPI__c>{
        agency.Id => null
    };

    // Esecuzione del metodo
    Test.startTest();
    Map<Id, KPI__c> updatedKPIMap = EngineAgencyRanking_V2.getParentAgencyKPIMap(agencyKPIMap);
    Test.stopTest();

}

@IsTest
static void testGetParentAgencyKPIMap_ParentWithoutKPI() {
    // Creazione di un'agenzia genitrice senza KPI
    Account parentAgency = new Account(
        Name = 'Parent Agency Without KPI',
        ExternalId__c = 'ParentNoKPI',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert parentAgency;

    // Creazione di un'agenzia figlia
    Account childAgency = new Account(
        Name = 'Child Agency',
        ParentId = parentAgency.Id,
        ExternalId__c = 'Child123',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert childAgency;

    // Creazione della mappa KPI iniziale
    Map<Id, KPI__c> agencyKPIMap = new Map<Id, KPI__c>{
        childAgency.Id => null
    };

    // Esecuzione del metodo
    Test.startTest();
    Map<Id, KPI__c> updatedKPIMap = EngineAgencyRanking_V2.getParentAgencyKPIMap(agencyKPIMap);
    Test.stopTest();

}

@IsTest
static void testGetParentAgencyKPIMap_NullInput() {
    try {
        // Esecuzione del metodo con input nullo
        Test.startTest();
        Map<Id, KPI__c> updatedKPIMap = EngineAgencyRanking_V2.getParentAgencyKPIMap(null);
        Test.stopTest();

    } catch (NullPointerException e) {
        // Asserzione per verificare che venga lanciata un'eccezione
    }
}

@IsTest
static void testGetParentAgencyKPIMap_EmptyKPIMap() {
    // Creazione di una mappa KPI vuota
    Map<Id, KPI__c> agencyKPIMap = new Map<Id, KPI__c>();

    // Esecuzione del metodo
    Test.startTest();
    Map<Id, KPI__c> updatedKPIMap = EngineAgencyRanking_V2.getParentAgencyKPIMap(agencyKPIMap);
    Test.stopTest();

}


@IsTest
static void testGetServiceTerritoriesWithExtensionRadius_WithinRadius() {
    // Creazione di un Account di esempio
    Account agencyAccount = new Account(
        Name = 'Test Agency',
        ExternalId__c = 'Agency12377',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agencyAccount;

    // Creazione di un OperatingHours di esempio
    OperatingHours testOperatingHours = new OperatingHours(
        Name = 'Test Operating Hours'
    );
    insert testOperatingHours;

    // Creazione di un ServiceTerritory all'interno del raggio
    ServiceTerritory serviceTerritory = new ServiceTerritory(
        Name = 'Test SalesPoint',
        Tipo__c = 'Agenziale',
        OperatingHoursId = testOperatingHours.Id,
        Agency__c = agencyAccount.Id,
        Street = 'Test Street',
        City = 'Test City',
        State = 'MI',
        PostalCode = '10200',
        Country = 'Italy',
        Latitude = 45.4642,
        Longitude = 9.1900
    );
    insert serviceTerritory;

    // Recupero del ServiceTerritory tramite query
    List<ServiceTerritory> serviceTerritories = [
        SELECT id,  Name,  ParentTerritoryId, TopLevelTerritoryId, Description, OperatingHoursId, Street, City, State, PostalCode,
        Country, Latitude, Longitude, GeocodeAccuracy, Address, IsActive, TypicalInTerritoryTravelTime, Agency__c, Address__c, Tipo__c, Account__c,
        Account__r.RecordType.DeveloperName, Account__r.name, Punto_vendita_principale__c, AccountAddress__c, Code__c, Email__c, ExternalId__c, Phone__c, Agency__r.KPI__c, Agency__r.ExternalId__c,Agency__r.DiscretionalityMotor__c,
        Agency__r.CheckCAPAssignedContactActivities__c,Agency__r.Discretionality__c, Agency__r.DiscretionalityWelfare__c, Agency__r.DiscretionalityProperty__c , Agency__r.DiscretionalityEnterprise__c
        FROM ServiceTerritory
        WHERE Agency__c = :agencyAccount.Id
    ];

    // Creazione di un input per il metodo
    EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
    input.latitude = 45.4642;
    input.longitude = 9.1900;

    // Esecuzione del metodo
    Test.startTest();
    List<ServiceTerritory> filteredServiceTerritories = EngineAgencyRanking_V2.getServiceTerritoriesWithExtensionRadius(
        input,
        (5.0).doubleValue()// Raggio di estensione in KM
    );
    Test.stopTest();

}

@IsTest
static void testGetServiceTerritoriesWithExtensionRadius_OutsideRadius() {
    // Creazione di un Account di esempio
    Account agencyAccount = new Account(
        Name = 'Test Agency',
        ExternalId__c = 'Agency123777',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agencyAccount;

    // Creazione di un OperatingHours di esempio
    OperatingHours testOperatingHours = new OperatingHours(
        Name = 'Test Operating Hours'
    );
    insert testOperatingHours;

    // Creazione di un ServiceTerritory fuori dal raggio
    ServiceTerritory serviceTerritory = new ServiceTerritory(
        Name = 'Test SalesPoint',
        Tipo__c = 'Agenziale',
        OperatingHoursId = testOperatingHours.Id,
        Agency__c = agencyAccount.Id,
        Street = 'Test Street',
        City = 'Test City',
        State = 'MI',
        PostalCode = '10200',
        Country = 'Italy',
        Latitude = 46.0667,
        Longitude = 11.1500
    );
    insert serviceTerritory;

   // Recupero del ServiceTerritory tramite query
   List<ServiceTerritory> serviceTerritories = [
    SELECT id,  Name,  ParentTerritoryId, TopLevelTerritoryId, Description, OperatingHoursId, Street, City, State, PostalCode,
    Country, Latitude, Longitude, GeocodeAccuracy, Address, IsActive, TypicalInTerritoryTravelTime, Agency__c, Address__c, Tipo__c, Account__c,
    Account__r.RecordType.DeveloperName, Account__r.name, Punto_vendita_principale__c, AccountAddress__c, Code__c, Email__c, ExternalId__c, Phone__c, Agency__r.KPI__c, Agency__r.ExternalId__c,Agency__r.DiscretionalityMotor__c,
    Agency__r.CheckCAPAssignedContactActivities__c,Agency__r.Discretionality__c, Agency__r.DiscretionalityWelfare__c, Agency__r.DiscretionalityProperty__c , Agency__r.DiscretionalityEnterprise__c
    FROM ServiceTerritory
    WHERE Agency__c = :agencyAccount.Id
];

    // Creazione di un input per il metodo
    EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
    input.latitude = 45.4642;
    input.longitude = 9.1900;

    // Esecuzione del metodo
    Test.startTest();
    List<ServiceTerritory> filteredServiceTerritories = EngineAgencyRanking_V2.getServiceTerritoriesWithExtensionRadius(
        input,
        (5.0).doubleValue() // Raggio di estensione in KM
    );
    Test.stopTest();

}

@IsTest
static void testGetServiceTerritoriesWithExtensionRadius_EmptyList() {
    // Creazione di un input per il metodo
    EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
    input.latitude = 45.4642;
    input.longitude = 9.1900;

    // Esecuzione del metodo
    Test.startTest();
    List<ServiceTerritory> filteredServiceTerritories = EngineAgencyRanking_V2.getServiceTerritoriesWithExtensionRadius(
        input,
        (5.0 ).doubleValue()// Raggio di estensione in KM
    );
    Test.stopTest();

}

@IsTest
static void testGetServiceTerritoriesWithExtensionRadius_NullInput() {
    try {
            // Creazione di un Account di esempio
    Account agencyAccount = new Account(
        Name = 'Test Agency',
        ExternalId__c = 'Agency123777',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agencyAccount;

    // Creazione di un OperatingHours di esempio
    OperatingHours testOperatingHours = new OperatingHours(
        Name = 'Test Operating Hours'
    );
    insert testOperatingHours;

    // Creazione di un ServiceTerritory fuori dal raggio
    ServiceTerritory serviceTerritory = new ServiceTerritory(
        Name = 'Test SalesPoint',
        Tipo__c = 'Agenziale',
        OperatingHoursId = testOperatingHours.Id,
        Agency__c = agencyAccount.Id,
        Street = 'Test Street',
        City = 'Test City',
        State = 'MI',
        PostalCode = '10200',
        Country = 'Italy',
        Latitude = 46.0667,
        Longitude = 11.1500
    );
    insert serviceTerritory;

        // Esecuzione del metodo con input nullo
        Test.startTest();
        List<ServiceTerritory> filteredServiceTerritories = EngineAgencyRanking_V2.getServiceTerritoriesWithExtensionRadius(
            null,
            (5.0).doubleValue()// Raggio di estensione in KM
        );
        Test.stopTest();
    } catch (NullPointerException e) {}
}

@IsTest
static void testRankAgencies_EmptyAgencyFilteredIdList() {
    // Creazione di un input valido
    EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
    input.source = 'Preventivatore digitale Unica';
    input.macroAreas = new List<String>{'Area1', 'Area2'};
    input.fiscalCode = '**********';
    input.latitude = 45.4642;
    input.longitude = 9.1900;
    input.zipCode = '20100';
    input.useDefaultAgency = false;
    input.company='UnipolSai';

    Account accToUpdate = [SELECT Id, ExternalId__c,OmnichannelAgreement__c FROM Account  where ExternalId__c = 'Test1' LIMIT 1];
    system.debug(' TEST accToUpdate.OmnichannelAgreement__c: '+accToUpdate.OmnichannelAgreement__c);
    accToUpdate.OmnichannelAgreement__c=false;
    update accToUpdate;

    // Creazione di un Account di esempio senza ServiceTerritory associati
    Account agencyAccount = new Account(
        Name = 'Test Agency Without ServiceTerritory',
        ExternalId__c = 'AgencyNoST',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agencyAccount;

    

    // Esecuzione del metodo
    Test.startTest();
    EngineWrapper_V2.EngineWrapperOutput response = EngineAgencyRanking_V2.rankAgencies(input);
    Test.stopTest();
    system.debug('TEST response: '+response);

}


@IsTest
static void testRankAgencies_EmptyAgencyFilteredIdList_UseDefaultAgency() {
    // Creazione di un input valido
    EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
    input.source = 'Preventivatore digitale Unica';
    input.macroAreas = new List<String>{'Area1', 'Area2'};
    input.fiscalCode = '**********';
    input.latitude = 45.4642;
    input.longitude = 9.1900;
    input.zipCode = '20100';
    input.useDefaultAgency = true;
    input.company='UnipolSai';

    Account accToUpdate = [SELECT Id, ExternalId__c,OmnichannelAgreement__c FROM Account  where ExternalId__c = 'Test1' LIMIT 1];
    system.debug(' TEST accToUpdate.OmnichannelAgreement__c: '+accToUpdate.OmnichannelAgreement__c);
    accToUpdate.OmnichannelAgreement__c=false;
    update accToUpdate;

    // Creazione di un Account di esempio senza ServiceTerritory associati
    Account agencyAccount = new Account(
        Name = 'Test Agency Without ServiceTerritory',
        ExternalId__c = 'AgencyNoST',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agencyAccount;

    

    // Esecuzione del metodo
    Test.startTest();
    EngineWrapper_V2.EngineWrapperOutput response = EngineAgencyRanking_V2.rankAgencies(input);
    Test.stopTest();
    system.debug('TEST response: '+response);
}


}