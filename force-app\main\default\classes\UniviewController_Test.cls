@isTest
public class UniviewController_Test {

    @isTest
    static void testGetUniviewDefinitionPositive() {
        UniviewController.testUniviewDefinitionMetadata  = (List<UniviewDefinition__mdt>) JSON.deserialize(
            '[{"attributes":{"type":"UniviewDefinition__mdt","url":"/services/data/v60.0/sobjects/UniviewDefinition__mdt/m0P1x0000001G0lEAE"},"MaximumUserPreferences__c":5,"RefreshInterval__c":300,"Object__c":"Opportunity","PageSize__c":10,"ObjectPlural__c":"Trattative","ScopingRule__c":true,"RecordTypes__c":"InterestShow","RefreshonFocus__c":true,"ShowPreferencesButtons__c":false,"Id":"m0P1x0000001G0lEAE"}]'
            , List<UniviewDefinition__mdt>.class
        );
        String testUniviewKey = 'OpportunityReport';
        Test.startTest();
        UniviewController.UniviewDefinition result = UniviewController.getUniviewDefinition(testUniviewKey);
		Test.stopTest();
        
        System.assertNotEquals(null, result, 'UniviewDefinition should not be null');
        System.assertEquals(5, result.maximumUserPreferences, 'MaximumUserPreferences should match'); 
        System.assertEquals(300, result.refreshInterval, 'RefreshInterval should match');
        System.assertEquals('Opportunity', result.objectType, 'ObjectType should match');
        System.assertEquals('Trattative', result.objectPlural, 'ObjectPlural should match'); 
        System.assertEquals(10, result.pageSize, 'PageSize should match');
        System.assertEquals(true, result.scopingRule, 'ScopingRule should match');
        System.assertEquals('InterestShow', result.recordTypes, 'RecordTypes should match');
    }
    
    @isTest
    static void testGetUniviewDefinitionNoData() {
        UniviewController.testUniviewDefinitionMetadata = new List<UniviewDefinition__mdt>();
        Test.startTest();
        UniviewController.UniviewDefinition resultNoData = UniviewController.getUniviewDefinition('NonExistentKey');
        Test.stopTest();

        System.assertEquals(null, resultNoData, 'Expected null result for non-existent key');
    }
}
