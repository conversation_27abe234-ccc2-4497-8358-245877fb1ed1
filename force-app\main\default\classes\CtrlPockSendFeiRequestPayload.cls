/*
* @description
* @cicd_tests CtrlFei_Test
*/
public without sharing class CtrlPockSendFeiRequestPayload {
    
    @AuraEnabled(cacheable=true)
    public static String getPayload(String feiid){

        String result = '';

        if(String.isBlank(feiid)) return result;

        System.debug('feiid '+feiid);

        FEI_Environment__c defaultOrganization = FEI_Environment__c.getInstance(UserInfo.getUserId());

        System.debug('@@@ '+defaultOrganization);

        FEI_Settings__mdt currentFEI = [SELECT Id, 
                                            Label,
                                            sendFEIRequest_Payload__c
                                        FROM FEI_Settings__mdt 
                                        WHERE Label = :feiid 
                                        AND Active__c = true 
                                        AND Environment__c = :defaultOrganization.Environment__c 
                                        LIMIT 1];

        if(String.isNotBlank(currentFEI.sendFEIRequest_Payload__c)){
            result = currentFEI.sendFEIRequest_Payload__c;
        }

        return result;

    }

}