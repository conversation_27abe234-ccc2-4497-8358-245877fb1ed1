@IsTest
public class ControllerAgendaTipoDispCanDig_Test {

    private static final String[] giorni = new String[] {
        'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
    };
   
    @TestSetup
    static void makeData(){
        String userId = UserInfo.getUserId();
        ServiceResource sr = new ServiceResource(
            RelatedRecordId = userId,
            Attiva_per_clienti_e_prospect__c = 'Nessuno',
            IsActive = true,
            Name = 'Test User'
        );
        insert sr;

       OperatingHours oh = new OperatingHours(
            Name = 'Test Operating Hours Punto Vendita',
            TimeZone= 'Europe/Rome'
        );
        insert oh;

        List<TimeSlot> tsList = new List<TimeSlot>(); 
        for(Integer i = 0; i < 7; i++) {
            String giorno = giorni[i];
            TimeSlot ts = new TimeSlot(
                OperatingHoursId = oh.Id,
                StartTime = Time.newInstance(9, 0, 0, 0),
                EndTime = Time.newInstance(18, 0, 0, 0),
                DayOfWeek = giorno
            );
            tsList.add(ts);
        }
        insert tsList;

        ServiceTerritory st = new ServiceTerritory(
            Name = 'Punto Vendita Test',
            OperatingHoursId = oh.Id,
            Street = 'Via Roma 10',
            City = 'Roma',
            State = 'RM',
            PostalCode = '00100',
            Country = 'Italy',
            Latitude = 41.9028,
            Longitude = 12.4964,
            Tipo__c = 'Agenziale',
            isActive = true
        );
        insert st;

        ServiceTerritoryMember stm = new ServiceTerritoryMember(
            ServiceResourceId = sr.Id,
            ServiceTerritoryId = st.Id,
            TerritoryType = 'P',
            EffectiveStartDate = Date.today(),
            EffectiveEndDate = Date.today().addDays(365),
            isActive__c = true
        );
        insert stm;
    }

    @IsTest 
    static void getDisponibilita_test() {
        Map<String,List<Object>> retMap = ControllerAgendaTipoDispCanDig.getDisponibilita();
        System.assertEquals(0, retMap.size(), 'The map should no contain one key');
    }

    /* Test con disponibilità già precedentemente settate da parte dell'utente. Ai fini del test viene preso lo 
       stesso OH creato per l'agenzia */
    @IsTest 
    static void getDisponibilitaWSTMOH_test() {
        ServiceTerritory st = [SELECT Id, OperatingHoursId FROM ServiceTerritory WHERE Name = 'Punto Vendita Test' LIMIT 1];
        ServiceTerritoryMember stm = [SELECT Id, OperatingHoursId FROM ServiceTerritoryMember LIMIT 1];
        stm.OperatingHoursId = st.OperatingHoursId;
        update stm;

        Map<String,List<Object>> retMap = ControllerAgendaTipoDispCanDig.getDisponibilita();
        System.assertEquals(7, retMap.size(), 'The map should contain info for all 7 days');
    }

    @IsTest
    static void getPuntiVenditaConOrari_test() {
        List<Map<String,Object>> retList = ControllerAgendaTipoDispCanDig.getPuntiVenditaConOrari();
        System.assertEquals(1, retList.size(), 'The list should contain one PV');
        System.assertEquals('Punto Vendita Test', retList[0].get('label'), 'The name of the PV should be "Punto Vendita Test"');
    }

    @IsTest
    static void saveDisponibilita_test() {
       UUID randomUuid = UUID.randomUUID();
       ControllerAgendaTipoDispCanDig.Disponibilita disp = new ControllerAgendaTipoDispCanDig.Disponibilita();
       disp.Id = randomUuid.toString();
       disp.puntoVenditaId = [SELECT Id FROM ServiceTerritory LIMIT 1].Id;
       disp.puntoVendita = [SELECT Name FROM ServiceTerritory LIMIT 1].Name;
       disp.oraInizio = '09:30';
       disp.oraFine = '17:30';

       String[] giorni = new String[] {
           'Lunedì', 'Martedì', 'Mercoledì', 'Giovedì', 'Venerdì'
       };

       List<ControllerAgendaTipoDispCanDig.GiornoDisponibilita> inputList = new List<ControllerAgendaTipoDispCanDig.GiornoDisponibilita>();

       for(String giorno : giorni) {
            ControllerAgendaTipoDispCanDig.GiornoDisponibilita giornoDisp = new ControllerAgendaTipoDispCanDig.GiornoDisponibilita();
            giornoDisp.giorno = giorno;
            giornoDisp.disponibilita = new List<ControllerAgendaTipoDispCanDig.Disponibilita>();
            giornoDisp.disponibilita.add(disp);
            inputList.add(giornoDisp);
       }

       String jsonInput = JSON.serialize(inputList);
       Map<String,Object> result = ControllerAgendaTipoDispCanDig.saveDisponibilita(jsonInput);
    }
}