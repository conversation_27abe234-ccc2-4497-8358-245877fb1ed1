<?xml version="1.0" encoding="UTF-8"?>
<CustomMetadata xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <label>ReassigntoAgencyAssegnato</label>
    <protected>false</protected>
    <values>
        <field>ButtonLabel__c</field>
        <value xsi:type="xsd:string">Riassegna in agenzia</value>
    </values>
    <values>
        <field>ButtonName__c</field>
        <value xsi:type="xsd:string">reassigntoAgencyBtn</value>
    </values>
    <values>
        <field>ButtonStyling__c</field>
        <value xsi:type="xsd:string">btn green-background</value>
    </values>
    <values>
        <field>Checks__c</field>
        <value xsi:type="xsd:string">{
    &quot;clientType&quot;: {
        &quot;value&quot;: &quot;Prospect;Cliente&quot;,
        &quot;operator&quot;: &quot;contains&quot;,
        &quot;failureVisibility&quot;: &quot;hidden&quot;
    },
    &quot;slaRemainingHours&quot;: {
        &quot;value&quot;: 0,
        &quot;operator&quot;: &quot;&gt;&quot;,
        &quot;failureVisibility&quot;: &quot;hidden&quot;
    },
    &quot;stage&quot;: {
        &quot;value&quot;: &quot;Assegnato&quot;,
        &quot;operator&quot;: &quot;=&quot;,
        &quot;failureVisibility&quot;: &quot;hidden&quot;
    }
}</value>
    </values>
    <values>
        <field>GroupName__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Icon__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>IsActive__c</field>
        <value xsi:type="xsd:boolean">false</value>
    </values>
    <values>
        <field>IsRight__c</field>
        <value xsi:type="xsd:boolean">false</value>
    </values>
    <values>
        <field>Object__c</field>
        <value xsi:type="xsd:string">Opportunity</value>
    </values>
    <values>
        <field>Order__c</field>
        <value xsi:type="xsd:double">3.0</value>
    </values>
    <values>
        <field>PermissionsRequired__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Position__c</field>
        <value xsi:type="xsd:string">Reassign</value>
    </values>
</CustomMetadata>