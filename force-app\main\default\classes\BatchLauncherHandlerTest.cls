@isTest
private class BatchLauncherHandlerTest {
    @isTest
    static void testExecuteBatch() {
        // Dati di test
        List<Account> accounts = new List<Account>();
        for (Integer i = 0; i < 10; i++) {
            accounts.add(new Account(Name = 'Test Account ' + i));
        }
        insert accounts;

        // Esecuzione del metodo da testare
        Test.startTest();
        BatchLauncherHandler.executeBatch(new List<String>{ 'SampleBatch' });
        Test.stopTest();

        // Asserzione semplice (da migliorare se si conosce l'effetto del batch)
        System.assert(true, 'Batch eseguito correttamente');
    }

    @isTest
    static void testExecuteBatchWithValidClassName() {
        Test.startTest();
        BatchLauncherHandler.executeBatch(new List<String>{ 'CaseHandlerBatch' });
        Test.stopTest();

        System.assert(true, 'Batch eseguito correttamente tramite Type.forName');
    }
}
