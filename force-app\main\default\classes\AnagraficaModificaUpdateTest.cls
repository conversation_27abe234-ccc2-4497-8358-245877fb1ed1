@isTest
public with sharing class AnagraficaModificaUpdateTest {
    @isTest
    static void AnagraficaModificaUpdateTest() {
        Id societyRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId();
		
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(Name = 'Cliente', FinServ__InverseRole__c = 'Agenzia');
        insert role;

        List<Account> accListToInsert = new List<Account>();
        Account accountCliente1 = new Account(
            FirstName = 'Cliente', LastName = '1',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('PersonAccount').getRecordTypeId(), 
            ExternalId__c = 'Cliente1'
        );
        accListToInsert.add(accountCliente1);
		Account accountSociety1 = new Account(
            Name = 'Scoietà 1', RecordTypeId = societyRecordTypeId,  
            ExternalId__c = 'SOC_1'
        );
		accListToInsert.add(accountSociety1);
		insert accListToInsert;
		
        FinServ__AccountAccountRelation__c accAccRelClienteSociety1 = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = accountCliente1.Id,
            FinServ__RelatedAccount__c = accountSociety1.Id,
            FinServ__Role__c = role.Id
            //RecordTypeId = AccountSocietyRecordTypeId
        );
		insert accAccRelClienteSociety1;
		
		AccountDetails__c accDetails1 = new AccountDetails__c(
        	Relation__c = accAccRelClienteSociety1.Id, SourceSystemIdentifier__c = '7174180', 
            FeaMail__c = '<EMAIL>', FeaMobile__c = '+39 **********',
            Country__c = 'ITA', FiscalCode__c = '****************', Street__c = 'Via del Campo',
            PostalCode__c = '20900', City__c = 'Firenze', State__c = 'TO', Mobile__c = '+39 *********',
            Email__c = '<EMAIL>'
        );
		
		insert accDetails1;

        Test.startTest();
        Map<String, Object> args = new Map<String, Object>();
        String bodyJson = '{"body":{"anagrafica":{"ciu":"7174180","compagnia":"unipolsai","agenzia":"01853","tipoAnagrafica":"D","tipoSoggetto":"PF","cognome":"AB","nome":"AF","codiceFiscale":"****************","statoCodiceFiscale":"N","sesso":"M","dataNascita":"1980-01-01","dataDecesso":"2008-01-01","tipoAnagraficaRichiesto":"D","statoCodiceFiscalePartitaIva":"N","comuneNascitaBelfiore":"H501","statoAnagrafica":"A","nazioneNascita":"H501","comuneNascita":"H501","provinciaNascita":"RM"},"indirizzo":[{"abbreviazioneProvincia":"BA","cabComune":"A001E","cabStato":"A116","codiceBelfioreComune":"A662","codiceBelfioreStato":"Z000","codiceIstatAnag1":"215","codiceIstatComune":"215","codiceUnsdM49":"150","codicePostale":"70126","dug":"VIA","dus":"CAPPUCCINI","indirizzoCompleto":"VIA DEI MILLE","localita":"BARI","presso":"C/O BAR","indirizzoBreve":"VIA CAPPUCCINI 20, MI","latitudine":45.470623000000003,"longitudine":9.2049059999999994,"distrettoCensuario":"1","numeroCivico":"12","tipoIndirizzo":"RESI","tipoNormalizzato":"S","flagPreview":false,"idIndirizzo":"5570438"},{"abbreviazioneProvincia":"BA","cabComune":"A001E","cabStato":"A116","codiceBelfioreComune":"A662","codiceBelfioreStato":"Z000","codiceIstatAnag1":"215","codiceIstatComune":"215","codiceUnsdM49":"150","codicePostale":"70126","dug":"VIA","dus":"CAPPUCCINI","indirizzoCompleto":"VIA DEI MILLE","localita":"BARI","presso":"C/O BAR","indirizzoBreve":"VIA CAPPUCCINI 20, MI","latitudine":45.470623000000003,"longitudine":9.2049059999999994,"distrettoCensuario":"1","numeroCivico":"12","tipoIndirizzo":"DOMI","tipoNormalizzato":"S","flagPreview":false,"idIndirizzo":"5570438"}],"contatti":[{"tipoContatto":"MAIL","contatto":"<EMAIL>","referente":null,"dataInizioEffetto":"2024-11-25T11:58:49.447445","dataFineEffetto":"2024-11-12","tipologiaContatto":"PER","flagPreferito":false,"mostraDatiTracciatura":true,"idContatto":"2678816"}],"privacy":{"datiAdesione":{"tipoPrivacy":"00","dataInizioEffetto":"2024-11-25T11:58:49.447445","applicazioneFine":null,"applicazioneInizio":"PUPTF"}},"professione":{"professione":{"codice":"1401","descrizione":"Impiegato"},"mercatoPreferenziale":{"codice":"01","descrizione":"Cooperative Della Lega"},"impiego":{"codice":"D","descrizione":"DIPENDENTE"},"settore":{"codice":"PUB","descrizione":"PUBBLICO"}},"datiAgenzia":{"flagClienteTop":false,"dataClienteTop":null,"flagAdesioneFEA":false,"flagProprietaContattiFea":false,"codiceSubagenzia":"915","codiceAgenziaPrevalente":"01853","flagAutorizzazioneCauzione":true,"dataCessazioneCliente":null,"statoSoggetto":"PO","codiceSoggettoCanale":null,"dataInizioEffetto":"2024-10-24","dataFineEffetto":null,"compagnia":"unipolsai","codiceCanale":"AGE"}}}';
        Map<String, Object> body = (Map<String,Object>) Json.deserializeUntyped(bodyJson);
        args.put('input', body);
        AnagraficaModificaUpdate.call('', args);
        Test.stopTest();
    }
}