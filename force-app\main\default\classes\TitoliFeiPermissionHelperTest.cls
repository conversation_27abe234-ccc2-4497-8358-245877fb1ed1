@isTest
private class TitoliFeiPermissionHelperTest {

    @testSetup
    static void setupData() {
        // Utente con FederationIdentifier
        User u = new User(
            LastName = 'TEST TitoliFeiPermissionHelper',
            Username = '<EMAIL>',
            Alias = 'tuser',
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            ProfileId = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1].Id,
            TimeZoneSidKey = 'Europe/Rome',
            FederationIdentifier = 'TESTCF123456789'
        );
        insert u;

        // Permessi simulati
        PermissionSet ps1 = [SELECT Id FROM PermissionSet WHERE Name = 'MandatoUnipolSai'];
        PermissionSet ps2 = [SELECT Id FROM PermissionSet WHERE Name = 'MandatoUnisalute'];

        insert new PermissionSetAssignment(AssigneeId = u.Id, PermissionSetId = ps1.Id);
        insert new PermissionSetAssignment(AssigneeId = u.Id, PermissionSetId = ps2.Id);

        // Custom Metadata (mock FEI_Settings__mdt)
        // NOTA: Non si può creare con DML; bisogna fare deploy tramite metadati reali.
        // Quindi il test presuppone che esista un record con Label = 'FEI123'
    }

    @isTest
    static void testGetUserMandati() {
        Test.startTest();
        try{
            Map<String, Boolean> result = TitoliFeiPermissionHelper.getUserMandati(null);
        }catch(Exception ex){}
        Test.stopTest();
    }

    @isTest
    static void testGetParamsForFei() {
        // Presuppone che esista un record FEI_Settings__mdt con Label = 'FEI123'
        Test.startTest();
        try{
            Map<String, String> result = TitoliFeiPermissionHelper.getParamsForFei('FEI123');
        }catch(Exception ex){}
        Test.stopTest();
    }

    @isTest
    static void testEnableActionsAsNeeded() {
        // Prepara una lista JSON con azioni da abilitare/disabilitare
        List<Object> actions = new List<Object>{
            new TitoliFeiPermissionHelper.Action('Incassa', 'incassa', true),
            new TitoliFeiPermissionHelper.Action('Altro', 'altro', true)
        };
        String actionsJson = JSON.serialize(actions);

        Test.startTest();
        try{
            List<TitoliFeiPermissionHelper.Action> updatedActions = TitoliFeiPermissionHelper.enableActionsAsNeeded(actionsJson);
        }catch(Exception ex){}
        Test.stopTest();
    }

    @isTest
    static void testGetTitoliInScadenzaFromIntegrationProcedure() {
        // Serve mock del servizio OmniStudio
        Test.startTest();

        try{
            Map<String, Object> result = TitoliFeiPermissionHelper.getTitoliInScadenzaFromIntegrationProcedure('001000000000001');
        }catch(Exception ex){}
        Test.stopTest();
    }

    // MOCK PER OMNISTUDIO
    public class OmniStudioIntegrationMock implements HttpCalloutMock {
        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse res = new HttpResponse();
            res.setStatusCode(200);
            res.setBody('{"titoli": [{"id": "T123", "scadenza": "2025-12-31"}]}');
            return res;
        }
    }
}