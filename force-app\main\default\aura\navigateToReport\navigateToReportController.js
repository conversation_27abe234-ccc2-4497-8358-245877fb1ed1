({    
    invoke : function(component, event) { 
        var workspaceAPI = component.find("workspace");
        var reportId = component.get("v.reportId");
        var closeTab = component.get("v.closeTab");
        console.log('reportId : ' + reportId);

        if(closeTab) {
            workspaceAPI.getFocusedTabInfo().then(function(response) {
                var focusedTabId = response.tabId;
                workspaceAPI.closeTab({tabId: focusedTabId});
            })
            .catch(function(error) {
                console.log(error);
            });
        }

        workspaceAPI.openTab({
            url: '/lightning/r/Report/' + reportId + '/view?queryScope=userFolders',
            focus: true
        });
 	}
})
