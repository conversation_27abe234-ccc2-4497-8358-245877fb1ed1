public with sharing class OpportunityWSWrapper {
    @AuraEnabled
    public String id;
    @AuraEnabled
    public String externalId;
    @AuraEnabled
    public String recordTypeId;
    @AuraEnabled
    public String domainType;
    @AuraEnabled
    public String name;
    @AuraEnabled
    public String accountId;
    @AuraEnabled
    public String agency;
    @AuraEnabled
    public String cip;
    @AuraEnabled
    public Boolean selectedByLocator;
    @AuraEnabled
    public String leadSource;
    @AuraEnabled
    public String channel;
    @AuraEnabled
    public String engagementPoint;
    @AuraEnabled
    public List<String> areaOfNeed;
    @AuraEnabled
    public Decimal amount;
    @AuraEnabled
    public Boolean hasCallMeBack;
    @AuraEnabled
    public String rating;
    @AuraEnabled
    public Datetime workingSLAExpiryDate;
    @AuraEnabled
    public Datetime takenInChargeDate;
    @AuraEnabled
    public Datetime takenInChargeSLAExpiryDate;
    @AuraEnabled
    public String proposalId;
    @AuraEnabled
    public Datetime proposalIssueDate;
    @AuraEnabled
    public String bankBranch;
    @AuraEnabled
    public String bankAgent;
    @AuraEnabled
    public QuoteWSWrapper quote;
    @AuraEnabled
    public Datetime appointmentDate; //Francesca R.
    @TestVisible
    private static Boolean testCoverage { get; set; }
}
