@isTest
private class NetworkUserHelperTest {

    @isTest
    static void testNetworkUserHelperMethods() {
        // Create test user with FederationIdentifier
        User u = new User(
            Username = '<EMAIL>',
            LastName = 'Test23',
            Email = '<EMAIL>',
            Alias = 'tuser23',
            TimeZoneSidKey = 'Europe/Rome',
            LocaleSidKey = 'it_IT',
            EmailEncodingKey = 'UTF-8',
            ProfileId = [SELECT Id FROM Profile WHERE Name = 'Unipol Standard User' LIMIT 1].Id,
            LanguageLocaleKey = 'it',
            FederationIdentifier = 'FED_TEST'
        );
        insert u;

        // Create test user WITHOUT FederationIdentifier
        User uNoFed = new User(
            Username = '<EMAIL>',
            LastName = 'Test25',
            Email = '<EMAIL>',
            Alias = 'tuser25',
            TimeZoneSidKey = 'Europe/Rome',
            LocaleSidKey = 'it_IT',
            EmailEncodingKey = 'UTF-8',
            ProfileId = u.ProfileId,
            LanguageLocaleKey = 'it'
            // FederationIdentifier left blank
        );
        insert uNoFed;

        Test.startTest();

        // Set current user context
        System.runAs(u) {
            // Create Society RecordType if not present
            RecordType rt;
            try {
                rt = [SELECT Id FROM RecordType WHERE SObjectType = 'Account' AND DeveloperName = 'Society' LIMIT 1];
            } catch (Exception e) {
                System.debug('RecordType Society non trovato: ' + e.getMessage());
            }

            // Create Account (Society)
            Account acc = new Account(
                Name = 'Società Test',
                RecordTypeId = rt != null ? rt.Id : null,
                ExternalId__c = 'SOC_TEST'
            );
            insert acc;

            // Create two NetworkUser__c records for the user and society
            NetworkUser__c nu1 = new NetworkUser__c(
                NetworkUser__c = 'Utenza1',
                FiscalCode__c = u.FederationIdentifier,
                Society__c = acc.ExternalId__c,
                IsActive__c = true,
                Preferred__c = true
            );
            NetworkUser__c nu2 = new NetworkUser__c(
                NetworkUser__c = 'Utenza2',
                FiscalCode__c = u.FederationIdentifier,
                Society__c = acc.ExternalId__c,
                IsActive__c = true,
                Preferred__c = false
            );
            insert new List<NetworkUser__c>{nu1, nu2};

            // Test getUserNetworkData - normal case
            List<NetworkUserHelper.NetworkUserWrapper> wrappers = NetworkUserHelper.getUserNetworkData();
            System.assertEquals(1, wrappers.size(), 'Should return one company');
            System.assertEquals('Società Test', wrappers[0].companyName, 'Company name should match');
            System.assertEquals(nu1.Id, wrappers[0].selectedUserId, 'Preferred user should be nu1');
            System.assertEquals(2, wrappers[0].options.size(), 'Should have two options');

            // Test setPreferredNetworkUser (switch preferred)
            NetworkUserHelper.setPreferredNetworkUser(nu2.Id, acc.ExternalId__c);

            // Query again to check preferred updated
            List<NetworkUser__c> nus = [SELECT Id, Preferred__c FROM NetworkUser__c WHERE Id IN :new List<Id>{nu1.Id, nu2.Id}];
            Boolean foundNu2Preferred = false;
            Boolean foundNu1NotPreferred = false;
            for (NetworkUser__c nu : nus) {
                if (nu.Id == nu2.Id) {
                    foundNu2Preferred = nu.Preferred__c;
                }
                if (nu.Id == nu1.Id) {
                    foundNu1NotPreferred = !nu.Preferred__c;
                }
            }
            System.assert(foundNu2Preferred, 'nu2 should now be preferred');
            System.assert(foundNu1NotPreferred, 'nu1 should no longer be preferred');

            // Test setPreferredNetworkUser with blank parameters (should do nothing and not throw)
            NetworkUserHelper.setPreferredNetworkUser('', '');
            NetworkUserHelper.setPreferredNetworkUser(null, null);

        }

       // Set current user context WITHOUT FederationIdentifier
        System.runAs(uNoFed) {
            List<NetworkUserHelper.NetworkUserWrapper> wrappersNoFed = NetworkUserHelper.getUserNetworkData();
            System.assertEquals(0, wrappersNoFed.size(), 'Should return empty list if FederationIdentifier is blank');
        }

        Test.stopTest();
    }
}