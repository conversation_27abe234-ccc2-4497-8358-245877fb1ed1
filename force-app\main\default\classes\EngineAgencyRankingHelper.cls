/*********************************************************************************
* @description    Helper class for EngineAgencyRanking_V2 to handle input checks and utility methods.
* @date           2025-04-10
**********************************************************************************/
public without sharing class EngineAgencyRankingHelper {

    @TestVisible
    private static List<CalculationMatrixRow> testingMatrixRows;
    private final static String LOOKUP_TABLE_INPUT_MERITOCRATIC = 'Meritocratico';
    private final static String MOTOR_PARAMETERS_MATRIX_CAP_LAT_LONG = 'GEO_CAP_LAT_LONG';//'GEO_Raggio_Agenzie';
    private final static String MOTOR_PARAMETERS_MATRIX_CAP_LAT_LONG_INPUT = 'Cap';
    private final static String PROSPECT_PURO = 'Prospect Puro';
    private final static String CLIENT_EX_CLIENTE_2_ANNI = 'Cliente Esistente o Ex cliente entro 2 anni';
    private final static String PROSPECT_ANAGRAFATO =  'Prospect Anagrafato';
    private static final String ROLE_CLIENTE = 'Cliente';
    private static final String RECORD_TYPE_AGENCY = 'Agency';
    private static final String TYPE_CLIENTE = 'Agenziale';

    /**
     * Validates the input parameters for the EngineAgencyRanking web service.
     *
     * @param input The EngineWrapperInput object containing the input parameters to be validated.
     * @throws EngineAgencyRanking_V2.AgencyRankingException if any of the following conditions are met:
     *         - input.source is blank
     *         - input.macroAreas is null or empty
     *         - both input.fiscalCode and input.pIva are blank
     *         - both input.latitude and input.longitude are null and input.zipCode is blank
     * 
     */
    public static void checkInputParameters(EngineWrapper_V2.EngineWrapperInput input) {
        // if (String.isBlank(input.source) || input.macroAreas == null || input.macroAreas.isEmpty()) {
        //     throw new EngineAgencyRanking_V2.AgencyRankingException('Missing input parameter: source/macroAreas');
        // }
        if (String.isBlank(input.fiscalCode) && String.isBlank(input.pIva)) {
            throw new EngineAgencyRanking_V2.AgencyRankingException('The fiscalCode and pIva are both blank; at least one of them must be filled');
        }
        if ((input.latitude == null || input.longitude == null) && String.isBlank(input.zipCode)) {
            throw new EngineAgencyRanking_V2.AgencyRankingException('The couple latitude/longitude and the zipCode is blank; at least one of them must be filled');
        }
        if (input.company == null || String.isBlank(input.company)) {
            throw new EngineAgencyRanking_V2.AgencyRankingException('The company in Input is must be filled');
        }
    }

    /**
     * Retrieves calculation matrix data based on the provided calculation matrix name.
     * 
     * @param calculationMatrixName The unique name of the calculation matrix to retrieve data for.
     * @return List<CalculationMatrixRow> A list of CalculationMatrixRow records that match the specified calculation matrix name.
     *         If the method is called during a test, it returns the test matrix rows.
     */
    public static List<CalculationMatrixRow> getCalculationMatrixData(String calculationMatrixName) {
        if (Test.isRunningTest()) {
            if(calculationMatrixName != MOTOR_PARAMETERS_MATRIX_CAP_LAT_LONG){
                testingMatrixRows = new List<CalculationMatrixRow> {
                    new CalculationMatrixRow(
                        InputData = '{"Canale": "Preventivatore digitale Unica","Motore":"Meritocratico"}',
                        OutputData = '{"%TassoContattabilità":40,"RaggioCentroideKM":1.5,"NumeroMaxRiassegnazioni":0,"AssegnazioneDefault":"Contact Center","AgenziaDefault":"AG-SOGEINT","RaggioEstensioneKM":3,"Discrezionalità":false}'
                    )
                };
                system.debug('# TESTING isRunningTest HERREEE->testingMatrixRows: ' + testingMatrixRows);
                return testingMatrixRows;
            }else{
                testingMatrixRows = new List<CalculationMatrixRow>{
                    new CalculationMatrixRow(
                        InputData = '{"CAP":20100}', // CAP valido
                        OutputData = '{"Latitude": 45.4642, "Longitude": 9.1900}'
                    )
                };
                system.debug('# TESTING isRunningTest HERREEE->testingMatrixRows: ' + testingMatrixRows);
                return testingMatrixRows;
            }
        }
        List<CalculationMatrixRow> matrixRows = [
            SELECT Id, InputData, OutputData 
            FROM CalculationMatrixRow 
            WHERE CalculationMatrixVersion.CalculationMatrix.UniqueName = :calculationMatrixName //Definizione_Cap_Lat_Long
            and CalculationMatrixVersion.IsEnabled=true
            //WITH USER_MODE
        ];
        return matrixRows;
    }

    /**
     * Retrieves meritocratic data from the provided matrix rows based on the input source.
     *
     * @param matrixRows List of CalculationMatrixRow objects containing input and output data.
     * @param input EngineWrapper_V2.EngineWrapperInput object containing the source information.
     * @return A Map<String, Object> containing the meritocratic data.
     * @throws EngineAgencyRanking_V2.AgencyRankingException if no meritocratic data is found in the system.
     */
    public static Map<String, Object> getMeritocraticData(List<CalculationMatrixRow> matrixRows, EngineWrapper_V2.EngineWrapperInput input) {
        Map<String, Object> meritocraticData;
         System.debug('# matrixRows sERIALIZE: ' + JSON.serialize(matrixRows));
         System.debug('# matrixRows: ' + matrixRows);
        if ( matrixRows == null || matrixRows.isEmpty()) {
            throw new EngineAgencyRanking_V2.AgencyRankingException('NO_MERITOCRATIC_DATA_FOUND_IN_SYSTEM');
        }
        for (CalculationMatrixRow row : matrixRows) {
            if ((row.InputData?.containsIgnoreCase(LOOKUP_TABLE_INPUT_MERITOCRATIC)) && (row.InputData?.containsIgnoreCase(input.source))) {
                meritocraticData = (Map<String, Object>) JSON.deserializeUntyped(row.OutputData);
                break;
            }
        }
        if (meritocraticData == null ) {
            throw new EngineAgencyRanking_V2.AgencyRankingException('NO_MERITOCRATIC_DATA_FOUND_IN_SYSTEM');
        }
        return meritocraticData;
    }

    /**
     * Retrieves the mandate of the agency based on the provided agency ID.
     *
     * @param agencyId The ID of the agency for which the mandate is to be retrieved.
     * @return List<AgencyMandate__c> A list of AgencyMandate__c records associated with the agency.
     * @throws EngineAgencyRanking_V2.AgencyRankingException If no mandates are found for the provided agency ID.
     */
    public static Map<Id, List<String>> getMandatesByAgencyIds(Set<Id> agencyIds) {
        // Validate input
        if (agencyIds == null || agencyIds.isEmpty()) {
            throw new EngineAgencyRanking_V2.AgencyRankingException('Agency IDs cannot be null or empty., in method getMandatesByAgencyIds');
        }

        Map<Id, List<String>> accountToRelatedNames = new Map<Id, List<String>>();

        List<FinServ__AccountAccountRelation__c> relations = [
            SELECT FinServ__RelatedAccount__r.Name, FinServ__Account__c
            FROM FinServ__AccountAccountRelation__c
            WHERE FinServ__Account__c IN :agencyIds
            AND FinServ__RelatedAccount__r.RecordType.DeveloperName = 'Society'
        ];

        for (FinServ__AccountAccountRelation__c rel : relations) {
            if (!accountToRelatedNames.containsKey(rel.FinServ__Account__c)) {
                accountToRelatedNames.put(rel.FinServ__Account__c, new List<String>());
            }
            accountToRelatedNames.get(rel.FinServ__Account__c).add(rel.FinServ__RelatedAccount__r.Name);
        }


        return accountToRelatedNames;
    }


    public static Boolean isMandatePresentForAgency(String companyMandate, Id agencyId, Map<Id, List<String>> accountToRelatedNames) {
        if (String.isBlank(companyMandate) || agencyId == null) return false;
    
        List<String> relatedNames = accountToRelatedNames.get(agencyId);
        if (relatedNames == null) return false;
    
        String inputLower = companyMandate.toLowerCase();
    
        for (String name : relatedNames) {
            if (name != null && name.toLowerCase() == inputLower) {
                return true;
            }
        }
    
        return false;
    }
    
    


    /**
     * Retrieves meritocratic data from the provided matrix rows based on the input source.
     *
     * @param matrixRows List of CalculationMatrixRow objects containing input and output data.
     
     * @return A Map<String, Object> containing the meritocratic data.
     * @throws EngineAgencyRanking_V2.AgencyRankingException if no meritocratic data is found in the system.
     */
    private static Map<String, Object> getMapDecisionMatrix(List<CalculationMatrixRow> matrixRows, String inputName) {
        Map<String, Object> matrixData;
        for (CalculationMatrixRow row : matrixRows) {
            system.debug('@#@#TESTrow.inputData: '+row.inputData);

            if ((row.InputData?.containsIgnoreCase(inputName))) {
                matrixData = (Map<String, Object>) JSON.deserializeUntyped(row.OutputData);
                break;
            }
        }
        System.debug('# matrixData: ' + JSON.serialize(matrixData));
        if (matrixData == null) {
            throw new EngineAgencyRanking_V2.AgencyRankingException('No sales point found for the specified zip code!');
        }
        return matrixData;
    }

    /**
     * Retrieves latitude and longitude from the Decision Matrix "Definizione_Cap_Lat_Long" based on the provided zip code.
     *
     * @param zipCode The zip code to search for in the Decision Matrix.
     * @return Map<String, Decimal> A map containing 'Latitude' and 'Longitude' if found.
     * @throws EngineAgencyRanking_V2.AgencyRankingException if no matching record is found in the Decision Matrix.
     */
    public static Map<String, Decimal> getLatLongFromDecisionMatrix(String zipCode) {
        // Validate input
        if (String.isBlank(zipCode)) {
            throw new EngineAgencyRanking_V2.AgencyRankingException('Zip code is blank; cannot retrieve latitude and longitude.');
        }

         // Check if the zip code is valid
         if (!Pattern.matches('^[0-9]+$', zipCode) || zipCode.length() != 5) {
            throw new EngineAgencyRanking_V2.AgencyRankingException('Invalid zip code format; must be a 5-digit number.');
        }
        
        List<CalculationMatrixRow> matrixRows = getCalculationMatrixData(MOTOR_PARAMETERS_MATRIX_CAP_LAT_LONG);
        system.debug('###TESSSSSTTTmatrixRows: ' + matrixRows);
        if (matrixRows == null || matrixRows.isEmpty()) {
            throw new EngineAgencyRanking_V2.AgencyRankingException('No records found in the Decision Matrix "Definizione_Cap_Lat_Long".');
        }
       
        
        Map<String, Object> decisionMatrixResult = getMapDecisionMatrix(matrixRows, zipCode);
        
        System.debug('# decisionMatrixResult: ' + JSON.serialize(decisionMatrixResult));

        // Check if the Decision Matrix returned a result
        if (decisionMatrixResult != null && decisionMatrixResult.containsKey('Latitude') && decisionMatrixResult.containsKey('Longitude')) {
            return new Map<String, Decimal>{
                'Latitude' => (Decimal) decisionMatrixResult.get('Latitude'),
                'Longitude' => (Decimal) decisionMatrixResult.get('Longitude')
            };
        } else {
            throw new EngineAgencyRanking_V2.AgencyRankingException('Zip code not found in the Decision Matrix "Definizione_Cap_Lat_Long".');
        }
    }

    /**
     * Sets the test matrix rows for testing purposes.
     *
     * @param matrixRows List of CalculationMatrixRow objects to be set as test data.
     */
    @TestVisible
    public static void setTestingMatrixRows(List<CalculationMatrixRow> matrixRows) {
        testingMatrixRows = matrixRows;
    }

    /**
     * Retrieves a set of unique Agency records associated with open opportunities for a given account relation ID.
     *
     * @param accountAccountRelationId The ID of the account-account relation to search for associated opportunities.
     * @return Set<Account> A set of unique Agency records associated with the open opportunities.
     * @throws EngineAgencyRanking_V2.AgencyRankingException If the user does not have sufficient permissions 
     *         to access or query the Opportunity object.
     */
    public static Set<Account> getAgencyforProspectAnag(String accountAccountRelationId) {
        // Create a Set to store unique Agency records
        Set<Account> agencySet = new Set<Account>();
        // Check if the Opportunity object is accessible and queryable
         Schema.DescribeSObjectResult describeResult = Schema.sObjectType.Opportunity;
         checkObjectPermissions(describeResult, 'Opportunity');
        
        List<Opportunity> opportunityList = [
                SELECT Id, Agency__r.AgencyCode__c,Agency__r.OmnichannelAgreement__c, Agency__r.Discretionality__c, Agency__r.DiscretionalityMotor__c, Agency__r.DiscretionalityWelfare__c, Agency__r.DiscretionalityProperty__c, Agency__r.DiscretionalityEnterprise__c,
                Agency__r.CheckCAPAssignedContactActivities__c
                FROM Opportunity 
                WHERE AccountId IN (SELECT FinServ__Account__c FROM FinServ__AccountAccountRelation__c 
                WHERE FinServ__Role__r.Name = :ROLE_CLIENTE 
                AND Id = :accountAccountRelationId)  
                AND StageName != 'Chiuso'
                AND StageName != 'Assegnato'
                AND StageName != 'Nuovo'
                ORDER BY CreatedDate DESC   
            ];
            system.debug('##opportunityList: ' + opportunityList);
        
        if (opportunityList.size() > 0) {
            // Iterate through the list of opportunities
            for (Opportunity opp : opportunityList) {
                // Check if the Agency__r lookup field is not null
                if (opp.Agency__r != null) {
                    // Add the Agency record to the Set
                    agencySet.add(opp.Agency__r);
                }
            }
            
        }
        return agencySet;
    }



        /**
     * Retrieves the default agency based on the provided account number.
     *
     * @param defaultAgencyAccountNumber The account number of the default agency to retrieve.
     * @return List<ServiceTerritory> A list containing the default agency's ServiceTerritory record.
     * @throws AgencyRankingException If no default agency is found.
     */
    public static List<ServiceTerritory> getDefaultAgency(String defaultAgencyAccountNumber) {
        if (!Schema.sObjectType.ServiceTerritory.isAccessible() || !Schema.sObjectType.ServiceTerritory.isQueryable()) {
            throw new EngineAgencyRanking_V2.AgencyRankingException('Insufficient permissions to access or query the ServiceTerritory object.');
        }
        List<ServiceTerritory> defaultAgencies = [
            SELECT Id, OwnerId, IsDeleted, Name,  ParentTerritoryId, TopLevelTerritoryId, Description, OperatingHoursId, Street, City, State, PostalCode,
                Country, Latitude, Longitude, GeocodeAccuracy, Address, IsActive, TypicalInTerritoryTravelTime, Agency__c, Address__c, Tipo__c, Account__c,
                Account__r.RecordType.DeveloperName, Account__r.name, Punto_vendita_principale__c, AccountAddress__c, Code__c, Email__c, ExternalId__c, Phone__c, Agency__r.KPI__c, Agency__r.ExternalId__c,Agency__r.DiscretionalityMotor__c,
                Agency__r.CheckCAPAssignedContactActivities__c,Agency__r.Discretionality__c, Agency__r.DiscretionalityWelfare__c, Agency__r.DiscretionalityProperty__c , Agency__r.DiscretionalityEnterprise__c
            FROM ServiceTerritory 
            WHERE Agency__r.AgencyCode__c =: defaultAgencyAccountNumber
            AND Agency__r.RecordType.DeveloperName =:RECORD_TYPE_AGENCY /*and Tipo__c =:TYPE_CLIENTE*/
            // LIMIT 1
        ]; 
        
        if(defaultAgencies.isEmpty()) {
            throw new EngineAgencyRanking_V2.AgencyRankingException('NO_DEFAULT_AGENCY_WAS_FOUND');
        }
        return defaultAgencies;
    }

    /**
     * Checks the user's permissions for accessing and querying a specific SObject type.
     *
     * @param sObjectType The Schema.SObjectType representing the object to check permissions for.
     * @param objectName The name of the object being checked, used for error messaging.
     * @throws EngineAgencyRanking_V2.AgencyRankingException If the user does not have sufficient permissions 
     *         to access or query the specified object.
     */
    private static void checkObjectPermissions(Schema.DescribeSObjectResult describeResult, String objectName) {
        if (!describeResult.isAccessible() || !describeResult.isQueryable()) {
            throw new EngineAgencyRanking_V2.AgencyRankingException(
                'Insufficient permissions to access or query the ' + objectName + ' object.'
            );
        }
    }



    /**
     * Retrieves a list of ServiceTerritory records based on the provided agency account IDs.
     *
     * @param agencyAccounIdList A set of agency account IDs to filter the ServiceTerritory records.
     * @return List<ServiceTerritory> A list of ServiceTerritory records that match the provided agency account IDs.
     * @throws AgencyRankingException If insufficient permissions to access or query the ServiceTerritory object.
     */
    public static List<ServiceTerritory> getTerrytoryByAgencyCodeList(Set<String> agencyAccounIdList) {
        // Validate CRUD permissions for the ServiceTerritory object
        Schema.DescribeSObjectResult describeResult = Schema.sObjectType.ServiceTerritory;
        checkObjectPermissions(describeResult, 'ServiceTerritory');
        system.debug('##agencyAccounIdList: ' + agencyAccounIdList);

        // Check if the agencyAccounIdList is null or empty        
        if (agencyAccounIdList == null || agencyAccounIdList.isEmpty()) {
            throw new EngineAgencyRanking_V2.AgencyRankingException('agencyAccounIdList cannot be null or empty for method "getTerrytoryByAgencyCodeList()".');
        }
        List<ServiceTerritory> terrytoryList = [
            SELECT Id, OwnerId, IsDeleted, Name,  ParentTerritoryId, TopLevelTerritoryId, Description, OperatingHoursId, Street, City, State, PostalCode,
                Country, Latitude, Longitude, GeocodeAccuracy, Address, IsActive, TypicalInTerritoryTravelTime, Agency__c, Address__c, Tipo__c, Account__c,
                Account__r.RecordType.DeveloperName, Account__r.name, Punto_vendita_principale__c, AccountAddress__c, Code__c, Email__c, ExternalId__c, Phone__c, Agency__r.KPI__c,Agency__r.DiscretionalityMotor__c,
                Agency__r.CheckCAPAssignedContactActivities__c,Agency__r.DiscretionalityWelfare__c, Agency__r.DiscretionalityProperty__c , Agency__r.DiscretionalityEnterprise__c
            FROM ServiceTerritory 
            WHERE Agency__c in :agencyAccounIdList
            AND Agency__r.RecordType.DeveloperName =:RECORD_TYPE_AGENCY /*and Tipo__c =:RECORD_TYPE_AGENCY*/
        ]; 
        system.debug('##terrtoryList: ' + terrytoryList);
        system.debug('##RECORD_TYPE_AGENCY: ' + RECORD_TYPE_AGENCY);
        // Check if the query returned any results
        if (terrytoryList.isEmpty()) {
            throw new EngineAgencyRanking_V2.AgencyRankingException('No ServiceTerritory records found for the provided agency account IDs: ' + agencyAccounIdList);
        }
        
        return terrytoryList;
    }


    /**
     * Retrieves a list of active insurance policies for a given account relation ID.
     *
     * @param accountAccountRelationId The ID of the account-account relation.
     * @return List<InsurancePolicy> A list of active or recently expired insurance policies.
     */
    private static List<InsurancePolicy> getActivePolicies(String accountAccountRelationId) {
        
        // Validate CRUD permissions for the InsurancePolicy object
        Schema.DescribeSObjectResult describeResult = Schema.sObjectType.InsurancePolicy;
        checkObjectPermissions(describeResult, 'InsurancePolicy');
        // Perform the query
        return [
            SELECT Id, AgencyCode__c,Agency__r.AgencyCode__c, Agency__r.OmnichannelAgreement__c, Agency__r.Discretionality__c, Agency__r.DiscretionalityMotor__c, Agency__r.DiscretionalityWelfare__c, Agency__r.DiscretionalityProperty__c, Agency__r.DiscretionalityEnterprise__c,
            Agency__r.CheckCAPAssignedContactActivities__c
            FROM InsurancePolicy
            WHERE (
                (ActiveDate__c = null OR ActiveDate__c > :Date.today()) 
                OR (ExpirationDate >= :Date.today().addYears(-2) AND status NOT IN ('NotActive', 'Initial', 'Suspended'))
            )
            AND NameInsuredId IN (
                SELECT FinServ__Account__c 
                FROM FinServ__AccountAccountRelation__c 
                WHERE FinServ__Role__r.Name =:ROLE_CLIENTE AND id = :accountAccountRelationId
            )
            AND Agency__c != null
            ORDER BY CreatedDate DESC
        ];
    }

    public static Map<String,Set<Account>> checkClientType( String accountAccountRelationId){
       
        //!IMPORTANTE! ->regola per le polizze in vigore è:
        //ActiveDate__c is null OR CurrentDate < ActiveDate__c 
        //poi potenzialmente si usera il campo Status per capire se è un cliente ha polizze attive o meno
        Map<String,Set<Account>> clientTypeAndAgencyCOde = new Map<String,Set<Account>>();
        String agencyCodeForProAnagClienteExCliente2Anni;
        // Create a Set to store unique Agency records
        Set<Account> agencySet = new Set<Account>();
        // Validate CRUD permissions for the InsurancePolicy object
        Schema.DescribeSObjectResult describeResult = Schema.sObjectType.InsurancePolicy;
        checkObjectPermissions(describeResult, 'InsurancePolicy');

        List<InsurancePolicy> policyList = getActivePolicies(accountAccountRelationId);
        System.debug('##policyList: ' + policyList);
            //SUBJECT->CLIENT_EX_CLIENTE_2_ANNI if there are active policies or expired policies within the last 2 years
            //SUBJECT->PROSPECT_PURO if there are no active policies and no expired policies within the last 2 years
            //and if there are not oppotunity for him
            //SUBJECT PROSPECT:ANAGRAFATO if there are oppotunity dot closed for him
            if (!policyList.isEmpty()) {                    
                    // Iterate through the list of opportunities
                    for (InsurancePolicy pol : policyList) {
                        // Check if the Agency__r lookup field is not null
                        if (pol.Agency__r != null) {
                            // Add the Agency record to the Set
                            agencySet.add(pol.Agency__r);
                        }
                    }
                    clientTypeAndAgencyCOde.put(CLIENT_EX_CLIENTE_2_ANNI,agencySet) ;                
                //agencyCodeForProAnagClienteExCliente2Anni=policyList[0].AgencyCode__c;
                //return CLIENT_EX_CLIENTE_2_ANNI;                
            } 
            else {
                agencySet=getAgencyforProspectAnag(accountAccountRelationId);
                if (agencySet.isEmpty()) {
                    clientTypeAndAgencyCOde.put(PROSPECT_PURO,null) ;
                    //return PROSPECT_PURO;
                }else{
                    //in tale caso si vuole come agenzia scelta quella con cui risulta associata nell'ultimo preventivo
                    clientTypeAndAgencyCOde.put( PROSPECT_ANAGRAFATO,agencySet) ;
                }
               // return PROSPECT_ANAGRAFATO;
            }
            return clientTypeAndAgencyCOde;
    }

    /**
     * Filters a set of Account records based on the OmnichannelAgreement__c field.
     *
     * @param agencyList The set of Account records to filter.
     * @return Set<Account> A set of Account records that have OmnichannelAgreement__c set to true.
     */
    
    public static Set<Account> filterOmnichannelAgreement(Set<Account> agencyList){
        Set<Account> filteredAgencies = new Set<Account>();
        for (Account agency : agencyList) {
            if (agency.OmnichannelAgreement__c == true) {
                filteredAgencies.add(agency);
            }
        }
        return filteredAgencies;

    }

     /**
     * Filters a set of Account records based on the AgencyToExlude Input list.
     *
     * @param agencyList The set of Account records to filter.
     * @return Set<Account> A set of Account records that have OmnichannelAgreement__c set to true.
     */
    
     public static Set<Account> filterAgToExcludeOnInput(Set<Account> agencyList,List<String> agenciestoExcludeIds){
        Set<Account> filteredAgencies = new Set<Account>();
        System.debug('##agencyList: ' + agencyList);
        System.debug('##agenciestoExcludeIds: ' + agenciestoExcludeIds);
        if (agenciestoExcludeIds == null || agenciestoExcludeIds.isEmpty()) {
            // If the exclusion list is empty, return the original agencyList
            return agencyList;
        }
        for (Account agency : agencyList) {
            System.debug('##agency: ' + agency.AgencyCode__c);
            System.debug('##agenciestoExcludeIds: ' + agenciestoExcludeIds);
            if (agency.agencyCode__c != null &&!agenciestoExcludeIds.contains(agency.AgencyCode__c)) {
                filteredAgencies.add(agency);
            }
        }
        System.debug('##filteredAgencies: ' + filteredAgencies);
        return filteredAgencies;

    }



    public static EngineWrapper_V2.EngineWrapperOutput buildResponse(Boolean result,String subjectType,List<EngineWrapper_V2.EngineSalesPoint> filteredEngineSalesPoint,List<EngineWrapper_V2.EngineExclusionSalesPoint> excludedEngineSalespoints, EngineWrapper_V2.EngineSettings engineSettings) {
        EngineWrapper_V2.EngineWrapperOutput response = new EngineWrapper_V2.EngineWrapperOutput(
                   result, 
                   subjectType,
                   filteredEngineSalesPoint, 
                   excludedEngineSalespoints, 
                   engineSettings
               );
        
        return response;
    }



    
 /**
     * Retrieves the parent agency for a given sales point.
     *
     * @param selectedSalespoint The sales point for which the parent agency is to be retrieved.
     * @param finalSalespoints A list of service territories to search for the parent agency.
     * @return An instance of EngineWrapper_V2.EngineAgency representing the parent agency, or null if not found.
     */
    public static EngineWrapper_V2.EngineAgency getParentAgency(ServiceTerritory finalSalespoints) {
        Id parentAgencyId = null;
        // Validate CRUD permissions for the ServiceTerritory object
        Schema.DescribeSObjectResult describeResult = Schema.sObjectType.ServiceTerritory;
        checkObjectPermissions(describeResult, 'ServiceTerritory');
            if(finalSalespoints!= null && finalSalespoints.Agency__c != null){
            parentAgencyId = finalSalespoints.Agency__c;
        }
        
        List<Account> selectedAgencyList = [
            SELECT Id, Name, ExternalId__c, Blacklist__c,BlackListUnica__c,BlackListBPER__c,BlacklistPrevidenza__c,OmnichannelAgreement__c, Type, 
            Discretionality__c, DiscretionalityMotor__c, BillingAddressFormula__c, IsLocatorEnabled__c, 
            CheckCAPAssignedContactActivities__c, BillingAddress, DiscretionalityWelfare__c,
            DiscretionalityProperty__c, DiscretionalityEnterprise__c, Agency__c, SubType__c, 
            District__c, VatCode__c, CAPActivity__c, ExtraCAP__c, 
            AgencyLoad__c, OmnichannelResponsible__c, AccountNumber, Size__c, Cluster__c 
            FROM Account 
            WHERE Id =: parentAgencyId 
            //WITH USER_MODE
            LIMIT 1
        ];
        ///Per il momento il parametro kpiSet del wrapper "EngineAgency" è vuoto 
        ////ma poi andranno presi dal costruttore corretto e popolati i KPI del SalesPoint correlato come segue sotto
        //// EngineWrapper_V2.KPISet kpiSet = new EngineWrapper_V2.KPISet(finalAgencyKPIMap.get(sp?.Agency__r.Id));
        ////Dove agencyKPIMap Map of Id to KPI__c objects representing the KPI data for each agency.

        return selectedAgencyList.isEmpty() ? null : new EngineWrapper_V2.EngineAgency(
                selectedAgencyList[0],
                // null,
                // new EngineWrapper_V2.KPISet(),
                // null,
                true
            );
    }
    



    /**
     * Checks if the input value is present in the multipicklist field of the Account.
     *
     * @param account The Account object containing the multipicklist field.
     * @param inputValue The input string to check against the multipicklist values.
     * @return Boolean True if the input value is present in the multipicklist, otherwise false.
     */
    // public static Boolean isValueInMultipicklist(Account account, String inputValue) {
    //     // Validate input
    //     if (String.isBlank(inputValue)) {
    //         throw new EngineAgencyRanking_V2.AgencyRankingException('Input value is blank; cannot check against multipicklist.');
    //     }

    //     // Check if the multipicklist field is populated
    //     if (String.isNotBlank(account.BlackListoAmbitoProdotto__c)) {
    //         // Split the multipicklist values into a Set
    //         Set<String> multipicklistValues = new Set<String>(account.BlackListoAmbitoProdotto__c.split(';'));

    //         // Check if the input value is present in the multipicklist
    //         return multipicklistValues.contains(inputValue);
    //     }

    //     return false; // Return false if the multipicklist is empty
    // }
}