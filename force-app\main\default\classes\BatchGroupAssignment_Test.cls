@IsTest
public class BatchGroupAssignment_Test {
    @testSetup
    static void setup() {
        NetworkUser__c nu = new NetworkUser__c();
        nu.NetworkUser__c = '000000';
        nu.FiscalCode__c = '****************';
        nu.IsActive__c = true;
        nu.Profile__c = 'A';
        nu.Society__c = 'SOC_1';

        NetworkUser__c nu2 = new NetworkUser__c();
        nu2.NetworkUser__c = '000001';
        nu2.FiscalCode__c = '****************';
        nu2.IsActive__c = true;
        nu2.Profile__c = 'A';
        nu2.Society__c = 'SOC_1';

        NetworkUser__c nu3 = new NetworkUser__c();
        nu3.NetworkUser__c = '000002';
        nu3.FiscalCode__c = null;
        nu3.IsActive__c = true;
        nu3.Profile__c = 'A';
        nu3.Society__c = 'SOC_1';

        NetworkUser__c nu4 = new NetworkUser__c();
        nu4.NetworkUser__c = '000003';
        nu4.FiscalCode__c = '****************';
        nu4.IsActive__c = true;
        nu4.Profile__c = 'A';
        nu4.Society__c = 'SOC_1';

        insert new List<NetworkUser__c>{ nu, nu2, nu3, nu4 };

        User user = new User(FirstName = 'Test', LastName = 'User', Username = '<EMAIL>', Alias = 'tuser', Email = '<EMAIL>', ProfileId = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1].Id, TimeZoneSidKey = 'Europe/Rome', LocaleSidKey = 'it_IT', EmailEncodingKey = 'UTF-8', LanguageLocaleKey = 'it', ExternalId__c = '****************');

        User user2 = new User(FirstName = 'Test', LastName = 'User', Username = '<EMAIL>', Alias = 'tuser', Email = '<EMAIL>', ProfileId = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1].Id, TimeZoneSidKey = 'Europe/Rome', LocaleSidKey = 'it_IT', EmailEncodingKey = 'UTF-8', LanguageLocaleKey = 'it', ExternalId__c = '****************');

        User user3 = new User(FirstName = 'Test', LastName = 'User', Username = '<EMAIL>', Alias = 'tuser', Email = '<EMAIL>', ProfileId = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1].Id, TimeZoneSidKey = 'Europe/Rome', LocaleSidKey = 'it_IT', EmailEncodingKey = 'UTF-8', LanguageLocaleKey = 'it', ExternalId__c = '****************');

        insert new List<User>{ user, user2, user3 };

        Group testGroup = new Group(Name = 'CIP_1_TEST_00111', DeveloperName = 'CIP_1_TEST_00111', Type = 'Regular', DoesIncludeBosses = true, DoesSendEmailToMembers = false);
        insert testGroup;

        Group__c groups = new Group__c(Name = 'CIP_1_TEST_00111', ExternalId__c = 'CIP_1_TEST_00111', PublicGroupId__c = testGroup.Id);
        insert groups;

        GroupAssignment__c ga = new GroupAssignment__c(CIP__c = groups.Id, NetworkUser__c = nu.Id, IsActive__c = false, TechStatus__c = 'TO_BE_PROCESSED');
        insert ga;

        GroupAssignment__c ga2 = new GroupAssignment__c(CIP__c = null, NetworkUser__c = null, IsActive__c = true, TechStatus__c = 'TO_BE_PROCESSED');
        insert ga2;

        GroupAssignment__c ga3 = new GroupAssignment__c(CIP__c = groups.Id, NetworkUser__c = nu2.Id, IsActive__c = true, TechStatus__c = 'TO_BE_PROCESSED');
        insert ga3;

        GroupAssignment__c ga4 = new GroupAssignment__c(CIP__c = groups.Id, NetworkUser__c = nu3.Id, IsActive__c = true, TechStatus__c = 'TO_BE_PROCESSED');
        insert ga4;

        GroupAssignment__c ga5 = new GroupAssignment__c(CIP__c = groups.Id, NetworkUser__c = nu4.Id, IsActive__c = true, TechStatus__c = 'TO_BE_PROCESSED');
        insert ga5;
    }

    @IsTest
    static void testDeleteInactiveMember() {
        User u = [SELECT Id FROM User WHERE ExternalId__c = '****************' LIMIT 1];
        Group g = [SELECT Id FROM Group WHERE DeveloperName = 'CIP_1_TEST_00111' LIMIT 1];

        // Membro che verrà rimosso
        GroupMember gm = new GroupMember(GroupId = g.Id, UserOrGroupId = u.Id);
        insert gm;

        // Act
        Test.startTest();
        Database.executeBatch(new BatchGroupAssignment(), 200);
        Database.executeBatch(new BatchGroupAssignment('TO_BE_PROCESSED'), 200);
        Test.stopTest();
    }
}
