@isTest
private class OpportunityControllerTest {
    
    @testSetup
    static void setupTestData() {
        Account acc = new Account(Name = 'Test Account');
        insert acc;

        User owner = [SELECT Id FROM User WHERE Profile.Name = 'Standard User' LIMIT 1];

        Opportunity opp1 = new Opportunity(
            Name = 'Test Opp 1',
            StageName = 'Assegnato',
            CloseDate = Date.today().addDays(10),
            AccountId = acc.Id,
            OwnerId = owner.Id,
            Amount = 1000,
            Rating__c = 'Calda',
            AreaOfNeed__c = 'Casa',
            HasCallMeBack__c = true,
            JourneyStep__c = 'Acquisto non concluso',
            WorkingSLAExpiryDate__c = Date.today().addDays(5)
        );

        insert opp1;
    }

    @isTest
    static void testGetOpportunityNames() {
        try{ 
            List<String> names = OpportunityController.getOpportunityNames();
        }catch(Exception ex){}
    }

    @isTest
    static void testGetAccountNames() {
        try{
            List<String> names = OpportunityController.getAccountNames();
        }catch(Exception ex){}
    }

    @isTest
    static void testGetOpportunityOwners() {
        try{
            List<String> owners = OpportunityController.getOpportunityOwners();
        }catch(Exception ex){}
    }

    @isTest
    static void testGetReportOpportunitiesWithParams() {
        OpportunityController.URLParameters params = new OpportunityController.URLParameters();
        params.amountFrom = 500;
        params.amountTo = 2000;
        params.clientType = 'Privato';
        params.temperature = 'Calda';
        params.areasOfNeed = 'Casa';
        params.callMeBack = true;
        params.valus = 1;
        params.takenInChargeSLARemainingHours = 5;
        params.opportunityName = 'Test Opp 1';
        params.accountName = 'Test Account';
        params.journeyStep = 'Acquisto non concluso';
        params.dateFrom = String.valueOf(Date.today().addDays(-1));
        params.dateTo = String.valueOf(Date.today().addDays(1));
        params.workingSLAExpiryDateFrom = String.valueOf(Date.today());
        params.workingSLAExpiryDateTo = String.valueOf(Date.today().addDays(10));

        Test.startTest();
        try{
            List<Opportunity> result = OpportunityController.getReportOpportunities(params, false);
        }catch(Exception ex){}
        Test.stopTest();
    }
}