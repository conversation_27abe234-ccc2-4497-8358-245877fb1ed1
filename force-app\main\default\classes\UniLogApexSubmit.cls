public without sharing class UniLogApexSubmit {

    public static void testGetCaller(){
        
        // uniLogger.writeInfo('myMessage', 'myPayload');

        StaticResource sr= [SELECT id,body FROM StaticResource WHERE Name = 'json_dump'];
        String allcontents = sr.body.toString();
        String charactersLessThan400k = allcontents.abbreviate(350000);
        charactersLessThan400k += '"}]';

        // Map<String, Object> reconstructJSON = (Map<String, Object>) JSON.deserializeUntyped(charactersLessThan400k);

        String prettyJSON = JSON.serializePretty(charactersLessThan400k);

        uniLogger.writeInfo('test1bugPayload', prettyJSON);

        system.debug(LoggingLevel.DEBUG, 'last 10 charac : ' + prettyJSON.right(10));


    }
    public static void testBigPayload(){


    }

        public static void testmanualLog(){
        // string myId = 'this is an id';
        // uniLogger.writeInfo('myMessage with Id' + myId, 'myPayload');

        // UniLogger myLog = unilogger('mymessage', 'mypayload', 3);
        // mylog
        //     .setReferenceIdentifier(myId)
        //     .setClassName((myclassName))
        //     .setLoggingLevel()
        //     .publishLog;

    }


}