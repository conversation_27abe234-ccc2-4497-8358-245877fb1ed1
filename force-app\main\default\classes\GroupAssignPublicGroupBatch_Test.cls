@isTest
private class GroupAssignPublicGroupBatch_Test {

    @testSetup
    static void setup() {

        NetworkUser__c nu = new NetworkUser__c();
        nu.NetworkUser__c = '000000'; 
        nu.FiscalCode__c = '****************';
        nu.IsActive__c = true;
        nu.Profile__c = 'A';
        nu.Society__c = 'SOC_1';
        
        insert new List<NetworkUser__c>{nu};

        User user = new User(
            FirstName = 'Test',
            LastName = 'User',
            Username = '<EMAIL>',
            Alias = 'tuser',
            Email = '<EMAIL>', 
            ProfileId = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1].Id,
            TimeZoneSidKey = 'Europe/Rome',
            LocaleSidKey = 'it_IT',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'it',
            ExternalId__c = '****************');

        insert new List<User>{user};

        Group testGroup = new Group( 
            Name = 'CIP_1_TEST_00111', 
            DeveloperName = 'CIP_1_TEST_00111', 
            Type = 'Regular', 
            DoesIncludeBosses = true, 
            DoesSendEmailToMembers = false); 
        insert testGroup; 

        Group__c groups = new Group__c(
            Name = 'CIP_1_TEST_00111',
            ExternalId__c = 'CIP_1_TEST_00111',
            PublicGroupId__c = testGroup.Id);
        insert groups;

        GroupAssignment__c ga = new GroupAssignment__c(
            CIP__c            = groups.Id,
            NetworkUser__c    = nu.Id,
            IsActive__c       = false,
            TechStatus__c     = 'TO_BE_PROCESSED'
        );
        insert ga;
    }

    @IsTest
    static void testBatch() {

        Test.startTest();
        Database.executeBatch(new GroupAssignPublicGroupBatch(), 200);
        Test.stopTest();
    }

    @IsTest
    static void testBatch1() {

        Group__c grp = [SELECT Id FROM Group__c LIMIT 1];

        Test.startTest();
        Database.executeBatch(new GroupAssignPublicGroupBatch(new Set<Id>{grp.Id}), 200);
        Test.stopTest();
    }

    @IsTest
    static void testBatch2() {

        Group__c grp = [SELECT Id FROM Group__c LIMIT 1];

        Test.startTest();
        Database.executeBatch(new GroupAssignPublicGroupBatch(grp.Id, grp.Id), 200);
        Test.stopTest();
    }

    @IsTest
    static void testBatch3() {

        Group__c grp = [SELECT Id, CreatedDate FROM Group__c LIMIT 1];

        Test.startTest();
        Database.executeBatch(new GroupAssignPublicGroupBatch(grp.CreatedDate.addDays(-2), grp.CreatedDate, 'CreatedDate'), 200);
        Test.stopTest();
    }
}