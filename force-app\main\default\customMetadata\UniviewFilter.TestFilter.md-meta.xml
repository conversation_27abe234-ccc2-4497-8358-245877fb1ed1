<?xml version="1.0" encoding="UTF-8"?>
<CustomMetadata xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <label>TestFilter</label>
    <protected>false</protected>
    <values>
        <field>FieldName__c</field>
        <value xsi:type="xsd:string">Name</value>
    </values>
    <values>
        <field>FieldOrder__c</field>
        <value xsi:type="xsd:double">1.0</value>
    </values>
    <values>
        <field>FieldType__c</field>
        <value xsi:type="xsd:string">Text</value>
    </values>
    <values>
        <field>FilterClass__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>FilterInputType__c</field>
        <value xsi:type="xsd:string">text</value>
    </values>
    <values>
        <field>FilterLabel__c</field>
        <value xsi:type="xsd:string">Nome trattativa</value>
    </values>
    <values>
        <field>FilterName__c</field>
        <value xsi:type="xsd:string">Name</value>
    </values>
    <values>
        <field>FilterOperatorList__c</field>
        <value xsi:type="xsd:string">uguale a</value>
    </values>
    <values>
        <field>FilterOperator__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>FilterOptions__c</field>
        <value xsi:type="xsd:string">QUERY</value>
    </values>
    <values>
        <field>FilterType__c</field>
        <value xsi:type="xsd:string">Picklist With Suggestions</value>
    </values>
    <values>
        <field>FilterValue__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>IsActive__c</field>
        <value xsi:type="xsd:boolean">true</value>
    </values>
    <values>
        <field>IsModifiable__c</field>
        <value xsi:type="xsd:boolean">true</value>
    </values>
    <values>
        <field>IsNullable__c</field>
        <value xsi:type="xsd:boolean">false</value>
    </values>
    <values>
        <field>Object__c</field>
        <value xsi:type="xsd:string">Opportunity</value>
    </values>
    <values>
        <field>UniviewKey__c</field>
        <value xsi:type="xsd:string">TestReport</value>
    </values>
</CustomMetadata>
