@isTest
public with sharing class NoteControllerTest {

    @testSetup
    static void setup() {
         Account account1 = new Account(Name = 'Test Agency 1', ExternalId__c = 'Test1', RecordTypeId = [SELECT Id FROM RecordType WHERE DeveloperName = 'Agency' AND SObjectType = 'Account' LIMIT 1].Id);
         insert account1;
        
         Account account3 = new Account(Name = 'Test Society 2', ExternalId__c = 'Test3', RecordTypeId = [SELECT Id FROM RecordType WHERE DeveloperName = 'IndustriesBusiness' AND SObjectType = 'Account' LIMIT 1].Id);
         insert account3;

         FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(Name = 'Cliente', FinServ__InverseRole__c = 'Agenzia');
         insert role;
 
         // Crea una relazione esistente tra Agenzia e Compagnia
         FinServ__AccountAccountRelation__c existingRelation = new FinServ__AccountAccountRelation__c(
             FinServ__Account__c = account3.Id,
             FinServ__RelatedAccount__c = account1.Id,
             FinServ__Role__c = role.Id
         );

        Group g = new Group(DeveloperName = 'R_Test1', Name = 'Agency');
        insert g;
        
        insert existingRelation;
        Note testNote = new Note(
            Title = 'Test Note',
            Body = 'This is a test note.',
            ParentId = existingRelation.Id
        );
        insert testNote;
    }

    @isTest
    static void testDeleteNote() {
        Note testNote = [SELECT Id FROM Note LIMIT 1];
        
        Test.startTest();
        NoteController.deleteNote(testNote.Id);
        Test.stopTest();
    }

    @isTest
    static void testUpdateNote() {
        Note testNote = [SELECT Id, Title, Body FROM Note LIMIT 1];
        
        String newTitle = 'Updated Test Note';
        String newBody = 'This is an updated test note.';

        Test.startTest();
        NoteController.updateNote(testNote.Id, newTitle, newBody);
        Test.stopTest();
        
        Note updatedNote = [SELECT Id, Title, Body FROM Note WHERE Id = :testNote.Id LIMIT 1];

        System.assertEquals(newTitle, updatedNote.Title);
        System.assertEquals(newBody, updatedNote.Body);
    }
    
    @isTest
    static void testDeleteNoteCase() {
        ContentNote cn = new ContentNote(Title = 'Test ContentNote', Content = Blob.valueOf('Test content'));
        insert cn;
    
        Test.startTest();
        NoteController.deleteNoteCase(cn.Id);
        Test.stopTest();
    
        List<ContentNote> notes = [SELECT Id FROM ContentNote WHERE Id = :cn.Id];
        System.assertEquals(0, notes.size(), 'ContentNote should be deleted');
    }
    
    @isTest
    static void testUpdateCaseNote() {
        ContentNote cn = new ContentNote(Title = 'Old Title', Content = Blob.valueOf('Old content'));
        insert cn;
    
        String newTitle = 'New Title';
        String newBody = 'Updated content';
    
        Test.startTest();
        NoteController.updateCaseNote(cn.Id, newTitle, newBody);
        Test.stopTest();
    
        ContentNote updated = [SELECT Title FROM ContentNote WHERE Id = :cn.Id];
        System.assertEquals(newTitle, updated.Title);
    }

    @isTest
    static void testGetNotesJson() {
        Account acc = [SELECT Id FROM Account LIMIT 1];
    
        ContentNote cn = new ContentNote(Title = 'Test Note', Content = Blob.valueOf('Test content'));
        insert cn;
    
        ContentVersion cv = new ContentVersion(
            Title = 'Test Note',
            PathOnClient = 'TestNote.txt',
            VersionData = Blob.valueOf('Test content'),
            IsMajorVersion = true
        );
        insert cv;
    
        ContentDocument cd = [SELECT Id FROM ContentDocument WHERE LatestPublishedVersionId = :cv.Id LIMIT 1];
    
        insert new ContentDocumentLink(
            ContentDocumentId = cd.Id,
            LinkedEntityId = acc.Id,
            ShareType = 'V',
            Visibility = 'AllUsers'
        );
    
        Test.startTest();
        String json = NoteController.getNotesJson(acc.Id);
        Test.stopTest();
    
        System.assert(json.contains('Test Note'), 'JSON should contain the note title');
    }

    @isTest
    static void testHasPermissionSet() {
        Profile p = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1];
        User u = new User(
            Username = '<EMAIL>',LastName = 'Test',Email = '<EMAIL>',Alias = 'tuserps',TimeZoneSidKey = 'Europe/Rome',LocaleSidKey = 'it_IT',
            EmailEncodingKey = 'UTF-8',ProfileId = p.Id,LanguageLocaleKey = 'it');
        insert u;

        PermissionSet ps = [SELECT Id FROM PermissionSet WHERE Name = 'OperativitaAttivitaNonAssegnate' LIMIT 1];

        PermissionSetAssignment psa = new PermissionSetAssignment(
            AssigneeId = u.Id,
            PermissionSetId = ps.Id
        );
        insert psa;

        System.runAs(u) {
            Boolean hasPS = NoteController.hasPermissionSet();
            System.assertEquals(true, hasPS, 'L\'utente dovrebbe avere il Permission Set');
        }
    }
}