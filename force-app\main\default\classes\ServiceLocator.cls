public with sharing class ServiceLocator {
    private static final Map<Type, Type> CUSTOM_TYPES_MAP = new Map<Type, Type>{};

    private static final Map<Type, Type> TEST_TYPES_MAP = new Map<Type, Type>{};

    private static Map<String, Object> typeNameToInstance = new Map<String, Object>();

    public static void registerTestType(Type original, Type mock) {
        if (Test.isRunningTest()) {
            TEST_TYPES_MAP.put(original, mock);
        }
    }
    
    public static Type resolve(Type t) {
        if (Test.isRunningTest()) {
            if (TEST_TYPES_MAP.containsKey(t)) {
                return TEST_TYPES_MAP.get(t);
            }
        }

        if (CUSTOM_TYPES_MAP.containsKey(t)) {
            return CUSTOM_TYPES_MAP.get(t);
        }

        return t;
    }

    public static Object getInstance(Type t) {
        if (typeNameToInstance.containsKey(t.getName())) {
            return typeNameToInstance.get(t.getName());
        }
        Type theType = resolve(t);
        Object requiredInstance = theType.newInstance();
        typeNameToInstance.put(t.getName(), requiredInstance);
        return requiredInstance;
    }
}