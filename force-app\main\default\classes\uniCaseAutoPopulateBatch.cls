global class uniCaseAutoPopulateBatch implements Database.Batchable<SObject> {

    global Database.QueryLocator start(Database.BatchableContext bc) {

        DateTime sixHoursAgo = System.now().addHours(-6);

        String baseFields = 'Id, Status, AccountId, Priority, AreaOfNeed__c, AssignedTo__c, Agency__c, AssignedGroup__c, Area__c, Detail__c, ' +
            'CCEngaged__c, DueDate__c, Insurance_Policy__c, Requestfromclient__c, Activity__c, Created_Date__c, LeoActivityCode__c, Nature__c, Source__c, ' +
            'ExternalId__c, isCCEnabled__c, BusinessKey__c, ClosedDate__c, StartDate__c, RecordTypeId, RecordType.DeveloperName ';
        
        String techFields = 'TECH_LeoHeat__c, TECH_LeoPriority__c, TECH_NeedsCloseCallout__c, TECH_AssignmentRules__c, TECH_getInTouch__c, ' +
            'TECH_PossibleAssignemnt__c, TECH_RequiredIncident__c, TECH_RequiredPolicy__c, TECH_ShowCloseManual__c, ' +
            'TECH_ShowOutcome__c, TECH_IsPlannable__c';

        String drFields = 'DrAgenziaFiglia__c, DrAgenziaMadre__c, DrAnnotazione__c, DrAssegnatarioDesc__c, DrAssegnatario__c, ' +
            'DrAssegnazione__c, DrCfPivaCliente__c, DrCodice__c, DrDominio__c, ' +
            'DrCompagnia__c, DrIdAttivitaLeonardo__c, DrContextEntity__c, DrFlagAnnotazione__c, DrCanale__c';

        String query = 'SELECT ' + baseFields + ', ' + techFields + ', ' + drFields +
            ' FROM Case WHERE CreatedDate >= :sixHoursAgo AND DrDominio__c != null AND DrCodice__c != null' +
            ' AND (RecordTypeId = null OR RecordType.DeveloperName = \'AttivitaContatto\')';

        return Database.getQueryLocator(query);
    }

    global void execute(Database.BatchableContext bc, List<Case> scope) {
        Set<String> leoCode = collectLeoCodes(scope);
        if (!leoCode.isEmpty()) {
            Map<Integer, CaseActivityInit__c> caseActInitMap = getCaseActivityInits(leoCode);
            if (!caseActInitMap.isEmpty() && scope != null && !scope.isEmpty()) {
                List<Case> caseAfterInit = processAndEnrichCases(scope, caseActInitMap);
                updateCasesWithHandling(caseAfterInit);
            }
        }
    }

    global void finish(Database.BatchableContext bc) {
        Integer batchSize = 30;
        Database.executeBatch(new ManageCaseBatch(Date.today()), batchSize); //richiamo al batch per gestire i coni
    }

    private Set<String> collectLeoCodes(List<Case> scope) {
        Set<String> leoCode = new Set<String>();
        for (Case c : scope) {
            if (c.DrDominio__c != null && c.DrCodice__c != null) {
                System.debug('Processing Case with DrDominio__c: ' + c.DrDominio__c + ' and DrCodice__c: ' + c.DrCodice__c);
                String leo = c.DrDominio__c + c.DrCodice__c;
                leoCode.add(leo);
            }
        }
        system.debug('Leo codes collected: ' + leoCode.size());
        System.debug('Leo codes: ' + leoCode);
        return leoCode;
    }

    private static Map<Integer, CaseActivityInit__c> getCaseActivityInits(Set<String> leoCodes) {
    List<CaseActivityInit__c> caseActInitList = [SELECT Name, LeoCode__c, NeedsCloseCallout__c, Source__c, CCEngaged__c, PossibleAssignemnt__c,
        AssignmentRules__c, LeoHeat__c, LeoPriority__c, ShowOutcome__c, IsPlannable__c, Nature__c, Area__c, ClosedDate__c, Activity__c, Detail__c, AreaOfNeed__c, DueDateDays__c, IsCallBack__c, IsReservedArea__c,
        RequiredPolicy__c, RequiredIncident__c, GetInTouch__c, ShowCloseManual__c, OverrideAgecy__c FROM CaseActivityInit__c WHERE OverrideAgecy__c = null AND LeoCode__c IN :leoCodes];

        Map<Integer, CaseActivityInit__c> caseActivityMap = new Map<Integer, CaseActivityInit__c>();
        for (CaseActivityInit__c caseActivity : caseActInitList) {
            Integer leo = Integer.valueOf(caseActivity.LeoCode__c);
            if (!caseActivityMap.containsKey(leo)) {
                caseActivityMap.put(leo, caseActivity);
            }
        }
        system.debug('CaseActivityInit__c records found: ' + caseActivityMap.size());
        System.debug('CaseActivityInit__c records: ' + caseActivityMap);
        return caseActivityMap;
    }

    private List<Case> processAndEnrichCases(List<Case> scope, Map<Integer, CaseActivityInit__c> caseActInitMap) {
        List<Case> caseAfterInit = processExternalCases(scope, caseActInitMap);
        CaseRulesBeforeInsert.populateAgencyOnAutomaticCase(caseAfterInit);
        CaseRulesBeforeInsert.setCasesPriority(caseAfterInit);
        CaseRulesAssignementBI.manageCaseGroupsOwnership(caseAfterInit);
        CaseRulesAssignementBI.checkPolicy(caseAfterInit);
        List<Case> newStatusCases = new List<Case>();
        List<Case> otherCases = new List<Case>();
        for (Case c : caseAfterInit) {
            if (c.Status == 'New') {
                newStatusCases.add(c);
            } else {
                otherCases.add(c);
            }
        }

        if (!newStatusCases.isEmpty()) {
            CaseRulesBeforeInsert.updateStatusToExpiredDueDate(newStatusCases);
            CaseRulesBeforeInsert.updateStatusToClosed(newStatusCases);
        }

        List<Case> allCases = new List<Case>();
        allCases.addAll(newStatusCases);
        allCases.addAll(otherCases);
            
        system.debug('Case after processing external cases: ' + allCases.size());
        System.debug('Case after processing external cases: ' + allCases);
        return allCases;
    }

    private static List<Case> processExternalCases(List<Case> newListCase, Map<Integer, CaseActivityInit__c> externalCase) {
        List<Case> casesToUpdate = new List<Case>();
        for (Case c : newListCase) {
            c.LeoActivityCode__c = (c.DrDominio__c != null && c.DrCodice__c != null) ? c.DrDominio__c + c.DrCodice__c : null;
            System.debug('dominio: ' + c.DrDominio__c + ' codice: ' + c.DrCodice__c + ' LeoActivityCode: ' + c.LeoActivityCode__c);
            if (externalCase.containsKey(Integer.valueOf(c.LeoActivityCode__c))) {
                System.debug('Matching CaseActivityInit__c found for LeoActivityCode: ' + c.LeoActivityCode__c);
                CaseActivityInit__c matchedRecord = externalCase.get(Integer.valueOf(c.LeoActivityCode__c));
                CaseRulesBeforeInsert.populateFieldsIfEmpty(c, matchedRecord);
            }
            c.StartDate__c = (c.StartDate__c == null) ? System.today() : c.StartDate__c;
            String recordTypeId = Schema.SObjectType.Case.getRecordTypeInfosByName().get('AttivitaContatto').getRecordTypeId();
            c.RecordTypeId = (c.RecordTypeId == null) ? recordTypeId : c.RecordTypeId;
            casesToUpdate.add(c);
        }
        System.debug('Cases to update after processing: ' + casesToUpdate.size());
        System.debug('Cases to update: ' + casesToUpdate);
        return casesToUpdate;
    } 
    
    private void updateCasesWithHandling(List<Case> caseAfterInit) {
        if (caseAfterInit != null && !caseAfterInit.isEmpty()) {
            try {
                Database.SaveResult[] results = Database.update(caseAfterInit, false);
                for (Integer i = 0; i < results.size(); i++) {
                    if (results[i].isSuccess()) {
                        continue;
                    }
                    logCaseUpdateError(caseAfterInit[i], results[i]);
                }
            } catch (Exception ex) {
                System.debug('Exception during Case update: ' + ex.getMessage());
            }
        }
    }

    private static void logCaseUpdateError(Case c, Database.SaveResult result) {
        System.debug('Error updating Case with Id: ' + c.Id + ' - ' + result.getErrors()[0].getMessage());
    }
}