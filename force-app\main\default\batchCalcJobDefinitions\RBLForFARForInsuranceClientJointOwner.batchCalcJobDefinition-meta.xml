<?xml version="1.0" encoding="UTF-8"?>
<BatchCalcJobDefinition xmlns="http://soap.sforce.com/2006/04/metadata">
    <aggregates>
        <fields>
            <aggregateFunction>Sum</aggregateFunction>
            <alias>InsuredAmountSum</alias>
            <sourceFieldName>FinancialAccount_InsuredAmount</sourceFieldName>
        </fields>
        <groupBy>FinServ_RelatedAccount_c</groupBy>
        <label>aggregate_FinancialAccount_InsuredAmount</label>
        <name>aggregate_FinancialAccount_InsuredAmount</name>
        <sourceName>FinServ_FinancialAccountRole_c_Filter</sourceName>
    </aggregates>
    <datasources>
        <fields>
            <alias>Id</alias>
            <dataType>Text</dataType>
            <name>Id</name>
        </fields>
        <fields>
            <alias>Name</alias>
            <dataType>Text</dataType>
            <name>Name</name>
        </fields>
        <label>Account</label>
        <name>Account</name>
        <sourceName>Account</sourceName>
        <type>StandardObject</type>
    </datasources>
    <datasources>
        <fields>
            <alias>FinancialAccount_InsuredAmount</alias>
            <dataType>Numeric</dataType>
            <name>FinServ__FinancialAccount__r.FinServ__InsuredAmount__c</name>
        </fields>
        <fields>
            <alias>FinancialAccount_RecordTypeName</alias>
            <dataType>Text</dataType>
            <name>FinServ__FinancialAccount__r.FinServ__RecordTypeName__c</name>
        </fields>
        <fields>
            <alias>FinServ_RelatedAccount_c</alias>
            <dataType>Text</dataType>
            <name>FinServ__RelatedAccount__c</name>
        </fields>
        <fields>
            <alias>FinServ_Role_c</alias>
            <dataType>Text</dataType>
            <name>FinServ__Role__c</name>
        </fields>
        <label>FinServ_FinancialAccountRole_c</label>
        <name>FinServ_FinancialAccountRole_c</name>
        <sourceName>FinServ__FinancialAccountRole__c</sourceName>
        <type>StandardObject</type>
    </datasources>
    <description>Client-level aggregation of all Financial Accounts with record type Insurance Policy where Client is designated as a Joint Owner on Financial Account Role. Multiple Joint Owners supported.</description>
    <executionPlatformType>CRMA</executionPlatformType>
    <filters>
        <criteria>
            <operator>Equals</operator>
            <sequence>1</sequence>
            <sourceFieldName>FinServ_Role_c</sourceFieldName>
            <value>Joint Owner</value>
        </criteria>
        <criteria>
            <operator>Equals</operator>
            <sequence>2</sequence>
            <sourceFieldName>FinancialAccount_RecordTypeName</sourceFieldName>
            <value>Insurance Policy</value>
        </criteria>
        <filterCondition>1 AND 2</filterCondition>
        <isDynamicFilter>false</isDynamicFilter>
        <label>FinServ_FinancialAccountRole_c_Filter</label>
        <name>FinServ_FinancialAccountRole_c_Filter</name>
        <sourceName>FinServ_FinancialAccountRole_c</sourceName>
    </filters>
    <isTemplate>false</isTemplate>
    <joins>
        <fields>
            <alias>Id</alias>
            <sourceFieldName>Id</sourceFieldName>
            <sourceName>Account</sourceName>
        </fields>
        <fields>
            <alias>InsuredAmountSum</alias>
            <sourceFieldName>InsuredAmountSum</sourceFieldName>
            <sourceName>aggregate_FinancialAccount_InsuredAmount</sourceName>
        </fields>
        <joinKeys>
            <primarySourceFieldName>Id</primarySourceFieldName>
            <secondarySourceFieldName>FinServ_RelatedAccount_c</secondarySourceFieldName>
        </joinKeys>
        <label>Account_aggregate_FinancialAccount_InsuredAmount</label>
        <name>Account_aggregate_FinancialAccount_InsuredAmount</name>
        <primarySourceName>Account</primarySourceName>
        <secondarySourceName>aggregate_FinancialAccount_InsuredAmount</secondarySourceName>
        <type>LeftOuter</type>
    </joins>
    <label>RBLForFARForInsuranceClientJointOwner</label>
    <processType>DataProcessingEngine</processType>
    <status>Inactive</status>
    <writebacks>
        <fields>
            <runtimeParameter>false</runtimeParameter>
            <sourceFieldName>Id</sourceFieldName>
            <targetFieldName>Id</targetFieldName>
        </fields>
        <fields>
            <runtimeParameter>false</runtimeParameter>
            <sourceFieldName>InsuredAmountSum</sourceFieldName>
            <targetFieldName>FinServ__TotalInsuranceJointOwner__c</targetFieldName>
        </fields>
        <isChangedRow>false</isChangedRow>
        <label>RBLForFARForInsuranceClientJointOwnerExport</label>
        <name>RBLForFARForInsuranceClientJointOwnerExport</name>
        <operationType>Update</operationType>
        <sourceName>Account_aggregate_FinancialAccount_InsuredAmount</sourceName>
        <storageType>sObject</storageType>
        <targetObjectName>Account</targetObjectName>
        <writebackSequence>1</writebackSequence>
        <writebackUser>0057Q00000A7swHQAR</writebackUser>
    </writebacks>
</BatchCalcJobDefinition>
