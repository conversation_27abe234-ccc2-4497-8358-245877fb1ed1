/**
 * @File Name         : InsurancePolicyBatchTest.cls
 * @Description       : Test class for InsurancePolicyBatch.
 * <AUTHOR> <EMAIL>
 * @Group             :
 * @Last Modified On  : 24-01-2025
 * @Last Modified By  : <EMAIL>
 **/
@isTest
public class InsurancePolicyBatchTest {
    @testSetup
    static void setupTestData() {
        // Create RecordTypes for Agency and Society
        Id agencyRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId();
        Id societyRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId();

        // Crea un record di ruolo (supponendo che l'oggetto sia FinServ__ReciprocalRole__c)
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(Name = 'Agenzia', FinServ__InverseRole__c = 'Compagnia');
        insert role;

        List<Account> personAccounts = new List<Account>();
        List<Account> agencyAccounts = new List<Account>();
        List<Account> societyAccounts = new List<Account>();
        List<InsurancePolicy> policies = new List<InsurancePolicy>();
        List<Group> groups = new List<Group>();
        List<FinServ__AccountAccountRelation__c> accAccRelations = new List<FinServ__AccountAccountRelation__c>();

        for (Integer i = 0; i < 5; i++) {
            // Create test data for Person Account
            personAccounts.add(new Account(RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('PersonAccount').getRecordTypeId(), FirstName = 'First ' + i, LastName = 'Last ' + i));
            // Create test data for Account (Agency)
            agencyAccounts.add(new Account(Name = 'Test Agency Account ' + i, RecordTypeId = agencyRecordTypeId, ExternalId__c = 'AGE_' + String.valueOf(10000 + i)));
            // Create test data for Account (Society)
            societyAccounts.add(new Account(Name = 'Test Society Account ' + i, RecordTypeId = societyRecordTypeId, ExternalId__c = 'SOC_' + String.valueOf(i + 1)));
        }
        System.debug('### DEV -> personAccounts = ' + personAccounts);
        System.debug('### DEV -> agencyAccounts = ' + agencyAccounts);
        System.debug('### DEV -> societyAccounts = ' + societyAccounts);
        insert personAccounts;
        insert agencyAccounts;
        insert societyAccounts;

        for (Integer i = 0; i < 5; i++) {
            // Create test data for InsurancePolicy
            policies.add(new InsurancePolicy(Name = 'Test Policy ' + i, Agency__c = agencyAccounts[i].Id, Society__c = societyAccounts[i].Id, CIP__c = '050', NameInsuredId = personAccounts[i].Id, CompanyCode__c = String.valueOf(i)));
            // Create test data for AccountAccountRelation
            FinServ__AccountAccountRelation__c aar1 = new FinServ__AccountAccountRelation__c(FinServ__Account__c = personAccounts[i].Id, FinServ__RelatedAccount__c = agencyAccounts[i].Id, FinServ__Role__c = role.Id);
            accAccRelations.add(aar1);
            FinServ__AccountAccountRelation__c aar2 = new FinServ__AccountAccountRelation__c(FinServ__Account__c = personAccounts[i].Id, FinServ__RelatedAccount__c = societyAccounts[i].Id, FinServ__Role__c = role.Id);
            accAccRelations.add(aar2);
        }
        System.debug('### DEV -> policies = ' + policies);
        System.debug('### DEV -> accAccRelations = ' + accAccRelations);
        insert policies;
        insert accAccRelations;

        for (Integer i = 0; i < 5; i++) {
            String agencyExternalId = agencyAccounts[i].ExternalId__c.replace('AGE_', '');
            String societyExternalId = societyAccounts[i].ExternalId__c.replace('SOC_', '');
            String groupName = 'CIP_' + societyExternalId + '_' + agencyExternalId + '_' + policies[i].CIP__c;
            groups.add(new Group(DeveloperName = groupName, Name = groupName));
        }
        System.debug('### DEV -> groups = ' + groups);
        insert groups;
    }

    @isTest
    static void testInsurancePolicyBatch() {
        // Start the test
        Test.startTest();

        // Execute the batch
        Database.executeBatch(new InsurancePolicyBatch());
        Database.executeBatch(new InsurancePolicyBatch(Date.today()));

        // End the test
        Test.stopTest();

        /*
        // Verify the results
        List<AccountShare> accShares = [SELECT Id, AccountId, UserOrGroupId, AccountAccessLevel FROM AccountShare];
        System.assertEquals(16, accShares.size(), 'Expected 16 AccountShare records.');

        List<FinServ__AccountAccountRelation__Share> agencyShares = [SELECT Id, ParentId, UserOrGroupId, AccessLevel FROM FinServ__AccountAccountRelation__Share WHERE ParentId IN (SELECT Id FROM FinServ__AccountAccountRelation__c WHERE FinServ__RelatedAccount__r.RecordTypeId = :Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId())];
        System.assertEquals(5, agencyShares.size(), 'Expected 5 FinServ__AccountAccountRelation__Share records for Agency.');

        List<FinServ__AccountAccountRelation__Share> societyShares = [SELECT Id, ParentId, UserOrGroupId, AccessLevel FROM FinServ__AccountAccountRelation__Share WHERE ParentId IN (SELECT Id FROM FinServ__AccountAccountRelation__c WHERE FinServ__RelatedAccount__r.RecordTypeId = :Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId())];
        System.assertEquals(5, societyShares.size(), 'Expected 5 FinServ__AccountAccountRelation__Share records for Society.');
        */
    }
}
