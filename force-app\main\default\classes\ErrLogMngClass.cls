/**
 * @File Name         : ErrLogMngClass.cls
 * @Description       : 
 * <AUTHOR> <EMAIL>
 * @Group             : 
 * @Last Modified On  : 02-01-2025
 * @Last Modified By  : <EMAIL>
**/
public with sharing class ErrLogMngClass {
    
    public void integrationErrLog(Exception exc, HttpRequest req, HttpResponse res, String className) {
        
        ErrorLogMng__c elm = new ErrorLogMng__c();
        elm.ReqMethod__c = req.getMethod();
        elm.ReqEndpoint__c = req.getEndpoint();
        elm.ReqBody__c = req.getBody();
        //elm.ReqTimeOut__c = req.getTimeout();

        elm.ResStatus__c = res.getStatus();
        elm.ResStatusCode__c = String.valueOf(res.getStatusCode());
        elm.ResBody__c = res.getBody();

        elm.ErrLogCause__c = String.valueOf(exc.getCause());
        elm.ErrLogClassName__c = className;
        elm.ErrLogLineNumber__c = String.valueOf(exc.getLineNumber());
        elm.ErrLogMessage__c = exc.getMessage();
        elm.ErrLogTypeName__c = exc.getTypeName();

        Database.SaveResult elmIns = Database.insert(elm);
    }

    public void dmlErr (DmlException dmlExc, String className) {
        
        ErrorLogMng__c elm = new ErrorLogMng__c();
        elm.ErrLogCause__c = String.valueOf(dmlExc.getCause());
        elm.ErrLogClassName__c = className;
        elm.ErrLogLineNumber__c = String.valueOf(dmlExc.getLineNumber());
        elm.ErrLogMessage__c = dmlExc.getMessage();
        elm.ErrLogTypeName__c = dmlExc.getTypeName();
        
        Database.SaveResult elmIns = Database.insert(elm);        
    }
}