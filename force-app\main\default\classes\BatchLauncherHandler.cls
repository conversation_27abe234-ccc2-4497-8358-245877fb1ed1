public class BatchLauncherHandler {
	 @invocablemethod(label='Execute Batch')
    public static void executeBatch(List<String> batchClassNames)
    {
        for (String batchClassName : batchClassNames) {
            Type batchType = Type.forName(batchClassName);
            if (batchType != null) {
                Database.Batchable<sObject> batchInstance = (Database.Batchable<sObject>) batchType.newInstance();
                Database.executeBatch(batchInstance);
            }
        }
    }
}