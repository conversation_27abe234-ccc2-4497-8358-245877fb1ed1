public with sharing class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {
    /******************************************************************************************
    * @description 
    * @return       Map<String, Object> - 
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Map<String, Object> getProfessioni() {
        Map<String, Object> ipInput = new Map<String, Object> {
            'actionToCall' => 'prof'
        };
        //AC: (aggiunge spiegazione)
        return AddressService.invokeIntegrationProcedureLookup('IntegrationAnag_IntegrationAnag', ipInput, null);
    }

    /******************************************************************************************
    * @description 
    * @param        ipInput: la mappa contiene codProf
    * @return       Map<String, Object> - 
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Map<String, Object> getRicProf(Map<String, Object> ipInput) {
        //mettere in system.label
        ipInput.put('actionToCall', 'ricProf');
        return AddressService.invokeIntegrationProcedureLookup('IntegrationAnag_IntegrationAnag', ipInput, null);
    }

    /******************************************************************************************
    * @description 
    * @return       Map<String, Object> - 
    *******************************************************************************************/
    @AuraEnabled
    public static Map<String, Object> getPersonaGiuridica() {
        Map<String, Object> ipInput = new Map<String, Object> {
            'actionToCall' => 'pg'
        };
        //AC: (aggiunge spiegazione)
        return AddressService.invokeIntegrationProcedureLookup('IntegrationAnag_IntegrationAnag', ipInput, null);
    }

    /******************************************************************************************
    * @description 
    * @param        ipInput: la mappa contiene codProf
    * @return       Map<String, Object> - 
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Map<String, Object> getRicPg(Map<String, Object> ipInput) {
        //mettere in system.label
        ipInput.put('actionToCall', 'ricPG');
        return AddressService.invokeIntegrationProcedureLookup('IntegrationAnag_IntegrationAnag', ipInput, null);
    }

    /******************************************************************************************
    * @description
    * @return       Map<String, Object> - 
    *******************************************************************************************/
    @AuraEnabled()
    public static Map<String, Object> getRicPgDinLpr() {
        Map<String, Object> ipInput = new Map<String, Object> {
            'actionToCall' => 'DinLpr'
        };
        return AddressService.invokeIntegrationProcedureLookup('IntegrationAnag_IntegrationAnag', ipInput, null);
    }
}