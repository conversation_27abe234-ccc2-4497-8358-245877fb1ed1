<?xml version="1.0" encoding="UTF-8"?>
<BatchCalcJobDefinition xmlns="http://soap.sforce.com/2006/04/metadata">
    <aggregates>
        <fields>
            <aggregateFunction>Sum</aggregateFunction>
            <alias>InsuredAmountSum</alias>
            <sourceFieldName>FinServ_InsuredAmount_c</sourceFieldName>
        </fields>
        <groupBy>FinServ_PrimaryOwner_c</groupBy>
        <label>aggregate_FinServ_InsuredAmount_c</label>
        <name>aggregate_FinServ_InsuredAmount_c</name>
        <sourceName>FinServ_FinancialAccount_c_RecordType_Filter</sourceName>
    </aggregates>
    <datasources>
        <fields>
            <alias>Id</alias>
            <dataType>Text</dataType>
            <name>Id</name>
        </fields>
        <fields>
            <alias>Name</alias>
            <dataType>Text</dataType>
            <name>Name</name>
        </fields>
        <label>Account</label>
        <name>Account</name>
        <sourceName>Account</sourceName>
        <type>StandardObject</type>
    </datasources>
    <datasources>
        <fields>
            <alias>FinServ_InsuredAmount_c</alias>
            <dataType>Numeric</dataType>
            <name>FinServ__InsuredAmount__c</name>
        </fields>
        <fields>
            <alias>FinServ_PrimaryOwner_c</alias>
            <dataType>Text</dataType>
            <name>FinServ__PrimaryOwner__c</name>
        </fields>
        <fields>
            <alias>RecordTypeId</alias>
            <dataType>Text</dataType>
            <name>RecordTypeId</name>
        </fields>
        <label>FinServ_FinancialAccount_c</label>
        <name>FinServ_FinancialAccount_c</name>
        <sourceName>FinServ__FinancialAccount__c</sourceName>
        <type>StandardObject</type>
    </datasources>
    <datasources>
        <fields>
            <alias>DeveloperName</alias>
            <dataType>Text</dataType>
            <name>DeveloperName</name>
        </fields>
        <fields>
            <alias>Id</alias>
            <dataType>Text</dataType>
            <name>Id</name>
        </fields>
        <label>RecordType</label>
        <name>RecordType</name>
        <sourceName>RecordType</sourceName>
        <type>StandardObject</type>
    </datasources>
    <description>Client-level aggregation of all Financial Accounts with record type Insurance Policy where Client is Primary Owner on the Financial Account.</description>
    <executionPlatformType>CRMA</executionPlatformType>
    <filters>
        <criteria>
            <operator>Equals</operator>
            <sequence>1</sequence>
            <sourceFieldName>DeveloperName</sourceFieldName>
            <value>InsurancePolicy</value>
        </criteria>
        <filterCondition>1</filterCondition>
        <isDynamicFilter>false</isDynamicFilter>
        <label>FinServ_FinancialAccount_c_RecordType_Filter</label>
        <name>FinServ_FinancialAccount_c_RecordType_Filter</name>
        <sourceName>FinServ_FinancialAccount_c_RecordType</sourceName>
    </filters>
    <isTemplate>false</isTemplate>
    <joins>
        <fields>
            <alias>Id</alias>
            <sourceFieldName>Id</sourceFieldName>
            <sourceName>Account</sourceName>
        </fields>
        <fields>
            <alias>InsuredAmountSum</alias>
            <sourceFieldName>InsuredAmountSum</sourceFieldName>
            <sourceName>aggregate_FinServ_InsuredAmount_c</sourceName>
        </fields>
        <joinKeys>
            <primarySourceFieldName>Id</primarySourceFieldName>
            <secondarySourceFieldName>FinServ_PrimaryOwner_c</secondarySourceFieldName>
        </joinKeys>
        <label>Account_aggregate_FinServ_InsuredAmount_c</label>
        <name>Account_aggregate_FinServ_InsuredAmount_c</name>
        <primarySourceName>Account</primarySourceName>
        <secondarySourceName>aggregate_FinServ_InsuredAmount_c</secondarySourceName>
        <type>LeftOuter</type>
    </joins>
    <joins>
        <fields>
            <alias>DeveloperName</alias>
            <sourceFieldName>DeveloperName</sourceFieldName>
            <sourceName>RecordType</sourceName>
        </fields>
        <fields>
            <alias>FinServ_InsuredAmount_c</alias>
            <sourceFieldName>FinServ_InsuredAmount_c</sourceFieldName>
            <sourceName>FinServ_FinancialAccount_c</sourceName>
        </fields>
        <fields>
            <alias>FinServ_PrimaryOwner_c</alias>
            <sourceFieldName>FinServ_PrimaryOwner_c</sourceFieldName>
            <sourceName>FinServ_FinancialAccount_c</sourceName>
        </fields>
        <joinKeys>
            <primarySourceFieldName>RecordTypeId</primarySourceFieldName>
            <secondarySourceFieldName>Id</secondarySourceFieldName>
        </joinKeys>
        <label>FinServ_FinancialAccount_c_RecordType</label>
        <name>FinServ_FinancialAccount_c_RecordType</name>
        <primarySourceName>FinServ_FinancialAccount_c</primarySourceName>
        <secondarySourceName>RecordType</secondarySourceName>
        <type>LeftOuter</type>
    </joins>
    <label>RBLForInsuranceClientPrimaryOwner</label>
    <processType>DataProcessingEngine</processType>
    <status>Inactive</status>
    <writebacks>
        <fields>
            <runtimeParameter>false</runtimeParameter>
            <sourceFieldName>Id</sourceFieldName>
            <targetFieldName>Id</targetFieldName>
        </fields>
        <fields>
            <runtimeParameter>false</runtimeParameter>
            <sourceFieldName>InsuredAmountSum</sourceFieldName>
            <targetFieldName>FinServ__TotalInsurancePrimaryOwner__c</targetFieldName>
        </fields>
        <isChangedRow>false</isChangedRow>
        <label>RBLForInsuranceClientPrimaryOwnerExport</label>
        <name>RBLForInsuranceClientPrimaryOwnerExport</name>
        <operationType>Update</operationType>
        <sourceName>Account_aggregate_FinServ_InsuredAmount_c</sourceName>
        <storageType>sObject</storageType>
        <targetObjectName>Account</targetObjectName>
        <writebackSequence>1</writebackSequence>
        <writebackUser>0057Q00000A7swHQAR</writebackUser>
    </writebacks>
</BatchCalcJobDefinition>
