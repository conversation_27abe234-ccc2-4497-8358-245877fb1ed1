@isTest
private class UpdateGroup_R_AGE_ALL_Batch_Test {

    /*
     * SETUP DATA IS NOT USED FOR STANDARD GROUP
    @testSetup
    static void setupData() {
        List<Group> groupAll = [SELECT Id FROM Group WHERE DeveloperName = 'R_AGE_ALL' LIMIT 1];
        Id ID_groupR_AGE_ALL;
               
        if (!groupAll.isEmpty()) {
        	ID_groupR_AGE_ALL = groupAll[0].id;
        }

        // Crea altri gruppi R_AGE_*
        List<Group> otherGroups = new List<Group>();
        for (Integer i = 1; i <= 3; i++) {
            otherGroups.add(new Group(
                Name = 'Gruppo R_AGE_' + i,
                DeveloperName = 'R_AGE_' + i,
                Type = 'Regular'
            ));
        }
        insert otherGroups;
    }
*/
    
    @isTest
    static void testBatchExecution() {
        Test.startTest();
        UpdateGroup_R_AGE_ALL_Batch batch = new UpdateGroup_R_AGE_ALL_Batch();
        Database.executeBatch(batch, 10000);
        Test.stopTest();

        // Recupera il gruppo R_AGE_ALL
        Group groupAll = [SELECT Id FROM Group WHERE DeveloperName = 'R_AGE_ALL' LIMIT 1];
        
        List<Group> groupR_AGEAll = [SELECT Id FROM Group WHERE DeveloperName like 'R_AGE%' AND DeveloperName <> 'R_AGE_ALL'];

        // Recupera i membri aggiornati
        List<GroupMember> updatedMembers = [
            SELECT UserOrGroupId FROM GroupMember WHERE GroupId = :groupAll.Id
            LIMIT 1000
        ];

        // Verifica che ci siano i membri (tutti i gruppi R_AGE_ tranne R_AGE_ALL)
        //System.assertEquals(groupR_AGEAll.size(), updatedMembers.size(), 'Il gruppo R_AGE_ALL dovrebbe avere'+ groupR_AGEAll.size() + ' membri.');
    }

    @isTest
    static void testScheduledExecution() {
        Test.startTest();
        String cronExp = '0 0 0 1 1 ? 2026'; // Esecuzione fittizia
        System.schedule('Test Scheduled Batch', cronExp, new UpdateGroup_R_AGE_ALL_Batch());
        Test.stopTest();
    }
}