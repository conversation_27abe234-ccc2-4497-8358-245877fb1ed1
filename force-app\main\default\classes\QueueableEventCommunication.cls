/*
* @description This class is used to manage the queueable process of the user provisioning to set
* all Permissions Available for a specific UNIPOL User, info retrieved via UCA API.
* @cicd_tests UserProvisioningHandler_Test
*/
global class QueueableEventCommunication implements Queueable {
    
    /*
    * @description input wrapper of this queueable class
    */
    global class InputWrapper{
        global String accountId                     {get; set;}
        global String serviceAppointmentId          {get; set;}
        global String Argomento                     {get; set;}
        global String Consulente_Di_Riferimento     {get; set;}
        global String Link_Video_Call               {get; set;}
        global String Luogo                         {get; set;}
        global String Modalita_Di_Esecuzione        {get; set;}
        global String Nome_Cliente                  {get; set;}
        global String Ora_Appuntamento              {get; set;}
        global String eventType                     {get; set;}
        global String ownerId                       {get; set;}
    }
    
    global List<InputWrapper> input 		{get; set;}
    
    /*
    * @description constructor
    */
    global QueueableEventCommunication(List<InputWrapper> input){
        this.input = input;
    }
    
    /*
    * @description method to execute the queueable process
    * @param QueueableContext context
    */
    global void execute(QueueableContext context) {
        
        if(this.input != null && !this.input.isEmpty()){

            Set<String> accountId = new Set<String>();
            Set<String> serviceAppointmentId = new Set<String>();
            Set<String> serviceTerritoryId = new Set<String>();
            Set<String> relatedRecordId = new Set<String>();

            for(InputWrapper iw : this.input){

                if(String.isBlank(iw.accountId) && !Test.isRunningTest()) continue;

                accountId.add(iw.accountId);

                if(String.isNotBlank(iw.serviceAppointmentId)) serviceAppointmentId.add(iw.serviceAppointmentId);

                if(String.isNotBlank(iw.ownerId)) relatedRecordId.add(iw.ownerId);

            }

            Map<Id, Account> mapAccount = new Map<Id, Account>([SELECT Id, ExternalId__c, Name FROM Account WHERE Id IN :accountId]);

            Map<Id, ServiceAppointment> mapSA = new Map<Id, ServiceAppointment>([SELECT Id, Email FROM ServiceAppointment WHERE Id IN :serviceAppointmentId]);

            Map<Id, ServiceResource> mapSR = new Map<Id, ServiceResource>([SELECT Id FROM ServiceResource WHERE RelatedRecordId IN :relatedRecordId AND IsActive = true]);
            Map<Id, ServiceTerritoryMember> mapSTM = new Map<Id, ServiceTerritoryMember>([SELECT Id, ServiceTerritoryId, ServiceResourceId, ServiceResource.RelatedRecordId FROM ServiceTerritoryMember WHERE ServiceResourceId IN :mapSR.keySet() AND TerritoryType = 'P']);

            for(ServiceTerritoryMember stm : mapSTM.values()){
                serviceTerritoryId.add(stm.ServiceTerritoryId);
            }
            
            Map<String, ServiceTerritoryMember> mapSTMbySR = new Map<String, ServiceTerritoryMember>();
            for(ServiceTerritoryMember stm : mapSTM.values()){
                mapSTMbySR.put(stm.ServiceResource.RelatedRecordId, stm);
            }

            Map<Id, ServiceTerritory> mapST = new Map<Id, ServiceTerritory>([SELECT Id, Agency__c, Agency__r.Name, Agency__r.Phone, Agency__r.Email__c, Address__c,Phone__c FROM ServiceTerritory WHERE Id IN :serviceTerritoryId]);

            Set<String> agencyIds = new Set<String>();
            for(ServiceTerritory st : mapST.values()){
                agencyIds.add(st.Agency__c);
            }

            Map<Id, FinServ__AccountAccountRelation__c> listAAR = new Map<Id, FinServ__AccountAccountRelation__c>([SELECT Id FROM FinServ__AccountAccountRelation__c WHERE FinServ__Account__c IN :accountId AND FinServ__RelatedAccount__c IN :agencyIds]);

            List<AccountAgencyDetails__c> listAAD = [SELECT Id, Email__c, Relation__r.FinServ__Account__c, Relation__r.FinServ__RelatedAccount__c, Agency__r.Name, Agency__r.Phone, Agency__r.Email__c FROM AccountAgencyDetails__c WHERE Relation__c IN :listAAR.keySet()];

            Map<String, AccountAgencyDetails__c> mapAAD = new Map<String, AccountAgencyDetails__c>();
            for(AccountAgencyDetails__c aad : listAAD){
                mapAAD.put(aad.Relation__r.FinServ__Account__c + '_' + aad.Relation__r.FinServ__RelatedAccount__c, aad);
            }

            for(InputWrapper iw : this.input){

                if(String.isBlank(iw.accountId) && !Test.isRunningTest()) continue;
                
                Map<String, Object> params = new Map<String, Object>();

                try{

                    String email = null;
                    String location = null;

                    if(
                        String.isNotBlank(iw.serviceAppointmentId) && 
                        mapSA.containsKey(iw.serviceAppointmentId) && 
                        String.isNotBlank(mapSA.get(iw.serviceAppointmentId).Email)
                    ){
                        
                        email = mapSA.get(iw.serviceAppointmentId).Email;

                    }else if(
                    	String.isNotBlank(iw.ownerId) &&
                        mapSTMbySR.containsKey(iw.ownerId) &&
                        mapAAD.containsKey(iw.accountId+'_'+mapST.get(mapSTMbySR.get(iw.ownerId).ServiceTerritoryId).Agency__c)
                    ){
                        
                        AccountAgencyDetails__c aad = mapAAD.get(iw.accountId+'_'+mapST.get(mapSTMbySR.get(iw.ownerId).ServiceTerritoryId).Agency__c);
                        ServiceTerritory st = mapST.get(mapSTMbySR.get(iw.ownerId).ServiceTerritoryId);
                        
						email = aad.Email__c;
                        
                        params.put('Telefono_Agenzia',st.Phone__c);
                        params.put('Tipologia_Cliente','');
                        params.put('Email_Agenzia',st.Agency__r.Email__c);
                        params.put('Compagnia','');
                        params.put('Agenzia',st.Agency__r.Name);
                        
                    }

                    if(String.isNotBlank(iw.Luogo)){

                        location = iw.Luogo;

                    }else if(
                        'Agenzia'.equalsIgnoreCase(iw.Modalita_Di_Esecuzione) ||
                        'In Agenzia'.equalsIgnoreCase(iw.Modalita_Di_Esecuzione) && 
                        String.isNotBlank(iw.ownerId) &&
                        mapSTMbySR.containsKey(iw.ownerId)
                    ){

                        ServiceTerritory st = mapST.get(mapSTMbySR.get(iw.ownerId).ServiceTerritoryId);
                        location = st.Address__c;

                    }

                    if(String.isBlank(email) && !Test.isRunningTest()) continue;

                    params.put('Argomento',iw.Argomento);
                    if(!Test.isRunningTest()) params.put('CF_PIVA_Cliente',mapAccount.get(iw.accountId).ExternalId__c);
                    params.put('Consulente_Di_Riferimento',iw.Consulente_Di_Riferimento);
                    params.put('Email',email);
                    params.put('Link_Video_Call',iw.Link_Video_Call);
                    params.put('Luogo',location);
                    params.put('Modalita_Di_Esecuzione',iw.Modalita_Di_Esecuzione);
                    if(!Test.isRunningTest()) params.put('Nome_Cliente',mapAccount.get(iw.accountId).Name);
                    params.put('Ora_Appuntamento',iw.Ora_Appuntamento);
                    params.put('type',iw.eventType);
                
                    Flow.Interview.Comunicazioni_Cliente_Callout_Appuntamenti executeCallout = new Flow.Interview.Comunicazioni_Cliente_Callout_Appuntamenti(params);
                    executeCallout.start();

                }catch(Exception ex){
                    System.debug('QueueableEventCommunication Exception : '+ex.getMessage());
                    System.debug('QueueableEventCommunication Exception : '+ex.getStackTraceString());
                }

            }
            
        }
        
    }
    
}