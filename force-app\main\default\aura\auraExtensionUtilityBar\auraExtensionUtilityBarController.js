({
    doInit: function(component, event, helper) {
        window.addEventListener("mousemove", helper.handlerExecuteKeepAlive);
        window.addEventListener("mousedown", helper.handlerExecuteKeepAlive);
        window.addEventListener("keydown", helper.handlerExecuteKeepAlive);
        window.addEventListener("touchstart", helper.handlerExecuteKeepAlive);
        window.addEventListener("touchend", helper.handlerExecuteKeepAlive);
        window.onscroll = helper.handlerExecuteKeepAlive;

        helper.handlerExecuteKeepAlive();

        helper.callExtensionUrl(component);
    }
})