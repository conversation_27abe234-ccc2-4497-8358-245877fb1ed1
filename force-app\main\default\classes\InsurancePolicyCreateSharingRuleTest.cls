@isTest
private with sharing class InsurancePolicyCreateSharingRuleTest {
    @TestSetup
    static void makeData() {
        Account account1 = new Account(Name = 'Test Agency 1', RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId());
        insert account1;
        Account account2 = new Account(Name = 'Test Society 1', RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId());
        insert account2;
        Group g = new Group(Name = 'PippoGroup', DeveloperName = 'PippoGroup');
        insert g;
        Case c = new Case();
        c.AccountId = account1.Id;
        c.Agency__c = account1.Id;
        c.Area__c = 'Anagrafica e Documenti';
        c.Detail__c = 'Raccolta documenti mancanti';
        c.Activity__c = 'Recupero Documenti';
        c.dueDate__c = Date.today().addDays(10);
        insert c;
        Opportunity opp = new Opportunity();
        opp.Name = 'Test Opp 1';
        opp.AccountId = account1.Id;
        opp.Agency__c = account1.Id;
        opp.StageName = 'Chiuso';
        opp.CloseDate = Date.today();
        insert opp;
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(Name = 'Agenzia', FinServ__InverseRole__c = 'Compagnia');
        insert role;
        FinServ__AccountAccountRelation__c aar = new FinServ__AccountAccountRelation__c();
        aar.FinServ__Account__c = account1.Id;
        aar.FinServ__RelatedAccount__c = account2.Id;
        aar.FinServ__Role__c = role.Id;
        aar.RecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AccountSociety').getRecordTypeId();
        insert aar;
    }

    @isTest
    static void testCreateSharingRule() {
        Id idAccount = [SELECT Id FROM Account WHERE RecordTypeId = :Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId() LIMIT 1].Id;
        Id idSociety = [SELECT Id FROM Account WHERE RecordTypeId = :Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId() LIMIT 1].Id;
        Id idGroup = [SELECT Id FROM Group LIMIT 1].Id;
        InsurancePolicyCreateSharingRule.FlowInputs flowInputs = new InsurancePolicyCreateSharingRule.FlowInputs();
        flowInputs.accountId = idAccount;
        flowInputs.groupId = idGroup;
        flowInputs.agencyId = idAccount;
        flowInputs.societyId = idSociety;
        Test.startTest();
        List<InsurancePolicyCreateSharingRule.Results> res = InsurancePolicyCreateSharingRule.createSharingRule(new List<InsurancePolicyCreateSharingRule.FlowInputs>{ flowInputs });
        Assert.areEqual('OK', res.get(0).esito, 'Share rule created');
        Test.stopTest();
    }
}
