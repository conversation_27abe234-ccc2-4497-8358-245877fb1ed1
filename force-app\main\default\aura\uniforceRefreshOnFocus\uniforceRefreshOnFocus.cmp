<aura:component implements="lightning:backgroundUtilityItem,flexipage:availableForAllPageTypes">
    <lightning:workspaceAPI aura:id="workspace"/>
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <lightning:messageChannel type="refreshChannel__c" aura:id="refreshMessageChannel" />        
    <aura:handler event="lightning:tabFocused" action="{! c.onTabFocused }"/>
</aura:component>