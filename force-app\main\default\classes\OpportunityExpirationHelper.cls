public without sharing class OpportunityExpirationHelper {



    // list of picklist values used in the class
    public static final string QUOTE_COMMERCIAL_STATUS_EXPIRED = 'EXPIRED';
    public static final string QUOTE_COMMERCIAL_STATUS_SOLD = 'SOLD';
    public static final string QUOTE_COMMERCIAL_STATUS_ACTIVE = 'ACTIVE';
    public static final string OPPORTUNITY_CLOSURE_SUBSTATUS_CHIOSO_CON_VENDITA = 'Chiuso con Vendita';
    public static final string OPPORTUNITY_CLOSURE_SUBSTATUS_FUORI_SLA = 'Fuori SLA';
    public static final string OPPORTUNITY_CLOSURE_SUBSTATUS_POLIZZA_EMESSA = 'Polizza emessa';
    public static final string OPPORTUNITY_STAGENAME_CLOSE = 'Chiuso';
    public static final string OPPORTUNITY_RECORD_TYPE_DEVELOPER_NAME_PRODOTTO = 'Prodotto';
    public static final string OPPORTUNITY_RECORD_TYPE_DEVELOPER_NAME_AGENZIALE = 'Agenziale';
    public static final string OPPORTUNITY_RECORD_TYPE_DEVELOPER_NAME_OMNICANALE = 'Omnicanale';




    ///////////////////////////////////////////////////////////////////////////////////////////
    ////////////// The following methods orchestrate the closure of object ////////////////////
    ///////////////////////////////////////////////////////////////////////////////////////////




    /************
     * @description             change Quote status to Expired for Quotes whose expirationDate is passed.
     *                          records all their parent Opportunity Product, which need to be inspected for expiration themselves (expiration due to all children are expired)
     * 
     * @input                   Set<Id> of all Quotes whose expiraetion date is passed (considering expirationDate EndOfDay EOD - so filter with expirationiDate = YESTERDAY)
     * @output                  Set<Id> of Opporutnity product which requires inspect for expiration (expiration due to all children being expired)
     */
    public static void closeExpiringQuotesOrchestrator(Set<Id> expiringQuoteIds) {

        List<Quote> expiringQuotes = [SELECT Id, OpportunityId, CommercialStatus__c, IsStored__c, Opportunity.Parent__c FROM Quote WHERE CommercialStatus__c = 'ACTIVE' AND Id IN :expiringQuoteIds];
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringQuotesOrchestrator | expiringQuotes.size() ' + expiringQuotes.size());

        Set<Id> opportunityProductIds = new Set<Id>();
        Set<Id> opportunityContainerIds = new Set<Id>();

        for(Quote expiringQuote : expiringQuotes){
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringQuotesOrchestrator | expiringQuote ' + expiringQuote);
            opportunityProductIds.add(expiringQuote.OpportunityId);
            opportunityContainerIds.add(expiringQuote.Opportunity.Parent__c);
        } 

        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringQuotesOrchestrator | expiringQuotes.size() ' + expiringQuotes.size());
        closeQuote(expiringQuotes);

        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringQuotesOrchestrator | opportunityContainerIds.size() ' + opportunityContainerIds.size());
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringQuotesOrchestrator | opportunityContainerIds ' + opportunityContainerIds);
        inspectOppContainersForAnticipatedExpiration(opportunityContainerIds);

        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringQuotesOrchestrator | opportunityProductIds.size() ' + opportunityProductIds.size());
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringQuotesOrchestrator | opportunityProductIds ' + opportunityProductIds);
        inspectOppProductForAnticipatedExpiration(opportunityProductIds);

        //return opportunityIds; edit handled in helper and not in BATCH EXECUTE
    }


    /*************
     * @description             Orchestrate the changing of Opportunity Product status to Close.
     *                          -> call the closing and rolloup method
     *                          -> close related Quotes
     *                          -> return Opp parent to inspect for anticipated expiration
     *                         
     * @input                   Set<Id> of expiring opporutnity Products
     * @output                  Set<Id> of parent records to inspect for AnticipatedExpiration 
     */
    public static void closeExpiringOppProductsOrchestrator(Set<Id> expiringOpportunityProducts){

        List<Opportunity> oppProductsToClose = [SELECT Id, Parent__c, (SELECT Id, CommercialStatus__c FROM Quotes) FROM Opportunity WHERE  StageName != :OPPORTUNITY_STAGENAME_CLOSE AND Id IN :expiringOpportunityProducts]; 
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppProductsOrchestrator | oppProductsToClose ' + oppProductsToClose);
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppProductsOrchestrator | oppProductsToClose.size() ' + oppProductsToClose.size());

        Set<Id> oppParentToCheck = new Set<Id>();
        List<Quote> quotesActiveToExpire = new List<Quote>();
        
        for(Opportunity oppProduct : oppProductsToClose) {
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppProductsOrchestrator | oppProduct ' + oppProduct);
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppProductsOrchestrator | oppProduct.Quotes.size() ' + oppProduct.Quotes.size());
            for(Quote quote: oppProduct.Quotes){
                System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppProductsOrchestrator | quote ' + quote);
                if(Quote.CommercialStatus__c == QUOTE_COMMERCIAL_STATUS_ACTIVE){
                    System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppProductsOrchestrator | is Quote commercial status ACTIVE ');
                    quotesActiveToExpire.add(Quote);
                } 
            }
            oppParentToCheck.add(oppProduct.Parent__c);
        }

        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppProductsOrchestrator | quotesActiveToExpire ' + quotesActiveToExpire);
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppProductsOrchestrator | quotesActiveToExpire.size() ' + quotesActiveToExpire.size());
        closeQuote(quotesActiveToExpire);

        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppProductsOrchestrator | oppParentToCheck ' + oppParentToCheck);
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppProductsOrchestrator | oppParentToCheck.size() ' + oppParentToCheck.size());
        inspectOppContainersForAnticipatedExpiration(oppParentToCheck);

        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppProductsOrchestrator | expiringOpportunityProducts ' + expiringOpportunityProducts);
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppProductsOrchestrator | expiringOpportunityProducts.size() ' + expiringOpportunityProducts.size());
        closeOppProductAndRollupFields(expiringOpportunityProducts);


    }

        /**********
     * @description             Orchestrate the change of Opportunity Container status to Close
     *                          1) call the closing mehtod for related Quotes
     *                          2) call the closing method for related Opp Product          
     *                          3) call the closing and rollup method for Opp Container
     */
    public static void closeExpiringOppContainerOrchestrator(Set<Id> expiringOpportunityIdsAgenzialeOrOmnicanale){

        List<Opportunity> oppContainerToCheck =  [SELECT Id,  (SELECT Id, StageName, ClosureSubstatus__c, (SELECT Id, CommercialStatus__c, IsStored__c FROM Quotes) FROM ChildOpportunities__r) FROM Opportunity WHERE StageName != :OPPORTUNITY_STAGENAME_CLOSE AND Id IN :expiringOpportunityIdsAgenzialeOrOmnicanale];
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppContainerOrchestrator | oppContainerToCheck ' + oppContainerToCheck);
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppContainerOrchestrator | oppContainerToCheck.size() ' + oppContainerToCheck.size());

        List<Quote> quotesActiveToExpire = new List<Quote>();
        Set<Id> oppProductsActiveToExpire = new Set<Id>();

        for(Opportunity oppContainer : oppContainerToCheck) {
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppContainerOrchestrator | oppContainer ' + oppContainer);
            for(Opportunity oppProduct: oppContainer.ChildOpportunities__r) {
                System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppContainerOrchestrator | oppProduct ' + oppProduct);
                if(oppProduct.StageName != OPPORTUNITY_STAGENAME_CLOSE) {
                    System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppContainerOrchestrator | oppProduct is Open (not closed)');
                    oppProductsActiveToExpire.add(oppProduct.Id);
                }
                for (Quote quote : oppProduct.Quotes){
                    System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppContainerOrchestrator | quote ' + quote);
                    if(quote.CommercialStatus__c == QUOTE_COMMERCIAL_STATUS_ACTIVE){
                        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppContainerOrchestrator | is Quote Active ');
                        quotesActiveToExpire.add(quote);
                    } 
                }
            }
        }

        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppContainerOrchestrator | quotesActiveToExpire ' + quotesActiveToExpire);
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppContainerOrchestrator | quotesActiveToExpire.size() ' + quotesActiveToExpire.size());
        closeQuote(quotesActiveToExpire);

        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppContainerOrchestrator | expiringOpportunityIdsAgenzialeOrOmnicanale ' + expiringOpportunityIdsAgenzialeOrOmnicanale);
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppContainerOrchestrator | expiringOpportunityIdsAgenzialeOrOmnicanale.size() ' + expiringOpportunityIdsAgenzialeOrOmnicanale.size());
        closeOppContainerAndRollupFields(expiringOpportunityIdsAgenzialeOrOmnicanale);

        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppContainerOrchestrator | oppProductsActiveToExpire ' + oppProductsActiveToExpire);
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeExpiringOppContainerOrchestrator | oppProductsActiveToExpire.size() ' + oppProductsActiveToExpire.size());
        closeOppProductAndRollupFields(oppProductsActiveToExpire);
        
    }



    /////////////////////////////////////////////////////////////////////////////////////////////
    ////////////// The following methods inspect for Anticipated Expiration  ////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////




    /********** 
     * @Description             Inspect Opportunity Products to check if they are expired (because all related Quotes are expired) and therefore should be closed. 
     *                          It may happen if all related Quotes are EXPIRED
     * 
     * @input                   Set<Id> of the opportunity Product which need to be inspected
     * @output                  Set<Id> of the opportunity Container which need to be inspected
     */
    public static void inspectOppProductForAnticipatedExpiration(Set<Id> oppProductIdsToInspect) {

        List<Opportunity> oppProductsToCheck = [SELECT Id, Parent__c, (SELECT Id, CommercialStatus__c FROM Quotes) FROM Opportunity WHERE StageName != :OPPORTUNITY_STAGENAME_CLOSE AND Id IN :oppProductIdsToInspect];
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | inspectOppProductForAnticipatedExpiration | oppProductsToCheck.size() ' + oppProductsToCheck.size());
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | inspectOppProductForAnticipatedExpiration | oppProductsToCheck ' + oppProductsToCheck);

        Set<Id> oppContainersToCheck = new Set<Id>();
        Set<Id> oppProductIdsToClose = new Set<Id>();

        for(Opportunity oppProduct : oppProductsToCheck) {
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | inspectOppProductForAnticipatedExpiration | oppProduct ' + oppProduct);
            Integer nbQuoteInactive = 0;
            Integer nbQuoteTotal = 0;
            for(Quote quote: oppProduct.Quotes){
                System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | inspectOppProductForAnticipatedExpiration | quote ' + quote);
                if(quote.CommercialStatus__c != QUOTE_COMMERCIAL_STATUS_ACTIVE){
                    System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | inspectOppProductForAnticipatedExpiration | is non active quote ');
                    nbQuoteInactive++;
                    
                } 
                nbQuoteTotal++;
            }

            // assumption is that if all Quotes are expired, we must close the Opp Product
            Boolean isAllQuotesExpired = false;
            if(nbQuoteTotal == nbQuoteInactive) isAllQuotesExpired = true;
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | inspectOppProductForAnticipatedExpiration | nbQuoteTotal == nbQuoteInactive ' + isAllQuotesExpired);
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | inspectOppProductForAnticipatedExpiration | nbQuoteTotal and nbQuoteInactive (' + nbQuoteTotal + ' and ' + nbQuoteInactive + ')');
            if(isAllQuotesExpired) {
                System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | inspectOppProductForAnticipatedExpiration | nbQuoteInactive == nbQuoteTotal ');
                oppProductIdsToClose.add(oppProduct.Id);
                oppContainersToCheck.add(oppProduct.Parent__c);
            }
        }

        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | inspectOppProductForAnticipatedExpiration | oppProductIdsToClose ' + oppProductIdsToClose);
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | inspectOppProductForAnticipatedExpiration | oppProductIdsToClose.size() ' + oppProductIdsToClose.size());
        closeOppProductAndRollupFields(oppProductIdsToClose);

        // handled in orchestrator
        // System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | inspectOppProductForAnticipatedExpiration | oppContainersToCheck ' + oppContainersToCheck);
        // return oppContainersToCheck;
    }

    /************
     * @description             Inspect Opportunity Container (Omnicanale or Agenziale) to check if theyr are expired (because all related Quote are inactive - either SOLD or EXPIRED)
     * @input                   Set<Id> of the Opp Containers which require an inspection
     */
    public static void inspectOppContainersForAnticipatedExpiration(Set<Id> myContainerOppsToInspect){

        List<Opportunity> oppContainersToCheck = [SELECT Id, (SELECT Id, ClosureSubstatus__c, (SELECT Id, CommercialStatus__c FROM Quotes) FROM ChildOpportunities__r) FROM Opportunity WHERE StageName != :OPPORTUNITY_STAGENAME_CLOSE AND Id IN :myContainerOppsToInspect];
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | inspectOppContainersForAnticipatedExpiration | oppContainersToCheck.size() ' + oppContainersToCheck.size());
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | inspectOppContainersForAnticipatedExpiration | oppContainersToCheck ' + oppContainersToCheck);

        Set<Id> oppContainersToClose = new Set<Id>();

        for(Opportunity oppContainer : oppContainersToCheck) {
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | inspectOppContainersForAnticipatedExpiration | oppContainer ' + oppContainer);
            Integer nbQuoteInactive = 0;
            Integer nbQuoteTotal = 0;
            for(Opportunity oppProduct: oppContainer.ChildOpportunities__r) {
                System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | inspectOppContainersForAnticipatedExpiration | oppProduct ' + oppProduct);
                for(Quote myQuote : oppProduct.Quotes){
                    System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | inspectOppContainersForAnticipatedExpiration | myQuote ' + myQuote);
                    if(myQuote.CommercialStatus__c != QUOTE_COMMERCIAL_STATUS_ACTIVE){
                        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | inspectOppContainersForAnticipatedExpiration | is non active quote');
                        nbQuoteInactive++;

                    }
                    nbQuoteTotal++;
                }
            }
            Boolean isAllQuotesExpired = false;
            if(nbQuoteTotal == nbQuoteInactive) isAllQuotesExpired = true;
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | inspectOppContainersForAnticipatedExpiration | nbQuoteTotal == nbQuoteInactive ' + isAllQuotesExpired);
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | inspectOppContainersForAnticipatedExpiration | nbQuoteTotal and nbQuoteInactive) ' + nbQuoteTotal + ' and ' + nbQuoteInactive);
            if(isAllQuotesExpired) oppContainersToClose.add(oppContainer.Id);
        }

        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | inspectOppContainersForAnticipatedExpiration | oppContainersToCheck -> closure confirmed for : ' + oppContainersToClose);
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | inspectOppContainersForAnticipatedExpiration | oppContainersToCheck.size() -> closure confirmed for : ' + oppContainersToClose.size());
        closeOppContainerAndRollupFields(oppContainersToClose);
    }




    /////////////////////////////////////////////////////////////////////////////////////////////
    ////////////// The following methods handle the closure and rollup //////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////
    



    /**********
     * @description             handles the closing of Quote, to the right status.
     *                          Input is List<Quote> to save some SOQL, but we need to adapt incoming calls to have the proper fields (CommercialStatus__c, isStored__c, ...)
     */
    public static void closeQuote(List<Quote> quotesToClose){
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeQuote');
        if(quotesToClose.isEmpty()) return;

        for(Quote myExpiringQuote : quotesToClose){
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeQuote | myExpiringQuote ' + myExpiringQuote);
            if(myExpiringQuote.CommercialStatus__c == QUOTE_COMMERCIAL_STATUS_ACTIVE){
                myExpiringQuote.CommercialStatus__c = QUOTE_COMMERCIAL_STATUS_EXPIRED;
                System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeQuote | proceeding with changing QUOTE_COMMERCIAL_STATUS from ACTIVE to EXPIRED');
            } 
        }

        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeQuote | quotesToClose.size() ' + quotesToClose.size());
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeQuote | quotesToClose ' + quotesToClose);
        List<Database.SaveResult> myResultQuotes = Database.update(quotesToClose, false);
        for(Database.SaveResult myResult : myResultQuotes){
            if(myResult.isSuccess()){
                System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeQuote | Successfully updated Quote. Quote ID: ' + myResult.Id);
            }
            else{
                System.debug(LoggingLevel.ERROR, 'OpportunityExpirationHelper | closeQuote | Problem while updating Quote. Quote ID: ' + myResult.Id);
                for(Database.Error error : myResult.getErrors()){
                    System.debug(LoggingLevel.ERROR, 'OpportunityExpirationHelper | closeQuote | The following error has occurred.');                    
                    System.debug(LoggingLevel.ERROR, 'OpportunityExpirationHelper | closeQuote | ' + error.getStatusCode() + ': ' + error.getMessage());
                    System.debug(LoggingLevel.ERROR, 'OpportunityExpirationHelper | closeQuote | Opportunity fields that affected this error: ' + error.getFields());
                }
            }
        }
    }

    /**********
     * @description             Handles the closing of Opportunity Product and filling of fields correctly (closeDate, StageName, ...)
     *                          Performs a rollup of information coming from Quote (areaOfNeed__c, amount, ...)
     */
    public static void closeOppProductAndRollupFields(Set<Id> oppProductIdsToClose){
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppProductAndRollupFields');
        if(oppProductIdsToClose.isEmpty()){
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppProductAndRollupFields | no opps - returning');
            return;
        } 

        
        List<Opportunity> oppProductsToClose = [SELECT Id, stageName, closeDate, AreaOfNeed__c, OverallAreasOfNeed__c,  (SELECT Id, CommercialStatus__c, QuoteAmount__c, AreasOfNeed__c FROM Quotes WHERE CommercialStatus__c = :QUOTE_COMMERCIAL_STATUS_SOLD) FROM Opportunity WHERE (StageName != :OPPORTUNITY_STAGENAME_CLOSE AND Id IN :oppProductIdsToClose)]; 
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppProductAndRollupFields | oppProductsToClose.size() ' + oppProductsToClose.size());
        
        for(Opportunity oppProductToClose : oppProductsToClose){
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppProductAndRollupFields | oppProductToClose ' + oppProductToClose);
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppProductAndRollupFields | quotes.size() with status SOLD : ' + oppProductToClose.Quotes.size());
            Boolean minimumOneSOLD = false;

            Decimal sumQuoteAmount = 0;
            Set<String> areaOfNeedRollup = new Set<String>();
            for(Quote myQuote : oppProductToClose.Quotes){
                System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppProductAndRollupFields | myQuote ' + myQuote);
                if(myQuote.CommercialStatus__c == QUOTE_COMMERCIAL_STATUS_SOLD){
                    if(myQuote.QuoteAmount__c != null) sumQuoteAmount += myQuote.QuoteAmount__c;
                    if(myQuote.AreasOfNeed__c != null) areaOfNeedRollup.addall(new Set<String>(myQuote.AreasOfNeed__c.split(';')));
                    minimumOneSOLD = true;
                    System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppProductAndRollupFields |  has QUOTE_COMMERCIAL_STATUS_ACTIVE');
                    System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppProductAndRollupFields |  current sumQuoteAmount ' + sumQuoteAmount);
                    System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppProductAndRollupFields |  current areaOfNeedRollup ' + areaOfNeedRollup);
                    System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppProductAndRollupFields |  current minimumOneSOLD ' + minimumOneSOLD);
                } 
            }

            oppProductToClose.Amount = sumQuoteAmount;
            oppProductToClose.stageName = OPPORTUNITY_STAGENAME_CLOSE;
            oppProductToClose.ClosureSubstatus__c = minimumOneSOLD ? OPPORTUNITY_CLOSURE_SUBSTATUS_POLIZZA_EMESSA : OPPORTUNITY_CLOSURE_SUBSTATUS_FUORI_SLA;
            oppProductToClose.closeDate = date.Today();
            oppProductToClose.ClosingDate__c = datetime.now();
            oppProductToClose.AreaOfNeed__c = String.join(areaOfNeedRollup, ';');
            oppProductToClose.OverallAreasOfNeed__c = String.join(areaOfNeedRollup, ';');
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppProductAndRollupFields | OpportunityExpirationHelper current oppProductToClose ' + oppProductToClose);
        }

        List<Database.SaveResult> myResultOppProductClosing = Database.update(oppProductsToClose, false);
        for(Database.SaveResult myResult : myResultOppProductClosing){
            if(myResult.isSuccess()){
                System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppProductAndRollupFields | Successfully updated Opp Product. Opp ID: ' + myResult.Id);
            }
            else{
                System.debug(LoggingLevel.ERROR, 'OpportunityExpirationHelper | closeOppProductAndRollupFields | Problem while updating Quote. Quote ID: ' + myResult.Id);
                for(Database.Error error : myResult.getErrors()){
                    System.debug(LoggingLevel.ERROR, 'OpportunityExpirationHelper | closeOppProductAndRollupFields | The following error has occurred.');                    
                    System.debug(LoggingLevel.ERROR, 'OpportunityExpirationHelper | closeOppProductAndRollupFields | ' + error.getStatusCode() + ': ' + error.getMessage());
                    System.debug(LoggingLevel.ERROR, 'OpportunityExpirationHelper | closeOppProductAndRollupFields | Opportunity fields that affected this error: ' + error.getFields());
                }
            }
        }
    }
    
    /**********
     * @description             Handles the closing of Opportunity Container and filling of fields correctly (closeDate, StageName, ...)
     *                          Performs a rollup of information coming from Quote (areaOfNeed__c, amount, ...)
     */
    public static void closeOppContainerAndRollupFields(Set<Id> OppContainerIdsToClose){
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppContainerAndRollupFields');
        if(OppContainerIdsToClose.isEmpty()){
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppContainerAndRollupFields | no opps - returning');
            return;
        } 

        System.debug(LoggingLevel.INFO, 'OpportunityExpirationHelper closeOppContainerAndRollupFields');

        List<Opportunity> oppContainersToClose =  [SELECT Id, AreaOfNeed__c, Amount, stageName, closeDate, OverallAreasOfNeed__c,  (SELECT Id, StageName, ClosureSubstatus__c, (SELECT Id, CommercialStatus__c, QuoteAmount__c, AreasOfNeed__c FROM Quotes WHERE CommercialStatus__c = :QUOTE_COMMERCIAL_STATUS_SOLD) FROM ChildOpportunities__r) FROM Opportunity WHERE StageName != :OPPORTUNITY_STAGENAME_CLOSE AND Id IN :OppContainerIdsToClose]; 
        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppContainerAndRollupFields | oppContainersToClose.size() ' + oppContainersToClose.size());


        for(Opportunity oppContainerToClose : oppContainersToClose){
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppContainerAndRollupFields | oppContainerToClose ' + oppContainerToClose);
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppContainerAndRollupFields | ChildOpportunities__r.size() ' + oppContainerToClose.ChildOpportunities__r.size());
            Decimal sumQuoteAmount = 0;
            Set<String> areaOfNeedRollup = new Set<String>();
            Boolean minimumOneSOLD = false;

            for(Opportunity oppProduct : oppContainerToClose.ChildOpportunities__r){
                System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppContainerAndRollupFields |  oppProduct ' +oppProduct.Id);
                System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppContainerAndRollupFields |  Quotes.size() with status SOLD' +oppProduct.Quotes.size());

                for(Quote myQuote : oppProduct.Quotes){
                    System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppContainerAndRollupFields |  myQuote ' +myQuote.Id);

                    if(myQuote.CommercialStatus__c == QUOTE_COMMERCIAL_STATUS_SOLD){
                        if(myQuote.QuoteAmount__c != null) sumQuoteAmount += myQuote.QuoteAmount__c;
                        if(myQuote.AreasOfNeed__c != null) areaOfNeedRollup.addall(new Set<String>(myQuote.AreasOfNeed__c.split(';')));
                        minimumOneSOLD = true;
                        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppContainerAndRollupFields |  has QUOTE_COMMERCIAL_STATUS_ACTIVE');
                        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppContainerAndRollupFields |  current sumQuoteAmount ' + sumQuoteAmount);
                        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppContainerAndRollupFields |  current areaOfNeedRollup ' + areaOfNeedRollup);
                        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppContainerAndRollupFields |  current minimumOneSOLD ' + minimumOneSOLD);

                    } 
                }
            }
            oppContainerToClose.Amount = sumQuoteAmount;
            oppContainerToClose.stageName = OPPORTUNITY_STAGENAME_CLOSE;
            oppContainerToClose.ClosureSubstatus__c = minimumOneSOLD ? OPPORTUNITY_CLOSURE_SUBSTATUS_POLIZZA_EMESSA : OPPORTUNITY_CLOSURE_SUBSTATUS_FUORI_SLA;
            oppContainerToClose.closeDate = date.Today();
            oppContainerToClose.ClosingDate__c = datetime.now();
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppContainerAndRollupFields |  areaOfNeedRollup ' + areaOfNeedRollup);
            oppContainerToClose.AreaOfNeed__c = String.join(areaOfNeedRollup, ';');
            oppContainerToClose.OverallAreasOfNeed__c = String.join(areaOfNeedRollup, ';');
            System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppContainerAndRollupFields |  oppContainerToClose ' + oppContainerToClose);
        }

        System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppContainerAndRollupFields |  oppContainersToClose.size() ' + oppContainersToClose.size());
        List<Database.SaveResult> myResultOppContainerClosing = Database.update(oppContainersToClose, false);
        for(Database.SaveResult myResult : myResultOppContainerClosing){
            if(myResult.isSuccess()){
                System.debug(LoggingLevel.DEBUG, 'OpportunityExpirationHelper | closeOppContainerAndRollupFields | Successfully updated Opp Container. Opp ID: ' + myResult.Id);
            }
            else{
                System.debug(LoggingLevel.WARN, 'OpportunityExpirationHelper | closeOppContainerAndRollupFields | Problem while updating Opp Container. Opp ID: ' + myResult.Id);
                for(Database.Error error : myResult.getErrors()){
                    System.debug(LoggingLevel.WARN, 'OpportunityExpirationHelper | closeOppContainerAndRollupFields | The following error has occurred.');                    
                    System.debug(LoggingLevel.WARN, 'OpportunityExpirationHelper | closeOppContainerAndRollupFields | ' + error.getStatusCode() + ': ' + error.getMessage());
                    System.debug(LoggingLevel.WARN, 'OpportunityExpirationHelper | closeOppContainerAndRollupFields | Opportunity fields that affected this error: ' + error.getFields());
                }
                // add log error
            }
        }
    }

    /**************
     * @description             In some cases we need to recalculate some fields on OppContainer (areaOfNeed__c, Amount, ...) while it is still open (logic is different than when it is closed) . This logic to handle the rollup logic. It should be called in orchestrator when applicable.
     *                          It should be called on Opp whenever there is a Quote expiration/closure with (isStored__c = true) TBC
     * 
     * @input                   Set<Id> of OppContainer where recalculation of rollup is required. Those OppContainers should be Open.
     */
    // public static void recalculateOppContainerRollupWhenStillOpen(Set<Id> oppContainerIdsToRecalculate){

    //     List<Opportunity> oppContainersToRecalculate =  [SELECT Id, AreaOfNeed__c, Amount, stageName, closeDate, OverallAreasOfNeed__c,  (SELECT Id, StageName, ClosureSubstatus__c, (SELECT Id, CommercialStatus__c, QuoteAmount__c, AreasOfNeed__c, IsStored__c FROM Quotes WHERE IsStored__c = false) FROM ChildOpportunities__r WHERE stageName != :OPPORTUNITY_STAGENAME_CLOSE) FROM Opportunity WHERE StageName != :OPPORTUNITY_STAGENAME_CLOSE AND Id IN :oppContainerIdsToRecalculate]; 

    //     for(Opportunity oppContainer : oppContainersToRecalculate){
    //         Decimal sumQuoteAmount = 0;
    //         Set<String> areaOfNeedRollup = new Set<String>();
    //         for(Opportunity oppProduct : oppContainer.ChildOpportunities__r){
    //             for(Quote myQuote : oppProduct.Quotes){
    //                 sumQuoteAmount += myQuote.QuoteAmount__c;
    //                 areaOfNeedRollup.addall(new Set<String>(myQuote.AreasOfNeed__c.split(';')));
    //             }
    //         }

    //         oppContainer.Amount = sumQuoteAmount;
    //         oppContainer.AreaOfNeed__c = String.join(areaOfNeedRollup, ';');
    //         oppContainer.OverallAreasOfNeed__c = String.join(areaOfNeedRollup, ';');
    //     }

    //     List<Database.SaveResult> myResultOppContainerUpdating = Database.update(oppContainersToRecalculate, false);
    //     for(Database.SaveResult myResult : myResultOppContainerUpdating){
    //         if(myResult.isSuccess()){
    //             System.debug(LoggingLevel.DEBUG, 'Successfully updated Opp Container. Opp ID: ' + myResult.Id);
    //         }
    //         else{
    //             System.debug(LoggingLevel.WARN, 'Problem while updating Opp Container. Opp ID: ' + myResult.Id);
    //             // add log error
    //         }
    //     }
    // }
}
