/**
 * @description Classe utility per la creazione di regole di sharing su oggetti Case, Opportunity, FinServ__AccountAccountRelation__c
 *              e AccountDetailsNPI__c associati ad un Account, tramite invocazione da Flow.
 *              Il metodo principale permette di condividere i record con un determinato gruppo.
 * <AUTHOR>
 * @since       23.04.2025
 */
public without sharing class InsurancePolicyCreateSharingRule {
    /**
     * @description     Metodo invocabile da Flow per creare le regole di sharing su Case, Opportunity, AccountAccountRelation e AccountDetailsNPI
     *                  relativi all'Account specificato.
     * @param flowInput Lista di input contenente accountId, groupId, agencyId e societyId.
     * @return          `List<Results>` -> Lista di risultati con esito dell'operazione e messaggio di errore (se presente).
     */
    @InvocableMethod
    public static List<Results> createSharingRule(List<FlowInputs> flowInput) {
        try {
            if (flowInput.isEmpty()) {
                return InsurancePolicyCreateSharingRuleHelper.buildErrorResults('Share non create: nessun input fornito', flowInput, null);
            }
            Set<String> setIdAccount = new Set<String>();
            Set<String> setIdAgency = new Set<String>();
            Set<String> setIdSociety = new Set<String>();
            for (FlowInputs input : flowInput) {
                setIdAccount.add(input.accountId);
                setIdAgency.add(input.agencyId);
                setIdSociety.add(input.societyId);
            }
            Map<Id, List<Case>> listCase = InsurancePolicyCreateSharingRuleHelper.getCases(setIdAccount, setIdAgency);
            Map<Id, List<Opportunity>> listOpportunity = InsurancePolicyCreateSharingRuleHelper.getOpportunities(setIdAccount, setIdAgency);
            Map<Id, List<FinServ__AccountAccountRelation__c>> listAccAccRel = InsurancePolicyCreateSharingRuleHelper.getAARs(setIdAccount, setIdAgency, setIdSociety);

            WrapperShare wrp = new WrapperShare();
            wrp.flowInput = flowInput;
            wrp.listCase = listCase;
            wrp.listOpportunity = listOpportunity;
            wrp.listAccAccRel = listAccAccRel;

            List<SObject> listRecordShareToInsert = InsurancePolicyCreateSharingRuleHelper.buildAllShares(wrp);
            InsurancePolicyCreateSharingRuleHelper.insertRecordShare(listRecordShareToInsert);
            return InsurancePolicyCreateSharingRuleHelper.buildSuccessResults(flowInput);
        } catch (Exception ex) {
            return InsurancePolicyCreateSharingRuleHelper.buildErrorResults(ex.getMessage(), flowInput, ex.getStackTraceString());
        }
    }

    /**
     * @description
     * Classe di input per il metodo invocabile, contiene accountId, groupId, agencyId e societyId.
     */
    public class FlowInputs {
        @InvocableVariable
        public String accountId;
        @InvocableVariable
        public String groupId;
        @InvocableVariable
        public String agencyId;
        @InvocableVariable
        public String societyId;
    }

    /**
     * @description
     * Classe di output per il metodo invocabile, contiene esito, messaggio e stack trace.
     */
    public class Results {
        @InvocableVariable
        public String esito;
        @InvocableVariable
        public String message;
        @InvocableVariable
        public String stackTraceString;
    }

    /**
     * @description
     * Wrapper per raggruppare gli input e le liste di record da condividere.
     */
    public class WrapperShare {
        public List<FlowInputs> flowInput;
        public Map<Id, List<Case>> listCase;
        public Map<Id, List<Opportunity>> listOpportunity;
        public Map<Id, List<FinServ__AccountAccountRelation__c>> listAccAccRel;
    }
}
