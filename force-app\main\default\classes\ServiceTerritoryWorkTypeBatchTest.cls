@isTest
private class ServiceTerritoryWorkTypeBatchTest {
    
    @testSetup
    static void setupTestData() {
        
        OperatingHours op = new OperatingHours(Name = 'TestOp');
        
        insert op;
        
        
        List<ServiceTerritory> serviceTerritories = new List<ServiceTerritory>();
        for (Integer i = 0; i < 5; i++) {
            serviceTerritories.add(new ServiceTerritory(Name = 'ServiceTerritory' + i, OperatingHoursId= op.Id));
        }
        insert serviceTerritories;

        
        List<WorkType> workTypes = new List<WorkType>();
        for (Integer i = 0; i < 3; i++) {
            workTypes.add(new WorkType(Name = 'WorkType' + i, EstimatedDuration = 60));
        }
        insert workTypes;

        
        List<ServiceTerritoryWorkType> existingJunctions = new List<ServiceTerritoryWorkType>();
        existingJunctions.add(new ServiceTerritoryWorkType(
            ServiceTerritoryId = serviceTerritories[0].Id,
            WorkTypeId = workTypes[0].Id
        ));
        insert existingJunctions;
    }

    @isTest
    static void testBatchExecution() {
        
        Test.startTest();

        ServiceTerritoryWorkTypeBatch batch = new ServiceTerritoryWorkTypeBatch('2025-06-05');
        Database.executeBatch(batch, 200);

        Test.stopTest();
    }
}