@isTest
public with sharing class OpportunityButtonWrapper_Test extends ButtonWrapperAbstract  {
	@isTest
    static void testCreateGenericChecksMap() {
        // Instantiate the Checks class
        OpportunityButtonWrapper.Checks checks = new OpportunityButtonWrapper.Checks();

        // Set the properties
        checks.clientType = new StringCheck();
        checks.slaRemainingHours = new NumberCheck();
        checks.stage = new StringCheck();
        checks.substatus = new StringCheck();
        checks.omnichannelAgreement = null;

        // Call the method to test
        checks.createGenericChecksMap();

        // Perform assertions
        System.assertNotEquals(null, checks.genericChecksMap.get('clientType'), 'clientType should not be null');
        System.assertNotEquals(null, checks.genericChecksMap.get('slaRemainingHours'), 'slaRemainingHours should not be null');
        System.assertNotEquals(null, checks.genericChecksMap.get('stage'), 'stage should not be null');
        System.assertNotEquals(null, checks.genericChecksMap.get('substatus'), 'substatus should not be null');
        System.assertEquals(null, checks.genericChecksMap.get('omnichannelAgreement'), 'omnichannelAgreement should be null because it was not initialized');
        
        // Check that the key 'omnichannelAgreement' has been removed
        System.assertEquals(false, checks.genericChecksMap.containsKey('omnichannelAgreement'), 'The key omnichannelAgreement should have been removed from the map');
    }
    
}
