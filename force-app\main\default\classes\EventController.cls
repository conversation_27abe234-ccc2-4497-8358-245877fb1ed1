public without sharing class EventController {
    public EventController() {}

    //Fetch the event record based on the eventId
    @AuraEnabled
    public static Map<String, Object> getEventRecord(Id eventId) {
        // Create a map of field values and error management
        Map<String, Object> returnValues = new Map<String, Object>();
        returnValues.put('error', false);
        try {
            Event evt = getEventByIdQry(eventId);

            List<EventRelation> relations = getEventRelationsByEventIdQry(eventId);
            /*
            Set<Id> userIdSet = new Set<Id>();
            for (EventRelation er : relations) {
                userIdSet.add(er.RelationId);
            }
            List<User> userList = getUsersInId(userIdSet);
            */
            //List<Contact> contactList = getContactsInId(contactIdSet);

            returnValues.put('eventRecord', evt);
            returnValues.put('attendeesList', relations);
            
            return returnValues;
        } catch(Exception e) {
            returnValues.put('error', true);
            returnValues.put('errorMessage', e.getMessage());
            returnValues.put('errorStackTraceString', e.getStackTraceString());
        }
        return returnValues;
    }

    //update the event record based on the eventId
    @AuraEnabled
    public static Map<String, Object> updateEvent(String eventId, Map<String, Object> fields, List<String> attendeeList) {
        Map<String, Object> returnValues = new Map<String, Object>();
        returnValues.put('error', false);
        try {
            // Update event details
            Event updatedEvent = updateEventDetails(eventId, fields);
            
            // Handle attendees if list is provided
            if (attendeeList != null) {
                attendeeList.add(updatedEvent.OwnerId);
                updateEventAttendees(eventId, attendeeList);
            }

            returnValues.put('success', true);
        } catch(Exception e) {
            returnValues.put('error', true);
            returnValues.put('errorMessage', e.getMessage());
            returnValues.put('errorStackTraceString', e.getStackTraceString());
        }
        return returnValues;
    }

    private static Event updateEventDetails(String eventId, Map<String, Object> fields) {
        Event evt = getEventByIdQry(eventId);
        
        // Basic fields
        evt.Subject = (String) fields.get('Subject');
        evt.Description = (String) fields.get('Description');
        evt.Location = (String) fields.get('Location');
        evt.IsAllDayEvent = Boolean.valueOf(fields.get('IsAllDayEvent'));
        
        // Custom fields
        evt.Telefono_Cliente__c = (String) fields.get('Telefono_Cliente__c');
        evt.Mail__c = Boolean.valueOf(fields.get('Mail__c'));
        evt.Modalit_di_esecuzione_incontro__c = (String) fields.get('Modalit_di_esecuzione_incontro__c');
        
        // Handle lookup fields
        evt.OwnerId = String.isBlank((String) fields.get('OwnerId')) ? null : (String) fields.get('OwnerId');
        evt.Service_Territory__c = String.isBlank((String) fields.get('Service_Territory__c')) ? null : (String) fields.get('Service_Territory__c');
        evt.Trattativa__c = String.isBlank((String) fields.get('Trattativa__c')) ? null : (String) fields.get('Trattativa__c');
        evt.Case__c = String.isBlank((String) fields.get('Case__c')) ? null : (String) fields.get('Case__c');
        evt.FinServ__Household__c = String.isBlank((String) fields.get('FinServ__Household__c')) ? null : (String) fields.get('FinServ__Household__c');

        // Event settings
        evt.IsPrivate = Boolean.valueOf(fields.get('IsPrivate'));
        evt.IsReminderSet = Boolean.valueOf(fields.get('IsReminderSet'));
        evt.ShowAs = (String) fields.get('ShowAs');

        // Handle dates
        Datetime startDate = (Datetime)JSON.deserialize('"' + (String)fields.get('StartDateTime') + '"', Datetime.class);
        Datetime endDate = (Datetime)JSON.deserialize('"' + (String)fields.get('EndDateTime') + '"', Datetime.class);
        Datetime reminderDate = (Datetime)JSON.deserialize('"' + (String)fields.get('ReminderDateTime') + '"', Datetime.class); // Add for fix defect 1338321
        
        // Handle calculate field
        String reminderMinutesBeforeStart =  String.isBlank((String) fields.get('ReminderMinutesBeforeStart')) ? null : (String) fields.get('ReminderMinutesBeforeStart');
        
        if (evt.IsReminderSet && reminderMinutesBeforeStart != '0') {
            Integer minutesBeforeStart = Integer.valueOf(reminderMinutesBeforeStart);
        	reminderDate =  startDate.addMinutes(- minutesBeforeStart); 
            evt.ReminderDateTime = reminderDate;
		}
        if (evt.ServiceAppointmentId != null) {

            ServiceAppointment sa = new ServiceAppointment(
                Id = evt.ServiceAppointmentId,
                SchedStartTime = startDate,
                SchedEndTime = endDate,
                ArrivalWindowStartTime = startDate,
                ArrivalWindowEndTime = endDate,
                EarliestStartTime = startDate,
                DueDate = endDate
            );
            update sa;

        } else {
            
            evt.StartDateTime = startDate;
            evt.EndDateTime = endDate;
        }
        
        update evt;
        return evt;
    }

    private static void updateEventAttendees(Id eventId, List<String> attendeeList) {
        // Delete relations not in new attendee list
        List<EventRelation> relationsToDelete = [
            SELECT Id 
            FROM EventRelation 
            WHERE EventId = :eventId
            AND RelationId NOT IN :attendeeList
            AND IsInvitee = true
        ];
        
        if (!relationsToDelete.isEmpty()) {
            delete relationsToDelete;
        }

        // Get existing relations to avoid duplicates
        Set<Id> existingAttendees = new Set<Id>();
        for (EventRelation er : getEventRelationsByEventIdQry(eventId)) {
            existingAttendees.add(er.RelationId);
        }
        
        // Create new relations only for new attendees
        List<EventRelation> newRelations = new List<EventRelation>();
        Id eventOwnerId = [SELECT OwnerId FROM Event WHERE Id = :eventId].OwnerId;
        
        for (String attendeeId : attendeeList) {
            if (!existingAttendees.contains(attendeeId) && attendeeId != eventOwnerId) {
                newRelations.add(new EventRelation(
                    EventId = eventId,
                    RelationId = attendeeId,
                    IsInvitee = true
                ));
            }
        }
        
        if (!newRelations.isEmpty()) {
            insert newRelations;
        }
    }

    //fetch the picklist values for the requested field
    @AuraEnabled(cacheable=true)
    public static List<Map<String, String>> getPicklistValues(String objectName, String fieldName) {
        List<Map<String, String>> options = new List<Map<String, String>>();
        
        Schema.SObjectType objectType = Schema.getGlobalDescribe().get(objectName);
        Schema.DescribeSObjectResult objDescribe = objectType.getDescribe();
        Schema.DescribeFieldResult fieldDescribe = objDescribe.fields.getMap().get(fieldName).getDescribe();
        
        List<Schema.PicklistEntry> picklistValues = fieldDescribe.getPicklistValues();
        
        for(Schema.PicklistEntry entry : picklistValues) {
            if(entry.isActive()) {
                options.add(new Map<String, String>{
                    'label' => entry.getLabel(),
                    'value' => entry.getValue()
                });
            }
        }
        return options;
    }

    @AuraEnabled
    public static String getUserName(Id userId) {
        try {
            return [SELECT Name FROM User WHERE Id = :userId LIMIT 1].Name;
        } catch (Exception e) {
            throw new AuraHandledException('Unable to fetch user name: ' + e.getMessage());
        }
    }

    @AuraEnabled
    public static String getContactName(Id contactId) {
        try {
            return [SELECT Name FROM Contact WHERE Id = :contactId LIMIT 1].Name;
        } catch (Exception e) {
            throw new AuraHandledException('Unable to fetch contact name: ' + e.getMessage());
        }
    }

    public static List<User> getUsersInId(Set<Id> userIdSet) {
        return [SELECT Id, Name FROM User WHERE Id IN :userIdSet];
    }

    public static List<Contact> getContactsInId(Set<Id> contactIdSet) {
        return [SELECT Id, Name FROM Contact WHERE Id IN :contactIdSet];
    }

    public static Event getEventByIdQry(Id eventId) {
        return [
            SELECT Id, 
                   OwnerId,
                   Owner.Name,
                   Subject,
                   Description,
                   StartDateTime,
                   EndDateTime,
                   IsAllDayEvent,
                   Location,
                   ApptBookingInfoUrl__c,
                   Telefono_Cliente__c,
                   IsPrivate,
                   IsReminderSet,
            	   ReminderDateTime, // Add for fix defect 1338321
                   Mail__c,
                   Modalit_di_esecuzione_incontro__c,
                   ShowAs,
                   FinServ__Household__c,
                   FinServ__Household__r.Name,
                   Service_Territory__c,
                   Trattativa__c,
                   Case__c,
                   ServiceAppointmentId,
                   Who.Name,
                   What.Name
            FROM Event 
            WHERE Id = :eventId 
            LIMIT 1
        ];
    }

    public static List<EventRelation> getEventRelationsByEventIdQry(Id eventId) {
        return [
            SELECT Id, RelationId, Relation.Name 
            FROM EventRelation 
            WHERE EventId = :eventId
            AND IsInvitee = true
        ];
    }
    
}