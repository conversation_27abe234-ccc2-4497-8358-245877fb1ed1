public without sharing class CtrlDemoExternal {

    public String fiscalCode                {get; set;}

    public CtrlDemoExternal() {

        this.fiscalCode = System.currentPageReference().getParameters().get('cf');

    }

    public PageReference performRedirect(){

        List<Account> acc = [SELECT Id FROM Account WHERE CF__c = :this.fiscalCode LIMIT 1];

        if(acc != null && !acc.isEmpty()){

            PageReference pg = new ApexPages.StandardController(acc[0]).view();
            pg.setRedirect(true);
            return pg;

        }

        return null;
        

    }
}