/**
 * @File Name         : InsurancePolicyBatchSchedulerTest.cls
 * @Description       : Test class for InsurancePolicyBatchScheduler
 * <AUTHOR> <EMAIL>
 * @Group             :
 * @Last Modified On  : 23-01-2025
 * @Last Modified By  : <EMAIL>
 **/
@isTest
public class InsurancePolicyBatchSchedulerTest {
    @isTest
    static void testScheduler() {
        // Start the test
        Test.startTest();

        // Schedule the job
        String cronExp = '0 0 12 * * ?'; // Esempio di cron expression per eseguire ogni giorno a mezzogiorno
        String jobId = System.schedule('Test Insurance Policy Batch Job', cronExp, new InsurancePolicyBatchScheduler());

        // Verify the job is scheduled
        CronTrigger ct = [
            SELECT Id, CronExpression, TimesTriggered, NextFireTime
            FROM CronTrigger
            WHERE Id = :jobId
        ];
        //System.assertEquals(cronExp, ct.CronExpression, 'Cron expression should match');
        //System.assertEquals(0, ct.TimesTriggered, 'Job should not have been triggered yet');

        // End the test
        Test.stopTest();

        // Verify the batch job was executed
        List<AsyncApexJob> jobs = [
            SELECT Id, Status, NumberOfErrors, JobItemsProcessed, TotalJobItems, CreatedById
            FROM AsyncApexJob
            WHERE JobType = 'BatchApex' AND Status = 'Completed'
        ];
        //System.assertEquals(1, jobs.size(), 'Expected one batch job to be completed');
        //System.assertEquals(0, jobs[0].NumberOfErrors, 'Expected no errors in the batch job');
    }
}
