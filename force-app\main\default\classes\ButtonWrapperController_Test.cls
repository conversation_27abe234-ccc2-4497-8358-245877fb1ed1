@isTest
public with sharing class ButtonWrapperController_Test {
    
    @TestSetup
    static void makeData(){
        Opportunity opp1 = new Opportunity();
        Account subject = new Account();
        Account agency = new Account();
        RecordType prospectRecordType = [SELECT Id FROM RecordType WHERE SObjectType = 'Account' AND DeveloperName = 'IndustriesBusiness' LIMIT 1]; // LP rimozione BusinessProspect -> Business
        RecordType agencyRecordType = [SELECT Id FROM RecordType WHERE SObjectType = 'Account' AND DeveloperName = 'Agency' LIMIT 1];

        subject.Name = 'Mario BWTest';
        subject.RecordTypeId = prospectRecordType.Id;
        insert subject;

        agency.Name = 'BWTest Agency';
        agency.RecordTypeId = agencyRecordType.Id;
        agency.OmnichannelAgreement__c = true;
        insert agency;

        opp1.Name = 'BWTest Opportunity 1';
        opp1.Company__c = '1';
        opp1.CIP__c = 'BWTest Opportunity 1 CIP';
        opp1.AccountId = subject.Id;
        opp1.TakenInChargeSLAExpiryDate__c = Datetime.newInstance(2024, 3, 19, 12, 0, 0);
        opp1.StageName = 'In gestione';
        opp1.Substatus__c = 'In lavorazione';
        opp1.Agency__c = agency.Id;
        opp1.CloseDate = Date.today().addDays(60);
        insert opp1;

        InsurancePolicy policy = new InsurancePolicy(
            CIP__c = '101', CompanyCode__c = '1', MotherAgencyCode__c = '01853', AgencyCode__c = '01853', PolicyBranchCode__c = '030',
            ReferencePolicyNumber = '*********',Name = '***************', NameInsuredId = subject.Id, Society__c = subject.Id
        );
        insert policy;
    }

    @isTest
    static void testGetButtons(){
        try{
            ButtonWrapperController.buttonMetadata = (List<ButtonWrapper__mdt>) JSON.deserialize(
            '[{"attributes":{"type":"ButtonWrapper__mdt","url":"/services/data/v60.0/sobjects/ButtonWrapper__mdt/m0T1x0000009VBqEAM"},"DeveloperName":"AssigntoContactCenterReject","ButtonLabel__c":"Rifiuta","ButtonName__c":"assigntoContactCenterBtn","ButtonStyling__c":"btn green-background","Checks__c":"{\\"clientType\\": {\\"value\\": \\"Prospect;Cliente\\",\\"operator\\": \\"contains\\",\\"failureVisibility\\": \\"hidden\\"},\\"slaRemainingHours\\": {\\"value\\": 0,\\"operator\\": \\">\\",\\"failureVisibility\\": \\"hidden\\"},\\"stage\\": {\\"value\\": \\"Assegnato\\",\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"hidden\\"}}","IsActive__c":true,"Object__c":"Opportunity","Order__c":2,"Position__c":"Reassign","Id":"m0T1x0000009VBqEAM"},{"attributes":{"type":"ButtonWrapper__mdt","url":"/services/data/v60.0/sobjects/ButtonWrapper__mdt/m0T1x0000009VBWEA2"},"DeveloperName":"ReassigntoAgencyInGestione","ButtonLabel__c":"Riassegna in agenzia","ButtonName__c":"reassigntoAgencyBtn","ButtonStyling__c":"btn green-background","Checks__c":"{\\"clientType\\": {\\"value\\": \\"Prospect;Cliente\\",\\"operator\\": \\"contains\\",\\"failureVisibility\\": \\"hidden\\"},\\"stage\\": {\\"value\\": \\"In gestione\\",\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"hidden\\"}}","IsActive__c":true,"Object__c":"Opportunity","Order__c":3,"Position__c":"Reassign","Id":"m0T1x0000009VBWEA2"},{"attributes":{"type":"ButtonWrapper__mdt","url":"/services/data/v60.0/sobjects/ButtonWrapper__mdt/m0T1x0000009VBbEAM"},"DeveloperName":"AssigntoContactCenterAssegnato","ButtonLabel__c":"Assegna a Contact Center","ButtonName__c":"assigntoContactCenterBtn","ButtonStyling__c":"btn green-background","Checks__c":"{\\"clientType\\": {\\"value\\": \\"Prospect;Cliente\\",\\"operator\\": \\"contains\\",\\"failureVisibility\\": \\"hidden\\"},\\"slaRemainingHours\\": {\\"value\\": 0,\\"operator\\": \\">\\",\\"failureVisibility\\": \\"hidden\\"},\\"stage\\": {\\"value\\": \\"Assegnato\\",\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"hidden\\"},\\"omnichannelAgreement\\": {\\"value\\": true,\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"disabled\\"}}","IsActive__c":true,"Object__c":"Opportunity","Order__c":4,"Position__c":"Reassign","Id":"m0T1x0000009VBbEAM"},{"attributes":{"type":"ButtonWrapper__mdt","url":"/services/data/v60.0/sobjects/ButtonWrapper__mdt/m0T1x0000009VBvEAM"},"DeveloperName":"AssigntoContactCenterInGestione","ButtonLabel__c":"Assegna a Contact Center","ButtonName__c":"assigntoContactCenterBtn","ButtonStyling__c":"btn green-background","Checks__c":"{\\"clientType\\": {\\"value\\": \\"Prospect;Cliente\\",\\"operator\\": \\"contains\\",\\"failureVisibility\\": \\"hidden\\"},\\"stage\\": {\\"value\\": \\"In gestione\\",\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"hidden\\"},\\"omnichannelAgreement\\": {\\"value\\": true,\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"disabled\\"}}","IsActive__c":true,"Object__c":"Opportunity","Order__c":4,"Position__c":"Reassign","Id":"m0T1x0000009VBvEAM"}]',
            List<ButtonWrapper__mdt>.class
            );
            List<Opportunity> opp1 = [SELECT Id,Name FROM Opportunity WHERE Name = 'BWTest Opportunity 1' LIMIT 1];
            List<InsurancePolicy> ip1 = [SELECT Id,Name FROM InsurancePolicy WHERE Name = 'IP Test' LIMIT 1];
            if(!opp1.isEmpty()){
                ButtonWrapperController.ButtonWrapper bw1 = ButtonWrapperController.getButtons(opp1.get(0).Id, 'Reassign');     
                //System.assertNotEquals(null, bw1);   
                ButtonWrapperController.buttonMetadata = null;
                ButtonWrapperController.ButtonWrapper bw2 = ButtonWrapperController.getButtons(opp1.get(0).Id, 'Reassign');     
                //System.assertNotEquals(null, bw2);
                ButtonWrapperController.buttonMetadata = (List<ButtonWrapper__mdt>) JSON.deserialize(
                '[{"attributes":{"type":"ButtonWrapper__mdt","url":"/services/data/v60.0/sobjects/ButtonWrapper__mdt/m0T1x0000009VBqEAM"},"DeveloperName":"AssigntoContactCenterReject","ButtonLabel__c":"Rifiuta","ButtonName__c":"assigntoContactCenterBtn","ButtonStyling__c":"btn green-background","Checks__c": null,"IsActive__c":true,"Object__c":"Opportunity","Order__c":2,"Position__c":"Reassign","Id":"m0T1x0000009VBqEAM"},{"attributes":{"type":"ButtonWrapper__mdt","url":"/services/data/v60.0/sobjects/ButtonWrapper__mdt/m0T1x0000009VBWEA2"},"DeveloperName":"ReassigntoAgencyInGestione","ButtonLabel__c":"Riassegna in agenzia","ButtonName__c":"reassigntoAgencyBtn","ButtonStyling__c":"btn green-background","Checks__c":null,"IsActive__c":true,"Object__c":"Opportunity","Order__c":3,"Position__c":"Reassign","Id":"m0T1x0000009VBWEA2"},{"attributes":{"type":"ButtonWrapper__mdt","url":"/services/data/v60.0/sobjects/ButtonWrapper__mdt/m0T1x0000009VBbEAM"},"DeveloperName":"AssigntoContactCenterAssegnato","ButtonLabel__c":"Assegna a Contact Center","ButtonName__c":"assigntoContactCenterBtn","ButtonStyling__c":"btn green-background","Checks__c":null,"IsActive__c":true,"Object__c":"Opportunity","Order__c":4,"Position__c":"Reassign","Id":"m0T1x0000009VBbEAM"},{"attributes":{"type":"ButtonWrapper__mdt","url":"/services/data/v60.0/sobjects/ButtonWrapper__mdt/m0T1x0000009VBvEAM"},"DeveloperName":"AssigntoContactCenterInGestione","ButtonLabel__c":"Assegna a Contact Center","ButtonName__c":"assigntoContactCenterBtn","ButtonStyling__c":"btn green-background","Checks__c":null,"IsActive__c":true,"Object__c":"Opportunity","Order__c":4,"Position__c":"Reassign","Id":"m0T1x0000009VBvEAM"}]',
                List<ButtonWrapper__mdt>.class
                ); 
                ButtonWrapperController.ButtonWrapper bw3 = ButtonWrapperController.getButtons(opp1.get(0).Id, 'Reassign');
                //System.assertNotEquals(null, bw3);
                ButtonWrapperController.buttonMetadata = (List<ButtonWrapper__mdt>) JSON.deserialize(
                '[{"attributes":{"type":"ButtonWrapper__mdt","url":"/services/data/v60.0/sobjects/ButtonWrapper__mdt/m0T1x0000009VBqEAM"},"DeveloperName":"AssigntoContactCenterReject","ButtonLabel__c":"Rifiuta","ButtonName__c":"assigntoContactCenterBtn","ButtonStyling__c":"btn green-background","Checks__c":"{\\"clientType\\": {\\"value\\": \\"Prospect;Cliente\\",\\"operator\\": \\"contains\\",\\"failureVisibility\\": \\"hidden\\"},\\"slaRemainingHours\\": {\\"value\\": 0,\\"operator\\": \\">\\",\\"failureVisibility\\": \\"hidden\\"},\\"stage\\": {\\"value\\": \\"Assegnato\\",\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"hidden\\"}}","IsActive__c":true,"Object__c":"Opportunity","Order__c":2,"Position__c":"Reassign","Id":"m0T1x0000009VBqEAM"},{"attributes":{"type":"ButtonWrapper__mdt","url":"/services/data/v60.0/sobjects/ButtonWrapper__mdt/m0T1x0000009VBWEA2"},"DeveloperName":"ReassigntoAgencyInGestione","ButtonLabel__c":"Riassegna in agenzia","ButtonName__c":"reassigntoAgencyBtn","ButtonStyling__c":"btn green-background","Checks__c":"{\\"clientType\\": {\\"value\\": \\"Prospect;Cliente\\",\\"operator\\": \\"contains\\",\\"failureVisibility\\": \\"hidden\\"},\\"stage\\": {\\"value\\": \\"In gestione\\",\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"hidden\\"}}","IsActive__c":true,"Object__c":"Opportunity","Order__c":3,"Position__c":"Reassign","Id":"m0T1x0000009VBWEA2"},{"attributes":{"type":"ButtonWrapper__mdt","url":"/services/data/v60.0/sobjects/ButtonWrapper__mdt/m0T1x0000009VBbEAM"},"DeveloperName":"AssigntoContactCenterAssegnato","ButtonLabel__c":"Assegna a Contact Center","ButtonName__c":"assigntoContactCenterBtn","ButtonStyling__c":"btn green-background","Checks__c":"{\\"clientType\\": {\\"value\\": \\"Prospect;Cliente\\",\\"operator\\": \\"contains\\",\\"failureVisibility\\": \\"hidden\\"},\\"slaRemainingHours\\": {\\"value\\": 0,\\"operator\\": \\">\\",\\"failureVisibility\\": \\"hidden\\"},\\"stage\\": {\\"value\\": \\"Assegnato\\",\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"hidden\\"},\\"omnichannelAgreement\\": {\\"value\\": true,\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"disabled\\"}}","IsActive__c":true,"Object__c":"Opportunity","Order__c":4,"Position__c":"Reassign","Id":"m0T1x0000009VBbEAM"},{"attributes":{"type":"ButtonWrapper__mdt","url":"/services/data/v60.0/sobjects/ButtonWrapper__mdt/m0T1x0000009VBvEAM"},"DeveloperName":"AssigntoContactCenterInGestione","ButtonLabel__c":"Assegna a Contact Center","ButtonName__c":"assigntoContactCenterBtn","ButtonStyling__c":"btn green-background","Checks__c":"{\\"clientType\\": {\\"value\\": \\"Prospect;Cliente\\",\\"operator\\": \\"contains\\",\\"failureVisibility\\": \\"hidden\\"},\\"stage\\": {\\"value\\": \\"In gestione\\",\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"hidden\\"},\\"omnichannelAgreement\\": {\\"value\\": true,\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"disabled\\"}}","IsActive__c":true,"Object__c":"Opportunity","Order__c":4,"Position__c":"Reassign","Id":"m0T1x0000009VBvEAM"}]',
                List<ButtonWrapper__mdt>.class
                );
                //ButtonWrapperController.ButtonWrapper bw4 = ButtonWrapperController.getButtons(ip1.get(0).Id, 'Reassign');
            }                      
        }catch(ButtonWrapperController.ButtonWrapperException e){
            //System.assertEquals('No button wrapper data were found on the record.', e.getMessage());
        }catch(AuraHandledException e){
            System.debug(e.getMessage());
        }
    }
}