/*********************************************************************************
* <AUTHOR>
* @description    Agency Engine class for the apex defined object definitions
* @date           2024-03-05
* @group          Agency Engine
**********************************************************************************/
public with sharing class EngineWrapper {
    private static Map<String, String> AREAS_OF_NEED_TO_MACRO_AREAS = new Map<String, String> {
        'Cane e Gatto' => 'Property', 
        'Casa' => 'Property', 
        'Famiglia' => 'Property',
        'Infortuni' => 'Welfare',
        'Mobilità' => 'Motor',
        'Salute' => 'Welfare',
        'Veicoli' => 'Motor',
        'Viaggio' => 'Property',
        'Previdenza integrativa' => 'Welfare'
    };

    public static List<String> getMacroAreas(String areasofNeed) {
        if(String.isEmpty(areasofNeed)) {
            return new List<String>();
        }

        List<String> areasofNeedList = areasofNeed.split(';');
        Set<String> macroAreas = new Set<String>();
        for(String area : areasofNeedList) {
            if(AREAS_OF_NEED_TO_MACRO_AREAS.containsKey(area)) {
                macroAreas.add(AREAS_OF_NEED_TO_MACRO_AREAS.get(area));
            }
        }
        return new List<String>(macroAreas);
    }

    public class EngineWrapperInput {
        @InvocableVariable
        public String clientType;
        @InvocableVariable
        public String source;
        @InvocableVariable
        public List<String> agenciestoExcludeIds;
        @InvocableVariable
        public Double latitude;
        @InvocableVariable
        public Double longitude;
        public List<String> macroAreas;
        @InvocableVariable
        public String areasofNeed;

        public EngineWrapperInput(
            String clientType,
            String source,
            List<String> agenciestoExcludeIds, 
            Double latitude,
            Double longitude,
            List<String> macroAreas,
            String areasofNeed
        ) {
            this.clientType = clientType;
            this.source = source;
            this.agenciestoExcludeIds = agenciestoExcludeIds;
            this.latitude = latitude;
            this.longitude = longitude;
            this.macroAreas = macroAreas;
            this.areasofNeed = areasofNeed;
        }

        public EngineWrapperInput() { }
    }

    public class EngineWrapperInvocableOutput {
        @InvocableVariable
        public Boolean isSuccess;
        @InvocableVariable
        public String errorMessage;
        @InvocableVariable
        public List<Account> selectedAgencies;
        @InvocableVariable
        public Account selectedSalespoint;
        @InvocableVariable
        public Account selectedAgency;

        public EngineWrapperInvocableOutput(Boolean isSuccess, String errorMessage) {
            this.isSuccess = isSuccess;
            this.errorMessage = errorMessage;
        }

        public EngineWrapperInvocableOutput(Boolean isSuccess, String errorMessage, List<Account> selectedAgencies, Account selectedSalespoint, Account selectedAgency) {
            this.isSuccess = isSuccess;
            this.errorMessage = errorMessage;
            this.selectedAgencies = selectedAgencies;
            this.selectedSalespoint = selectedSalespoint;
            this.selectedAgency = selectedAgency;
        }
    }

    public class EngineWrapperOutput {
        public EngineWrapperInput engineWrapperInput;
        public EngineSettings engineSettings;
        public Boolean isSuccess;
        public EngineError engineError;
        public List<EngineAgency> matchingSalespoints;
        public EngineAgency selectedSalespoint;
        public EngineAgency selectedAgency;
        public List<EngineExclusionAgency> excludedSalespoints;

        public EngineWrapperOutput(
            EngineWrapperInput engineWrapperInput, 
            Boolean isSuccess, 
            EngineError engineError, 
            EngineSettings engineSettings
        ) {
            this.engineWrapperInput = engineWrapperInput;
            this.isSuccess = isSuccess;
            this.engineError = engineError;
        }

        public EngineWrapperOutput(
            EngineWrapperInput engineWrapperInput, 
            Boolean isSuccess, 
            List<EngineAgency> matchingSalespoints, 
            EngineAgency selectedSalespoint,
            EngineAgency selectedAgency,
            List<EngineExclusionAgency> excludedSalespoints, 
            EngineSettings engineSettings
        ) {
            this.engineWrapperInput = engineWrapperInput;
            this.isSuccess = isSuccess;
            this.matchingSalespoints = matchingSalespoints;
            this.selectedSalespoint = selectedSalespoint;
            this.selectedAgency = selectedAgency;
            this.excludedSalespoints = excludedSalespoints;
        }
    }

    public class EngineSettings {
        public Boolean discretionality;
        public Double extensionRangeKM;
        public String defaultAgency;
        public String defaultAssignment;
        public Integer maxReassignments;
        public Double centerRangeKM;
        public Double contactRate;

        public EngineSettings(
            Boolean discretionality,
            Double extensionRangeKM,
            String defaultAgency,
            String defaultAssignment,
            Integer maxReassignments,
            Double centerRangeKM,
            Double contactRate
        ) {
            this.discretionality = discretionality;
            this.extensionRangeKM = extensionRangeKM;
            this.defaultAgency = defaultAgency;
            this.defaultAssignment = defaultAssignment;
            this.maxReassignments = maxReassignments;
            this.centerRangeKM = centerRangeKM;
            this.contactRate = contactRate;
        }
    }

    public class EngineError {
        public String errorKeyword;
        public String errorDescription;

        public EngineError(String errorKeyword) {
            this.errorKeyword = errorKeyword;
        }
    }

    public class EngineExclusionAgency {
        public EngineAgency excludedAgency;
        public String exclusionReason;

        public EngineExclusionAgency(EngineAgency excludedAgency, String exclusionReason) {
            this.excludedAgency = excludedAgency;
            this.exclusionReason = exclusionReason;
        }
    }

    public class KPISet {
        public String name; // Name
        public Double contactRate; // ContactRate__c
        public Double contactableClientsNumber; // NumberContactableClients__c
        public Double portfolioClientsNumber; // NumberClientsPortfolio__c
        public Double contactActivitiesProcessingAssigned; // ContactActivitiesProcessingAssigned__c
        public Double benchmarkContactActivitiesAssigned; // BenchmarkContactActivitiesAssigned__c
        public Double weightContactActivitiesAssigned; // WeightContactActivitiesAssigned__c
        public Double scoreProcessingAssignedActivities; // ScoreProcessingAssignedActivities__c
        public Double conversionAssignedContactActivities; // ConversionAssignedContactActivities__c
        public Double benchmarkConversionAssignedActivities; // BenchmarkConversionAssignedActivities__c
        public Double scoreConversionAssignedActivities; // ScoreConversionAssignedActivities__c
        public Double weightConversionAssignedActivities; // WeightConversionAssignedActivities__c
        public Double averageLeadProcessingTime; // AverageLeadProcessingTime__c
        public Double benchmarkAverageLeadProcessingTime; // BenchmarkAverageLeadProcessingTime__c
        public Double scoreAverageLeadProcessingTime; // ScoreAverageLeadProcessingTime__c
        public Double weightAverageLeadProcessingTime; // WeightAverageLeadProcessingTime__c
        public Double digitalPenetration; // DigitalPenetration__c
        public Double benchmarkDigitalPenetration; // BenchmarkDigitalPenetration__c
        public Double scoreDigitalPenetration; // ScoreDigitalPenetration__c
        public Double weightDigitalPenetration; // WeightDigitalPenetration__c
        public Double privateAreaRegistration; // PrivateAreaRegistration__c
        public Double benchmarkPrivateAreaRegistration; // BenchmarkPrivateAreaRegistration__c
        public Double scorePrivateAreaRegistration; // ScorePrivateAreaRegistration__c
        public Double weightPrivateAreaRegistration; // WeightPrivateAreaRegistration__c
        public Double omnichannelQuotes; // OmnichannelQuotes__c
        public Double benchmarkOmnichannelQuotes; // BenchmarkOmnichannelQuotes__c
        public Double scoreOmnichannelQuotes; // ScoreOmnichannelQuotes__c
        public Double weightOmnichannelQuotes; // WeightOmnichannelQuotes__c

        public KPISet(KPI__c kpi) {
            this.name = kpi.Name;
            this.contactRate = kpi.ContactRate__c;
            this.contactableClientsNumber = kpi.NumberContactableClients__c;
            this.portfolioClientsNumber = kpi.NumberClientsPortfolio__c;
            this.contactActivitiesProcessingAssigned = kpi.ContactActivitiesProcessingAssigned__c;
            this.benchmarkContactActivitiesAssigned = kpi.BenchmarkContactActivitiesAssigned__c;
            this.weightContactActivitiesAssigned = kpi.WeightContactActivitiesAssigned__c;
            this.scoreProcessingAssignedActivities = kpi.ScoreProcessingAssignedActivities__c;
            this.conversionAssignedContactActivities = kpi.ConversionAssignedContactActivities__c;
            this.benchmarkConversionAssignedActivities = kpi.BenchmarkConversionAssignedActivities__c;
            this.scoreConversionAssignedActivities = kpi.ScoreConversionAssignedActivities__c;
            this.weightConversionAssignedActivities = kpi.WeightConversionAssignedActivities__c;
            this.averageLeadProcessingTime = kpi.AverageLeadProcessingTime__c;
            this.benchmarkAverageLeadProcessingTime = kpi.BenchmarkAverageLeadProcessingTime__c;
            this.scoreAverageLeadProcessingTime = kpi.ScoreAverageLeadProcessingTime__c;
            this.weightAverageLeadProcessingTime = kpi.WeightAverageLeadProcessingTime__c;
            this.digitalPenetration = kpi.DigitalPenetration__c;
            this.benchmarkDigitalPenetration = kpi.BenchmarkDigitalPenetration__c;
            this.scoreDigitalPenetration = kpi.ScoreDigitalPenetration__c;
            this.weightDigitalPenetration = kpi.WeightDigitalPenetration__c;
            this.privateAreaRegistration = kpi.PrivateAreaRegistration__c;
            this.benchmarkPrivateAreaRegistration = kpi.BenchmarkPrivateAreaRegistration__c;
            this.scorePrivateAreaRegistration = kpi.ScorePrivateAreaRegistration__c;
            this.weightPrivateAreaRegistration = kpi.WeightPrivateAreaRegistration__c;
            this.omnichannelQuotes = kpi.OmnichannelQuotes__c;
            this.benchmarkOmnichannelQuotes = kpi.BenchmarkOmnichannelQuotes__c;
            this.scoreOmnichannelQuotes = kpi.ScoreOmnichannelQuotes__c;
            this.weightOmnichannelQuotes = kpi.WeightOmnichannelQuotes__c;
        }
    }

    public class EngineAgency implements Comparable {
        public Id sfAccountId; // Id
        public String agencyCode; // ExternalId__c
        public String name; // Name
        public String billingAddress; // BillingAddressFormula__c
        public Double score; 
        public Double distance;
        Transient Boolean discretionality;
        public String agencyNumber; // AccountNumber
        public String type; // Type
        public String subtype; // SubType__c
        public String district; // District__c
        public String vatCode; // VatCode__c
        public Double capActivity; // CAPActivity__c
        public Double extraCap; // ExtraCAP__c
        public Double agencyLoad; // AgencyLoad__c
        public Boolean discretionalityMotor; // DiscretionalityMotor__c
        public Boolean discretionalityWelfare; // DiscretionalityWelfare__c
        public Boolean discretionalityProperty; // DiscretionalityProperty__c
        public Boolean discretionalityEnterprise; // DiscretionalityEnterprise__c
        public Boolean blacklist; // Blacklist__c
        public Boolean omnichannelAgreement; // OmnichannelAgreement__c
        public Boolean omnichannelResponsible; // OmnichannelResponsible__c
        public Boolean isLocatorEnabled; // IsLocatorEnabled__c
        public String size; // Size__c
        public String cluster; // Cluster__c
        public KPISet kpiSet;
        Transient Boolean isSortingDiscretionalityOn = false;
        Transient Boolean isAgency;

        public EngineAgency(
            Account agency,
            Double score, 
            Double distance,
            Boolean discretionality,
            KPISet kpiSet,
            Boolean isSortingDiscretionalityOn,
            Boolean isAgency
        ) {
            this.sfAccountId = agency?.Id;
            this.agencyCode = isAgency ? agency?.ExternalId__c : agency?.Agency__r?.ExternalId__c;
            this.name = agency?.Name;
            this.billingAddress = isAgency ? agency?.BillingAddressFormula__c : agency?.Agency__r?.BillingAddressFormula__c;
            this.score = score; 
            this.distance = distance;
            this.discretionality = discretionality;
            this.agencyNumber = isAgency ? agency?.AccountNumber : agency?.Agency__r?.AccountNumber;
            this.type = isAgency ? agency?.Type : agency?.Agency__r?.Type;
            this.subtype = agency?.SubType__c;
            this.district = isAgency ? agency?.District__c : agency?.Agency__r?.District__c;
            this.vatCode = isAgency ? agency?.VatCode__c : agency?.Agency__r?.VatCode__c;
            this.capActivity = isAgency ? agency?.CAPActivity__c : agency?.Agency__r?.CAPActivity__c;
            this.extraCap = isAgency ? agency?.ExtraCAP__c : agency?.Agency__r?.ExtraCAP__c;
            this.agencyLoad = isAgency ? agency?.AgencyLoad__c : agency?.Agency__r?.AgencyLoad__c;
            this.discretionalityMotor = isAgency ? agency?.DiscretionalityMotor__c : agency?.Agency__r?.DiscretionalityMotor__c;
            this.discretionalityWelfare = isAgency ? agency?.DiscretionalityWelfare__c : agency?.Agency__r?.DiscretionalityWelfare__c;
            this.discretionalityProperty = isAgency ? agency?.DiscretionalityProperty__c : agency?.Agency__r?.DiscretionalityProperty__c;
            this.discretionalityEnterprise = isAgency ? agency?.DiscretionalityEnterprise__c : agency?.Agency__r?.DiscretionalityEnterprise__c;
            this.blacklist = isAgency ? agency?.Blacklist__c : agency?.Agency__r?.Blacklist__c;
            this.omnichannelAgreement = isAgency ? agency?.OmnichannelAgreement__c : agency?.Agency__r?.OmnichannelAgreement__c;
            this.omnichannelResponsible = isAgency ? agency?.OmnichannelResponsible__c : agency?.Agency__r?.OmnichannelResponsible__c;
            this.isLocatorEnabled = agency?.IsLocatorEnabled__c;
            this.size = isAgency ? agency?.Size__c : agency?.Agency__r?.Size__c;
            this.cluster = isAgency ? agency?.Cluster__c : agency?.Agency__r?.Cluster__c;
            this.kpiSet = kpiSet;
            this.isSortingDiscretionalityOn = isSortingDiscretionalityOn;
        }

        /********************************************************************************************************************
        * <AUTHOR>
        * @date         2024-03-05
        * @description  The method overides the standard sorting used by apex
        * @param        compareTo (Object): the object to compare against the object the sorting was called on
        * @return       Integer: returns -1 if the object the sorting was called on is ranked lower than the compareTo object
        *               1 if it is ranked higher than the compareTo object or 0 if they are ranked evenly
        ********************************************************************************************************************/
        public Integer compareTo(Object compareTo) {
            EngineAgency compareToAgency = (EngineAgency) compareTo;

            // Sorting for discretionality if it's active in the lookup table            
            if(isSortingDiscretionalityOn && (discretionality && !compareToAgency.discretionality)) {
                return -1;
            }
            else if(isSortingDiscretionalityOn && (!discretionality && compareToAgency.discretionality)) {
                return 1;
            }

            // Sorting for score
            if((score != null && compareToAgency?.score != null) && (score > compareToAgency.score)) {
                return -1;
            }
            else if((score != null && compareToAgency?.score != null) && (score < compareToAgency.score)) {
                return 1;
            }

            // Sorting for distance
            if((distance != null && compareToAgency?.distance != null) && (distance < compareToAgency.distance)) {
                return -1;
            }
            else if((distance != null && compareToAgency?.distance != null) && (distance > compareToAgency.distance)) {
                return 1;
            }
            return 0;
        }
    }
}
