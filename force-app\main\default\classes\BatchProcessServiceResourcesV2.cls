global class BatchProcessServiceResourcesV2 implements Database.Batchable<SObject>, Schedulable, Database.Stateful {
    private Set<String> networkUserFiscalCode = new Set<String>();
    private Date min;
    private Date max;
    private Boolean concatenate = false;

    private static Map<String, String> societyMap = new Map<String, String>{ 'MandatoUnipolSai' => '1', 'MandatoUnipolRental' => '2', 'MandatoUnipolTech' => '3', 'MandatoUniSalute' => '4' };

    global BatchProcessServiceResourcesV2() {
        this(false);
    }

    global BatchProcessServiceResourcesV2(Boolean concatenate) {
        this.concatenate = concatenate;
    }

    global BatchProcessServiceResourcesV2(Set<String> networkUserFiscalCode) {
        this.networkUserFiscalCode = networkUserFiscalCode;
    }

    global BatchProcessServiceResourcesV2(Date min, Date max) {
        this.min = min;
        this.max = max;
    }

    global Database.QueryLocator start(Database.BatchableContext BC) {
        String query = 'SELECT Id, NetworkUser__c, FiscalCode__c, IsActive__c, Profile__c,Society__c, Agency__r.AgencyCode__c FROM NetworkUser__c WHERE Agency__c != null AND NetworkUser__c != null AND FiscalCode__c != null';

        if (networkUserFiscalCode != null && !networkUserFiscalCode.isEmpty()) {
            query += ' AND FiscalCode__c IN :networkUserFiscalCode';
        }

        if (networkUserFiscalCode != null && !networkUserFiscalCode.isEmpty()) {
            query += ' AND LastModifiedDate >= :min AND LastModifiedDate <= :max';
        }

        return Database.getQueryLocator(query);
    }

    global void execute(Database.BatchableContext BC, List<NetworkUser__c> scope) {
        /*
         * Identify User Fiscal Code and Groups by agency codes
         */
        Set<String> fiscalCodes = new Set<String>();
        Set<String> groupNames = new Set<String>();

        for (NetworkUser__c nu : scope) {
            fiscalCodes.add(nu.FiscalCode__c);
            groupNames.add('R_AGE_' + nu.Agency__r.AgencyCode__c);
            groupNames.add('AGE_' + nu.Agency__r.AgencyCode__c);
            groupNames.add(nu.Society__c);
        }

        /*
         * Create User Map (FederationIdentifier => User ID)
         */
        Map<String, Id> mapCFUser = new Map<String, Id>();
        List<User> userList = [SELECT Id, FiscalCode__c FROM User WHERE FiscalCode__c IN :fiscalCodes];
        for (User u : userList) {
            mapCFUser.put(u.FiscalCode__c, u.Id);
        }

        /*
         * Create Permission Set Society Map (Society => Id)
         */
        Map<String, Id> mapSocietyPermissionSet = new Map<String, Id>();
        List<PermissionSet> psList = [SELECT Id, Name FROM PermissionSet WHERE Name IN ('MandatoUnipolSai', 'MandatoUnipolRental', 'MandatoUnipolTech', 'MandatoUniSalute')];
        for (PermissionSet ps : psList) {
            mapSocietyPermissionSet.put(societyMap.get(ps.Name), ps.Id);
        }

        /*
         * Create User Permission Set Assignment Map (FederationIdentifier => List Permission Set Assignment)
         */
        Map<String, Map<String, PermissionSetAssignment>> mapCFPermissionSetAssignment = new Map<String, Map<String, PermissionSetAssignment>>();
        for (PermissionSetAssignment psa : [SELECT Id, PermissionSet.Name, PermissionSetGroupId, AssigneeId, Assignee.FederationIdentifier FROM PermissionSetAssignment WHERE PermissionSet.Name IN ('MandatoUnipolSai', 'MandatoUnipolRental', 'MandatoUnipolTech', 'MandatoUniSalute') AND Assignee.FederationIdentifier IN :fiscalCodes]) {
            if (!mapCFPermissionSetAssignment.containsKey(psa.Assignee.FederationIdentifier)) {
                mapCFPermissionSetAssignment.put(psa.Assignee.FederationIdentifier, new Map<String, PermissionSetAssignment>());
            }

            mapCFPermissionSetAssignment.get(psa.Assignee.FederationIdentifier).put(societyMap.get(psa.PermissionSet.Name), psa);
        }

        /*
         * Create User Netowrk User Map (FiscalCode => List Society)
         */
        Map<String, Set<String>> mapCFSociety = new Map<String, Set<String>>();
        Map<String, List<NetworkUser__c>> mapCFNewtorkUser = new Map<String, List<NetworkUser__c>>();

        /* for (NetworkUser__c nu : [SELECT id, FiscalCode__c, Agency__c, Agency__r.ExternalId__c, Society__c, IsActive__c FROM NetworkUser__c WHERE IsActive__c = TRUE AND Society__c != NULL AND FiscalCode__c IN :fiscalCodes]) {
            if (!mapCFSociety.containsKey(nu.FiscalCode__c)) {
                mapCFSociety.put(nu.FiscalCode__c, new Set<String>());
            }
            if (!mapCFNewtorkUser.containsKey(nu.FiscalCode__c)) {
                mapCFNewtorkUser.put(nu.FiscalCode__c, new List<NetworkUser__c>());
            }

            mapCFSociety.get(nu.FiscalCode__c).add(nu.Society__c.replace('SOC_', ''));
            mapCFNewtorkUser.get(nu.FiscalCode__c).add(nu);
        } */

        // START FIX - MANAGE ALL RELATED NETWORKUSER
        List<NetworkUser__c> allNU = [SELECT Id, NetworkUser__c, FiscalCode__c, IsActive__c, Profile__c,Society__c, Agency__r.AgencyCode__c FROM NetworkUser__c WHERE FiscalCode__c IN :fiscalCodes AND Agency__c != null AND NetworkUser__c != null AND Society__c != null];
        Map<String, List<NetworkUser__c>> mapAllCFNU = new Map<String, List<NetworkUser__c>>();
        Map<String, Map<String, List<NetworkUser__c>>> mapSocietyNU = new Map<String, Map<String, List<NetworkUser__c>>>();
        for(NetworkUser__c nu : allNU){

            if(nu.IsActive__c){
                if (!mapCFSociety.containsKey(nu.FiscalCode__c)) {
                    mapCFSociety.put(nu.FiscalCode__c, new Set<String>());
                }
                if (!mapCFNewtorkUser.containsKey(nu.FiscalCode__c)) {
                    mapCFNewtorkUser.put(nu.FiscalCode__c, new List<NetworkUser__c>());
                }

                mapCFSociety.get(nu.FiscalCode__c).add(nu.Society__c.replace('SOC_', ''));
                mapCFNewtorkUser.get(nu.FiscalCode__c).add(nu);
            }

            if(mapAllCFNU.containsKey(nu.FiscalCode__c)){
                mapAllCFNU.get(nu.FiscalCode__c).add(nu);
            }else{
                mapAllCFNU.put(nu.FiscalCode__c, new List<NetworkUser__c>{nu});
            }

            if(mapSocietyNU.containsKey(nu.FiscalCode__c)){

                if(mapSocietyNU.get(nu.FiscalCode__c).containsKey(nu.Society__c)){
                    mapSocietyNU.get(nu.FiscalCode__c).get(nu.Society__c).add(nu);
                }else{
                    mapSocietyNU.get(nu.FiscalCode__c).put(nu.Society__c, new List<NetworkUser__c>{nu});
                }

            }else{
                mapSocietyNU.put(nu.FiscalCode__c, new Map<String,List<NetworkUser__c>>{nu.Society__c => new List<NetworkUser__c>{nu}});
            }
        }
        // END FIX - MANAGE ALL RELATED NETWORKUSER

        /*
         * Create Group Map (Group Name => Group ID)
         */
        Map<String, Id> mapGroup = new Map<String, Id>();
        List<Group> groupList = [SELECT Id, DeveloperName FROM Group WHERE DeveloperName IN :groupNames];
        for (Group g : groupList) {
            mapGroup.put(g.DeveloperName, g.Id);
        }

        /*
         * Get Existing Group Members
         */
        List<GroupMember> memberList = [SELECT Id, Group.DeveloperName, UserOrGroup.ExternalId__c FROM GroupMember WHERE UserOrGroup.ExternalId__c IN :fiscalCodes AND Group.DeveloperName IN :groupNames];
        Map<String, GroupMember> mapMembers = new Map<String, GroupMember>();
        for (GroupMember gm : memberList) {
            mapMembers.put(gm.UserOrGroup.ExternalId__c + '_' + gm.Group.DeveloperName, gm);
        }

        List<GroupMember> gmToDelete = new List<GroupMember>();
        List<GroupMember> gmToInsert = new List<GroupMember>();

        List<NetworkUser__c> nuDefaultToUpdate = new List<NetworkUser__c>();

        /*
         * Iterate over scope to manage insert/delete
         */
        for (NetworkUser__c nue : scope) {
            if (!mapCFUser.containsKey(nue.FiscalCode__c))
                continue;

            // START FIX - MANAGE ALL RELATED NETWORKUSER
            if(!mapAllCFNU.containsKey(nue.FiscalCode__c)) continue;

            if(
                mapSocietyNU.containsKey(nue.FiscalCode__c)
            ){
                for(String s : mapSocietyNU.get(nue.FiscalCode__c).keySet()){

                    if(mapSocietyNU.get(nue.FiscalCode__c).get(s).size() == 1){
                        NetworkUser__c n = mapSocietyNU.get(nue.FiscalCode__c).get(s)[0];
                        if(n.IsActive__c){
                            n.Preferred__c = true;
                            nuDefaultToUpdate.add(n);
                        }
                    }
                    
                }
            }

            for(NetworkUser__c nu : mapAllCFNU.get(nue.FiscalCode__c)){
                if (nu.IsActive__c) {
                    // IsActive__c = true --> GroupMember must be created

                    String groupRes = 'R_AGE_' + nu.Agency__r.AgencyCode__c;
                    String groupBase = 'AGE_' + nu.Agency__r.AgencyCode__c;
                    String groupSoc = nu.Society__c;

                    if ('A'.equalsIgnoreCase(nu.Profile__c) && mapGroup.containsKey(groupRes)) {
                        gmToInsert.add(new GroupMember(UserOrGroupId = mapCFUser.get(nu.FiscalCode__c), GroupId = mapGroup.get(groupRes)));
                    }

                    if (mapGroup.containsKey(groupBase)) {
                        gmToInsert.add(new GroupMember(UserOrGroupId = mapCFUser.get(nu.FiscalCode__c), GroupId = mapGroup.get(groupBase)));
                    }

                    if (mapGroup.containsKey(groupSoc)) {
                        gmToInsert.add(new GroupMember(UserOrGroupId = mapCFUser.get(nu.FiscalCode__c), GroupId = mapGroup.get(groupSoc)));
                    }
                } else {
                    // IsActive__c = false --> GroupMember must be deleted (if exists)

                    String groupRes = nu.FiscalCode__c + '_' + 'R_AGE_' + nu.Agency__r.AgencyCode__c;
                    String groupBase = nu.FiscalCode__c + '_' + 'AGE_' + nu.Agency__r.AgencyCode__c;
                    String groupSoc = nu.FiscalCode__c + '_' + nu.Society__c;

                    if (mapMembers.containsKey(groupRes))
                        gmToDelete.add(mapMembers.get(groupRes));
                    if (mapMembers.containsKey(groupBase))
                        gmToDelete.add(mapMembers.get(groupBase));
                    if (mapMembers.containsKey(groupSoc))
                        gmToDelete.add(mapMembers.get(groupSoc));
                }
            }
            // END FIX - MANAGE ALL RELATED NETWORKUSER
        }

        List<PermissionSetAssignment> psaToInsert = new List<PermissionSetAssignment>();
        List<PermissionSetAssignment> psaToDelete = new List<PermissionSetAssignment>();
        List<User> userToUpdate = new List<User>();

        for (String fiscalCode : fiscalCodes) {
            // Logica di popolamento IdAzienda
            List<NetworkUser__c> networkUserList = mapCFNewtorkUser.get(fiscalCode);

            Id agenzia = null;
            if (networkUserList.size() > 1) {
                //IdAzienda__c
                for (NetworkUser__c nu : networkUserList) {
                    if (nu.Society__c == 'SOC_1' && nu.Agency__c != null) {
                        agenzia = nu.Agency__c;
                        break;
                    }
                }
            } else if (networkUserList.size() == 1 && networkUserList.get(0).Agency__c != null) {
                agenzia = networkUserList.get(0).Agency__c;
            }

            userToUpdate.add(new User(Id = mapCFUser.get(fiscalCode), IdAzienda__c = agenzia));

            // enabled society by Network User
            Set<String> enabledSociety = mapCFSociety.get(fiscalCode);

            // Existing permission set assignment
            Map<String, PermissionSetAssignment> existingBySociety = mapCFPermissionSetAssignment.containsKey(fiscalCode) ? mapCFPermissionSetAssignment.get(fiscalCode) : new Map<String, PermissionSetAssignment>();

            // Create the PSA if not assigned to user
            if (enabledSociety != null) {
                for (String society : enabledSociety) {
                    if (!existingBySociety.containsKey(society)) {
                        psaToInsert.add(new PermissionSetAssignment(AssigneeId = mapCFUser.get(fiscalCode), PermissionSetId = mapSocietyPermissionSet.get(society)));
                    }
                }
            }

            // delete redundant psa
            for (String society : existingBySociety.keySet()) {
                if (enabledSociety == null || !enabledSociety.contains(society)) {
                    psaToDelete.add(existingBySociety.get(society));
                }
            }
        }

        if (!gmToDelete.isEmpty())
            Database.delete(gmToDelete, false);

        if (!gmToInsert.isEmpty())
            Database.insert(gmToInsert, false);

        if (!psaToDelete.isEmpty())
            Database.delete(psaToDelete, false);

        if (!psaToInsert.isEmpty())
            Database.insert(psaToInsert, false);

        if(!nuDefaultToUpdate.isEmpty() && !Test.isRunningTest()) Database.update(nuDefaultToUpdate, false);

        if (!userToUpdate.isEmpty())
            Database.update(userToUpdate, false);
    }

    global void finish(Database.BatchableContext BC) {
        if (concatenate) {
            Database.executeBatch(new ServiceTerritoryWorkTypeBatch(concatenate), 200);
        }
    }

    global void execute(SchedulableContext SC) {
        Database.executeBatch(new BatchProcessServiceResourcesV2(), 200);
    }
}