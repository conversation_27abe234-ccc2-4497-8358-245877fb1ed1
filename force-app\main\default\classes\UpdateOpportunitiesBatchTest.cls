@isTest
private class UpdateOpportunitiesBatchTest {
    @isTest
    static void testBatchExecution() {
        // Recupera il RecordType esistente "Omnicanale" per le Opportunità
        RecordType rt = [SELECT Id FROM RecordType 
                         WHERE SObjectType = 'Opportunity' 
                         AND DeveloperName = 'Omnicanale' 
                         LIMIT 1];

        // Crea un'opportunità che rispetta i criteri del batch
        Opportunity opp = new Opportunity(
            Name = 'Test Opportunity',
            StageName = 'Assegnato',
            CloseDate = Date.today().addDays(10),
            RecordTypeId = rt.Id,
            TakenInChargeSLAExpiryDate__c = Date.today().addDays(-1)
        );
        insert opp;

        // Esecuzione batch
        Test.startTest();
        Database.executeBatch(new UpdateOpportunitiesBatch(), 200);
        Test.stopTest();

        // Verifica che l'opportunità sia stata aggiornata
        Opportunity updatedOpp = [SELECT StageName, TakenInChargeDate__c 
                                  FROM Opportunity 
                                  WHERE Id = :opp.Id];
        
        System.assertEquals('In gestione', updatedOpp.StageName);
        System.assertEquals(Date.today(), updatedOpp.TakenInChargeDate__c);
    }
}