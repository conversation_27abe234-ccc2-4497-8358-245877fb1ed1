@isTest
public class CaseCalculatePriorityBatchTest {
@testSetup
    static void setup() {
        
     CaseActivityInit__c w1 = new CaseActivityInit__c(Type__c='Weight',WeightAttribute__c='ATTIVITA',WeightValue__c=0.6);
     insert w1;
        
     CaseActivityInit__c w2 = new CaseActivityInit__c(Type__c='Weight',WeightAttribute__c='SCADENZA',WeightValue__c=0.3);
     insert w2;
        
     CaseActivityInit__c w3 = new CaseActivityInit__c(Type__c='Weight',WeightAttribute__c='CALORE-FLAG',WeightValue__c=0.1);
     insert w3;
        
     CaseActivityInit__c sa1 = new CaseActivityInit__c(Type__c='Score',Area__c='Portafoglio Vita', Activity__c = 'Riscatti e Liquidazioni', WeightValue__c=0.75);
     insert sa1;
     
     CaseActivityInit__c sa2 = new CaseActivityInit__c(Type__c='Score',Area__c='Richieste commerciali', Activity__c = 'Versamenti aggiuntivi', WeightValue__c=1);
     insert sa2;
        
     CaseActivityInit__c sf1 = new CaseActivityInit__c(Type__c='Score',LeoHeat__c = 'CALDO', LeoPriority__c = false , WeightValue__c=0.66);
     insert sf1;
     
     CaseActivityInit__c sf2 = new CaseActivityInit__c(Type__c='Score',LeoHeat__c = 'CALDISSIMO', LeoPriority__c = true , WeightValue__c=1);
     insert sf2;
        
    RecordType RTAttivitaContatto = [SELECT id FROM RecordType WHERE DeveloperName = 'AttivitaContatto' and SobjectType = 'Case' LIMIT 1];
        
    Case c1 = new Case(
        Priority = 'Low',
        Status = 'New',
        Area__c = 'Portafoglio Vita',
        Activity__c = 'Riscatti e Liquidazioni',
        TECH_LeoHeat__c = 'CALDO',
        TECH_LeoPriority__c = false,
        Detail__c = 'Richiesta liquidazione/riscatto',
        RecordTypeId = RTAttivitaContatto.id,
        dueDate__c = Date.today()+2      
    );
    insert c1;
        
    Case c2 = new Case(
        Priority = 'Low',
        Status = 'New',
        Area__c = 'Richieste commerciali',
        Activity__c = 'Versamenti aggiuntivi',
        TECH_LeoHeat__c = 'CALDISSIMO',
        TECH_LeoPriority__c = true,
        Detail__c = 'Individuali',
        RecordTypeId = RTAttivitaContatto.id,
        dueDate__c = Date.today()+2      
    );
    insert c2;
    
        Case c3 = new Case(
        Priority = 'Medium',
        Status = 'New',
        Area__c = 'Portafoglio Vita',
        Activity__c = 'Altre Gestioni Collettive',
        TECH_LeoPriority__c = false,
        Detail__c = 'Richiesta esclusione assicurato',
        RecordTypeId = RTAttivitaContatto.id,
        dueDate__c = Date.today()+15      
    );
    insert c3;
        
    }
    
@isTest
static void testCalculatePriority() {
    Test.startTest();
    Id batchJobId = Database.executeBatch(new CaseCalculatePriorityBatch());
    Test.stopTest();  
	}
}