public without sharing class STProductTabsetController
{
    public class ProductContainer
    {
        @AuraEnabled
        public Boolean callMeBackAllowed;
        @AuraEnabled
        public Boolean quotesAllowed;
        @AuraEnabled
        public String id;
        @AuraEnabled
        public String domainType;
        @AuraEnabled
        public String wasReassigned;
        @AuraEnabled
        public String engagementPoint;
        @AuraEnabled
        public String closureType;
        @AuraEnabled
        public String source;
        @AuraEnabled
        public String saleChannel;
        @AuraEnabled
        public String channel;
        @AuraEnabled
        public String productCode;
    }

    @AuraEnabled
    public static List<ProductContainer> retrieveProducts(String recordId)
    {
        try
        {
            List<Opportunity> products = [SELECT Id, Name, DomainType__c, AssignmentCounter__c, EngagementPoint__c, ClosureSubstatus__c, LeadSource, PolicyChannel__c, Channel__c FROM Opportunity WHERE RecordType.DeveloperName = 'Prodotto' AND Parent__c =: recordId];

            Set<String> domains = new Set<String>();
            for(Opportunity product : products)
            {
                domains.add(product.DomainType__c);
            }

            List<ProductConfiguration__mdt> configs = [SELECT Id, DeveloperName, CallMeBack__c, DomainType__c, Quote__c, ProductCode__c FROM ProductConfiguration__mdt WHERE DomainType__c IN :domains];

            Map<String, ProductConfiguration__mdt> domainToConfigMap = new Map<String, ProductConfiguration__mdt>();
            for(ProductConfiguration__mdt config : configs)
            {
                domainToConfigMap.put(config.DomainType__c, config);
            }

            return buildProductContainers(products, domainToConfigMap);
        }
        catch (Exception e)
        {
            throw new AuraHandledException(e.getMessage());
        }
    }

    private static List<ProductContainer> buildProductContainers(List<Opportunity> products, Map<String, ProductConfiguration__mdt> domainConfigs)
    {
        List<ProductContainer> result = new List<ProductContainer>();

        for(Opportunity product : products)
        {
            ProductContainer container = new ProductContainer();
            container.id = product.Id;
            container.domaintype = product.DomainType__c;
            container.wasReassigned = product.AssignmentCounter__c > 1 ? 'Si' : 'No';
            container.engagementPoint = product.EngagementPoint__c != null ? product.EngagementPoint__c : '-';
            container.source = product.LeadSource != null ? product.LeadSource : '-';
            container.closureType = product.ClosureSubstatus__c != null ? product.ClosureSubstatus__c : '-';
            container.saleChannel = product.PolicyChannel__c != null ? product.PolicyChannel__c : '-';
            container.channel = product.Channel__c != null ? product.Channel__c : '-';

            ProductConfiguration__mdt currentConfig = domainConfigs.get(container.domaintype);
            container.callMeBackAllowed = currentConfig.CallMeBack__c;
            container.quotesAllowed = currentConfig.Quote__c;
            container.productCode = currentConfig.ProductCode__c;

            result.add(container);
        }

        return result;
    }
}