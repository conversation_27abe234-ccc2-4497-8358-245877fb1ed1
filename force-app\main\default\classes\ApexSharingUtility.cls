public with sharing class ApexSharingUtility {

    /*********************************************************************************************************
    * <AUTHOR> Digital
    * @date             21/02/2024
    * @description      This method is used to create the Sharing record of an Object and populated the neccessary fields
    * @param            shareObjName (String): The sharing name of an Object
    * @param            recordId (Id): The user or group IDs to which you’re granting access
    * @param            userOrGroupId (Id): The record Id which you’re granting access
    * @param            accessLevel (String): The level of access that the specified user or group has been granted for a share sObject
    * @param            rowCause (String): The reason why the user or group is being granted access
    * @return           Sharing record of the Object shareObjName
    *********************************************************************************************************/
    
    public static SObject createShareRecord(String shareObjName,Id recordId ,Id userOrGroupId, String accessLevel,String rowCause) {
        SObject genericObjectShr;

        try {
            Schema.SObjectType objectTypeSchema = Schema.getGlobalDescribe().get(shareObjName);
            
            if (objectTypeSchema != null) {
                genericObjectShr = objectTypeSchema.newSObject();
            } else {
                System.debug('Custom object type not found: ' + shareObjName);
            }
        } catch (Exception e) {
            System.debug('Exception: ' + e.getMessage());
        }

        throwIfBlank(userOrGroupId, 'userOrGroupId is null, empty, or contains only whitespace characters.');
        throwIfBlank(recordId, 'recordId is null, empty, or contains only whitespace characters.');

        genericObjectShr.put(getParentId(shareObjName),recordId);
        genericObjectShr.put('UserOrGroupId',userOrGroupId);

        if (accessLevel!=null && !String.isBlank(accessLevel)) {
            genericObjectShr.put(getAccessLevel(shareObjName),accessLevel);
        }else{
            genericObjectShr.put(getAccessLevel(shareObjName),'Read');
        }
        if (rowCause!=null && !String.isBlank(rowCause)) {
            genericObjectShr.put('RowCause',rowCause);
        }else{
            genericObjectShr.put('RowCause','Manual');
        }

        return genericObjectShr;
    }

    /*********************************************************************************************************
    * <AUTHOR> Digital
    * @date             21/02/2024
    * @description      The method return the Sharing name of an Object when giving the Api name of an Object
    * @param            objName (String): The Api name of the object
    * @return           String : the Sharing name of an Object
    *********************************************************************************************************/
    private static String generateName(String objName){
        if(objName.endsWith('__c')){
            return objName.replace('__c','__Share');
        }else{
            return objName+'Share';
        }
    }

    /*********************************************************************************************************
    * <AUTHOR> Digital
    * @date             21/02/2024
    * @description      The method return the name of a field about the record when giving the Sharing name of an Object
    * @param            shareObjName (String): The Sharing name of an Object
    * @return           String : the name of a field about the record
    *********************************************************************************************************/
    private static String getParentId(String shareObjName){
        if(shareObjName.endsWith('__Share')){
            return 'ParentId';
        }else{
            return shareObjName.replace('Share','Id');
        }
    }

    /*********************************************************************************************************
    * <AUTHOR> Digital
    * @date             21/02/2024
    * @description      The method return the name of a field about the AccessLevel when giving the sharing name of an Object
    * @param            shareObjName (String): The Sharing name of an Object
    * @return           String : the name of a field about the AccessLevel
    *********************************************************************************************************/
    private static String getAccessLevel(String shareObjName){
        if(shareObjName.endsWith('__Share')){
            return 'AccessLevel';
        }else{
            return shareObjName.replace('Share','AccessLevel');
        }
    }

    /*********************************************************************************************************
    * <AUTHOR> Digital
    * @date             21/02/2024
    * @description      This method throws an exception if the input string is blank or null.
    * @param            str (String): The string to check for blankness
    * @param            message (String): The message to include in the exception if the input string is blank
    * @throws           IllegalArgumentException if the input string is blank or null
    *********************************************************************************************************/
    private static void throwIfBlank(String str, String message) {
      if (str == null || String.isBlank(str)) {
          throw new IllegalArgumentException(message);
      }
    }

    /*********************************************************************************************************
    * <AUTHOR> Digital
    * @date             21/02/2024
    * @description      This method throws an exception if the input list is null or empty.
    * @param            genericlist (List<Object>): The list to check for emptiness
    * @param            message (String): The message to include in the exception if the input list is null or empty
    * @return           IllegalArgumentException if the input list is null or empty
    *********************************************************************************************************/
    private static void throwIfEmpty(List<Object> genericlist, String message) {
        if (genericlist == null || genericlist.isEmpty()) {
            throw new IllegalArgumentException(message);
        }
    }

    /*********************************************************************************************************
    * <AUTHOR> Digital
    * @date             21/02/2024
    * @description      
    * @param            objName (String): The Api Name of the object
    * @param            recordIds (List<Id>): The list of record Ids which you’re granting access
    * @param            userOrGroupId (Id): The user or group IDs to which you’re granting access
    * @param            accessLevel (String): The level of access that the specified user or group has been granted for a share sObject
    * @param            rowCause (String): The reason why the user or group is being granted access
    * @return           List of Booleans if the sharing were successful or not 
    *********************************************************************************************************/
    public static List<Database.SaveResult> shareTo(String objName,List<Id> recordIds,Id userOrGroupId,String accessLevel,String rowCause){
      List<Boolean> results = new List<Boolean>();
      List<SObject> genericObjectShr = new List<SObject>();

      throwIfBlank(objName, 'objName is null, empty, or contains only whitespace characters.');
      throwIfEmpty(recordIds, 'recordIds is null or empty.');
      throwIfBlank(userOrGroupId, 'userOrGroupId is null, empty, or contains only whitespace characters.');

      for (Id id : recordIds){
          genericObjectShr.add(createShareRecord(generateName(objName),id,userOrGroupId,accessLevel,rowCause));
      }

      Database.SaveResult[] saveResults = Database.insert(genericObjectShr,false);
      return saveResults;
  }

}