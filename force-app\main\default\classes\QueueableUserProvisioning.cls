/*
* @description This class is used to manage the queueable process of the user provisioning to set
* all Permissions Available for a specific UNIPOL User, info retrieved via UCA API.
* @cicd_tests UserProvisioningHandler_Test
*/
global class QueueableUserProvisioning implements Queueable {
    
    /*
    * @description input wrapper of this queueable class
    */
    global class InputWrapper{
        global String fiscalCode 			{get;set;}
        global String networkIdentifier 	{get;set;}
        global String permissionSets 		{get;set;}
    }
    
    global List<InputWrapper> input 		{get; set;}
    
    /*
    * @description constructor
    */
    global QueueableUserProvisioning(List<InputWrapper> input){
        this.input = input;
    }
    
    /*
    * @description method to execute the queueable process
    * @param QueueableContext context
    */
    global void execute(QueueableContext context) {
        
        if(this.input != null && !this.input.isEmpty()){
            
			Set<String> recordKeys = new Set<String>();
            for(InputWrapper iw : this.input){
                recordKeys.add(iw.fiscalCode);
            }
            
            List<NetworkUser__c> networkUserList = [SELECT Id, FiscalCode__c, NetworkUser__c FROM NetworkUser__c WHERE FiscalCode__c IN :recordKeys];
            Map<String, NetworkUser__c> mapUsers = new Map<String, NetworkUser__c>();
            for(NetworkUser__c nu : networkUserList){
                mapUsers.put(nu.FiscalCode__c+'_'+nu.NetworkUser__c, nu);
            }
            
            for(InputWrapper iw : this.input){
                
                String key = iw.fiscalCode + '_' + iw.networkIdentifier;
                
                if(mapUsers.containsKey(key)){
                    
                    mapUsers.get(key).PermissionSets__c = iw.permissionSets;
                    
                }
                
            }
            
            if(!mapUsers.isEmpty()) Database.update(mapUsers.values(),false);
            
        }
        
    }
    
}