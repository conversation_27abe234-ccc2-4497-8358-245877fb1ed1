@isTest
public with sharing class DeleteCommunicationBatch_Test {
    @testSetup
    static void makeData(){
        Campaign testCampaign = new Campaign();
        testCampaign.Name = 'Test Campaign 00';
        testCampaign.Status = 'Approvato';
        testCampaign.EndDate = Date.newInstance(2024,2,11);
        insert testCampaign;

        List<Campaign> campaignList = new List<Campaign>();
        campaignList = [SELECT Id,Name,Status,EndDate FROM Campaign WHERE Name = 'Test Campaign 00' LIMIT 1];
        
        // Insert 100 Communication records
        List<Communication__c> allTestComms = new List<Communication__c>();
        for(Integer i = 0; i < 100; i++){
            Communication__c testComm = new Communication__c();
            testComm.CampaignId__c = !campaignList.isEmpty() ? campaignList.get(0).Id : null;
            allTestComms.add(testComm);
        }
        insert allTestComms;
    }

    @isTest
    static void testExecute(){
        Test.startTest();
        Id batchJobId = Database.executeBatch(new DeleteCommunicationBatch());
        Test.stopTest();
        List<Campaign> campaignList = new List<Campaign>();
        campaignList = [SELECT Id,Name,Status,EndDate FROM Campaign WHERE Name = 'Test Campaign 00' LIMIT 1];
        Id campaignId = campaignList.get(0).Id;
        List<Communication__c> allTestComms = new List<Communication__c>();
        allTestComms = [SELECT Id,Name,CampaignId__c FROM Communication__c WHERE CampaignId__c =: campaignId];
        System.assertEquals(0, allTestComms.size());
    }
}
