public with sharing class Create<PERSON><PERSON><PERSON>ontroller {
        public class DependentPicklistData {
            @AuraEnabled public List<String> areas;
            @AuraEnabled public Map<String, List<String>> areaToActivities;
            @AuraEnabled public Map<String, List<String>> activityToDetails;
        }
    
        @AuraEnabled
        public static DependentPicklistData getDependentPicklistValues() {
            Set<String> areaSet = new Set<String>();
            Map<String, Set<String>> areaToActivities = new Map<String, Set<String>>();
            Map<String, Set<String>> activityToDetails = new Map<String, Set<String>>();
    
            List<CaseActivityInit__c> records = [SELECT Area__c, Activity__c, Detail__c 
                                                 FROM CaseActivityInit__c 
                                                 WHERE OpenManualAutomatic__c IN('Apertura MANUAL','Apertura MIX')
                                                 ];

            records.sort();
                                            
            for (CaseActivityInit__c rec : records) {
                String area = rec.Area__c;
                String activity = rec.Activity__c;
                String detail = rec.Detail__c;
    
                if (String.isNotBlank(area)) {
                    areaSet.add(area);
    
                    if (!areaToActivities.containsKey(area)) {
                        areaToActivities.put(area, new Set<String>());
                    }
                    if (String.isNotBlank(activity)) {
                        areaToActivities.get(area).add(activity);
                    }
                }
    
                if (String.isNotBlank(activity)) {
                    if (!activityToDetails.containsKey(activity)) {
                        activityToDetails.put(activity, new Set<String>());
                    }
                    if (String.isNotBlank(detail)) {
                        activityToDetails.get(activity).add(detail);
                    }
                }
            }
    
            // Conversione Set → List per serializzazione LWC-friendly
            DependentPicklistData result = new DependentPicklistData();
            result.areas = new List<String>(areaSet);
            result.areaToActivities = new Map<String, List<String>>();
            result.activityToDetails = new Map<String, List<String>>();
    
            for (String a : areaToActivities.keySet()) {
                result.areaToActivities.put(a, new List<String>(areaToActivities.get(a)));
            }
            for (String act : activityToDetails.keySet()) {
                result.activityToDetails.put(act, new List<String>(activityToDetails.get(act)));
            }
            result.areas.sort();

            return result;
        }
    }