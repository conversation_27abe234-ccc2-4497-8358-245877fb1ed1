<aura:component implements="lightning:backgroundUtilityItem">
    <aura:attribute name="userRecord" type="Object" />
    <force:recordData
        aura:id="recordLoader"
        recordId="{!$SObjectType.CurrentUser.Id}"
        fields="Id, Username, Name, Email, FederationIdentifier"
        targetFields="{!v.userRecord}"
        recordUpdated="{!c.handleUserRecordUpdate}"
    />
    <ltng:require
        styles=""
        scripts="{!$Resource.QuantumMetric}"
        afterScriptsLoaded=""
    />
</aura:component>