/**
 * @File Name         : ServiceTerritoryWorkTypeBatch.cls
 * @Description       : 
 * <AUTHOR> <EMAIL>
 * @Group             : 
 * @Last Modified On  : 30-01-2025
 * @Last Modified By  : <EMAIL>
@cicd_tests ServiceTerritoryWorkTypeBatchTest
**/

global class ServiceTerritoryWorkTypeBatch implements Database.Batchable<SObject>, Database.Stateful {
    private String dateFrom;
    private Boolean concatenate = false;

    public ServiceTerritoryWorkTypeBatch() {
        this(false);
    }

    public ServiceTerritoryWorkTypeBatch(Boolean concatenate) {
        this.concatenate = concatenate;
    }

    public ServiceTerritoryWorkTypeBatch(String dateFrom) {
        this(dateFrom, false);
    }

    public ServiceTerritoryWorkTypeBatch(String dateFrom, Boolean concatenate) {
        this.concatenate = concatenate;
        this.dateFrom = dateFrom;
    }
    
    global Database.QueryLocator start(Database.BatchableContext batchableContext) {
        
        if(dateFrom == null) {
            return Database.getQueryLocator([
                SELECT Id 
                FROM ServiceTerritory
                WHERE CreatedDate >= YESTERDAY
            ]);
        } else {
            return Database.getQueryLocator([
            SELECT Id 
            FROM ServiceTerritory
            WHERE CreatedDate >= :Date.valueOf(dateFrom)
        ]);
        }
    }
    
    global void execute(Database.BatchableContext batchableContext, List<ServiceTerritory> scope) {
        List<ServiceTerritoryWorkType> junctionsToInsert = new List<ServiceTerritoryWorkType>();

        // Fetch all WorkTypes
        List<WorkType> workTypes = [SELECT Id FROM WorkType];

        for (ServiceTerritory serviceTerritory : scope) {
            // Create a junction object for each WorkType
            for (WorkType workType : workTypes) {
                ServiceTerritoryWorkType junction = new ServiceTerritoryWorkType(
                    ServiceTerritoryId = serviceTerritory.Id,
                    WorkTypeId = workType.Id
                );
                junctionsToInsert.add(junction);
            }
        }
    
        if (!junctionsToInsert.isEmpty()) {
            try {
                Database.insert(junctionsToInsert, false);
            } catch (DmlException e) {
                
                System.debug('Errore durante l\'inserimento: ' + e.getMessage());
            }
        }
    }
    
    global void finish(Database.BatchableContext batchableContext) {
        // Optional: Perform any final operations after the batch job completes
        System.debug('Batch job completed successfully.');
        if(concatenate) {
            Database.executeBatch(new ManageGroupBatch(concatenate), 200);
        }
    }
}