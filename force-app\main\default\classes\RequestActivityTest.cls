@IsTest
private class RequestActivityTest {
    @IsTest
    static void testRequestActivityProperties() {
        // Crea un'istanza della classe
        RequestActivity req = new RequestActivity();

        // Imposta i valori
        req.isCallMeBack = true;
        req.createdDate = DateTime.now();
        req.timeSlot = '10:00 - 11:00';
        req.stageName = 'In Progress';
        req.notes = 'Test note';
        req.numeroRicontatto = '1234567890';
        req.dataScadenza = Date.today().addDays(7);
        req.areaOfNeed = 'Support';
        req.codDomainActivity = 'ACT123';
        req.testCoverage = true;

        // Asserzioni per verificare che i valori siano corretti
        System.assertEquals(true, req.isCallMeBack);
        System.assertEquals('10:00 - 11:00', req.timeSlot);
        System.assertEquals('In Progress', req.stageName);
        System.assertEquals('Test note', req.notes);
        System.assertEquals('1234567890', req.numeroRicontatto);
        System.assertEquals(Date.today().addDays(7), req.dataScadenza);
        System.assertEquals('Support', req.areaOfNeed);
        System.assertEquals('ACT123', req.codDomainActivity);
        System.assertEquals(true, req.testCoverage);
    }
}
