public with sharing class CaseRelatedListController {
    public CaseRelatedListController() {

    }

    @AuraEnabled
    public static List<Case> getRelatedCases(Id accountId, String status) {
        try {
            String statusCondition;
            if (status == 'Closed') {

                statusCondition = 'AND Status = \'Closed\'';
            } else {

                statusCondition = 'AND Status != \'Closed\'';
            }

            String query = 'SELECT Id, CaseNumber, Activity__c, Detail__c, AreaOfNeed__c, DueDate__c, AssignedTo__c, AssignedTo__r.Name, toLabel(Priority), Area__c, CreatedDate, toLabel(Status), Account.Name, Source__c, Assignee__c '
                            + 'FROM Case '
                            + 'WHERE AccountId = :accountId AND RecordType.DeveloperName = \'AttivitaContatto\' ' + statusCondition;
            List<Case> caseList = Database.query(query);
            
            return caseList;

        } catch (Exception e) {

            throw new AuraHandledException(e.getMessage());
        }
    }
}