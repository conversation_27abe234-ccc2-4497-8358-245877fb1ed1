@isTest
public with sharing class EngineWrapper_V2Test {

    @IsTest
    static void testGetIsKPISetActive() {
        // Simulazione del comportamento del metodo `getIsKPISetActive`
        Test.startTest();
        Boolean isActive = EngineWrapper_V2.getIsKPISetActive();
        Test.stopTest();

        // Asserzioni
        System.assertEquals(false, isActive, 'The method should return false by default if no metadata is configured.');
    }

    @IsTest
static void testEngineSalesPoint() {
    // Creazione di un Account di esempio
    Account agencyAccount = new Account(
        Name = 'Test Agency',
        ExternalId__c = 'Agency123',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agencyAccount;

    // Creazione di un OperatingHours di esempio
    OperatingHours testOperatingHours = new OperatingHours(
        Name = 'Test Operating Hours'
    );
    insert testOperatingHours;

    // Creazione di un ServiceTerritory associato all'account
    ServiceTerritory serviceTerritory = new ServiceTerritory(
        Name = 'Test SalesPoint',
        Tipo__c = 'Agenziale',
        OperatingHoursId = testOperatingHours.Id,
        Agency__c = agencyAccount.Id,
        Street = 'Test Street',
        City = 'Test City',
        State = 'MI',
        PostalCode = '10200',
        Country = 'Italy'
    );
    insert serviceTerritory;

    // Creazione di un KPISet di esempio
    EngineWrapper_V2.KPISet kpiSet = new EngineWrapper_V2.KPISet();
    kpiSet.name = 'Test KPI';
    kpiSet.contactRate = 85.0;

    // Creazione di un EngineAgency di esempio
    EngineWrapper_V2.EngineAgency agency = new EngineWrapper_V2.EngineAgency(
        agencyAccount,
        // true, // discretionality
        // kpiSet,
        // false, // isSortingDiscretionalityOn
        true // isAgency
    );

    // Creazione di un'istanza di EngineSalesPoint utilizzando il costruttore corretto
    EngineWrapper_V2.EngineSalesPoint salesPoint = new EngineWrapper_V2.EngineSalesPoint(
        serviceTerritory,
        // (90.0).doubleValue(), // score
        (5.0).doubleValue(), // distance
        // true, // discretionality
        // kpiSet,
        // false, // isSortingDiscretionalityOn
        // true, // isAgency
        agency // relatedEngineAgency
    );

    // Asserzioni
    System.assertNotEquals(null, salesPoint, 'The EngineSalesPoint instance should not be null.');
    System.assertEquals(serviceTerritory.Id, salesPoint.salespoint, 'The salespoint ID should match the ServiceTerritory ID.');
    // System.assertEquals(90.0, salesPoint.score, 'The score should match the input value.');
    System.assertEquals(5.0, salesPoint.distance, 'The distance should match the input value.');
    // System.assertEquals(true, salesPoint.discretionality, 'The discretionality flag should be true.');
}

@IsTest
static void testGetMacroAreas() {
    // Input valido
    String areasOfNeed = 'Casa;Salute;Mobilità';

    // Esecuzione del metodo
    Test.startTest();
    List<String> macroAreas = EngineWrapper_V2.getMacroAreas(areasOfNeed);
    Test.stopTest();

    // Asserzioni
    System.assertEquals(3, macroAreas.size(), 'The method should return three macro areas.');
    System.assert(macroAreas.contains('Property'), 'The macro area "Property" should be included.');
    System.assert(macroAreas.contains('Welfare'), 'The macro area "Welfare" should be included.');
    System.assert(macroAreas.contains('Motor'), 'The macro area "Motor" should be included.');
}

@IsTest
static void testEngineWrapperInput() {
    // Creazione di un'istanza della classe con il costruttore completo
    EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput(
        'Preventivatore digitale',
        new List<String>{'Agency1', 'Agency2'},
        (45.4642).doubleValue(),
        (9.1900).doubleValue(),
        new List<String>{'Motor', 'Property'},
        'Casa;Mobilità',
        '1990-01-01',
        '12345678901',
        'ABCDEF12G34H567I',
        '20100',
        'Via Roma',
        '10',
        true,
        'UnipolSai'
    );

    // Asserzioni
    System.assertEquals('Preventivatore digitale', input.source, 'The source should match the input value.');
    System.assertEquals(2, input.agenciestoExcludeIds.size(), 'The list of agencies to exclude should have two entries.');
    System.assertEquals(45.4642, input.latitude, 'The latitude should match the input value.');
    System.assertEquals(true, input.useDefaultAgency, 'The useDefaultAgency flag should be true.');
}

@IsTest
static void testEngineWrapperInvocableOutput() {
    // Creazione di un'istanza della classe con il costruttore completo
    EngineWrapper_V2.EngineWrapperInvocableOutput output = new EngineWrapper_V2.EngineWrapperInvocableOutput(
        true,
        'No errors',
        new List<ServiceTerritory>(),
        null,
        null
    );

    // Asserzioni
    System.assertEquals(true, output.isSuccess, 'The isSuccess flag should be true.');
    System.assertEquals('No errors', output.errorMessage, 'The errorMessage should match the input value.');
    System.assertEquals(0, output.selectedAgencies.size(), 'The selectedAgencies list should be empty.');
}

@IsTest
static void testEngineWrapperOutput() {
    // Creazione di un'istanza della classe con il costruttore completo
    EngineWrapper_V2.EngineWrapperOutput output = new EngineWrapper_V2.EngineWrapperOutput(
        true,
        'Prospect',
        new List<EngineWrapper_V2.EngineSalesPoint>(),
        new List<EngineWrapper_V2.EngineExclusionSalesPoint>(),
        new EngineWrapper_V2.EngineSettings(true, (5.0).doubleValue(), 'DefaultAgency', 'Assignment', 3, (10.0).doubleValue(), (80.0).doubleValue())
    );

    // Asserzioni
    System.assertEquals(true, output.isSuccess, 'The isSuccess flag should be true.');
    System.assertEquals('Prospect', output.subjectType, 'The subjectType should match the input value.');
    System.assertEquals(0, output.matchingSalespoints.size(), 'The matchingSalespoints list should be empty.');
    // System.assertNotEquals(null, output.engineSettings, 'The engineSettings should not be null.');
}


@IsTest
static void testEngineSettings() {
    // Creazione di un'istanza della classe
    EngineWrapper_V2.EngineSettings settings = new EngineWrapper_V2.EngineSettings(
        true,
        (5.0).doubleValue(),
        'DefaultAgency',
        'Assignment',
        3,
        (10.0).doubleValue(),
        (80.0).doubleValue()
    );

    // Asserzioni
    System.assertEquals(true, settings.discretionality, 'The discretionality flag should be true.');
    System.assertEquals(5.0, settings.extensionRangeKM, 'The extensionRangeKM should match the input value.');
    System.assertEquals('DefaultAgency', settings.defaultAgency, 'The defaultAgency should match the input value.');
}

@IsTest
static void testEngineError() {
    // Creazione di un'istanza della classe
    EngineWrapper_V2.EngineError error = new EngineWrapper_V2.EngineError('ERROR_CODE');

    // Asserzioni
    System.assertEquals('ERROR_CODE', error.errorKeyword, 'The errorKeyword should match the input value.');
}

@IsTest
static void testEngineExclusionSalesPoint() {

    
    // Creazione di un Account di esempio
    Account agencyAccount = new Account(
        Name = 'Test Agency',
        ExternalId__c = 'Agency123',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agencyAccount;

    // Creazione di un OperatingHours di esempio
    OperatingHours testOperatingHours = new OperatingHours(
        Name = 'Test Operating Hours'
    );
    insert testOperatingHours;

    // Creazione di un ServiceTerritory associato all'account
    ServiceTerritory serviceTerritory = new ServiceTerritory(
        Name = 'Test SalesPoint',
        Tipo__c = 'Agenziale',
        OperatingHoursId = testOperatingHours.Id,
        Agency__c = agencyAccount.Id,
        Street = 'Test Street',
        City = 'Test City',
        State = 'MI',
        PostalCode = '10200',
        Country = 'Italy'
    );
    insert serviceTerritory;

    // Creazione di un KPISet di esempio
    EngineWrapper_V2.KPISet kpiSet = new EngineWrapper_V2.KPISet();
    kpiSet.name = 'Test KPI';
    kpiSet.contactRate = 85.0;

    // Creazione di un EngineAgency di esempio
    EngineWrapper_V2.EngineAgency agency = new EngineWrapper_V2.EngineAgency(
        agencyAccount,
        // true, // discretionality
        // kpiSet,
        // false, // isSortingDiscretionalityOn
        true // isAgency
    );
    // Creazione di un'istanza della classe
    // Creazione di un'istanza di EngineSalesPoint utilizzando il costruttore corretto
    EngineWrapper_V2.EngineSalesPoint salesPoint = new EngineWrapper_V2.EngineSalesPoint(
        serviceTerritory,
        // (90.0).doubleValue(), // score
        (5.0).doubleValue(), // distance
        // true, // discretionality
        // kpiSet,
        // false, // isSortingDiscretionalityOn
        // true, // isAgency
        agency // relatedEngineAgency
    );

    EngineWrapper_V2.EngineExclusionSalesPoint exclusion = new EngineWrapper_V2.EngineExclusionSalesPoint(salesPoint, 'Reason');

    // Asserzioni
    System.assertEquals('Reason', exclusion.exclusionReason, 'The exclusionReason should match the input value.');
    System.assertEquals(salesPoint, exclusion.excludedSalesPoint, 'The excludedSalesPoint should match the input value.');
}


@IsTest
static void testKPISetConstructor() {
    // Creazione di un Account di esempio
    Account agencyAccount = new Account(
        Name = 'Test Agency',
        ExternalId__c = 'Agency123',
        Size__c= 'Grande',
        RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId()
    );
    insert agencyAccount;

    // Creazione di un record KPI__c associato all'agenzia
    KPI__c kpi = new KPI__c(
        Agency__c = agencyAccount.Id,
        ContactRate__c = (85.0).doubleValue(),
        ScoreTotal__c = (100.0).doubleValue()
        // Dimensione__c = 'Large' // Campo obbligatorio
    );
    insert kpi;

    // Creazione di un'istanza di KPISet utilizzando il costruttore
    Test.startTest();
    EngineWrapper_V2.KPISet kpiSet = new EngineWrapper_V2.KPISet(kpi);
    Test.stopTest();

    // Asserzioni
    System.assertNotEquals(null, kpiSet, 'The KPISet instance should not be null.');
    // System.assertEquals('Large', kpiSet.name, 'The name should match the Dimensione__c field of the KPI__c record.');
    System.assertEquals((85.0).doubleValue(), kpiSet.contactRate, 'The contactRate should match the ContactRate__c field of the KPI__c record.');
    // System.assertEquals((100.0).doubleValue(), kpiSet.scoreTotal, 'The scoreTotal should match the ScoreTotal__c field of the KPI__c record.');
}

}