public class GroupHelper {
    public static Map<String, Group> getGroupsByExternalIds(Set<String> externalIds) {
        Map<String, Group> externalIdToGroupMap = new Map<String, Group>();
        List<Group> groupsList =  [SELECT Id, DeveloperName FROM Group WHERE DeveloperName IN :externalIds AND Type = 'Regular'];
        for(Group g : groupsList){
            if(g.DeveloperName != null){
                externalIdToGroupMap.put(g.DeveloperName, g);
            }
        }
        return externalIdToGroupMap;
    }
}