@isTest
private class PolicyWSWrapperTest {
    @isTest
    static void testWrapperInstantiation() {
        PolicyWSWrapper wrapper = new PolicyWSWrapper();
        wrapper.id = '001xx000003DGX1';
        wrapper.agencyCode = 'AG001';
        wrapper.appendixNumber = 'APP123';
        wrapper.archiveNumber = 'ARCH456';
        wrapper.areaOfNeed = 'Area 51';
        wrapper.cip = 'CIP001';
        wrapper.company = 'Test Company';
        wrapper.companyCode = 'TC001';
        wrapper.effectiveDate = System.now();
        wrapper.expirationDate = System.now().addYears(1);
        wrapper.externalId = 'EXT123';
        wrapper.folderId = 'FOL456';
        wrapper.lob1 = 'Auto';
        wrapper.lob2 = 'Home';
        wrapper.lob3 = 'Life';
        wrapper.managementBranchCode = 'MB001';
        wrapper.name = 'Test Policy';
        wrapper.policyBranchCode = 'PB001';
        wrapper.policyName = 'Policy Name';
        wrapper.policyType = 'Standard';
        wrapper.position = 'Manager';
        wrapper.premiumAmount = 1234.56;
        wrapper.premiumFrequency = 'Annual';
        wrapper.referencePolicyNumber = 'REF789';
        wrapper.sourceSystem = 'SystemX';
        wrapper.sourceSystemId = 'SYS123';
        wrapper.status = 'Active';
        wrapper.substatus = 'Pending';

        // Imposta la variabile di test visibile
        PolicyWSWrapper.testCoverage = true;

        // Asserzione semplice per garantire che l'oggetto sia stato creato
        System.assertNotEquals(null, wrapper);
    }
}