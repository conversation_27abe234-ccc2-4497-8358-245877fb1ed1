public with sharing class gestisciAzioniTrattativeController {

    @AuraEnabled(cacheable=true)
    public static List<Opportunity> findOpportunitySelected(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return new List<Opportunity>();
        }
        System.debug('findOpportunity ids: ' + ids);
    
        return [
            SELECT Id, Name,StageName
            FROM Opportunity
            WHERE Id IN :ids and 
            StageName != 'Chiuso'
        ];
    }


     @AuraEnabled(cacheable=true)
    public static StageFlag checkPermissions() {
        StageFlag result = new StageFlag();

       List<User> CurrentUser = [ SELECT id 
                                  FROM User 
                                  WHERE id =: UserInfo.getUserId() 
                                  AND Id IN (SELECT AssigneeId 
                                             FROM PermissionSetAssignment
                                             WHERE PermissionSet.Name = 'RicezioneAssegnazioneLead'
                                            )
       ];

        if(CurrentUser.isEmpty()){
          result.hasPermission = false;
        }else{
          result.hasPermission = true;
        }

        return result;
    }


    @AuraEnabled(cacheable=true)
    public static StageFlag getStatusOpportunity(List<String> ids) {

        StageFlag result = new StageFlag();
        result.hasInGestione = false;
        result.hasInAssegnato = false;
      
        List<opportunity> oppList = [SELECT StageName
            FROM Opportunity
            WHERE Id IN :ids
        ];
        
        for(opportunity opp : oppList){
            if(opp.StageName == 'In gestione'){ 
              result.hasInGestione = true;
            }else if(opp.StageName == 'Assegnato'){
              result.hasInAssegnato = true;
            }

            if(result.hasInGestione && result.hasInAssegnato){
                break;
            }
        }

        return result;
    }

    public class StageFlag{
        @AuraEnabled public Boolean hasInGestione;
        @AuraEnabled public Boolean hasInAssegnato;
        @AuraEnabled public Boolean hasPermission;
    }    
}