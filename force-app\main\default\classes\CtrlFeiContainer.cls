/*
* @cicd_tests CtrlFei_Test
*/
public without sharing class CtrlFeiContainer {

    public class InsurancePolicyWrapper{

        @AuraEnabled public String policyNumber             {get; set;}
        @AuraEnabled public String policyName               {get; set;}
        @AuraEnabled public String federationId             {get; set;}

    }
    
    @AuraEnabled(cacheable=true)
    public static InsurancePolicyWrapper getCurrentInsurancePolicy(String recordId){
        
        User currentUser = [SELECT Id, FederationIdentifier FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];
        InsurancePolicy currentInsurancePolicy = [SELECT Id, Name, PolicyName FROM InsurancePolicy WHERE Id = :recordId LIMIT 1];

        InsurancePolicyWrapper ipw = new InsurancePolicyWrapper();
        ipw.policyNumber = currentInsurancePolicy.Name;
        ipw.policyName = currentInsurancePolicy.PolicyName;
        ipw.federationId = currentUser.FederationIdentifier;

        return ipw;

    }

    public class FeiContainerWrapper{
        @AuraEnabled public List<CtrlFeiUserSelect.NetworkUserWrapper> users = new List<CtrlFeiUserSelect.NetworkUserWrapper>();
        @AuraEnabled public CtrlFeiTabExtension.FeiTabExtensionWrapper extension = new CtrlFeiTabExtension.FeiTabExtensionWrapper();
    }
    
    @AuraEnabled(cacheable=true)
    public static FeiContainerWrapper getWrapper(String permissionSetName, String society){

        FeiContainerWrapper result = new FeiContainerWrapper();

        result.users = CtrlFeiUserSelect.getPicklistUsers(permissionSetName, society);
        result.extension = CtrlFeiTabExtension.getExtensionUrl();

        return result;

    }

    @AuraEnabled
    public static String callIntegrationProcedure(String FEIID, Object feiRequestPayload, String recordId, String UserID, String feiAddressEndpointPayload, String cfcontid, Boolean defaultChecked, String society){

        String result;

        System.debug('@@ '+feiRequestPayload);
        System.debug('@@ '+JSON.serialize(feiRequestPayload));

        try{

            Map<String, Object> ipInput = new Map<String, Object>();

            ipInput.put('FEIID',FEIID);

            /* if(String.isNotBlank(feiRequestPayload)){ */
                ipInput.put('feiRequestPayload',feiRequestPayload);
            /* }else{
                ipInput.put('feiRequestPayload','{}');
            } */

            ipInput.put('recordId',recordId);
            ipInput.put('UserID',UserID);
            
            if(String.isNotBlank(feiAddressEndpointPayload)){
                ipInput.put('feiAddressEndpointPayload',feiAddressEndpointPayload);
            }else{
                ipInput.put('feiAddressEndpointPayload','{}');
            }
            
            ipInput.put('cfcontid',cfcontid);

            Map<String, Object> ipOutput = (Map<String, Object>) omnistudio.IntegrationProcedureService.runIntegrationService('FEI_IntegrationManager', ipInput, null);

            System.debug('output : '+ipOutput);

            result = (String)ipOutput.get('forwardEndpoint');

            if(defaultChecked && String.isNotBlank(society)){

                List<NetworkUser__c> nu = [SELECT Id, Society__c FROM NetworkUser__c WHERE IsActive__c = true AND NetworkUser__c = :UserID LIMIT 1];
                if(!nu.isEmpty()){
                    CtrlUserDefaultRACF.setPreferredNetworkUser(nu[0].Id, nu[0].Society__c);
                }
                
            }
        
        }catch(Exception ex){
            System.debug('Exception IP message: '+ex.getMessage());
            System.debug('Exception IP stack: '+ex.getStackTraceString());
        }

        return result;

    }

}