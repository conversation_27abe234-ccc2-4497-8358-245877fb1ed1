@isTest
private class SinistriHandler_TestClass {

    @isTest
    static void testCreateUrlSinistri() {
        // Prepara l'input simulato
        Map<String, Object> sinistro1 = new Map<String, Object>{
            'numeroSinistro' => '123456'
        };
        Map<String, Object> sinistro2 = new Map<String, Object>{
            'numeroSinistro' => '789012'
        };

        List<Object> sinistriList = new List<Object>{ sinistro1, sinistro2 };

        Map<String, Object> input = new Map<String, Object>{
            'HTTPAction' => sinistriList
        };
        Map<String, Object> inputMap = new Map<String, Object>{
            'input' => input
        };
        Map<String, Object> outputMap = new Map<String, Object>();
        Map<String, Object> optionsMap = new Map<String, Object>();

        // Esegui il metodo
        Test.startTest();
        SinistriHandler sh = new SinistriHandler();
        sh.invokeMethod('createUrlSinistri',inputMap, outputMap, optionsMap);
        Test.stopTest();

        // Verifica l'output
        System.assert(outputMap.containsKey('HTTPAction'), 'OutputMap should contain HTTPAction key');
        List<Object> outputList = (List<Object>) outputMap.get('HTTPAction');
        System.assertEquals(2, outputList.size(), 'Output list should contain 2 elements');

        for (Object obj : outputList) {
            Map<String, Object> sinistro = (Map<String, Object>) obj;
            System.assert(sinistro.containsKey('url'), 'Each sinistro should have a url');
            String url = (String) sinistro.get('url');
            System.assert(url.contains('/flow/FEIQuickActionSinistri'), 'URL should contain the expected path');
            System.assert(url.contains('numeroSinistro='), 'URL should contain numeroSinistro parameter');
        }
    }
}