@isTest
private class ControllerDispCanaliDigTest {

    @testSetup
    static void setupTestData() {
        // Creare un utente di test
        Profile userProfile = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1];
        User testUser = new User(
            FirstName = 'Test',
            LastName = 'User',
            Email = '<EMAIL>',
            Username = '<EMAIL>' + System.currentTimeMillis(),
            Alias = 'tuser',
            ProfileId = userProfile.Id,
            TimeZoneSidKey = 'GMT',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US'
        );
        insert testUser;
        
        OperatingHours op = new OperatingHours(Name = 'TestOp');
        
        insert op;
        // Crea un ServiceResource per l'utente
        ServiceResource serviceResource = new ServiceResource(
            Name = 'Test Service Resource',
            RelatedRecordId = testUser.Id,
            isActive = true,
            Attiva_su_canali_digitali__c = true,
            Attiva_per_clienti_e_prospect__c = 'Clienti e Prospect'
        );
        insert serviceResource;

        // Creare ServiceTerritory e ServiceTerritoryMember
        ServiceTerritory serviceTerritory = new ServiceTerritory(
            Name = 'Test Territory',
            OperatingHoursId= op.Id,
            isActive = true
            
        );
        insert serviceTerritory;

        ServiceTerritoryMember stm = new ServiceTerritoryMember(
            ServiceResourceId = serviceResource.Id,
            ServiceTerritoryId = serviceTerritory.Id,
            OperatingHoursId = op.Id,
            EffectiveStartDate = Date.today()
        );
        insert stm;
        
        Time myTime = Time.newInstance(18, 30, 2, 20);
        
        // Creare TimeSlot
        TimeSlot timeSlot = new TimeSlot(
            StartTime = myTime,
            EndTime = myTime.addHours(1),
            DayOfWeek = 'Monday',
            OperatingHoursId = op.Id
        );
        insert timeSlot;
    }

    @isTest
    static void testGetDispCanaliDigitali() {
        // Recupera un record ServiceResourceId esistente
        ServiceResource serviceResource = [SELECT Id FROM ServiceResource LIMIT 1];

        // Esegui il metodo da testare
        Test.startTest();
        Map<String, Object> result = ControllerDispCanaliDig.getDispCanaliDigitali(serviceResource.Id);
        Test.stopTest();

    }
}