global with sharing class ReportQueryController implements Metadata.DeployCallback, Callable { 

    public Object call(String action, Map<String, Object> args) {
        switch on action {
            when 'getReportOpportunities' {
                System.debug('# args: ' + args);
                Map<String, Object> input = (Map<String, Object>) args.get('input');
                String query = createQuery(input);
                List<Opportunity> reportOpportunities = Database.query(query);
                System.debug('# reportOpportunities.size(): ' + reportOpportunities.size() + ' => ' + reportOpportunities);
                Map<String, Object> output = (Map<String, Object>) args.get('output');
                output.put('result', reportOpportunities);
                return reportOpportunities;
            }
            when else {
                System.debug('Method not implemented');
            }
        }
        return null;
    }

    // private String getMostRecentQuery() {
    //     List<ReportQuery__mdt> queries = [SELECT Query__c, CreatedDate__c FROM ReportQuery__mdt ORDER BY CreatedDate__c DESC];

    //     String query;
    //     if(!queries.isEmpty()) {
    //         query = queries[0].Query__c;
    //     }

    //     return query;
    // }

    private String createQuery(Map<String, Object> input) {
        String query = 'SELECT Id,  AccountNameFormula__c, JourneyStep__c, AreasOfNeedFormula__c, ' +
        'Amount, AccountValusFormula__c, CreatedDate, WorkingSLAExpiryDate__c, TemperatureFormula__c, TakenInChargeSLARemainingDays__c ' + 
        'FROM Opportunity WHERE StageName = \'Assegnato\'';
        String orderByQuery = ' ORDER BY TemperatureFormula__c, TakenInChargeSLARemainingDays__c';
        
        // Get URL Parameters
        String amount = input.containsKey('amount') ? (String) input.get('amount') : null;
        String clientType = input.containsKey('clientType') ? (String) input.get('clientType') : null;
        System.debug('# amount: ' + amount);
        System.debug('# clientType: ' + clientType);
        
        // Check URL Parameters and construct query
        if(String.isNotBlank(amount) && !amount.contains('{Params.')) {
            query += ' AND Amount = ' + amount;
        }
        /*
        if(String.isNotBlank(clientType) && !clientType.contains('{Params.')) {
            query += ' AND AccountRecordTypeFormula__c = \'' + clientType + '\'';
        }*/
        
        // Add ordering to the query
        query += orderByQuery;
        System.debug('# query: ' + query);

        // saveQuery(query);
        return query;
    }

    // private static void saveQuery(String query) {
    //     DateTime currentDatetime = DateTime.now();
    //     String unixTimeStamp = String.valueOf(currentDatetime.getTime());
    //     Metadata.CustomMetadata customMetadata =  new Metadata.CustomMetadata();
    //     customMetadata.fullName = 'ReportQuery.Test1';
    //     customMetadata.label = 'Test1';

    //     /* Create the Object of CustomMetadataValue */
    //     Metadata.CustomMetadataValue queryField = new Metadata.CustomMetadataValue();
    //     queryField.field = 'Query__c';
    //     queryField.value = query;
    //     customMetadata.values.add(queryField);

    //     Metadata.CustomMetadataValue createdDateField = new Metadata.CustomMetadataValue();
    //     createdDateField.field = 'CreatedDate__c';
    //     createdDateField.value = currentDatetime;
    //     customMetadata.values.add(createdDateField);

    //     // Create deployment containter and deploy metadata
    //     Metadata.DeployContainer metadataContainer = new Metadata.DeployContainer();
    //     metadataContainer.addMetadata(customMetadata);
    //     UrlParserController callback = new UrlParserController();
    //     Id jobId = Metadata.Operations.enqueueDeployment(metadataContainer, callback);
    // }

    public void handleResult(Metadata.DeployResult result,
                             Metadata.DeployCallbackContext context) {
        if (result.status == Metadata.DeployStatus.Succeeded) {
            System.debug('The metadata deployment was successful.');
        } else {
            System.debug('The metadata deployment was not successful.');
        }
    }
}