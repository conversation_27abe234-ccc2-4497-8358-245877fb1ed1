global with sharing class OmniStudioController  implements System.Callable, omnistudio.VlocityOpenInterface { //NOPMD
    
    public Object call(String action, Map<String, Object> args) {
        Map<String, Object> input = (Map<String, Object>)args.get('input');
        Map<String, Object> output = (Map<String, Object>)args.get('output');
        Map<String, Object> options = (Map<String, Object>)args.get('options');

        return invokeMethod(action, input, output, options);
    }

    public Boolean invokeMethod(String methodName, Map<String,Object> inputs, Map<String,Object> output, Map<String,Object> options) { //NOPMD
        Boolean result = true;
        try{
            if(methodName.equals('getTimelineDatas')){
                
                
                List<TimelineData> timelineDataList = this.getTimelineDatas((String)inputs.get('recordId'));
                System.debug(timelineDataList.get(0));
                if (timelineDataList != null && !timelineDataList.isEmpty()) {
                    List<Map<String, Object>> outputList = new List<Map<String, Object>>();
                    for (TimelineData timelineData : timelineDataList) {
                        Map<String, Object> recordOutput = new Map<String, Object>();
                        setOutputFields(recordOutput, timelineData);
                        outputList.add(recordOutput);
                    }
                    
                    output.put('taskSelected', true);
                    output.put('contactHistorySelected', true);
                    output.put('records', outputList);
                }
            }
        } catch(Exception e){       
            result = false;         
        }

        return result;          
    }

    private static Object setOutputFields(Map<String,Object> output, TimelineData data) {
        output.put('isExpanded', data.isExpanded);
        output.put('subject', data.subject);
        output.put('actorName', data.actorName);
        output.put('createdDate', data.createdDate);
        output.put('description', data.description);

        return output;
    }

    // ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Timeline ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    public class TimelineData {
        public Boolean isExpanded { get; set; }
        public String subject { get; set; }
        public String actorName { get; set; }
        public String createdDate { get; set; }
        public String description { get; set; }

        public TimelineData(Boolean isExpanded, String subject, String actorName, String createdDate, String description) { //NOPMD
            this.isExpanded = isExpanded;
            this.subject = subject;
            this.actorName = actorName;
            this.createdDate = createdDate;
            this.description = description;
        }
    }

    public List<TimelineData> getTimelineDatas(String recordId) {
        Opportunity opp = getOpportunity(recordId);

        List<TimelineData> tempTimelineDatas = new List<TimelineData>();

        List<TimelineData> timelineDataFromTasks = convertTaskToTimelineData(opp);
        for (TimelineData timelineData : timelineDataFromTasks) {
            tempTimelineDatas.add(timelineData);
        }

        List<TimelineData> timelineDataFromContactHistory = convertContactHistoryToTimelineData(opp);
        for (TimelineData timelineData : timelineDataFromContactHistory) {
            tempTimelineDatas.add(timelineData);
        }

        return (!tempTimelineDatas.isEmpty()) ? sortData(tempTimelineDatas) : null;
    }

    private List<TimelineData> sortData(List<TimelineData> dataList) {
        List<DateTime> dateTimeList = new List<DateTime>();
        for (TimelineData data : dataList) {
            try {
                dateTimeList.add(DateTime.valueOfGmt(data.createdDate + ':00'));
            } catch (Exception e) {
                System.debug('Invalid date format: ' + data.createdDate);
            }
        }
        dateTimeList.sort();
        System.debug(dateTimeList);

        List<TimelineData> sortedTimelineDatas = new List<TimelineData>();
        for (DateTime dt : dateTimeList) {
            for (TimelineData data : dataList) {
                if (DateTime.valueOfGmt(data.createdDate + ':00') == (dt)) {
                    sortedTimelineDatas.add(data);
                    break;
                }
            }
        }
        System.debug(sortedTimelineDatas);

        return (!dataList.isEmpty()) ? sortedTimelineDatas : null;
    }

    private List<TimelineData> convertTaskToTimelineData(Opportunity opp) {
        List<TimelineData> dataList = new List<TimelineData>();

        List<Task> tasks = getTasks(opp.Id);
        
        for (Task task : tasks) {
            TimelineData data = new TimelineData(false, task.Subject, getUserName(task.OwnerId), task.CreatedDateFormula__c, task.Description);
            dataList.add(data);
        }
        return (!dataList.isEmpty()) ? dataList : null;
    }

    private List<TimelineData> convertContactHistoryToTimelineData(Opportunity opp) {
        List<TimelineData> dataList = new List<TimelineData>();

        List<ContactHistory__c> contactHistoryList = getContactHistory(opp.Id);
        
        for (ContactHistory__c contactHistory : contactHistoryList) {
            TimelineData data = new TimelineData(false, contactHistory.Name, contactHistory.Actor__c, contactHistory.CreatedDate__c, contactHistory.Description__c);
            dataList.add(data);
        }
        return (!dataList.isEmpty()) ? dataList : null;
    }

    private static Opportunity getOpportunity(String recordId) {
        try{
            Id currentId = Id.valueOf(recordId);
            List<Opportunity> oppList = new List<Opportunity>();
            oppList = [ SELECT Id //NOPMD
                        FROM Opportunity
                        WHERE Id =: currentId
                        LIMIT 1 ];

            return (!oppList.isEmpty()) ? oppList.get(0) : null;
        }
        catch(Exception e){
            System.debug('An error has occurred: ' + e.getMessage());
            return null;
        }
    }

    private static List<Task> getTasks(String opportunityId) {
        try {
            Id currentId = Id.valueOf(opportunityId);
            List<Task> taskList = new List<Task>();
            taskList = [SELECT Id, CreatedDateFormula__c, Description, OwnerId, Subject, WhatId //NOPMD
                        FROM Task
                        WHERE WhatId = :currentId];

            return (!taskList.isEmpty()) ? taskList : null;
        } catch (Exception e) {
            System.debug('An error has occurred: ' + e.getMessage());
            return null;
        }
    }

    private static String getUserName(String ownerId) {
        try {
            Id currentId = Id.valueOf(ownerId);
            List<User> userList = new List<User>();
            userList = [SELECT Id, Name //NOPMD
                        FROM User
                        WHERE Id = :currentId];

            return (!userList.isEmpty()) ? userList.get(0).Name : null;
        } catch (Exception e) {
            System.debug('An error has occurred: ' + e.getMessage());
            return null;
        }
    }

    private static List<ContactHistory__c> getContactHistory(String opportunityId) {
        try {
            Id currentId = Id.valueOf(opportunityId);
            List<ContactHistory__c> contactHistoryList = new List<ContactHistory__c>();
            contactHistoryList = [SELECT Id, Actor__c, CreatedById, CreatedDate__c, Description__c, Name, Opportunity__c //NOPMD
                        FROM ContactHistory__c
                        WHERE Opportunity__c = :currentId];

            return (!contactHistoryList.isEmpty()) ? contactHistoryList : null;
        } catch (Exception e) {
            System.debug('An error has occurred: ' + e.getMessage());
            return null;
        }
    }

    // -----------------------------------------------------------------------------
}
