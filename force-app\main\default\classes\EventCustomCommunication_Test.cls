@isTest
public with sharing class EventCustomCommunication_Test {
    
    @isTest
    static void test_event_delete(){

        Test.startTest();

        try{
            Event evt = new Event();
            evt.StartDateTime = DateTime.now();
            evt.EndDateTime = DateTime.now();
            evt.Subject = 'TEST';
            evt.Modalit_di_esecuzione_incontro__c = 'Telefonico';
            insert evt;

            delete evt;
        }catch(Exception ex){
            System.debug('Exception --> '+ex.getStackTraceString());
        }

        Test.stopTest();

    }

}