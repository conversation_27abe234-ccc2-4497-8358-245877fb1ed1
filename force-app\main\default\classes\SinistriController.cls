public without sharing class SinistriController {
    @AuraEnabled
    public static Map<String, Object> getSinistriFromIntegrationProcedure(String accountId){
        Id currentUserId = UserInfo.getUserId();
        Map<String, Object> ipInput = new Map<String, Object>{'recordId' => accountId, 'userId' => currentUserId};
        Map<String, Object> ipOutput = (Map<String, Object>) omnistudio.IntegrationProcedureService.runIntegrationService('Sinistri_GetData', ipInput, null);   
         return ipOutput;
    }
}