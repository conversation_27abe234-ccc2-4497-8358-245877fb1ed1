global without sharing class SinistriHandler implements System.Callable  {

    global Object call(String action, Map<String, Object> args) {
        Map<String, Object> outputMap = new Map<String, Object>();
        invokeMethod(action, args, outputMap, new Map<String, Object>());
        return outputMap;
    }

    public boolean invokeMethod(String methodName, Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        if (methodName.equals('createUrlSinistri')) {
            SinistriHandler.createUrlSinistri(inputMap, outMap, options);
        }
        return true;
    }

    public static void createUrlSinistri(Map<String, Object> inputMap, Map<String, Object> outputMap, Map<String, Object> optionsMap) {
        Map<String,Object> input = inputMap.containsKey('input') ? (Map<String,Object>)inputMap.get('input') : null;
        Object obj = input.containsKey('HTTPAction') ? input.get('HTTPAction') : null;
        System.debug(obj); 

        List<Object> sinistriList = new List<Object>();
        List<Object> sinistriListOutput = new List<Object>();
        if(obj != null){
            sinistriList = (List<Object>) obj;
            for(Object sinistro : sinistriList){
                System.debug('inizio '+ sinistro); 
                Map<String,Object> sinistroMap = (Map<String,Object>)sinistro;
                sinistroMap.put('url','/flow/FEIQuickActionSinistri?FEIID=SXCC.INTERROGAZIONE&numeroSinistro=' + sinistroMap.get('numeroSinistro'));
                sinistriListOutput.add(sinistroMap);
                System.debug('fine '+ sinistroMap.get('url')); 
            }
        }
        outputMap.put('HTTPAction', sinistriListOutput);
    }

}