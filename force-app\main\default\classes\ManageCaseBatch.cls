/**
 * @description This Batch create a record share of Case
 * <AUTHOR>
 * @since       30.04.2025
 * @testClass   ManageCaseBatchTest
 */
public with sharing class ManageCaseBatch implements Database.Batchable<SObject>, Database.Stateful {
    Date dt;

    public ManageCaseBatch() {
        this.dt = null;
    }

    public ManageCaseBatch(Object condition) {
        String stringDate = condition?.toString();
        try {
            this.dt = Date.valueOf(stringDate);
            return;
        } catch (Exception ex) {
        }
        try {
            this.dt = Date.parse(stringDate);
            return;
        } catch (Exception ex) {
        }
    }

    public Database.QueryLocator start(Database.BatchableContext bc) {
        if (dt == null) {
            return Database.getQueryLocator(
                [
                    SELECT Id, AccountId, Agency__c, AssignedTo__c, AssignedGroup__c, isSetRef__c
                    FROM Case
                    WHERE LastModifiedDate >= YESTERDAY
                ]
            );
        } else {
            return Database.getQueryLocator(
                [
                    SELECT Id, AccountId, Agency__c, AssignedTo__c, AssignedGroup__c, isSetRef__c
                    FROM Case
                    WHERE LastModifiedDate >= :this.dt
                ]
            );
        }
    }

    public void execute(Database.BatchableContext bc, List<SObject> scope) {
        try {
            ConiAssignmentServiceBatch.performShares(scope, new Map<Id, SObject>(scope));
        } catch (Exception ex) {
            System.debug('***** ERROR *****');
            System.debug('* getMessage: ' + ex.getMessage() + ' *');
            System.debug('* getStackTraceString: ' + ex.getStackTraceString() + ' *');
            System.debug('***** ERROR *****');
        }
    }

    public void finish(Database.BatchableContext bc) {
    }
}
