({
    callExtensionUrl: function(component) {
        var action = component.get("c.getExtensionUrl");

        action.setCallback(this, function(response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var result = response.getReturnValue();

                if (result) {
                    component.set("v.keepAliveURL", result.keepAliveURL);
                    component.set("v.keepAliveTimeoutMS", result.keepAliveTimeoutMS);

                    // Per test:
                    // component.set("v.keepAliveTimeoutMS", 10000);

                    this.checkInitialLoad(component);

                    setInterval($A.getCallback(() => {
                        try {
                            let lastDateTime = JSON.parse(localStorage.getItem("LastActivityDateTime"));
                            let innerDateNow = Date.now();
                            let timeout = component.get("v.keepAliveTimeoutMS");
                            const innerURL = new URL(component.get("v.keepAliveURL"));

                            if ((innerDateNow - lastDateTime) <= timeout) {
                                window.postMessage({ type: "OPEN_BACKGROUND_TAB", url: component.get("v.keepAliveURL"), hostname: innerURL.hostname }, "*");
                            }
                        } catch (err) {
                            console.error('Error in interval:', err);
                        }
                    }), component.get("v.keepAliveTimeoutMS"));
                }
            } else {
                console.error("Error calling getExtensionUrl:", response.getError());
            }
        });

        $A.enqueueAction(action);
    },

    checkInitialLoad: function(component) {
        try {
            let lastDateTime = JSON.parse(localStorage.getItem("LastActivityDateTime"));
            let now = Date.now();
            let timeout = component.get("v.keepAliveTimeoutMS");
            const innerURL = new URL(component.get("v.keepAliveURL"));

            if ((now - lastDateTime) <= timeout) {
                window.postMessage({ type: "OPEN_BACKGROUND_TAB", url: component.get("v.keepAliveURL"), hostname: innerURL.hostname }, "*");
            }
        } catch (error) {
            console.error('Error in checkInitialLoad:', error);
        }
    },

    handlerExecuteKeepAlive: function() {
        try {
            localStorage.setItem("LastActivityDateTime", JSON.stringify(Date.now()));
        } catch (e) {
            console.error("Error saving to localStorage:", e);
        }
    }
})