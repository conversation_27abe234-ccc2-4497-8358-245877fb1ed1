public class PolicyKey {
        public String companyCode;
        public String motherAgencyCode;
        public String agencyCode;
        public String branchCode;
        public String policyNumber;
        public String leo;
        public String businessKey;

    public PolicyKey(String companyCode,String motherAgencyCode, String agencyCode, String branchCode, String policyNumber, String leo, String businessKey) {
        this.companyCode = companyCode;
        this.motherAgencyCode = motherAgencyCode;
        this.agencyCode = agencyCode;
        this.branchCode = branchCode;
        this.policyNumber = policyNumber;
        this.leo = leo;
        this.businessKey = businessKey;
    }

    public PolicyKey(String policyNumber, String leo, String businessKey) {
        this.policyNumber = policyNumber;
        this.leo = leo;
        this.businessKey = businessKey;
    }

    public static PolicyKey fromBusinessKey(String businessKey, String leo) {
        if (String.isBlank(businessKey)) return null;

        if (businessKey.length() != 15 && businessKey.length() != 23) {
            throw new IllegalArgumentException('Lunghezza businessKey non supportata: ' + businessKey);
        }

        try {
            System.debug('&& :' + businessKey.length() + ' : '+ leo);
            if(businessKey.length() == 15){
                System.debug('&& :' + businessKey + ' : '+ leo);
                String policyNumber = businessKey;
                return new PolicyKey(policyNumber, leo, businessKey);
            }else if(businessKey.length() == 23){
                String companyCode = businessKey.substring(0, 1);
                String motherAgencyCode = businessKey.substring(1, 6);
                String agencyCode = businessKey.substring(6, 11);
                String branchCode = businessKey.substring(11, 14);
                String policyNumber = businessKey.substring(14);

                return new PolicyKey(companyCode, motherAgencyCode, agencyCode, branchCode, policyNumber, leo, businessKey);
            }
        } catch (Exception e) {
            System.debug('Errore nel parsing della businessKey: ' + businessKey + ' - ' + e.getMessage());
        }
        return null;
    }

    public String toSoqlCondition() {
        if (businessKey.length() == 15) {
            return '(ReferencePolicyNumber = \'' + String.escapeSingleQuotes(policyNumber) + '\')';
        } else if (businessKey.length() == 23) {
            return '(CompanyCode__c = \'' + String.escapeSingleQuotes(companyCode) + '\'' +
                ' AND MotherAgencyCode__c = \'' + String.escapeSingleQuotes(motherAgencyCode) + '\'' +
                ' AND AgencyCode__c = \'' + String.escapeSingleQuotes(agencyCode) + '\'' +
                ' AND PolicyBranchCode__c = \'' + String.escapeSingleQuotes(branchCode) + '\'' +
                ' AND ReferencePolicyNumber = \'' + String.escapeSingleQuotes(policyNumber) + '\')';
        } else {
            throw new IllegalArgumentException('Unsupported businessKey length: ' + businessKey);
        }
    }

    public Boolean matches(InsurancePolicy policy) {
        if (policy == null) return false;

        if (businessKey.length() == 15) {
            return policy.ReferencePolicyNumber == policyNumber;
        } else if (businessKey.length() == 23) {
            return policy.CompanyCode__c == companyCode &&
                policy.MotherAgencyCode__c == motherAgencyCode &&
                policy.AgencyCode__c == agencyCode &&
                policy.PolicyBranchCode__c == branchCode &&
                policy.ReferencePolicyNumber == policyNumber;
        } else {
            return false;
        }
    }

    public String getReturnKey() {
        return leo + '-' + businessKey;
    }
    
    public class AssignmentContext {
        public Case c;
        public Map<String, String> cipMap;
        public Map<String, Id> cipToGroupId;
        
        public AssignmentContext(Case c, Map<String, String> cipMap, Map<String, Id> cipToGroupId) {
            this.c = c;
            this.cipMap = cipMap;
            this.cipToGroupId = cipToGroupId;
        }
    }

    public class AssignmentRuleMaps {
        public Map<String, String> cipMapR01R07;
        public Map<String, Id> cipToGroupIdR01R07;
        public Map<String, String> cipMapR02R08;
        public Map<String, Id> cipToGroupIdR02R08;

        public AssignmentRuleMaps(
            Map<String, String> cipMapR01R07,
            Map<String, Id> cipToGroupIdR01R07,
            Map<String, String> cipMapR02R08,
            Map<String, Id> cipToGroupIdR02R08
        ) {
            this.cipMapR01R07 = cipMapR01R07;
            this.cipToGroupIdR01R07 = cipToGroupIdR01R07;
            this.cipMapR02R08 = cipMapR02R08;
            this.cipToGroupIdR02R08 = cipToGroupIdR02R08;
        }
    }

    public class AssignmentContextData {
        public Map<String, Id> cipToGroupId;
        public Map<String, User> userMap;
        public AssignmentRuleMaps ruleMaps;

        public AssignmentContextData(
            Map<String, Id> cipToGroupId,
            Map<String, User> userMap,
            AssignmentRuleMaps ruleMaps
        ) {
            this.cipToGroupId = cipToGroupId;
            this.userMap = userMap;
            this.ruleMaps = ruleMaps;
        }
    }

    public class CaseClassificationResult {
        public Set<String> cipSet = new Set<String>();
        public Set<String> fiscalCodes = new Set<String>();
        public List<Case> r01r07Cases = new List<Case>();
        public List<Case> r02r08Cases = new List<Case>();
        public List<Case> casesWithPolicy = new List<Case>();
    }


}