@isTest
private with sharing class ManageAccountAccountRelationBatchTest {
    @testSetup
    static void setup() {
        // Crea dati di test necessari per i test
        Account account1 = new Account(Name = 'Test Agency 1', RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId());
        insert account1;

        Account account2 = new Account(Name = 'Test Society 1', RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId(), ExternalId__c = 'PippoGroup');
        insert account2;

        Account account3 = new Account(Name = 'Test Society 2', RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId(), ExternalId__c = 'R_PippoGroup');
        insert account3;

        // Crea un record di ruolo (supponendo che l'oggetto sia FinServ__ReciprocalRole__c)
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(Name = 'Agenzia', FinServ__InverseRole__c = 'Compagnia');
        insert role;

        // Crea una relazione esistente tra Agenzia e Compagnia
        FinServ__AccountAccountRelation__c existingRelation = new FinServ__AccountAccountRelation__c(FinServ__Account__c = account1.Id, FinServ__RelatedAccount__c = account2.Id, FinServ__Role__c = role.Id);
        FinServ__AccountAccountRelation__c existingRelation2 = new FinServ__AccountAccountRelation__c(FinServ__Account__c = account1.Id, FinServ__RelatedAccount__c = account2.Id, FinServ__Role__c = role.Id, RecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AccountSociety').getRecordTypeId(), Cauzioni__c = 'Test');
        FinServ__AccountAccountRelation__c existingRelation3 = new FinServ__AccountAccountRelation__c(FinServ__Account__c = account1.Id, FinServ__RelatedAccount__c = account2.Id, FinServ__Role__c = role.Id, RecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AccountAgency').getRecordTypeId(), Cauzioni__c = 'Test');
        insert new List<FinServ__AccountAccountRelation__c>{ existingRelation, existingRelation2, existingRelation3 };
        Group pippoGroup = new Group(Name = 'PippoGroup', DeveloperName = 'PippoGroup');
        insert pippoGroup;
        Group r_pippoGroup = new Group(Name = 'R_PippoGroup', DeveloperName = 'R_PippoGroup');
        insert r_pippoGroup;
    }

    @isTest
    static void testBatchWithoutInputParameter() {
        FinServ__AccountAccountRelation__c aar = [
            SELECT Id
            FROM FinServ__AccountAccountRelation__c
            WHERE RecordTypeId = :Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AccountSociety').getRecordTypeId()
            LIMIT 1
        ];
        aar.Cauzioni__c = 'Test2';
        update aar;
        Test.startTest();
        Database.executeBatch(new ManageAccountAccountRelationBatch());
        Test.stopTest();
    }

    @isTest
    static void testBatchWithInputParameter() {
        FinServ__AccountAccountRelation__c aar = [
            SELECT Id
            FROM FinServ__AccountAccountRelation__c
            WHERE RecordTypeId = :Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AccountAgency').getRecordTypeId()
            LIMIT 1
        ];
        aar.Cauzioni__c = 'Test2';
        update aar;
        Test.startTest();
        Database.executeBatch(new ManageAccountAccountRelationBatch(Date.today()));
        Test.stopTest();
    }

    @isTest
    static void testBatchWithBadInputParameter() {
        Test.startTest();
        Database.executeBatch(new ManageAccountAccountRelationBatch(null));
        Test.stopTest();
    }

    @isTest
    static void testBatchWithAllInputParamteres() {
        Test.startTest();
        Database.executeBatch(new ManageAccountAccountRelationBatch(Date.today(), 'AccountSociety', new List<String>()));
        Test.stopTest();
    }
}
