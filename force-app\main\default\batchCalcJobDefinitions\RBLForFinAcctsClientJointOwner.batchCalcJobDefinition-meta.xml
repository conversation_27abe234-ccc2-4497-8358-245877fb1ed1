<?xml version="1.0" encoding="UTF-8"?>
<BatchCalcJobDefinition xmlns="http://soap.sforce.com/2006/04/metadata">
    <aggregates>
        <fields>
            <aggregateFunction>Sum</aggregateFunction>
            <alias>BalanceSum</alias>
            <sourceFieldName>FinServ_Balance_c</sourceFieldName>
        </fields>
        <groupBy>FinServ_JointOwner_c</groupBy>
        <label>aggregate_FinServ_Balance_c</label>
        <name>aggregate_FinServ_Balance_c</name>
        <sourceName>FinServ_FinancialAccount_c</sourceName>
    </aggregates>
    <datasources>
        <fields>
            <alias>Id</alias>
            <dataType>Text</dataType>
            <name>Id</name>
        </fields>
        <fields>
            <alias>Name</alias>
            <dataType>Text</dataType>
            <name>Name</name>
        </fields>
        <label>Account</label>
        <name>Account</name>
        <sourceName>Account</sourceName>
        <type>StandardObject</type>
    </datasources>
    <datasources>
        <fields>
            <alias>FinServ_Balance_c</alias>
            <dataType>Numeric</dataType>
            <name>FinServ__Balance__c</name>
        </fields>
        <fields>
            <alias>FinServ_JointOwner_c</alias>
            <dataType>Text</dataType>
            <name>FinServ__JointOwner__c</name>
        </fields>
        <label>FinServ_FinancialAccount_c</label>
        <name>FinServ_FinancialAccount_c</name>
        <sourceName>FinServ__FinancialAccount__c</sourceName>
        <type>StandardObject</type>
    </datasources>
    <description>Client-level aggregation of all Financial Account balances where Client is Joint Owner on the Financial Account. One Joint Owner only.</description>
    <executionPlatformType>CRMA</executionPlatformType>
    <isTemplate>false</isTemplate>
    <joins>
        <fields>
            <alias>BalanceSum</alias>
            <sourceFieldName>BalanceSum</sourceFieldName>
            <sourceName>aggregate_FinServ_Balance_c</sourceName>
        </fields>
        <fields>
            <alias>Id</alias>
            <sourceFieldName>Id</sourceFieldName>
            <sourceName>Account</sourceName>
        </fields>
        <joinKeys>
            <primarySourceFieldName>Id</primarySourceFieldName>
            <secondarySourceFieldName>FinServ_JointOwner_c</secondarySourceFieldName>
        </joinKeys>
        <label>Account_aggregate_FinServ_Balance_c</label>
        <name>Account_aggregate_FinServ_Balance_c</name>
        <primarySourceName>Account</primarySourceName>
        <secondarySourceName>aggregate_FinServ_Balance_c</secondarySourceName>
        <type>LeftOuter</type>
    </joins>
    <label>RBLForFinAcctsClientJointOwner</label>
    <processType>DataProcessingEngine</processType>
    <status>Inactive</status>
    <writebacks>
        <fields>
            <runtimeParameter>false</runtimeParameter>
            <sourceFieldName>Id</sourceFieldName>
            <targetFieldName>Id</targetFieldName>
        </fields>
        <fields>
            <runtimeParameter>false</runtimeParameter>
            <sourceFieldName>BalanceSum</sourceFieldName>
            <targetFieldName>FinServ__TotalFinAcctsJointOwner__c</targetFieldName>
        </fields>
        <isChangedRow>false</isChangedRow>
        <label>RBLForFinAcctsClientJointOwnerExport</label>
        <name>RBLForFinAcctsClientJointOwnerExport</name>
        <operationType>Update</operationType>
        <sourceName>Account_aggregate_FinServ_Balance_c</sourceName>
        <storageType>sObject</storageType>
        <targetObjectName>Account</targetObjectName>
        <writebackSequence>1</writebackSequence>
        <writebackUser>0057Q00000A7swHQAR</writebackUser>
    </writebacks>
</BatchCalcJobDefinition>
