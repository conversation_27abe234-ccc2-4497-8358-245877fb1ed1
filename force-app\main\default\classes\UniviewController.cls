/***************************************************************************************************************************************************
* <AUTHOR>
* @description    UniviewController class is responsible for retrieving Uniview definition settings based on a given key. The class contains methods
*                 for querying metadata and a nested class representing the Uniview definition structure. It handles exceptions and supports unit testing.
* @date           2024-01-29
****************************************************************************************************************************************************/
public with sharing class UniviewController {

    // A list to hold test metadata for unit tests. This list is marked as @TestVisible to allow test methods to set its value.
    @TestVisible private static List<UniviewDefinition__mdt> testUniviewDefinitionMetadata;
    
    /******************************************************************************************
    * @description  Retrieves Uniview definition settings based on the provided Uniview key. Queries 
    *               the metadata for the specific key and constructs a UniviewDefinition object.
    * @param        univiewKey - The key used to identify the specific Uniview definition metadata.
    * @return       UniviewDefinition - An object containing the Uniview settings.
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static UniviewDefinition getUniviewDefinition(String univiewKey) {
        try {
            List<UniviewDefinition__mdt> univiewDefinitionMetadata = [
                SELECT MaximumUserPreferences__c, OuterboundQuery__c, RefreshInterval__c, Object__c, 
                PageSize__c, ObjectPlural__c, ScopingRule__c, RecordTypes__c, RefreshonFocus__c, ShowPreferencesButtons__c
                FROM UniviewDefinition__mdt
                WHERE UniviewKey__c =: univiewKey
                WITH USER_MODE
            ];
            if(Test.isRunningTest()) {
                univiewDefinitionMetadata = testUniviewDefinitionMetadata;
            }

            UniviewDefinition univiewSettings;            
            if(!univiewDefinitionMetadata.isEmpty()) {
                univiewSettings = new UniviewDefinition(
                    (Integer) univiewDefinitionMetadata[0].MaximumUserPreferences__c,
                    (Integer) univiewDefinitionMetadata[0].RefreshInterval__c,
                    univiewDefinitionMetadata[0].OuterboundQuery__c,
                    univiewDefinitionMetadata[0].Object__c,
                    univiewDefinitionMetadata[0].ObjectPlural__c,
                    (Integer) univiewDefinitionMetadata[0].PageSize__c,
                    univiewDefinitionMetadata[0].ScopingRule__c,
                    univiewDefinitionMetadata[0].RecordTypes__c,
                    univiewDefinitionMetadata[0].RefreshonFocus__c,
                    univiewDefinitionMetadata[0].ShowPreferencesButtons__c
                );
            }

            return univiewSettings;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    // Inner class representing the Uniview definition structure. Contains fields that store various Uniview settings and a constructor to initialize these fields.
    public class UniviewDefinition {
        @AuraEnabled
        public Integer maximumUserPreferences {get;set;}
        @AuraEnabled
        public Integer refreshInterval {get;set;}
        @AuraEnabled
        public String outerboundQuery {get;set;}
        @AuraEnabled
        public String objectType {get;set;}
        @AuraEnabled
        public String objectPlural {get;set;}
        @AuraEnabled
        public Integer pageSize {get;set;}
        @AuraEnabled
        public Boolean scopingRule {get;set;}
        @AuraEnabled
        public String recordTypes {get;set;}
        @AuraEnabled
        public Boolean refreshonFocus {get;set;}
        @AuraEnabled
        public Boolean showPreferencesButtons {get;set;}

        // Constructor to initialize the UniviewDefinition object with provided values.
        public UniviewDefinition(
            Integer maximumUserPreferences, 
            Integer refreshInterval, 
            String outerboundQuery,
            String objectType,
            String objectPlural,
            Integer pageSize,
            Boolean scopingRule,
            String recordTypes,
            Boolean refreshonFocus,
            Boolean showPreferencesButtons
        ) {
            this.maximumUserPreferences = maximumUserPreferences;
            this.refreshInterval = refreshInterval;
            this.outerboundQuery = outerboundQuery;
            this.objectType = objectType;
            this.objectPlural = objectPlural;
            this.pageSize = pageSize;
            this.scopingRule = scopingRule;
            this.recordTypes = recordTypes;
            this.refreshonFocus = refreshonFocus;
            this.showPreferencesButtons = showPreferencesButtons;
        }  
    }
}
