/*
* @description Utility class to handle UserProvisioning
* @cicd_tests UserProvisioningHandler_Test
*/
public class UserProvisioningUtils {
    
    /*
    * @description This method is called to retrieve Network Users (UNIPOL User) related to a specific Fiscal Code
    * @param userCF String of the fiscal code used to retrieve the list of Network User
    * @return List<String> List of Network User
    */
    public static List<String> getRelatedNetworkUser(String userCF){
        
        List<NetworkUser__c> users = [SELECT Id, NetworkUser__c, Agency__c, Agency__r.AgencyCode__c, Society__c FROM NetworkUser__c WHERE FiscalCode__c = :userCF AND IsActive__c = true];
        
        List<String> result = new List<String>();
        
        for(NetworkUser__c nu : users){
            result.add(nu.NetworkUser__c);
        }
        
        return result;
        
    }

    public static List<NetworkUser__c> getRelatedNetworkUserRecords(String userCF){

        return [SELECT Id, NetworkUser__c, Agency__c, Agency__r.AgencyCode__c, Society__c FROM NetworkUser__c WHERE FiscalCode__c = :userCF AND IsActive__c = true];

    }

    public static List<NetworkUser__c> getRelatedNetworkUserRecordsList(Set<String> userCFs){

        return [SELECT Id, NetworkUser__c, Agency__c, Agency__r.AgencyCode__c, Society__c FROM NetworkUser__c WHERE NetworkUser__c IN :userCFs AND IsActive__c = true];

    }
    
    /*
    * @description This method is called to retrieve configuration map of Permission Set where 
    * KEY = UNIPOL Permission Code
    * VALUE = Permission Set Name
    * @return List<String> List of Network User
    */
    public static Map<String, Set<String>> getMapCodePermissionSet(){
        
        Map<String, UCA_Mapping__mdt> mappingUCA = UCA_Mapping__mdt.getAll();
        
        Map<String, Set<String>> mapCodePermissionSet = new Map<String, Set<String>>();
        for(UCA_Mapping__mdt uca : mappingUCA.values()){
            if(String.isNotBlank(uca.Permission_Set_Name__c)){

                if(mapCodePermissionSet.containsKey(uca.Code__c)){
                    mapCodePermissionSet.get(uca.Code__c).addAll(uca.Permission_Set_Name__c.split(','));
                }else{
                    Set<String> tempSet = new Set<String>();
                    tempSet.addAll(uca.Permission_Set_Name__c.split(','));
                    mapCodePermissionSet.put(uca.Code__c, tempSet);
                }
            }
        }

        if(Test.isRunningTest()){
            mapCodePermissionSet.put('0000',new Set<String>{'UNIT_TEST_CLASS'});
        }
        
        return mapCodePermissionSet;
    }
    
    /*
    * @description This method is called check which users have a specific permission set
    * @param String Permission Set Name
    * @return Set<String> Set of UNIPOL Users
    */
    public static Set<String> getUserByPermissionSet(String permissionSetName){
        
        System.debug('@@@ '+permissionSetName);
        
        Set<String> result = new Set<String>();

        List<String> comparingList = permissionSetName.split(',');
        
        User currentUser = [SELECT Id, FederationIdentifier FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];

        UserProvisioningAPI.UCAResponse response = UserProvisioningAPI.getUCAData(currentUser.FederationIdentifier);
        Map<String, Set<String>> mapCodePermissionSet = getMapCodePermissionSet();
        System.debug('@@@ mapCodePermissionSet --> '+mapCodePermissionSet);
        Map<String, Set<String>> mapUserPermissionSet = new Map<String, Set<String>>();
        
        for(UserProvisioningAPI.UCARequestItem uri : response.result){

            Boolean found = false;

            if(uri.gruppi != null && !uri.gruppi.isEmpty()){

                for(String s : uri.gruppi){

                    if(mapCodePermissionSet.containsKey(s)){

                        for(String compareTo : comparingList){

                            if(
                                mapCodePermissionSet.get(s).contains(compareTo) &&
                                !result.contains(uri.utenza)
                            ){
                                result.add(uri.utenza);
                                found = true;
                                break;
                            }

                        }

                        if(found) break;
                        
                        /* if(
                            permissionSetName.contains(mapCodePermissionSet.get(s))
                            mapCodePermissionSet.get(s).contains(permissionSetName) &&
                            !result.contains(uri.utenza)
                        ){
                            result.add(uri.utenza);
                            found = true;
                            break;
                        } */

                    }

                }

            }

            if(found) continue;
            
            if(uri.embedded != null){
            
                UserProvisioningAPI.EmbeddedItem embedded = uri.embedded;
                
                if(embedded.abilitazioniOperative != null){
                    
                    UserProvisioningAPI.AbilitazioniOperativeItem operative = embedded.abilitazioniOperative;
                    
                    if(operative.abilitazioni != null && !operative.abilitazioni.isEmpty()){
                        
                        for(UserProvisioningAPI.AbilitazioneItem abilitazione : operative.abilitazioni){
                            
                            if(
                                mapCodePermissionSet.containsKey(abilitazione.codice)
                            ){

                                for(String compareTo : comparingList){

                                    if(
                                        mapCodePermissionSet.get(abilitazione.codice).contains(compareTo) &&
                                        !result.contains(uri.utenza)
                                    ){
                                        result.add(uri.utenza);
                                        found = true;
                                        break;
                                    }
        
                                }
        
                                if(found) break;

                            }

                            /* if(
                            	mapCodePermissionSet.containsKey(abilitazione.codice) &&
                                mapCodePermissionSet.get(abilitazione.codice).contains(permissionSetName) &&
                                !result.contains(uri.utenza)
                            ){
                                result.add(uri.utenza);
                                break;
                            } */
                            
                        }
                        
                    }
                    
                }
                
            }
            
        }
        
        return result;
        
    }
    
    /*
    * @description This method is called to check what permissions should be assigned to the User, based on UCA API
    * @param UCADATA UserProvisioningAPI.UCAResponse Wrapper to manage the UCA API Response
    * @param mapCodePermissionSet Map<String, List<String>> KEY = UNIPOL Permission Code --> VALUE = List of Permission Set Names
    * @param mapUserCode Map<String, Set<String>> KEY = UNIPOL User --> VALUE = Set of Permission Set Names
    * @param permissionSetToDo Set<String> as reference, Set of Permissino Set Names to be assigned to the user
    */
    public static void extractPermissionSetToDo(UserProvisioningAPI.UCAResponse UCAData, Map<String, Set<String>> mapCodePermissionSet, Map<String, Set<String>> mapUserCode, Set<String> permissionSetToDo){
        
        for(UserProvisioningAPI.UCARequestItem uri : UCAData.result){

            if(uri.gruppi != null && !uri.gruppi.isEmpty()){

                for(String s : uri.gruppi){

                    if(mapCodePermissionSet.containsKey(s)){
                        permissionSetToDo.addAll(mapCodePermissionSet.get(s));
                        
                        if(mapUserCode.containsKey(uri.utenza)){
                            mapUserCode.get(uri.utenza).addAll(mapCodePermissionSet.get(s));
                        }else{
                            Set<String> tempSet = new Set<String>();
                            tempSet.addAll(mapCodePermissionSet.get(s));
                            mapUserCode.put(uri.utenza, tempSet);
                        }
                    }

                }

            }
            
            if(uri.embedded != null){
            
                UserProvisioningAPI.EmbeddedItem embedded = uri.embedded;
                
                if(embedded.abilitazioniOperative != null){
                    
                    UserProvisioningAPI.AbilitazioniOperativeItem operative = embedded.abilitazioniOperative;
                    
                    if(operative.abilitazioni != null && !operative.abilitazioni.isEmpty()){
                        
                        for(UserProvisioningAPI.AbilitazioneItem abilitazione : operative.abilitazioni){
                            
                            if(!mapCodePermissionSet.containsKey(abilitazione.codice)) continue;

                            if(mapCodePermissionSet.containsKey(abilitazione.codice)) permissionSetToDo.addAll(mapCodePermissionSet.get(abilitazione.codice));
                            
                            if(mapUserCode.containsKey(uri.utenza)){
                                mapUserCode.get(uri.utenza).addAll(mapCodePermissionSet.get(abilitazione.codice));
                            }else{
                                Set<String> tempSet = new Set<String>();
                                tempSet.addAll(mapCodePermissionSet.get(abilitazione.codice));
                                mapUserCode.put(uri.utenza, tempSet);
                            }
                            
                        }
                        
                    }
                    
                }
                
            }
            
        }
        
    }
    
    /*
    * @description This method is called to create an instance of PermissionSetAssignment object
    * @param assigneeId the value to insert in the field AssigneId
    * @param permissionSetId the value to insert in the field PermissionSetId
    * @return PermissionSetAssignemnt an instance of the PermissionSetAssignment object
    */
    public static PermissionSetAssignment createPermissionSetAssignment(String assigneeId, String permissionSetId){
        
        PermissionSetAssignment psa = new PermissionSetAssignment();
        psa.AssigneeId = assigneeId;
        psa.PermissionSetId = permissionSetId;
        
        return psa;
        
    }
    
    /*
    * @description This method is called to create an instance of QueueableUserProvisioning.InputWrapper object
    * @param fiscalCode the value to insert in the field fiscalCode, it is the fiscal code of the user
    * @param networkIdentifier the value to insert in the field networkIdentifier, it is the UNIPOL User Code
    * @param permissionSets the value to insert in the field permissionSets, it is the permission set name
    * @return QueueableUserProvisioning.InputWrapper an instance of the QueueableUserProvisioning.InputWrapper object
    */
    public static QueueableUserProvisioning.InputWrapper createQueueableInputWrapper(String fiscalCode, String networkIdentifier, String permissionSets){
        
        QueueableUserProvisioning.InputWrapper iw = new QueueableUserProvisioning.InputWrapper();
        iw.fiscalCode = fiscalCode;
        iw.networkIdentifier = networkIdentifier;
        iw.permissionSets = permissionSets;
        
        return iw;
        
    }

    public static UserProvisioningUpdate.UCAResult transformUCAResponse(UserProvisioningAPI.UCAResponse ucaresponse){

        UserProvisioningUpdate.UCAResult result = new UserProvisioningUpdate.UCAResult();
        result.users = new List<UserProvisioningUpdate.UserType>();

        if(ucaresponse != null && ucaresponse.result != null){

            for(UserProvisioningAPI.UCARequestItem item : ucaresponse.result){

                UserProvisioningUpdate.UserType currentUser = new UserProvisioningUpdate.UserType();
                currentUser.userId = item.utenza;
                currentUser.groups = item.gruppi;
                currentUser.cin = new List<String>();

                if(
                    item.embedded != null &&
                    item.embedded.abilitazioniOperative != null &&
                    item.embedded.abilitazioniOperative.abilitazioni != null &&
                    !item.embedded.abilitazioniOperative.abilitazioni.isEmpty()
                ){

                    for(UserProvisioningAPI.AbilitazioneItem ai : item.embedded.abilitazioniOperative.abilitazioni){

                        currentUser.cin.add(ai.codice);

                    }

                }

                result.users.add(currentUser);

            }
            
        }

        return result;
        
    }

    public static List<NetworkUser__c> getAllowedNetworkUser(String permissionSetName, String society){

        List<NetworkUser__c> result = new List<NetworkUser__c>();

        User currentUser = [SELECT Id, UCA_Permissions__c, FederationIdentifier FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];

        String ucaJSON = currentUser.UCA_Permissions__c;
        Set<String> userRACF = new Set<String>();

        if(String.isBlank(permissionSetName)){
            
            result = [SELECT Id,NetworkUser__c, Society__c FROM NetworkUser__c WHERE FiscalCode__c = :currentUser.FederationIdentifier and IsActive__c = true];

        }else if(String.isNotBlank(ucaJSON)){

            UserProvisioningUpdate.UCAResult ucaResponse = (UserProvisioningUpdate.UCAResult)JSON.deserialize(ucaJSON, UserProvisioningUpdate.UCAResult.class);

            for(UserProvisioningUpdate.UserType ut : ucaResponse.users){

                if(ut.groups.contains(permissionSetName)){
                    userRACF.add(ut.userId);
                    continue;
                }

                if(ut.cin.contains(permissionSetName)){
                    userRACF.add(ut.userId);
                    continue;
                }

            }

            if(!userRACF.isEmpty()){

                result = [SELECT Id, NetworkUser__c, Society__c FROM NetworkUser__c WHERE NetworkUser__c IN :userRACF AND IsActive__c = true];

            }

        }

        return result;

    }

    public static List<NetworkUser__c> getAllowedNetworkUser(String federationIdentifier, String permissionSetName, String society, UserProvisioningUpdate.UCAResult ucaResponse){

        List<NetworkUser__c> result = new List<NetworkUser__c>();

        Set<String> userRACF = new Set<String>();

        if(String.isBlank(permissionSetName)){
            
            if(String.isNotBlank(society)){
                result = [SELECT Id,NetworkUser__c, Society__c FROM NetworkUser__c WHERE FiscalCode__c = :federationIdentifier AND IsActive__c = true AND Society__c = :society];
            }else{
                result = [SELECT Id,NetworkUser__c, Society__c FROM NetworkUser__c WHERE FiscalCode__c = :federationIdentifier AND IsActive__c = true];
            }

        }else if(ucaResponse != null){

            for(UserProvisioningUpdate.UserType ut : ucaResponse.users){

                if(ut.groups.contains(permissionSetName)){
                    userRACF.add(ut.userId);
                    continue;
                }

                if(ut.cin.contains(permissionSetName)){
                    userRACF.add(ut.userId);
                    continue;
                }

            }

            if(!userRACF.isEmpty()){

                if(String.isNotBlank(society)){
                    result = [SELECT Id, NetworkUser__c, Society__c FROM NetworkUser__c WHERE NetworkUser__c IN :userRACF AND IsActive__c = true AND Society__c = :society];
                }else{
                    result = [SELECT Id, NetworkUser__c, Society__c FROM NetworkUser__c WHERE NetworkUser__c IN :userRACF AND IsActive__c = true];
                }

            }

        }

        return result;

    }

    public static Boolean isEligible(String networkUser, String permissionSetName, UserProvisioningUpdate.UCAResult ucaResponse){

        Boolean result = false;

        if(String.isBlank(permissionSetName)){
            result = true;
        }else if(ucaResponse == null){
            result = false;
        }else{

            for(UserProvisioningUpdate.UserType ut : ucaResponse.users){

                if(networkUser.equalsIgnoreCase(ut.userId)){

                    if(ut.groups.contains(permissionSetName)){
                        result = true;
                        break;
                    }

                    if(ut.cin.contains(permissionSetName)){
                        result = true;
                        break;
                    }

                }

            }

        }

        return result;

    }
}