/**********************************************************************************************************************************************
* <AUTHOR>
* @description    Assignment and Reassignment Engine class for the calculation of an opportunity's expiry date using business or calendar hours
* @date           2024-29-01
* @group          Assignment and Reassignment Engine
***********************************************************************************************************************************************/

public with sharing class AssignmentFlowHandler {
    //Constants
    public static final Integer HOURS_PER_DAY_BUSINESS = 9;
    public static final Integer HOURS_PER_DAY_CALENDAR = 24;
    public static final Integer MILLISECONDS_IN_ONE_HOUR = 3600000;

    public class FlowInputStructure{
        @InvocableVariable
        public Id businessHoursId; // Account.BusinessHours.Id

        @InvocableVariable
        public Datetime startDate; // Opportunity start date

        @InvocableVariable
        public String remainingTime; // Opportunity remaining time until expiration

        @InvocableVariable
        public String unitOfMeasure; // hour or day

        @InvocableVariable
        public String hoursType; // business or calendar

        public FlowInputStructure() {}
    }

    public class FlowOutputStructure{
        @InvocableVariable
        public Datetime expiryDate; // Calculated opportunity expiry date

        @InvocableVariable
        public Boolean isSuccess; // Flag for successful calculation

        @InvocableVariable
        public String errorMessage; // Message when error occurs

        public FlowOutputStructure(Datetime expiryDate){
            this.expiryDate = expiryDate;
            this.isSuccess = true;
            this.errorMessage = null;
        }

        public FlowOutputStructure(String errorMessage){
            this.isSuccess = false;
            this.errorMessage = errorMessage;
        }
    }
	
    /********************************************************************************************************************
    * <AUTHOR>
    * @date         2024-29-01
    * @description  The method is called in context of a flow and returns a list of FlowOutputStructure objects
    * 				In this method the opportunity's expiry date is calculated using business or calendar hours
    * @return       List<FlowOutputStructure>
    ********************************************************************************************************************/
    @InvocableMethod
    public static List<FlowOutputStructure> getTakenInChargeExpiryDateWithBH(List<FlowInputStructure> inputsList){
        try {
            FlowInputStructure hourInfo = inputsList.get(0);
            Integer hoursPerDay = 1;
             //if hours, we don't have to multiply
            if(hourInfo.unitOfMeasure == 'day') {
                hoursPerDay = hourInfo.hoursType == 'business' ? HOURS_PER_DAY_BUSINESS : HOURS_PER_DAY_CALENDAR;
            }
            Long remainingTimeInMs = ((long.valueOf(hourInfo.remainingTime)) * hoursPerDay) * MILLISECONDS_IN_ONE_HOUR;
            Datetime expiryDate = BusinessHours.add(hourInfo.businessHoursId, hourInfo.startDate, remainingTimeInMs);
            FlowOutputStructure fos = new FlowOutputStructure(expiryDate);
            return new List<FlowOutputStructure>{fos};
        } catch(Exception e) {
            FlowOutputStructure fos = new FlowOutputStructure(e.getMessage());
            return new List<FlowOutputStructure>{fos};
        }
    }
}