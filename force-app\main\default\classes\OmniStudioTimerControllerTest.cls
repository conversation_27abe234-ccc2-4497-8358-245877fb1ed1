@isTest
public class OmniStudioTimerControllerTest {
    @TestSetup
    static void makeData(){
      Opportunity testOpportunity = new Opportunity();
      Opportunity testOpportunity2 = new Opportunity();
      Account testPersonAccount = new Account();
      Account testAgencyAccount = new Account();
      List<Account> accountsToBeInserted = new List<Account>();
      List<RecordType> agencyRT = new List<RecordType>();
      List<RecordType> prospectRT = new List<RecordType>();
      List<BusinessHours> standardBusinessHours = new List<BusinessHours>();
    
      agencyRT = [SELECT Id FROM RecordType WHERE SObjectType = 'Account' AND DeveloperName = 'Agency' LIMIT 1];
      prospectRT = [SELECT Id FROM RecordType WHERE SObjectType = 'Account' AND DeveloperName = 'Prospect' ORDER BY CreatedDate LIMIT 1];
      standardBusinessHours = [SELECT Id, name from BusinessHours where name = 'Standard Office BH' LIMIT 1];
    
      if(!agencyRT.isEmpty() && !prospectRT.isEmpty() && !standardBusinessHours.isEmpty()){
        testPersonAccount.RecordTypeId = prospectRT.get(0).Id;
        testPersonAccount.BillingCity = 'Roma';
        testPersonAccount.BillingCountry = 'Italy';
        testPersonAccount.BillingLatitude = 41.**************;
        testPersonAccount.BillingLongitude = 12.***************;
        testPersonAccount.BillingPostalCode = '00154';
        testPersonAccount.BillingStreet = '13 Via Cristoforo Colombo';
        testPersonAccount.FirstName = 'Mario';
        testPersonAccount.LastName = 'Test';
        testPersonAccount.Salutation = 'Mr.';
        accountsToBeInserted.add(testPersonAccount);
    
        testAgencyAccount.RecordTypeId = agencyRT.get(0).Id;
        testAgencyAccount.AccountNumber = 'T0000';
        testAgencyAccount.Area__c = 'Roma';
        testAgencyAccount.BillingCity = 'Roma';
        testAgencyAccount.BillingCountry = 'Italy';
        testAgencyAccount.BillingLatitude = 41.86505;
        testAgencyAccount.BillingLongitude = 12.49762;
        testAgencyAccount.BillingPostalCode = '00147';
        testAgencyAccount.BillingStreet = 'Via Cristoforo Colombo 177';
        testAgencyAccount.BusinessHours__c = standardBusinessHours.get(0).Id;
        testAgencyAccount.Name = 'Test Agenzia 0000';
        accountsToBeInserted.add(testAgencyAccount);
        insert accountsToBeInserted;
    
        List<Account> subject = [SELECT Id, Name FROM Account WHERE LastName = 'Test' LIMIT 1];
        List<Account> agency = [SELECT Id, BusinessHours__c FROM Account WHERE AccountNumber = 'T0000' LIMIT 1];
        testOpportunity.AccountId = subject.get(0).Id;
        testOpportunity.Agency__c = agency.get(0).Id;
        testOpportunity.AreaOfNeed__c = 'Veicoli';
        testOpportunity.AssignmentMode__c = 'Automatica';
        testOpportunity.Channel__c = 'Preventivatore digitale Unica';
        testOpportunity.CloseDate = Date.today().addDays(60);
        testOpportunity.ContactChannel__c = 'Agenzia';
        testOpportunity.Name = 'T000102610';
        testOpportunity.Probability = 10;
        testOpportunity.Rating__c = 'Caldissima';
        testOpportunity.SingleAreaOfNeed__c = 'Veicoli';
        testOpportunity.StageName = 'Assegnato';
        testOpportunity.TakenInChargeSLAStartDate__c = Datetime.newInstance(2030, 3, 25, 12, 0, 0);
        testOpportunity.TakenInChargeSLAExpiryDate__c = Datetime.newInstance(2030, 3, 26, 13, 0, 0);
        testOpportunity.WorkingSLAStartDate__c = Datetime.newInstance(2030, 5, 25, 12, 0, 0);
        testOpportunity.WorkingSLAExpiryDate__c = Datetime.newInstance(2030, 5, 26, 13, 0, 0);
        insert testOpportunity;
          
        testOpportunity2.AccountId = subject.get(0).Id;
        testOpportunity2.Agency__c = agency.get(0).Id;
        testOpportunity2.AreaOfNeed__c = 'Veicoli';
        testOpportunity2.AssignmentMode__c = 'Automatica';
        testOpportunity2.Channel__c = 'Preventivatore digitale Unica';
        testOpportunity2.CloseDate = Date.today().addDays(60);
        testOpportunity2.ContactChannel__c = 'Agenzia';
        testOpportunity2.Name = 'T000102611';
        testOpportunity2.Probability = 10;
        testOpportunity2.Rating__c = 'Caldissima';
        testOpportunity2.SingleAreaOfNeed__c = 'Veicoli';
        testOpportunity2.StageName = 'Assegnato';
        testOpportunity2.TakenInChargeSLAStartDate__c = Datetime.newInstance(2030, 3, 25, 12, 0, 0);
        testOpportunity2.TakenInChargeSLAExpiryDate__c = Datetime.newInstance(2030, 3, 25, 13, 0, 0);
        testOpportunity2.WorkingSLAStartDate__c = Datetime.newInstance(2030, 5, 25, 12, 0, 0);
        testOpportunity2.WorkingSLAExpiryDate__c = Datetime.newInstance(2030, 5, 25, 13, 0, 0);
        insert testOpportunity2;
      }
    }
    
    @isTest
    static void testGetOpportunity(){
        List<Opportunity> opp = [SELECT Id 
                                 FROM Opportunity 
                                 LIMIT 1];
        
        Opportunity result1 = OmnistudioTimerController.getOpportunity(opp.get(0).Id);
        
        System.assertNotEquals(null, result1, 'Expected to return something');
        
        Opportunity testOpp = new Opportunity();
        Opportunity result2 = OmnistudioTimerController.getOpportunity(testOpp.Id);
        
        System.assertEquals(null, result2, 'Expected to return null');
    }
    
    @isTest
    static void testGetSLAOmniStudio(){
        List<Opportunity> opp = [SELECT Id, TakenInChargeSLAExpiryDate__c, TakenInChargeSLARemainingHours__c, TakenInChargeSLARemainingDays__c, WorkingSLAExpiryDate__c, WorkingSLARemainingHours__c, WorkingSLARemainingDays__c 
                                 FROM Opportunity 
                                 LIMIT 1];
        
        // Taken in Charge
        OmniStudioTimerController.SLAOmniStudio result1 = OmniStudioTimerController.getSLAOmniStudio(opp.get(0), true);
        System.assertEquals('26/03/2030, 13:00', result1.expiryDate, 'Expected remaining days: TIC');
        System.assertEquals(1, result1.remainingDays, 'Expected remaining hours: TIC');
        System.assertEquals(1, result1.remainingHours, 'Expected total remaining hours: TIC');
        System.assertEquals(25, result1.totalRemainingHours, 'Expected total remaining hours: TIC');
        
        // Working
        OmniStudioTimerController.SLAOmniStudio result2 = OmniStudioTimerController.getSLAOmniStudio(opp.get(0), false);
        System.assertEquals('26/05/2030, 13:00', result2.expiryDate, 'Expected remaining days: W');
        System.assertEquals(1, result2.remainingDays, 'Expected remaining hours: W');
        System.assertEquals(1, result2.remainingHours, 'Expected total remaining hours: W');
        System.assertEquals(25, result2.totalRemainingHours, 'Expected total remaining hours: W');
    }
    
    @isTest
    static void testCalculateCalendarTime() {
        List<Opportunity> opp = [SELECT Id, TakenInChargeSLARemainingHours__c, TakenInChargeSLARemainingDays__c, WorkingSLARemainingHours__c, WorkingSLARemainingDays__c 
                                 FROM Opportunity 
                                 LIMIT 1];
          
        // Taken in Charge
        OmniStudioTimerController.TimeSLA result1 = OmniStudioTimerController.calculateCalendarTime(opp.get(0), true);
        System.assertNotEquals(null, result1, 'Expected to return something');
        System.assertEquals(1, result1.remainingDays, 'Expected remaining days: 1');
        System.assertEquals(1, result1.remainingHours, 'Expected remaining hours: 1');
        System.assertEquals(25, result1.totalRemainingHours, 'Expected total remaining hours: 25');
        
        // Working
        OmniStudioTimerController.TimeSLA result2 = OmniStudioTimerController.calculateCalendarTime(opp.get(0), false);
        System.assertNotEquals(null, result2, 'Expected to return something');
        System.assertEquals(1, result2.remainingDays, 'Expected remaining days: 1');
        System.assertEquals(1, result2.remainingHours, 'Expected remaining hours: 1');
        System.assertEquals(25, result2.totalRemainingHours, 'Expected total remaining hours: 25');
    }
    
    @isTest
    static void testGetRemainingHours() {
        // Business hours (9 hours)
        Decimal result1 = OmniStudioTimerController.getRemainingHours(18, true);
        System.assertEquals(0, result1, 'Expected remaining hours: 0');

        // Non-business hours (24 hours)
        Decimal result2 = OmniStudioTimerController.getRemainingHours(30, false);
        System.assertEquals(6, result2, 'Expected remaining hours: 6');
    }
    
    @isTest
    static void testGetCruscottoTakeInChargeSLA() {
        OmniStudioTimerController omnistudioTimerController = new OmniStudioTimerController();
        Opportunity opp = new Opportunity();
        opp.Name = 'Test Opp';
        
        String recordId = opp.Id;

        Test.startTest();
        OmnistudioTimerController.SLAOmniStudio result = omnistudioTimerController.getCruscottoTakeInChargeSLA(recordId);
        Test.stopTest();
        
        System.assertEquals(null, result);
        
    }
    
    @isTest
    static void getCruscottoWorkingSLA() {
        OmniStudioTimerController omnistudioTimerController = new OmniStudioTimerController();
        Opportunity opp = new Opportunity();
        opp.Name = 'Test Opp';
        
        String recordId = opp.Id;

        Test.startTest();
        OmnistudioTimerController.SLAOmniStudio result = omnistudioTimerController.getCruscottoWorkingSLA(recordId);
        Test.stopTest();
        
        System.assertEquals(null, result);
    }
    
    @isTest 
    static void testCallMethod() {
        OmniStudioTimerController omnistudioTimerController = new OmniStudioTimerController();
        
        List<Opportunity> opp = [SELECT Id, TakenInChargeSLARemainingHours__c, TakenInChargeSLARemainingDays__c, WorkingSLARemainingHours__c, WorkingSLARemainingDays__c 
                                 FROM Opportunity 
                                 LIMIT 1];
        
        Map<String, Object> input = new Map<String, Object>();
        input.put('recordId', opp.get(0).Id);
        
        Map<String, Object> args = new Map<String, Object>();
        args.put('input', input);
        args.put('output', new Map<String, Object>());
        args.put('options', new Map<String, Object>());

        // Test 'getCruscottoTakeInChargeSLA' method
        Object result1 = omnistudioTimerController.call('getCruscottoTakeInChargeSLA', args);
        System.assert(result1 instanceof Boolean, 'getCruscottoTakeInChargeSLA method failed');

        // Test 'getCruscottoWorkingSLA' method
        Object result2 = omnistudioTimerController.call('getCruscottoWorkingSLA', args);
        System.assert(result2 instanceof Boolean, 'getCruscottoWorkingSLA method failed');
        
        // Test invalid method
        Object result3 = omnistudioTimerController.call('invalidMethod', args);
        System.assert(result3 instanceof Boolean && !(Boolean)result3, 'Invalid method unexpectedly succeeded');
    }
    
    @isTest 
    static void testInvokeMethod() {
        OmniStudioTimerController omnistudioTimerController = new OmniStudioTimerController();
        
        List<Opportunity> opp = [SELECT Id, TakenInChargeSLAExpiryDate__c, TakenInChargeSLARemainingHours__c, TakenInChargeSLARemainingDays__c, WorkingSLARemainingHours__c, WorkingSLARemainingDays__c 
                                 FROM Opportunity 
                                 LIMIT 1];
        
        Map<String, Object> inputs = new Map<String, Object>();
        inputs.put('recordId', opp.get(0).Id);

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        // Test 'getCruscottoTakeInChargeSLA' method
        Boolean result1 = omnistudioTimerController.invokeMethod('getCruscottoTakeInChargeSLA', inputs, output, options);
        System.assert(result1, 'getCruscottoTakeInChargeSLA method failed');

        // Test 'getCruscottoWorkingSLA' method
        Boolean result2 = omnistudioTimerController.invokeMethod('getCruscottoWorkingSLA', inputs, output, options);
        System.assert(result2, 'getCruscottoWorkingSLA method failed');
        
        // Test invalid method
        Boolean result3 = omnistudioTimerController.invokeMethod('invalidMethod', inputs, output, options);
        System.assert(!result3, 'Invalid method unexpectedly succeeded');
        
        
        List<Opportunity> opp2 = [SELECT Id, TakenInChargeSLARemainingHours__c, TakenInChargeSLARemainingDays__c, WorkingSLARemainingHours__c, WorkingSLARemainingDays__c 
                                 FROM Opportunity 
                                 LIMIT 1];
        
        Map<String, Object> inputs2 = new Map<String, Object>();
        inputs2.put('recordId', opp2.get(0).Id);

        Map<String, Object> output2 = new Map<String, Object>();
        Map<String, Object> options2 = new Map<String, Object>();

        // Test 'getCruscottoTakeInChargeSLA' method
        Boolean result4 = omnistudioTimerController.invokeMethod('getCruscottoTakeInChargeSLA', inputs2, output2, options2);
        System.assert(result4, 'getCruscottoTakeInChargeSLA method failed');
        
        // Test 'getCruscottoWorkingSLA' method
        Boolean result5 = omnistudioTimerController.invokeMethod('getCruscottoWorkingSLA', inputs2, output2, options2);
        System.assert(result5, 'getCruscottoWorkingSLA method failed');
    }
    
}