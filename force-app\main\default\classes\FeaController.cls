public with sharing class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object checkAggiorna(Map<String, Object> ipInput) {
        Map<String, Object> outputMap = new Map<String,Object>();
        Map<String, Object> optionsMap = new Map<String,Object>();
        //Input Parameter: ciu
        FEA_Helper.checkAggiorna(ipInput, outputMap, optionsMap);
        return outputMap;
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object refreshStatoEmail(Map<String, Object> ipInput) {
        Map<String, Object> outputMap = new Map<String,Object>();
        Map<String, Object> optionsMap = new Map<String,Object>();
        //Input Parameter: ciu
        FEA_Helper.refreshStatoEmail(ipInput, outputMap, optionsMap);
        return outputMap;
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object getContatti(Map<String, Object> ipInput) {
        //AC: riciclo AddressService per invocare la procedura
        return AddressService.invokeIntegrationProcedureLookup('RestGet_FeaContatti', ipInput, null);
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object getStampe(Map<String, Object> ipInput) {
        //AC: riciclo AddressService per invocare la procedura
        if((String) ipInput.get('processType') == 'FEA'){
        return AddressService.invokeIntegrationProcedureLookup('RestGet_FeaLogStampa', ipInput, null);
        } else {
            return AddressService.invokeIntegrationProcedureLookup('RestGet_PrivacyLogStampa', ipInput, null);
        }
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object getFea(Map<String, Object> ipInput) {
        //AC: riciclo AddressService per invocare la procedura
        return AddressService.invokeIntegrationProcedureLookup('RestGet_FeaGetDati', ipInput, null);
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object getDocumentaleLogStampa(Map<String, Object> ipInput) {
        //@GP: riciclo AddressService per invocare la procedura che recupera i documenti codificati in Base64
        //Params: ciu, tipoDocAnagrafica, idDocAnagrafica 
        System.debug('@GP ipInput: ' + ipInput);
        return AddressService.invokeIntegrationProcedureLookup('RestGet_FeaDownloadDocumentale', ipInput, null);
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object getContattiForm(Map<String, String> data) {
        String recordId = data.get('id');
        List<AccountDetails__c> recapiti = [SELECT Email__c, Mobile__c, OtherEmail__c, OtherMobile__c, RecordType.DeveloperName
            FROM AccountDetails__c
            WHERE Relation__r.FinServ__Account__r.Id = :recordId and Recordtype.DeveloperName IN ('PA', 'MDM')];
        Map<String, String> response = new Map<String, String>();
        for (AccountDetails__c r : recapiti) {
            if (r.RecordType.DeveloperName == 'PA') {
                response.put('mobile', r.Mobile__c);
                response.put('email', r.Email__c);
            } else {
                response.put('otherMobile', r.OtherMobile__c);
                response.put('otherEmail', r.OtherEmail__c);
            }
        }
        return response;
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object conferma(Map<String, Object> data) {
        //Controllo dati univoci
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();
        FEA_Helper.checkDatiUnivoci(data, outputMap, optionsMap);
        System.debug('@GP outputMap checkDatiUnivoci: ' + outputMap);
        //Costruzione xml dentro If
        if(outputMap.get('status') == true){
            outputMap.clear();
            FEA_Helper.createBarcodeAndXML(data, outputMap, optionsMap);
        }
        return outputMap;
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object revoca(Map<String, Object> data) {
        //@GP: riciclo AddressService per invocare la procedura che invia un aggiornamento o la revoca della FEA
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();
        FEA_Helper.revocaFEA(data, outputMap, optionsMap);

        if(outputMap.get('status') == true){
            //Params: contactTypeFilter, revoca, body 
            Map<String,Object> ipInput = new Map<String,Object>();
            ipInput.put('body',(String) outputMap.get('requestPayload'));
            ipInput.put('activeUserId',(String) outputMap.get('activeUserId'));
            ipInput.put('accountDetailsId',(String) outputMap.get('accountDetailsId'));
            ipInput.put('mostraDatiTracciatura',true);
            return AddressService.invokeIntegrationProcedureLookup('RestGet_aggiornamentoFea', ipInput, null);
        } else {
            return outputMap;
        }
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object checkFeaAssociazioneStrumenti(Map<String, Object> data) {
        //Params: compagnia (1,2,3,4), codiceFiscale 
        return AddressService.invokeIntegrationProcedureLookup('RestGet_FeaAssociazioneStrumenti', data, null);
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object checkDocumento(Map<String, String> data) {
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();
        FEA_Helper.checkActiveDocument(data, outputMap, optionsMap);
        return outputMap;
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object certifcazioneContatti(Map<String, String> data) {
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();
        //Input Parameters: email, cellulare, ciu
        FEA_Helper.certifcazioneContatti(data, outputMap, optionsMap);

        //GP: richiamo la procedure
        Map<String,Object> ipInput = new Map<String,Object>();
        ipInput.put('body',(String)outputMap.get('requestPayload'));
        return AddressService.invokeIntegrationProcedureLookup('RestGet_certifcazioneContattiFEA', ipInput, null);
    }

}