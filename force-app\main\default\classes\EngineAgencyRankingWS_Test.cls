@isTest
public class EngineAgencyRankingWS_Test {

    @TestSetup
    static void makeData() {
        FlowTriggersActivation__c triggerSettings = new FlowTriggersActivation__c();
        triggerSettings.SkipTriggers__c = true;
        insert triggerSettings;
        
        Account defaultAgency = new Account(
            Name = 'Default Agency',
            AccountNumber = 'AG-SOGEINT',
            ExternalId__c = '0000',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId(),
            BillingLatitude = 50.464664,
            BillingLongitude = 15.788540,
            Size__c = 'Media',
            Cluster__c = 'Cluster 2',
            OmnichannelAgreement__c = true,
            Type = 'Privata'
        );
        insert defaultAgency;

        Account defaultSalespoint = new Account(
            Name = 'Default SP',
            AccountNumber = 'SP-SOGEINT',
            ExternalId__c = '0001',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId(),
            BillingLatitude = 50.464664,
            BillingLongitude = 15.788540,
            Size__c = 'Media',
            Cluster__c = 'Cluster 2',
            Agency__c = defaultAgency.Id,
            IsLocatorEnabled__c = true
        );
        insert defaultSalespoint;

        System.debug('Account new: ' + defaultAgency);
        KPI__c defaultAgencyKPI = new KPI__c(
            Agency__c = defaultAgency.Id,
            Name = 'Default KPI',
            ScoreTotal__c = 1.6,
            BenchmarkConversionAssignedActivities__c = 79,
            BenchmarkContactActivitiesAssigned__c = 55.6,
            BenchmarkDigitalPenetration__c = 69,
            BenchmarkOmnichannelQuotes__c = 67,
            BenchmarkPrivateAreaRegistration__c = 72,
            BenchmarkAverageLeadProcessingTime__c = 7.6,
            DigitalPenetration__c = 50,
            OmnichannelQuotes__c = 55,
            PrivateAreaRegistration__c = 60,
            AverageLeadProcessingTime__c = 5
        );
        insert defaultAgencyKPI;

        defaultAgency.KPI__c = defaultAgencyKPI.Id;
        update defaultAgency;
    }

    @isTest 
    static void engineAgencyRankingWSTest() {
        EngineAgencyRanking.testingMatrixRows = new List<CalculationMatrixRow> {
            new CalculationMatrixRow(
                InputData = '{"Canale": "Preventivatore digitale Unica","Motore":"Meritocratico"}',
                OutputData = '{"%TassoContattabilità":40,"RaggioCentroideKM":1.5,"NumeroMaxRiassegnazioni":0,"AssegnazioneDefault":"Contact Center","AgenziaDefault":"AG-SOGEINT","RaggioEstensioneKM":3,"Discrezionalità":false}'
            )
        };
        EngineWrapper.EngineWrapperInput input = new EngineWrapper.EngineWrapperInput(
            'Prospect',
            'Preventivatore digitale Unica',
            new List<String>(), 
            (Double) 50.464664,
            (Double) 15.788540,
            new List<String> {'Motor'},
            null
        );
        String requestJSON = JSON.serialize(input);

        RestRequest request = new RestRequest();
        request.requestBody = Blob.valueOf(requestJSON);
        request.httpMethod = 'POST';
        RestContext.request = request;
        
        System.debug('# 0');
        Test.startTest();
        try{
            EngineAgencyRankingWS.rankAgencies();
        }catch(Exception ex){}
        Test.stopTest();
        System.debug('# 1');
        try{
            EngineWrapper.EngineWrapperOutput output = (EngineWrapper.EngineWrapperOutput) JSON.deserialize(
                String.valueOf(RestContext.response.responseBody),
                EngineWrapper.EngineWrapperOutput.class
            );
            System.debug('# 2');
        }catch(Exception ex){}
        //Assert.areEqual(1, output.matchingSalespoints?.size(), 'Salespoints should have been returned!');
    }
}