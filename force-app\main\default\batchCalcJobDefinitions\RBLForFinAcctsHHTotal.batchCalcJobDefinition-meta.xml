<?xml version="1.0" encoding="UTF-8"?>
<BatchCalcJobDefinition xmlns="http://soap.sforce.com/2006/04/metadata">
    <aggregates>
        <fields>
            <aggregateFunction>Sum</aggregateFunction>
            <alias>BalanceSum</alias>
            <sourceFieldName>FinServ_Balance_c</sourceFieldName>
        </fields>
        <groupBy>FinServ_Household_c</groupBy>
        <label>aggregate_FinServ_Balance_c</label>
        <name>aggregate_FinServ_Balance_c</name>
        <sourceName>FinServ_FinancialAccount_c</sourceName>
    </aggregates>
    <datasources>
        <fields>
            <alias>Id</alias>
            <dataType>Text</dataType>
            <name>Id</name>
        </fields>
        <fields>
            <alias>Name</alias>
            <dataType>Text</dataType>
            <name>Name</name>
        </fields>
        <fields>
            <alias>RecordTypeId</alias>
            <dataType>Text</dataType>
            <name>RecordTypeId</name>
        </fields>
        <label>Account</label>
        <name>Account</name>
        <sourceName>Account</sourceName>
        <type>StandardObject</type>
    </datasources>
    <datasources>
        <fields>
            <alias>FinServ_Balance_c</alias>
            <dataType>Numeric</dataType>
            <name>FinServ__Balance__c</name>
        </fields>
        <fields>
            <alias>FinServ_Household_c</alias>
            <dataType>Text</dataType>
            <name>FinServ__Household__c</name>
        </fields>
        <label>FinServ_FinancialAccount_c</label>
        <name>FinServ_FinancialAccount_c</name>
        <sourceName>FinServ__FinancialAccount__c</sourceName>
        <type>StandardObject</type>
    </datasources>
    <datasources>
        <fields>
            <alias>DeveloperName</alias>
            <dataType>Text</dataType>
            <name>DeveloperName</name>
        </fields>
        <fields>
            <alias>Id</alias>
            <dataType>Text</dataType>
            <name>Id</name>
        </fields>
        <label>RecordType</label>
        <name>RecordType</name>
        <sourceName>RecordType</sourceName>
        <type>StandardObject</type>
    </datasources>
    <description>Group-level aggregation of all Financial Account balances.</description>
    <executionPlatformType>CRMA</executionPlatformType>
    <filters>
        <criteria>
            <operator>Equals</operator>
            <sequence>1</sequence>
            <sourceFieldName>DeveloperName</sourceFieldName>
            <value>IndustriesHousehold</value>
        </criteria>
        <filterCondition>1</filterCondition>
        <isDynamicFilter>false</isDynamicFilter>
        <label>Account_RecordType_Filter</label>
        <name>Account_RecordType_Filter</name>
        <sourceName>Account_RecordType</sourceName>
    </filters>
    <isTemplate>false</isTemplate>
    <joins>
        <fields>
            <alias>DeveloperName</alias>
            <sourceFieldName>DeveloperName</sourceFieldName>
            <sourceName>RecordType</sourceName>
        </fields>
        <fields>
            <alias>Id</alias>
            <sourceFieldName>Id</sourceFieldName>
            <sourceName>Account</sourceName>
        </fields>
        <joinKeys>
            <primarySourceFieldName>RecordTypeId</primarySourceFieldName>
            <secondarySourceFieldName>Id</secondarySourceFieldName>
        </joinKeys>
        <label>Account_RecordType</label>
        <name>Account_RecordType</name>
        <primarySourceName>Account</primarySourceName>
        <secondarySourceName>RecordType</secondarySourceName>
        <type>LeftOuter</type>
    </joins>
    <joins>
        <fields>
            <alias>BalanceSum</alias>
            <sourceFieldName>BalanceSum</sourceFieldName>
            <sourceName>aggregate_FinServ_Balance_c</sourceName>
        </fields>
        <fields>
            <alias>Id</alias>
            <sourceFieldName>Id</sourceFieldName>
            <sourceName>Account_RecordType_Filter</sourceName>
        </fields>
        <joinKeys>
            <primarySourceFieldName>Id</primarySourceFieldName>
            <secondarySourceFieldName>FinServ_Household_c</secondarySourceFieldName>
        </joinKeys>
        <label>Account_RecordType_Filter_aggregate_FinServ_Balance_c</label>
        <name>Account_RecordType_Filter_aggregate_FinServ_Balance_c</name>
        <primarySourceName>Account_RecordType_Filter</primarySourceName>
        <secondarySourceName>aggregate_FinServ_Balance_c</secondarySourceName>
        <type>LeftOuter</type>
    </joins>
    <label>RBLForFinAcctsHHTotal</label>
    <processType>DataProcessingEngine</processType>
    <status>Inactive</status>
    <writebacks>
        <fields>
            <runtimeParameter>false</runtimeParameter>
            <sourceFieldName>Id</sourceFieldName>
            <targetFieldName>Id</targetFieldName>
        </fields>
        <fields>
            <runtimeParameter>false</runtimeParameter>
            <sourceFieldName>BalanceSum</sourceFieldName>
            <targetFieldName>FinServ__TotalFinAcctsPrimaryOwner__c</targetFieldName>
        </fields>
        <isChangedRow>false</isChangedRow>
        <label>RBLForFinAcctsHHTotalExport</label>
        <name>RBLForFinAcctsHHTotalExport</name>
        <operationType>Update</operationType>
        <sourceName>Account_RecordType_Filter_aggregate_FinServ_Balance_c</sourceName>
        <storageType>sObject</storageType>
        <targetObjectName>Account</targetObjectName>
        <writebackSequence>1</writebackSequence>
        <writebackUser>0057Q00000A7swHQAR</writebackUser>
    </writebacks>
</BatchCalcJobDefinition>
