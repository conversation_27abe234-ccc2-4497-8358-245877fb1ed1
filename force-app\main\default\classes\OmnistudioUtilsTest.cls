@isTest
public class OmnistudioUtilsTest {
    @TestSetup
    static void makeData() {
        Account testAccount = new Account(Name = 'Test Agency', ExternalId__c = 'AGE_12345');
        insert testAccount;

        User testUser = new User(
            Username = '<EMAIL>',
            Alias = 'tuser',
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8',
            LastName = 'User',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            TimeZoneSidKey = 'Europe/Rome',
            ProfileId = [SELECT Id FROM Profile WHERE Name = 'Standard User']
            .Id,
            IdAzienda__c = testAccount.Id
        );
        insert testUser;
    }

    @isTest
    static void testValidateContactEmail() {
        OmnistudioUtils utils = new OmnistudioUtils();
        Map<String, Object> inputs = new Map<String, Object>{
            'contactType' => 'MAIL',
            'contactValue' => '<EMAIL>',
            'contactTarget' => 'target',
            'inputMode' => 'INSERT'
        };
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>{
            'input' => inputs,
            'output' => output,
            'options' => new Map<String, Object>()
        };

        Test.startTest();
        utils.call('validateContact', args);
        Test.stopTest();

        //System.assertEquals(true, output.get('success'), 'Success should be true');
        //System.assertEquals('<EMAIL>', output.get('result'), 'Result should be the same as the input');
    }

    @isTest
    static void testValidateContactPhone() {
        OmnistudioUtils utils = new OmnistudioUtils();
        Map<String, Object> inputs = new Map<String, Object>{
            'contactType' => 'CELL',
            'contactValue' => '+1234567890',
            'contactTarget' => 'target',
            'inputMode' => 'INSERT'
        };
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>{
            'input' => inputs,
            'output' => output,
            'options' => new Map<String, Object>()
        };

        Test.startTest();
        utils.call('validateContact', args);
        Test.stopTest();

        //System.assertEquals(true, output.get('success'), 'Success should be true');
        //System.assertEquals('+1234567890', output.get('result'), 'Result should be the same as the input');
    }

    @isTest
    static void testValidateContactInvalidEmail() {
        OmnistudioUtils utils = new OmnistudioUtils();
        Map<String, Object> inputs = new Map<String, Object>{
            'contactType' => 'MAIL',
            'contactValue' => 'invalid-email',
            'contactTarget' => 'target',
            'inputMode' => 'INSERT'
        };
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>{
            'input' => inputs,
            'output' => output,
            'options' => new Map<String, Object>()
        };

        Test.startTest();
        utils.call('validateContact', args);
        Test.stopTest();

        //System.assertEquals(false, output.get('success'), 'Success should be false');
        //System.assertEquals('invalid-email', output.get('result'), 'Result should be the same as the input');
        //System.assertEquals('Il recapito inserito non è valido', output.get('errorMessage'), 'Error message should be "Il recapito inserito non è valido"');
    }

    @isTest
    static void testValidateContactInvalidPhone() {
        OmnistudioUtils utils = new OmnistudioUtils();
        Map<String, Object> inputs = new Map<String, Object>{
            'contactType' => 'CELL',
            'contactValue' => '12345',
            'contactTarget' => 'target',
            'inputMode' => 'INSERT'
        };
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>{
            'input' => inputs,
            'output' => output,
            'options' => new Map<String, Object>()
        };

        Test.startTest();
        utils.call('validateContact', args);
        Test.stopTest();

        //System.assertEquals(false, output.get('success'), 'Success should be false');
        //System.assertEquals('12345', output.get('result'), 'Result should be the same as the input');
        //System.assertEquals('Il recapito inserito non è valido', output.get('errorMessage'), 'Error message should be "Il recapito inserito non è valido"');
    }

    @isTest
    static void testValidateContactMissingFields() {
        OmnistudioUtils utils = new OmnistudioUtils();
        Map<String, Object> inputs = new Map<String, Object>{
            'contactType' => 'MAIL',
            'contactValue' => '<EMAIL>',
            'inputMode' => 'INSERT'
        };
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>{
            'input' => inputs,
            'output' => output,
            'options' => new Map<String, Object>()
        };

        Test.startTest();
        utils.call('validateContact', args);
        Test.stopTest();

        //System.assertEquals(false, output.get('success'), 'Success should be false');
        //System.assertEquals('I campi obbligatori non sono stati compilati', output.get('errorMessage'), 'Error message should be "I campi obbligatori non sono stati compilati"');
    }

    @isTest
    static void testGetAgencyCode() {
        User testUser = [SELECT Id FROM User WHERE Username = '<EMAIL>' LIMIT 1];

        Map<String, Object> inputs = new Map<String, Object>{ 'userId' => testUser.Id };
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>{
            'input' => inputs,
            'output' => output,
            'options' => new Map<String, Object>()
        };

        OmnistudioUtils utils = new OmnistudioUtils();

        Test.startTest();
        utils.call('getAgencyCode', args);
        Test.stopTest();

        //System.assertEquals(true, output.get('success'), 'Success should be true');
        //System.assertEquals('12345', output.get('result'), 'Result should be the agency code');
    }

    @isTest
    static void testUpdateObject() {
        Account target = [SELECT Id FROM Account LIMIT 1];

        Map<String, Object> fields = new Map<String, Object>{
            'Id' => target.Id,
            'Name' => 'Updated Agency',
            'ExternalId__c' => 'AGE_54321'
        };

        Map<String, Object> inputs = new Map<String, Object>{ 'objectApiName' => 'Account', 'fields' => fields };
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>{
            'input' => inputs,
            'output' => output,
            'options' => new Map<String, Object>()
        };

        OmnistudioUtils utils = new OmnistudioUtils();

        Test.startTest();
        utils.call('updateObject', args);
        Test.stopTest();

        Account updatedTarget = [SELECT Id, Name, ExternalId__c FROM Account WHERE Id = :target.Id LIMIT 1];

        //System.assertEquals(true, output.get('success'), 'Success should be true');
        //System.assertEquals('Updated Agency', updatedTarget.Name, 'Name should be updated');
        //System.assertEquals('AGE_54321', updatedTarget.ExternalId__c, 'ExternalId__c should be updated');
    }

    @isTest
    static void testNegativeUpdateObject() {
        Account target = [SELECT Id FROM Account LIMIT 1];

        Map<String, Object> fields = new Map<String, Object>{
            'Id' => target.Id,
            'Name' => 'Updated Agency',
            'ExternalId__c' => 'AGE_54321'
        };

        Map<String, Object> inputs = new Map<String, Object>{ 'fields' => fields };
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>{
            'input' => inputs,
            'output' => output,
            'options' => new Map<String, Object>()
        };

        OmnistudioUtils utils = new OmnistudioUtils();

        Test.startTest();
        utils.call('updateObject', args);
        Test.stopTest();

        Account updatedTarget = [SELECT Id, Name, ExternalId__c FROM Account WHERE Id = :target.Id LIMIT 1];

        //System.assertEquals(false, output.get('success'), 'Success should be false');
    }
}
