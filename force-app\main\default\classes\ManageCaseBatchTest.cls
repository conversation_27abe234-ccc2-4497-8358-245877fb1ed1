@isTest
private with sharing class ManageCaseBatchTest {
    @TestSetup
    static void makeData() {
        Account accAgency = new Account(
            Name = 'BONASS S.R.L.',
            ExternalId__c = 'AGE_01853',
            FullName__c = 'BONASS S.R.L.',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName()
                .get('Agency')
                .getRecordTypeId()
        );
        insert accAgency;
        Group gp = new Group();
        gp.Name = 'COLL_' + accAgency.ExternalId__c;
        gp.Description = 'Test Descrizione';
        insert gp;
        Id RTGRPCollaborativo = Schema.SObjectType.Group__c.getRecordTypeInfosByDeveloperName()
            .get('UserGroup')
            .getRecordTypeId();
        Integer rand = Math.round(Math.random() * 1000);
        Group__c collaborativo = new Group__c();
        collaborativo.Name = accAgency.Name;
        collaborativo.RecordTypeId = RTGRPCollaborativo;
        collaborativo.ExternalId__c = accAgency.ExternalId__c + String.valueOf(rand);
        collaborativo.Agenzia__c = accAgency.Id;
        collaborativo.PublicGroupId__c = gp.Id;
        collaborativo.Description__c = gp.Description;
        insert collaborativo;
        Case c = new Case(
            dueDate__c = Date.today().addDays(365),
            Area__c = 'Sinistri',
            Detail__c = 'Contatta per sinistro aperto',
            Activity__c = 'Memo Sinistri',
            Agency__c = accAgency.Id,
            AssignedTo__c = UserInfo.getUserId()
        );
        insert c;
    }

    @isTest
    static void testBatchWithoutInputParameter() {
        Test.startTest();
        Database.executeBatch(new ManageCaseBatch());
        Test.stopTest();
    }

    @isTest
    static void testBatchWithoutInputParameterWithAssignedGroup() {
        Id idGroup = [SELECT Id FROM Group__c LIMIT 1].Id;
        Case c = [SELECT Id FROM Case LIMIT 1];
        c.AssignedTo__c = null;
        c.AssignedGroup__c = idGroup;
        update c;
        Test.startTest();
        Database.executeBatch(new ManageCaseBatch());
        Test.stopTest();
    }

    @isTest
    static void testBatchWithInputParameter() {
        Test.startTest();
        Database.executeBatch(new ManageCaseBatch(Date.today()));
        Test.stopTest();
    }

    @isTest
    static void testBatchWitestBatchWithBadInputParameterthInputParameter() {
        Test.startTest();
        Database.executeBatch(new ManageCaseBatch(null));
        Test.stopTest();
    }
}
