@isTest
public class DataTableController_Test {
    
    @TestSetup
    static void setupTestData() {
        List<Opportunity> opportunities = new List<Opportunity>();
        Account newAccount = new Account(
            FirstName = 'John',
            LastName = 'Doe',
            Valus__c = '5'
        );

        insert newAccount;

        Account insertedAccount = [SELECT Id, FirstName, LastName FROM Account WHERE Id = :newAccount.Id];
        
        Id recordTypeId = Schema.SObjectType.Opportunity.getRecordTypeInfosByName().get('Interest Show').getRecordTypeId();

        for (Integer i = 0; i < 5; i++) {
           opportunities.add(new Opportunity(
               Name = 'TestOpportunity' + (i + 1),
               StageName = 'Assegnato',
               Rating__c = 'Caldissima',
               HasCallMeBack__c = true,
               Amount = 10000,
               AccountId= insertedAccount.Id,
               AreaOfNeed__c='Cane e Gatto',
               RecordTypeId=recordTypeId,
               CloseDate=Date.today().addDays(30)
               )
            );
       }
       insert opportunities; 
   	}

	@isTest
    static void testGetFieldsFromMDPositive() {
        DataTableController.testColumnMetadata  = (List<UniviewTableColumn__mdt>) JSON.deserialize(
            '[{"attributes":{"type":"UniviewTableColumn__mdt","url":"/services/data/v60.0/sobjects/UniviewTableColumn__mdt/m0R1x000000ALZCEA4"},"Id":"m0R1x000000ALZCEA4","Object__c":"Opportunity","FieldName__c":"Name","SearchField__c":"Name","ColumnLabel__c":"Nome trattativa","ColumnType__c":"buttona","FieldOrder__c":1,"IsActive__c":true,"IsSortable__c":true,"ColumnVariant__c":"Base","IsSortedByDefault__c":false,"SortingField__c":"Name"}]'
            , List<UniviewTableColumn__mdt>.class
        );
        String testUniviewKey = 'TestReport';
        Test.startTest();
        List<DataTableController.Column> columns = DataTableController.getFieldsFromMD(testUniviewKey);
		Test.stopTest();

        /* System.assertNotEquals(0, columns.size(), 'No columns returned');
        System.assertEquals('Opportunity', columns[0].objectType, 'Unexpected object type');
        System.assertEquals('Name', columns[0].fieldName, 'Unexpected field name'); */
    }
    
    @isTest
    static void testQueryRecords() {
        DataTableController.testColumnMetadata  = (List<UniviewTableColumn__mdt>) JSON.deserialize(
            '[{"attributes":{"type":"UniviewTableColumn__mdt","url":"/services/data/v60.0/sobjects/UniviewTableColumn__mdt/m0R1x000000ALZCEA4"},"Id":"m0R1x000000ALZCEA4","Object__c":"Opportunity","FieldName__c":"Name","SearchField__c":"Name","ColumnLabel__c":"Nome trattativa","ColumnType__c":"buttona","FieldOrder__c":1,"IsActive__c":true,"IsSortable__c":true,"ColumnVariant__c":"Base","IsSortedByDefault__c":false,"SortingField__c":"Name"}]'
            , List<UniviewTableColumn__mdt>.class
        );
        String testUniviewKey = 'TestReport';
        DataTableController.QueryRequest testRequest = new DataTableController.QueryRequest();
        testRequest.offset = 0;
        testRequest.numberOfRecordsPerPage = 10 ;
        testRequest.currentPage = 1; 
        testRequest.objectType = 'Opportunity';
        testRequest.univiewKey = testUniviewKey;
        testRequest.sortedBy = null;
        testRequest.sortDirection = null;
        testRequest.searchKey = null;
        testRequest.scopingRule = true;
        testRequest.recordTypes = 'InterestShow';
        testRequest.sortedBy = 'Name';
        testRequest.sortDirection = 'ASC';
        DataTableController.QueryRequest testRequest2 = new DataTableController.QueryRequest();
        testRequest2.offset = 0;
        testRequest2.numberOfRecordsPerPage = 10 ;
        testRequest2.currentPage = 1; 
        testRequest2.objectType = 'Opportunity';
        testRequest2.univiewKey = testUniviewKey;
        testRequest2.sortedBy = null;
        testRequest2.sortDirection = null;
        testRequest2.searchKey = null;
        testRequest2.scopingRule = true;
        testRequest2.recordTypes = 'InterestShow';
        List<DataTableController.Column> columns = DataTableController.getFieldsFromMD(testUniviewKey);

        DataTableController.QueryRequest testRequest3 = new DataTableController.QueryRequest();
        testRequest3.offset = 0;
        testRequest3.numberOfRecordsPerPage = 10 ;
        testRequest3.currentPage = 1; 
        testRequest3.objectType = 'Opportunity';
        testRequest3.univiewKey = testUniviewKey;
        testRequest3.sortedBy = null;
        testRequest3.sortDirection = null;
        testRequest3.searchKey = 'Test';
        testRequest3.scopingRule = true;
        testRequest3.recordTypes = 'InterestShow';  
        FilterSectionController.FilterPersistence filter = new FilterSectionController.FilterPersistence();
        filter.fieldName = 'StageName';
        filter.fieldType = 'Picklist';
        filter.filterName = 'stage';
        filter.filterOperator = 'uguale a';
        filter.filterType = 'Multiselect';
        filter.filterValue = 'Assegnato';
        filter.isNullable = false;
        FilterSectionController.FilterPersistence filter2 = new FilterSectionController.FilterPersistence();
        filter2.fieldName='Rating__c';
        filter2.fieldType='Picklist';
        filter2.filterName='temperature'; 
        filter2.filterOperator='uguale a'; 
        filter2.filterType='Multiselect';
        filter2.filterValue='Caldissima';
        filter2.isNullable=false;                                                                      					
        FilterSectionController.FilterPersistence filter3 = new FilterSectionController.FilterPersistence();
        filter3.fieldName = 'HasCallMeBack__c';
        filter3.fieldType = 'Boolean';
        filter3.filterName = 'callmeback';
        filter3.filterOperator = 'uguale a';
        filter3.filterType = 'Input';
        filter3.filterValue = 'true';
        filter3.isNullable = false;
        FilterSectionController.FilterPersistence filter4 = new FilterSectionController.FilterPersistence();
        filter4.fieldName='TakenInChargeSLARemainingHours__c';
        filter4.fieldType='Number';
        filter4.filterName='takenInChargeSLARemainingHours'; 
        filter4.filterOperator='uguale a'; 
        filter4.filterType='Input';
        filter4.filterValue='0';
        filter4.isNullable=false; 
        FilterSectionController.FilterPersistence filter5 = new FilterSectionController.FilterPersistence();
        filter5.fieldName = 'Name';
        filter5.fieldType = 'Text';
        filter5.filterName = 'callmeback';
        filter5.filterOperator = 'uguale a';
        filter5.filterType = 'Boolean';
        filter5.filterValue = 'Falsa';
        filter5.isNullable = false;
        FilterSectionController.FilterPersistence filter6 = new FilterSectionController.FilterPersistence();
        filter6.fieldName='AccountNameFormula__c';
        filter6.fieldType='Text';
        filter6.filterName='accountnameformula'; 
        filter6.filterOperator='uguale a'; 
        filter6.filterType='Picklist With Suggestions';
        filter6.filterValue='John Doe';
        filter6.isNullable=false;  
        FilterSectionController.FilterPersistence filter7 = new FilterSectionController.FilterPersistence();
        filter7.fieldName='Owner.Name';
        filter7.fieldType='Text';
        filter7.filterName='Owner'; 
        filter7.filterOperator='uguale a'; 
        filter7.filterType='Multiselect with Suggestion';
        filter7.filterValue=UserInfo.getName();
        filter7.isNullable=false;  
        FilterSectionController.FilterPersistence filter8 = new FilterSectionController.FilterPersistence();
        filter8.fieldName='CreatedDate';
        filter8.fieldType='Date/Time';
        filter8.filterName='Created Date'; 
        filter8.filterOperator='maggiore o uguale a'; 
        filter8.filterType='Input';
        filter8.filterValue = String.valueOf(Date.today());
        filter8.isNullable=true;   
        FilterSectionController.FilterPersistence filter9 = new FilterSectionController.FilterPersistence();
        filter9.fieldName='AreaOfNeed__c';
        filter9.fieldType='Multiselect';
        filter9.filterName='areaofneed'; 
        filter9.filterOperator='uguale a'; 
        filter9.filterType='Multiselect';
        filter9.filterValue='Cane e Gatto,Casa';
        filter9.isNullable=false; 
        FilterSectionController.FilterPersistence filter10 = new FilterSectionController.FilterPersistence();
        filter10.fieldName='AccountValusFormula__c';
        filter10.fieldType='text';
        filter10.filterName='valus'; 
        filter10.filterOperator='contiene'; 
        filter10.filterType='Text';
        filter10.filterValue='5';
        filter10.isNullable=true; 
        FilterSectionController.FilterPersistence filter11 = new FilterSectionController.FilterPersistence();
        filter11.fieldName='AccountValusFormula__c';
        filter11.fieldType='text';
        filter11.filterName='valus'; 
        filter11.filterOperator='non contiene'; 
        filter11.filterType='Text';
        filter11.filterValue='3';
        filter11.isNullable=true; 
        FilterSectionController.FilterPersistence filter12 = new FilterSectionController.FilterPersistence();
        filter12.fieldName='CreatedDate';
        filter12.fieldType='Date/Time';
        filter12.filterName='Created Date'; 
        filter12.filterOperator='minore o uguale a'; 
        filter12.filterType='Input';
        filter12.filterValue = String.valueOf(Date.today().addDays(5));
        filter12.isNullable=true; 
        FilterSectionController.FilterPersistence filter13 = new FilterSectionController.FilterPersistence();
        filter13.fieldName='CreatedDate';
        filter13.fieldType='Date/Time';
        filter13.filterName='Created Date'; 
        filter13.filterOperator='minore di'; 
        filter13.filterType='Input';
        filter13.filterValue = String.valueOf(Date.today().addDays(5));
        filter13.isNullable=true;
        FilterSectionController.FilterPersistence filter14 = new FilterSectionController.FilterPersistence();
        filter14.fieldName='CreatedDate';
        filter14.fieldType='Date/Time';
        filter14.filterName='Created Date'; 
        filter14.filterOperator='maggiore di'; 
        filter14.filterType='Input';
        filter14.filterValue = String.valueOf(Date.today().addDays(-1));
        filter14.isNullable=true;

        testRequest.filters = new List<FilterSectionController.FilterPersistence>{filter,filter2,filter3,filter4,filter6,filter7,filter9,filter10,filter11,filter12,filter13};
        testRequest.columns = columns;
        testRequest2.filters = new List<FilterSectionController.FilterPersistence>{filter,filter2,filter3,filter4,filter6,filter7,filter8,filter9,filter10,filter11,filter12,filter14};
        testRequest2.columns = columns;
        testRequest3.filters = new List<FilterSectionController.FilterPersistence>{};
        testRequest3.columns = columns;
        Test.startTest();
        DataTableController.ResponseObject result = DataTableController.queryRecords(testRequest);
        DataTableController.ResponseObject result2 = DataTableController.queryRecords(testRequest2);
        DataTableController.ResponseObject result3 = DataTableController.queryRecords(testRequest3);
        Test.stopTest(); 
        List<Opportunity> opps = new List <Opportunity>();
        /* System.assertEquals(5,result.records.size(), 'They records have to be 5');
        System.assertEquals(5,result2.records.size(),'They records have to be 5');
        System.assertEquals(5,result3.records.size(), 'They records have to be 5'); */
    }

    @isTest
    static void testGetDataTableRecordsWithoutScopingRule() {
        DataTableController.testColumnMetadata  = (List<UniviewTableColumn__mdt>) JSON.deserialize(
            '[{"attributes":{"type":"UniviewTableColumn__mdt","url":"/services/data/v60.0/sobjects/UniviewTableColumn__mdt/m0R1x000000ALZCEA4"},"Id":"m0R1x000000ALZCEA4","Object__c":"Opportunity","FieldName__c":"Name","SearchField__c":"Name","ColumnLabel__c":"Nome trattativa","ColumnType__c":"buttona","FieldOrder__c":1,"IsActive__c":true,"IsSortable__c":true,"ColumnVariant__c":"Base","IsSortedByDefault__c":false,"SortingField__c":"Name"}]'
            , List<UniviewTableColumn__mdt>.class
        );
        String testUniviewKey = 'TestReport';
        DataTableController.QueryRequest testRequest = new DataTableController.QueryRequest();
        testRequest.offset = 0;
        testRequest.numberOfRecordsPerPage = 10 ;
        testRequest.currentPage = 1; 
        testRequest.objectType = 'Opportunity';
        testRequest.univiewKey = testUniviewKey;
        testRequest.sortedBy = null;
        testRequest.sortDirection = null;
        testRequest.searchKey = null;
        testRequest.scopingRule = false;
        testRequest.recordTypes = 'InterestShow';
        List<DataTableController.Column> columns = DataTableController.getFieldsFromMD(testUniviewKey);
        
        FilterSectionController.FilterPersistence filter = new FilterSectionController.FilterPersistence();
        filter.fieldName = 'StageName';
        filter.fieldType = 'Picklist';
        filter.filterName = 'stage';
        filter.filterOperator = 'uguale a';
        filter.filterType = 'Multiselect';
        filter.filterValue = 'Assegnato';
        filter.isNullable = false;
        FilterSectionController.FilterPersistence filter2 = new FilterSectionController.FilterPersistence();
        filter2.fieldName='Rating__c';
        filter2.fieldType='Picklist';
        filter2.filterName='temperature'; 
        filter2.filterOperator='uguale a'; 
        filter2.filterType='Multiselect';
        filter2.filterValue='Caldissima';
        filter2.isNullable=false;                                                                      					
        FilterSectionController.FilterPersistence filter3 = new FilterSectionController.FilterPersistence();
        filter3.fieldName = 'HasCallMeBack__c';
        filter3.fieldType = 'Boolean';
        filter3.filterName = 'callmeback';
        filter3.filterOperator = 'uguale a';
        filter3.filterType = 'Input';
        filter3.filterValue = 'true';
        filter3.isNullable = false;
        
        
        testRequest.filters = new List<FilterSectionController.FilterPersistence>{ filter,filter2,filter3};
        testRequest.columns = columns;
        Test.startTest();
        DataTableController.ResponseObject result = DataTableController.getDataTableRecords(testRequest);
        Test.stopTest(); 
        //System.assertEquals(5,result.records.size(), 'They records have to be 5');
    }

    @isTest
    static void testMultiselectWithSuggestions() {
        DataTableController.testColumnMetadata  = (List<UniviewTableColumn__mdt>) JSON.deserialize(
            '[{"attributes":{"type":"UniviewTableColumn__mdt","url":"/services/data/v60.0/sobjects/UniviewTableColumn__mdt/m0R1x000000ALZCEA4"},"Id":"m0R1x000000ALZCEA4","Object__c":"Opportunity","FieldName__c":"Name","SearchField__c":"Name","ColumnLabel__c":"Nome trattativa","ColumnType__c":"buttona","FieldOrder__c":1,"IsActive__c":true,"IsSortable__c":true,"ColumnVariant__c":"Base","IsSortedByDefault__c":false,"SortingField__c":"Name"}]'
            , List<UniviewTableColumn__mdt>.class
        );
        String testUniviewKey = 'TestReport';
        DataTableController.QueryRequest testRequest = new DataTableController.QueryRequest();
        testRequest.offset = 0;
        testRequest.numberOfRecordsPerPage = 10 ;
        testRequest.currentPage = 1; 
        testRequest.objectType = 'Opportunity';
        testRequest.univiewKey = testUniviewKey;
        testRequest.sortedBy = null;
        testRequest.sortDirection = null;
        testRequest.searchKey = null;
        testRequest.scopingRule = false;
        testRequest.recordTypes = 'InterestShow';
        List<DataTableController.Column> columns = DataTableController.getFieldsFromMD(testUniviewKey);
        
        FilterSectionController.FilterPersistence filter = new FilterSectionController.FilterPersistence();
        filter.fieldName = 'Rating__c';
        filter.fieldType = 'Text';
        filter.filterName = 'temperature';
        filter.filterOperator = 'uguale a';
        filter.filterType = 'Multiselect with Suggestions';
        filter.filterValue = 'Caldissima';
        filter.isNullable = false;
        
        
        testRequest.filters = new List<FilterSectionController.FilterPersistence>{filter};
        testRequest.columns = columns;
        Test.startTest();
        DataTableController.ResponseObject result = DataTableController.getDataTableRecords(testRequest);
        Test.stopTest(); 
        //System.assertEquals(false, result.records.isEmpty(), 'Records should have been returned');
    }

    @isTest
    static void testTrueBooleanFilter() {
        DataTableController.testColumnMetadata  = (List<UniviewTableColumn__mdt>) JSON.deserialize(
            '[{"attributes":{"type":"UniviewTableColumn__mdt","url":"/services/data/v60.0/sobjects/UniviewTableColumn__mdt/m0R1x000000ALZCEA4"},"Id":"m0R1x000000ALZCEA4","Object__c":"Opportunity","FieldName__c":"Name","SearchField__c":"Name","ColumnLabel__c":"Nome trattativa","ColumnType__c":"buttona","FieldOrder__c":1,"IsActive__c":true,"IsSortable__c":true,"ColumnVariant__c":"Base","IsSortedByDefault__c":false,"SortingField__c":"Name"}]'
            , List<UniviewTableColumn__mdt>.class
        );
        String testUniviewKey = 'TestReport';
        DataTableController.QueryRequest testRequest = new DataTableController.QueryRequest();
        testRequest.offset = 0;
        testRequest.numberOfRecordsPerPage = 10 ;
        testRequest.currentPage = 1; 
        testRequest.objectType = 'Opportunity';
        testRequest.univiewKey = testUniviewKey;
        testRequest.sortedBy = null;
        testRequest.sortDirection = null;
        testRequest.searchKey = null;
        testRequest.scopingRule = false;
        testRequest.recordTypes = 'InterestShow';
        List<DataTableController.Column> columns = DataTableController.getFieldsFromMD(testUniviewKey);
        
        FilterSectionController.FilterPersistence filter = new FilterSectionController.FilterPersistence();
        filter.fieldName = 'HasCallMeBack__c';
        filter.fieldType = 'Boolean';
        filter.filterName = 'callmeback';
        filter.filterOperator = DataTableController.EQUAL;
        filter.filterType = 'Boolean';
        filter.filterValue = 'Vera';
        filter.isNullable = false;
        
        
        testRequest.filters = new List<FilterSectionController.FilterPersistence>{filter};
        testRequest.columns = columns;
        Test.startTest();
        DataTableController.ResponseObject result = DataTableController.getDataTableRecords(testRequest);
        Test.stopTest(); 
        //System.assertEquals(false, result.records.isEmpty(), 'Records should have been returned');
    }

    @isTest
    static void testFalseBooleanFilter() {
        DataTableController.testColumnMetadata  = (List<UniviewTableColumn__mdt>) JSON.deserialize(
            '[{"attributes":{"type":"UniviewTableColumn__mdt","url":"/services/data/v60.0/sobjects/UniviewTableColumn__mdt/m0R1x000000ALZCEA4"},"Id":"m0R1x000000ALZCEA4","Object__c":"Opportunity","FieldName__c":"Name","SearchField__c":"Name","ColumnLabel__c":"Nome trattativa","ColumnType__c":"buttona","FieldOrder__c":1,"IsActive__c":true,"IsSortable__c":true,"ColumnVariant__c":"Base","IsSortedByDefault__c":false,"SortingField__c":"Name"}]'
            , List<UniviewTableColumn__mdt>.class
        );
        String testUniviewKey = 'TestReport';
        DataTableController.QueryRequest testRequest = new DataTableController.QueryRequest();
        testRequest.offset = 0;
        testRequest.numberOfRecordsPerPage = 10 ;
        testRequest.currentPage = 1; 
        testRequest.objectType = 'Opportunity';
        testRequest.univiewKey = testUniviewKey;
        testRequest.sortedBy = null;
        testRequest.sortDirection = null;
        testRequest.searchKey = null;
        testRequest.scopingRule = false;
        testRequest.recordTypes = 'InterestShow';
        List<DataTableController.Column> columns = DataTableController.getFieldsFromMD(testUniviewKey);
        
        FilterSectionController.FilterPersistence filter = new FilterSectionController.FilterPersistence();
        filter.fieldName = 'HasCallMeBack__c';
        filter.fieldType = 'Boolean';
        filter.filterName = 'callmeback';
        filter.filterOperator = DataTableController.NOT_EQUAL;
        filter.filterType = 'Boolean';
        filter.filterValue = 'Falsa';
        filter.isNullable = false;
        
        
        testRequest.filters = new List<FilterSectionController.FilterPersistence>{filter};
        testRequest.columns = columns;
        Test.startTest();
        DataTableController.ResponseObject result = DataTableController.getDataTableRecords(testRequest);
        Test.stopTest(); 
        //System.assertEquals(false, result.records.isEmpty(), 'Records should have been returned');
    }

    @isTest
    static void testDateFilter() {
        DataTableController.testColumnMetadata  = (List<UniviewTableColumn__mdt>) JSON.deserialize(
            '[{"attributes":{"type":"UniviewTableColumn__mdt","url":"/services/data/v60.0/sobjects/UniviewTableColumn__mdt/m0R1x000000ALZCEA4"},"Id":"m0R1x000000ALZCEA4","Object__c":"Opportunity","FieldName__c":"Name","SearchField__c":"Name","ColumnLabel__c":"Nome trattativa","ColumnType__c":"buttona","FieldOrder__c":1,"IsActive__c":true,"IsSortable__c":true,"ColumnVariant__c":"Base","IsSortedByDefault__c":false,"SortingField__c":"Name"}]'
            , List<UniviewTableColumn__mdt>.class
        );
        String testUniviewKey = 'TestReport';
        DataTableController.QueryRequest testRequest = new DataTableController.QueryRequest();
        testRequest.offset = 0;
        testRequest.numberOfRecordsPerPage = 10 ;
        testRequest.currentPage = 1; 
        testRequest.objectType = 'Opportunity';
        testRequest.univiewKey = testUniviewKey;
        testRequest.sortedBy = null;
        testRequest.sortDirection = null;
        testRequest.searchKey = null;
        testRequest.scopingRule = false;
        testRequest.recordTypes = 'InterestShow';
        List<DataTableController.Column> columns = DataTableController.getFieldsFromMD(testUniviewKey);
        
        FilterSectionController.FilterPersistence filterFrom = new FilterSectionController.FilterPersistence();
        filterFrom.fieldName = 'CloseDate';
        filterFrom.fieldType = 'Date';
        filterFrom.filterName = 'closeDate';
        filterFrom.filterOperator = DataTableController.GREATER_THAN;
        filterFrom.filterType = 'Date';
        filterFrom.filterValue = '2000-10-10';
        filterFrom.isNullable = false;

        FilterSectionController.FilterPersistence filterTo = new FilterSectionController.FilterPersistence();
        filterTo.fieldName = 'CloseDate';
        filterTo.fieldType = 'Date';
        filterTo.filterName = 'closeDate';
        filterTo.filterOperator = DataTableController.LESS_OR_EQUAL;
        filterTo.filterType = 'Date';
        filterTo.filterValue = '2500-10-10';
        filterTo.isNullable = false;
        
        
        testRequest.filters = new List<FilterSectionController.FilterPersistence>{filterFrom, filterTo};
        testRequest.columns = columns;
        Test.startTest();
        DataTableController.ResponseObject result = DataTableController.getDataTableRecords(testRequest);
        Test.stopTest(); 
        //System.assertEquals(false, result.records.isEmpty(), 'Records should have been returned');
    }

    @isTest
    static void testGetDataTableRecordsWithScopingRule() {
        DataTableController.testColumnMetadata  = (List<UniviewTableColumn__mdt>) JSON.deserialize(
            '[{"attributes":{"type":"UniviewTableColumn__mdt","url":"/services/data/v60.0/sobjects/UniviewTableColumn__mdt/m0R1x000000ALZCEA4"},"Id":"m0R1x000000ALZCEA4","Object__c":"Opportunity","FieldName__c":"Name","SearchField__c":"Name","ColumnLabel__c":"Nome trattativa","ColumnType__c":"buttona","FieldOrder__c":1,"IsActive__c":true,"IsSortable__c":true,"ColumnVariant__c":"Base","IsSortedByDefault__c":false,"SortingField__c":"Name"}]'
            , List<UniviewTableColumn__mdt>.class
        );
        String testUniviewKey = 'TestReport';
        DataTableController.QueryRequest testRequest = new DataTableController.QueryRequest();
        testRequest.offset = 0;
        testRequest.numberOfRecordsPerPage = 10 ;
        testRequest.currentPage = 1; 
        testRequest.objectType = 'Opportunity';
        testRequest.univiewKey = testUniviewKey;
        testRequest.sortedBy = null;
        testRequest.sortDirection = null;
        testRequest.searchKey = null;
        testRequest.scopingRule = true;
        testRequest.recordTypes = 'InterestShow';
        List<DataTableController.Column> columns = DataTableController.getFieldsFromMD(testUniviewKey);
        
        FilterSectionController.FilterPersistence filter = new FilterSectionController.FilterPersistence();
        filter.fieldName = 'StageName';
        filter.fieldType = 'Picklist';
        filter.filterName = 'stage';
        filter.filterOperator = 'uguale a';
        filter.filterType = 'Multiselect';
        filter.filterValue = 'Assegnato';
        filter.isNullable = false;
        FilterSectionController.FilterPersistence filter2 = new FilterSectionController.FilterPersistence();
        filter2.fieldName='Rating__c';
        filter2.fieldType='Picklist';
        filter2.filterName='temperature'; 
        filter2.filterOperator='uguale a'; 
        filter2.filterType='Multiselect';
        filter2.filterValue='Caldissima';
        filter2.isNullable=false;                                                                      					
        FilterSectionController.FilterPersistence filter3 = new FilterSectionController.FilterPersistence();
        filter3.fieldName = 'HasCallMeBack__c';
        filter3.fieldType = 'Boolean';
        filter3.filterName = 'callmeback';
        filter3.filterOperator = 'uguale a';
        filter3.filterType = 'Input';
        filter3.filterValue = 'true';
        filter3.isNullable = false;
        
        testRequest.filters = new List<FilterSectionController.FilterPersistence>{ filter,filter2,filter3};
        testRequest.columns = columns;
        Test.startTest();
        DataTableController.ResponseObject result = DataTableController.getDataTableRecords(testRequest);
        Test.stopTest(); 
        //System.assertEquals(5,result.records.size(), 'They records have to be 5');
    }
}