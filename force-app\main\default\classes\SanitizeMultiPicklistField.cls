/**
 * <AUTHOR>
 * @date 2024-9-25
 * @description Apex Action used to clean up multipicklist fields from duplicate values
 * @param input List<String> list containing the values that need to be cleaned
 * @return List<String> list containing the clean values
 */
public with sharing class SanitizeMultiPicklistField
{
    @InvocableMethod(label = 'Make Unique Multi-Picklist Value')
    public static List<String> sanitizeField(List<String> input)
    {
        if(input == null || input.isEmpty())
        {
            return new List<String>();
        }

        List<String> result = new List<String>();

        for(String current : input)
        {
            if(current == null)
            {
                continue;
            }
            List<String> parts = current.split(';');
            Set<String> uniqueItems = new Set<String>(parts);
            String value = String.join(uniqueItems, ';');
            result.add(value);
        }

        return result;
    }
}
