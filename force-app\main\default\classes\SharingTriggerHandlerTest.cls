@isTest
private class SharingTriggerHandlerTest {

    @isTest static void testHandle_WithUserRecord() {
        // Arrange: create a test User
        Profile p = [SELECT Id FROM Profile WHERE Name='Standard User' LIMIT 1];
        User u = new User(
            Username = 'testuser' + System.currentTimeMillis() + '@example.com',
            Alias = 'tuser',
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8',
            LastName = 'Test',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            ProfileId = p.Id,
            TimeZoneSidKey = 'Europe/Brussels'
        );
        insert u;

        // Act
        Test.startTest();
        SharingTriggerHandler.handle(new List<SObject>{ u });
        Test.stopTest();
    }
}