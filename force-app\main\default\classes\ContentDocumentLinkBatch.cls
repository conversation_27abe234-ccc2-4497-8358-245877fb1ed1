global class ContentDocumentLinkBatch implements Database.Batchable<SObject>, Database.Stateful {

    private Set<Id> contentDocIds;
    private DateTime dateStart;
    private DateTime dateEnd;

    private Integer successCount = 0;
    private Integer errorCount = 0;
    private Integer toCreateCount = 0;

    global ContentDocumentLinkBatch() {}

    global ContentDocumentLinkBatch(Set<Id> contentDocIds) {
        this.contentDocIds = contentDocIds;
    }

    global ContentDocumentLinkBatch(DateTime lastModifiedDateStart, DateTime lastModifiedDateEnd) {
        this.dateStart = lastModifiedDateStart;
        this.dateEnd = lastModifiedDateEnd;
    }

    global Database.QueryLocator start(Database.BatchableContext BC) {
        String query = 'SELECT Id FROM ContentDocument';

        if (contentDocIds != null && !contentDocIds.isEmpty()) {
            query += ' WHERE Id IN :contentDocIds';
        } else if (dateStart != null && dateEnd != null) {
            query += ' WHERE LastModifiedDate >= :dateStart AND LastModifiedDate <= :dateEnd';
        }

        System.debug(LoggingLevel.DEBUG, 'Query Start: ' + query);
        return Database.getQueryLocator(query);
    }

    global void execute(Database.BatchableContext BC, List<ContentDocument> scope) {
        try {
            Set<Id> docIds = new Set<Id>();
            for (ContentDocument doc : scope) {
                docIds.add(doc.Id);
            }
            System.debug('docIds:: ' + docIds);

            // Get latest ContentVersion for each ContentDocument
            Map<Id, ContentVersion> latestVersions = new Map<Id, ContentVersion>();
            for (ContentVersion cv : [
                SELECT Id, ContentDocumentId, ExternalId__c, VersionNumber
                FROM ContentVersion
                WHERE ContentDocumentId IN :docIds
                ORDER BY VersionNumber DESC
            ]) {
                if (!latestVersions.containsKey(cv.ContentDocumentId)) {
                    latestVersions.put(cv.ContentDocumentId, cv);
                }
            }
            System.debug('latestVersions:: ' + latestVersions);

            Set<String> accDetailExtIds = new Set<String>();
            Map<String, Id> mapExtIdToAccountDetails = new Map<String, Id>();

            // Parsing ExternalId__c and build keys
            Map<Id, String> docToAccDetailKey = new Map<Id, String>();
            for (ContentVersion cv : latestVersions.values()) {
                try {
                    if (String.isBlank(cv.ExternalId__c)) continue;

                    String[] parts = cv.ExternalId__c.split('_');
                    if (parts.size() < 2) continue;

                    String codiceCliente = parts[0];
                    String societa = parts[parts.size() - 1];

                    String key = 'ANAG2_' + codiceCliente + '_SOC_' + societa;
                    docToAccDetailKey.put(cv.ContentDocumentId, key);
                    accDetailExtIds.add(key);
                } catch (Exception parseEx) {
                    System.debug(LoggingLevel.ERROR, 'Errore nel parsing ExternalId__c: ' + parseEx.getMessage());
                    errorCount++;
                }
            }
            System.debug('docToAccDetailKey:: ' + docToAccDetailKey);
            System.debug('accDetailExtIds:: ' + accDetailExtIds);

            // Query AccountDetails
            Set<Id> accountIds = new Set<Id>();
            for (AccountDetails__c accDet : [
                SELECT Id, ExternalId__c, Relation__r.FinServ__Account__c
                FROM AccountDetails__c
                WHERE ExternalId__c IN :accDetailExtIds
            ]) {
                mapExtIdToAccountDetails.put(accDet.ExternalId__c, accDet.Id);
                accountIds.add(accDet.Relation__r.FinServ__Account__c);
            }
            System.debug('mapExtIdToAccountDetails:: ' + mapExtIdToAccountDetails);
            System.debug('accountIds:: ' + accountIds);

            // Query all accound agency details
            Map<String, Set<Id>> mapExtIdAgencyDetails = new Map<String, Set<Id>>();
            for(AccountAgencyDetails__c accAgencyDet : [
                SELECT Id, ExternalId__c, Relation__r.FinServ__Account__c 
                FROM AccountAgencyDetails__c 
                WHERE Relation__r.FinServ__Account__c IN :accountIds
            ]) {
                if (String.isBlank(accAgencyDet.ExternalId__c)) continue;

                for(String extId : mapExtIdToAccountDetails.keySet()) {
                    if(accAgencyDet.ExternalId__c.contains(extId)) {
                        if(!mapExtIdAgencyDetails.containsKey(extId)) {
                            mapExtIdAgencyDetails.put(extId, new Set<Id>());
                        }
                        mapExtIdAgencyDetails.get(extId).add(accAgencyDet.Id);
                    }
                }
            }
            System.debug('mapExtIdAgencyDetails:: ' + mapExtIdAgencyDetails);

            // Existing links
            Map<String, ContentDocumentLink> existingLinks = new Map<String, ContentDocumentLink>();
            for (ContentDocumentLink link : [
                SELECT Id, ContentDocumentId, LinkedEntityId
                FROM ContentDocumentLink
                WHERE ContentDocumentId IN :docToAccDetailKey.keySet()
            ]) {
                existingLinks.put(link.ContentDocumentId + '_' + link.LinkedEntityId, link);
            }
            System.debug('existingLinks:: ' + existingLinks);

            List<ContentDocumentLink> linksToInsert = new List<ContentDocumentLink>();

            for (Id docId : docToAccDetailKey.keySet()) {
                String extKey = docToAccDetailKey.get(docId);
                Id accDetailId = mapExtIdToAccountDetails.get(extKey);
                Set<Id> accAgencDetailIds = mapExtIdAgencyDetails.get(extKey);
                if (accDetailId == null) continue;

                String compositeKey = docId + '_' + accDetailId;
                if (!existingLinks.containsKey(compositeKey)) {
                    linksToInsert.add(new ContentDocumentLink(
                        ContentDocumentId = docId,
                        LinkedEntityId = accDetailId,
                        ShareType = 'V', // View access
                        Visibility = 'AllUsers'
                    ));
                }

                if(accAgencDetailIds != null && !accAgencDetailIds.isEmpty()) {
                    for(Id accAgencyDet : accAgencDetailIds) {
                        if (!existingLinks.containsKey(compositeKey)) {
                            linksToInsert.add(new ContentDocumentLink(
                                ContentDocumentId = docId,
                                LinkedEntityId = accAgencyDet,
                                ShareType = 'V', // View access
                                Visibility = 'AllUsers'
                            ));
                        }
                    }
                }
            }

            toCreateCount += linksToInsert.size();
            if (!linksToInsert.isEmpty()) {
                System.debug('linksToInsert:: ' + linksToInsert);
                Database.insert(linksToInsert, false);
                successCount += linksToInsert.size();
            }

        } catch (Exception ex) {
            System.debug(LoggingLevel.ERROR, 'Errore nell\'execute: ' + ex.getMessage());
            errorCount++;
        }
    }

    global void finish(Database.BatchableContext BC) {
        System.debug(LoggingLevel.DEBUG, '--- Riepilogo Batch ---');
        System.debug(LoggingLevel.DEBUG, 'ContentDocumentLink da creare: ' + toCreateCount);
        System.debug(LoggingLevel.DEBUG, 'ContentDocumentLink creati con successo: ' + successCount);
        System.debug(LoggingLevel.ERROR, 'ContentDocumentLink in errore: ' + errorCount);
    }

    global class BatchableException extends Exception {}
}