public with sharing class OpportunityCreator {
    @AuraEnabled
    public static Id createOpportunity(Id accountId) {
        try {
            Opportunity opp = new Opportunity(
                Name = 'Nuova Opportunità',
                AccountId = accountId,
                StageName = 'In gestione',
                CloseDate = Date.today().addDays(365),
                AssignedTo__c = UserInfo.getUserId()
            );

            insert opp;
            return opp.Id;

        } catch (Exception e) {
            // Log dell'errore (opzionale)
            System.debug('Errore durante la creazione dell\'opportunità: ' + e.getMessage());

            // Rilancia l'errore per gestirlo nel LWC
            throw new AuraHandledException('Errore durante la creazione dell\'opportunità: ' + e.getMessage());
        }
    }
}