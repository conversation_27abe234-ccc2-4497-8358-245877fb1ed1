public with sharing class CustomLogJSONViewerLX {
    
    @AuraEnabled(cacheable=true)
    public static String getRelatedFilesByRecordId(String recordId) {

        // Get ContentDocumentLink       
        String docLink = [SELECT ContentDocumentId 
                    FROM ContentDocumentLink 
                    WHERE LinkedEntityId = :recordId
                    LIMIT 1].ContentDocumentId;
    
        ContentVersion doc = [SELECT VersionData 
                              FROM ContentVersion 
                              WHERE ContentDocumentId = :docLink
                              ORDER BY CreatedDate DESC 
                              LIMIT 1];

        return doc.VersionData.toString();
    }

}