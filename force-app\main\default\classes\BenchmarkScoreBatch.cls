/***************************************************************************************************************************************************
* <AUTHOR>
* @description    BenchmarkScoreBatch class for processing and updating KPI records with benchmark scores. This class implements Database.Batchable 
*                 and Schedulable interfaces, allowing it to run as a batch job or a scheduled job. It retrieves KPI records, calculates average 
*                 benchmark scores based on Agency Size and Cluster, and updates the KPI records with these scores.
* @date           2024-03-22
****************************************************************************************************************************************************/

global class BenchmarkScoreBatch implements Database.Batchable<sObject>, Schedulable {

    /******************************************************************************************
    * @description  This method generates the query to retrieve KPI records for processing in batch.
    * @param        BC - The batchable context instance.
    *******************************************************************************************/
    global Database.QueryLocator start(Database.BatchableContext BC) {
        String query = 'SELECT Id, Agency__r.Size__c, Agency__r.Cluster__c, ScoreTotal__c  FROM KPI__c limit 50000';
        return Database.getQueryLocator(query);
    }

    
    /******************************************************************************************
    * @description  This method processes the batch of KPI records, calculates the benchmark scores, and updates the records.
    * @param        BC - The batchable context instance.
    * @param        scope - The list of KPI records to process.
    *******************************************************************************************/
    global void execute(Database.BatchableContext BC, List<KPI__c> scope) {
        System.debug(JSON.serialize(scope));
        if(scope != null && !scope.isEmpty()) {
            Map<String, Decimal> mapScore = new Map<String, Decimal>();
            List<AggregateResult> listKpi = [SELECT Agency__r.Size__c, 
                                                    Agency__r.Cluster__c,
                                                    AVG(ScoreTotal__c) AverageScoreTotal
                                            FROM KPI__c
                                            GROUP BY Agency__r.Size__c, 
                                                     Agency__r.Cluster__c];

            for(AggregateResult ar : listKpi){
                mapScore.put(ar.get('Size__c') + '__' + ar.get('Cluster__c') + '__AverageScoreTotal', (Decimal)ar.get('AverageScoreTotal'));
            }

            for (KPI__c kpiRecord : scope) {
                kpiRecord.BenchmarkScore__c = (Decimal) mapScore.get(kpiRecord.Agency__r.Size__c + '__' + kpiRecord.Agency__r.Cluster__c + '__AverageScoreTotal');
            }
            
            update scope;
        }
    }

    /******************************************************************************************
    * @description  This method is executed after all batches are processed.
    * @param        BC - The batchable context instance.
    *******************************************************************************************/
    global void finish(Database.BatchableContext BC) { }

    /******************************************************************************************
    * @description  This method schedules the batch job for execution.
    * @param        sc - The schedulable context instance.
    *******************************************************************************************/
    global void execute(SchedulableContext sc) {
        BenchmarkScoreBatch batchJob = new BenchmarkScoreBatch();
        Database.executeBatch(batchJob);
    }
}