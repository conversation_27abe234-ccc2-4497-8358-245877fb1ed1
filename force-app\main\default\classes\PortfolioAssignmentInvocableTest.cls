@isTest
private class PortfolioAssignmentInvocableTest {

    @isTest
    static void testCallWithValidRequest() {
        // Create a valid InvocableRequest
        PortfolioAssignmentInvocable.InvocableRequest request = new PortfolioAssignmentInvocable.InvocableRequest();
        request.operation = 'TestOperation';
        request.agencyCode = 'TestAgencyCode';
        request.contractId = 'TestContractId';
        request.cip = 'TestCIP';

        // Call the method with a valid request
        Test.startTest();
        PortfolioAssignmentInvocable.call(new List<PortfolioAssignmentInvocable.InvocableRequest>{ request });
        Test.stopTest();
    }

    @isTest
    static void testCallWithEmptyRequest() {
        // Call the method with an empty request list
        try {
            Test.startTest();
            PortfolioAssignmentInvocable.call(new List<PortfolioAssignmentInvocable.InvocableRequest>());
            Test.stopTest();
            System.assert(false, 'Expected PortfolioAssignmentException was not thrown');
        } catch (PortfolioAssignmentInvocable.PortfolioAssignmentException e) {
            System.assertEquals('No valid requests were provided to the portfolio assignment callout', e.getMessage());
        }
    }
    
    @isTest
    static void testPortfolioAssignmentRequestConstructor() {
        String agencyCode = 'AG001';
        String contractId = 'C123';
        String cip = 'CIP456';
        
        PortfolioAssignmentInvocable.PortfolioAssignmentRequest request =
            new PortfolioAssignmentInvocable.PortfolioAssignmentRequest(agencyCode, contractId, cip);
        
        System.assertEquals(agencyCode, request.agencyCode, 'agencyCode dovrebbe essere assegnato correttamente');
        System.assertEquals(contractId, request.contractId, 'contractId dovrebbe essere assegnato correttamente');
        System.assertEquals(cip, request.cip, 'cip dovrebbe essere assegnato correttamente');
    }

}