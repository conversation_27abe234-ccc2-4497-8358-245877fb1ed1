@isTest
public with sharing class EngineAgencyRankingInvocable_Test {
    @TestSetup
    static void makeData() {
        FlowTriggersActivation__c triggerSettings = new FlowTriggersActivation__c();
        triggerSettings.SkipTriggers__c = true;
        insert triggerSettings;

        Account defaultAgency = new Account(
            Name = 'Default Agency',
            AccountNumber = 'AG-SOGEINT',
            ExternalId__c = '0000',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName()
                .get('Agency')
                .getRecordTypeId(),
            BillingLatitude = 50.464664,
            BillingLongitude = 15.788540,
            Size__c = 'Media',
            Cluster__c = 'Cluster 2',
            OmnichannelAgreement__c = true,
            Type = 'Privata'
        );
        insert defaultAgency;

        Account defaultSalespoint = new Account(
            Name = 'Default SP',
            AccountNumber = 'SP-SOGEINT',
            ExternalId__c = '0001',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName()
                .get('Agency')
                .getRecordTypeId(),
            BillingLatitude = 50.464664,
            BillingLongitude = 15.788540,
            Size__c = 'Media',
            Cluster__c = 'Cluster 2',
            Agency__c = defaultAgency.Id,
            IsLocatorEnabled__c = true
        );
        insert defaultSalespoint;

        System.debug('Account new: ' + defaultAgency);
        KPI__c defaultAgencyKPI = new KPI__c(
            Agency__c = defaultAgency.Id,
            Name = 'Default KPI',
            ScoreTotal__c = 1.6,
            // NumberClientsPortfolio__c = 221,
            // NumberContactableClients__c = 491
            BenchmarkConversionAssignedActivities__c = 79,
            BenchmarkContactActivitiesAssigned__c = 55.6,
            BenchmarkDigitalPenetration__c = 69,
            BenchmarkOmnichannelQuotes__c = 67,
            BenchmarkPrivateAreaRegistration__c = 72,
            BenchmarkAverageLeadProcessingTime__c = 7.6,
            // ConversionAssignedContactActivities__c = 72,
            // ContactActivitiesProcessingAssigned__c = 45,
            DigitalPenetration__c = 50,
            OmnichannelQuotes__c = 55,
            PrivateAreaRegistration__c = 60,
            AverageLeadProcessingTime__c = 5
        );
        insert defaultAgencyKPI;

        defaultAgency.KPI__c = defaultAgencyKPI.Id;
        update defaultAgency;
    }

    @IsTest
    static void rankAgenciesTest() {
        EngineAgencyRanking.testingMatrixRows = new List<CalculationMatrixRow>{
            new CalculationMatrixRow(
                InputData = '{"Canale": "Preventivatore digitale Unica","Motore":"Meritocratico"}',
                OutputData = '{"%TassoContattabilità":40,"RaggioCentroideKM":1.5,"NumeroMaxRiassegnazioni":0,"AssegnazioneDefault":"Contact Center","AgenziaDefault":"AG-SOGEINT","RaggioEstensioneKM":3,"Discrezionalità":false}'
            )
        };
        EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput();
        input.source = 'Preventivatore digitale Unica';
        input.macroAreas = new List<String>{'Area1', 'Area2'};
        input.fiscalCode = '1234567890';
        input.latitude = 45.4642;
        input.longitude = 9.1900;
        input.zipCode = '20100';
        input.useDefaultAgency = false;
        input.agenciestoExcludeIds = new List<Id>();
        input.company= 'UniploSai';

        Test.startTest();
        try{
            List<EngineWrapper_V2.EngineWrapperInvocableOutput> output = EngineAgencyRankingInvocable.rankAgencies(new List<EngineWrapper_V2.EngineWrapperInput> {input});
        }catch(Exception ex){}
        Test.stopTest();

        //Assert.areEqual(1, output[0].selectedAgencies?.size(), 'Salespoints should have been returned!');
    }

    @IsTest
    static void rankAgenciesErrorTest() {
        EngineAgencyRanking.testingMatrixRows = new List<CalculationMatrixRow>{
            new CalculationMatrixRow(
                InputData = '{"Canale": "Preventivatore digitale Unica","Motore":"Meritocratico"}',
                OutputData = '{"%TassoContattabilità":40,"RaggioCentroideKM":1.5,"NumeroMaxRiassegnazioni":0,"AssegnazioneDefault":"Contact Center","AgenziaDefault":"AG-SOGEINT","RaggioEstensioneKM":3,"Discrezionalità":false}'
            )
        };

        Test.startTest();
        try{
            EngineWrapper_V2.EngineWrapperInvocableOutput output = EngineAgencyRankingInvocable.getSelectedAccounts(new EngineWrapper_V2.EngineWrapperOutput('',true,null,null));
        }catch(Exception ex){}
        Test.stopTest();

        //System.debug('# output: ' + output);
        //Assert.areEqual('NO_REQUEST', output[0].errorMessage, 'NO_REQUEST error message should have been returned!');
    }
}