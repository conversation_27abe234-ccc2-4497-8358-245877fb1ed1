public with sharing class ReportQueryControllerV2 {

    @AuraEnabled(cacheable=true)
    public static List<Opportunity> getOpportunities(String amount) {
    
        String query = 'SELECT Id, Name, CloseDate, Amount FROM Opportunity';
        // if (String.isNotBlank(amount)) {
        //     query += ' WHERE Amount = :amount';
        // }
        
        
        return Database.query(query);
    }
}
