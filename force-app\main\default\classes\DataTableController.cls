/***************************************************************************************************************************************************
* <AUTHOR>
* @description    DataTableController class for handling operations related to data tables, including querying records based on dynamic filters, 
*                 retrieving metadata for columns, and processing the results for display in a data table. This class supports both with sharing 
*                 and without sharing contexts for data access and ensures secure and efficient data retrieval.
* @date           2024-01-29
****************************************************************************************************************************************************/
public inherited sharing class  DataTableController {

    // Custom exception class for DataTableController
    public class DataTableControllerException extends Exception {}

    // Constants for different filter operators
    public final static String EQUAL = 'uguale a';
    public final static String NOT_EQUAL = 'diverso';
    public final static String GREATER_THAN = 'maggiore di';
    public final static String LESS_THAN = 'minore di';
    public final static String GREATER_OR_EQUAL = 'maggiore o uguale a';
    public final static String LESS_OR_EQUAL = 'minore o uguale a';
    public final static String CONTAINS = 'contiene';
    public final static String NOT_CONTAINS = 'non contiene';
    
    // Map of filter operators to their corresponding SQL symbols
    public final static Map<String, String> OPERATORS = new Map<String, String> {
        EQUAL => '=',
        NOT_EQUAL => '!=',
        GREATER_THAN => '>',
        LESS_THAN => '<',
        GREATER_OR_EQUAL => '>=',
        LESS_OR_EQUAL => '<=',
        CONTAINS => 'LIKE',
        NOT_CONTAINS => 'NOT LIKE'
    };

    // List to store test column metadata for testing purposes
    @TestVisible private static List<UniviewTableColumn__mdt> testColumnMetadata;
    
    /******************************************************************************************
    * @description  Retrieves column metadata based on the provided key from custom metadata type UniviewTableColumn__mdt.
    * @param        univiewKey - The key used to filter the metadata records.
    * @return       A list of Column objects containing the metadata information.
    * @throws       AuraHandledException if any exception occurs during the execution.
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public  static List<Column> getFieldsFromMD(String univiewKey) {
        try {
            List<UniviewTableColumn__mdt> columnMetadata = [
                SELECT id,Object__c, FieldName__c, SearchField__c, ColumnLabel__c, ColumnType__c, FieldOrder__c, IsActive__c, IsSortable__c,
                ColumnYearFormat__c, ColumnMonthFormat__c, ColumnDayFormat__c, ColumnHourFormat__c, ColumnMinuteFormat__c, ColumnCurrencyCode__c, 
                ColumnVariant__c, DefaultSortDirection__c, DefaultSortOrder__c, IsSortedByDefault__c, SortingField__c
                FROM UniviewTableColumn__mdt 
                WHERE UniviewKey__c =: univiewKey AND IsActive__c = true
                WITH USER_MODE
                ORDER BY FieldOrder__c ASC
            ];
            if(Test.isRunningTest()) {
                columnMetadata = testColumnMetadata;
            }

            List<Column> columns = new List<Column>();
            for(UniviewTableColumn__mdt column : columnMetadata) {
                columns.add(new Column(
                    column.Object__c,
                    column.FieldName__c,
                    column.SearchField__c,
                    column.ColumnLabel__c,
                    column.ColumnType__c,
                    column.ColumnYearFormat__c,
                    column.ColumnMonthFormat__c,
                    column.ColumnDayFormat__c,
                    column.ColumnHourFormat__c,
                    column.ColumnMinuteFormat__c,
                    column.ColumnCurrencyCode__c,
                    column.ColumnVariant__c,
                    (Integer) column.FieldOrder__c,
                    column.IsSortable__c,
                    column.IsActive__c,
                    column.DefaultSortDirection__c,
                    (Integer) column.DefaultSortOrder__c,
                    column.IsSortedByDefault__c,
                    column.SortingField__c
                ));
            }
            return columns;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    // Inner class to represent a column in the data table
    public class Column {
        @AuraEnabled
        public String objectType {get;set;}
        @AuraEnabled
        public String fieldName {get;set;}
        @AuraEnabled
        public String searchField {get;set;}
        @AuraEnabled
        public String columnLabel {get;set;}
        @AuraEnabled
        public String columnType {get;set;}
        @AuraEnabled
        public String columnYearFormat {get;set;}
        @AuraEnabled
        public String columnMonthFormat {get;set;}
        @AuraEnabled
        public String columnDayFormat {get;set;}
        @AuraEnabled
        public String columnHourFormat {get;set;}
        @AuraEnabled
        public String columnMinuteFormat {get;set;}
        @AuraEnabled
        public String columnCurrencyCode {get;set;}
        @AuraEnabled
        public String columnVariant {get;set;}
        @AuraEnabled
        public Integer fieldOrder {get;set;}
        @AuraEnabled
        public Boolean isSortable {get;set;}
        @AuraEnabled
        public Boolean isActive {get;set;}
        @AuraEnabled
        public String defaultSortDirection {get;set;}
        @AuraEnabled
        public Integer defaultSortOrder {get;set;}
        @AuraEnabled
        public Boolean isSortedByDefault {get;set;}
        @AuraEnabled
        public String sortingField {get;set;}

        // Constructor for Column class
        public Column(
            String objectType,
            String fieldName,
            String searchField,
            String columnLabel,
            String columnType,
            String columnYearFormat,
            String columnMonthFormat,
            String columnDayFormat,
            String columnHourFormat,
            String columnMinuteFormat,
            String columnCurrencyCode,
            String columnVariant,
            Integer fieldOrder,
            Boolean isSortable,
            Boolean isActive,
            String defaultSortDirection,
            Integer defaultSortOrder,
            Boolean isSortedByDefault,
            String sortingField
        ) {
            this.objectType = objectType;
            this.fieldName = fieldName;
            this.searchField = searchField;
            this.columnLabel = columnLabel;
            this.columnType = columnType;
            this.columnYearFormat = columnYearFormat;
            this.columnMonthFormat = columnMonthFormat;
            this.columnDayFormat = columnDayFormat;
            this.columnHourFormat = columnHourFormat;
            this.columnMinuteFormat = columnMinuteFormat;
            this.columnCurrencyCode = columnCurrencyCode;
            this.columnVariant = columnVariant;
            this.fieldOrder = fieldOrder;
            this.isSortable = isSortable;
            this.isActive = isActive;
            this.defaultSortDirection = defaultSortDirection;
            this.defaultSortOrder = defaultSortOrder;
            this.isSortedByDefault = isSortedByDefault;
            this.sortingField = sortingField;
        }

        // Default constructor for Column class
        public Column() {}
    }

    // Inner class to represent the response object containing the queried records and pagination info
    public class ResponseObject {
        @AuraEnabled
        public List<SObject> records {get;set;}
        @AuraEnabled
        public Integer numberOfPages {get;set;}
        
        // Constructor for ResponseObject class
        public ResponseObject(List<SObject> records, Integer numberOfPages) {
            this.records = records;
            this.numberOfPages = numberOfPages;
        }
    }

    // Inner class to represent the query request parameters
    public class QueryRequest{
        @AuraEnabled
        public List<FilterSectionController.FilterPersistence> filters {get;set;}
        @AuraEnabled
        public List<Column> columns {get;set;}
        @AuraEnabled
        public Integer offset {get;set;}
        @AuraEnabled
        public Integer numberOfRecordsPerPage {get;set;}
        @AuraEnabled
        public Integer currentPage {get;set;}
        @AuraEnabled
        public String objectType {get;set;}
        @AuraEnabled
        public String univiewKey {get;set;}
        @AuraEnabled
        public String sortedBy {get;set;}
        @AuraEnabled
        public String sortDirection {get;set;}
        @AuraEnabled
        public String searchKey {get;set;}
        @AuraEnabled
        public Boolean scopingRule {get;set;}
        @AuraEnabled
        public String recordTypes {get;set;}
    }

    /******************************************************************************************
    * @description  Executes a dynamic query based on the specified request parameters, including filters, sorting, and pagination.
    * @param        request - The QueryRequest object containing the query parameters.
    * @return       A ResponseObject containing the queried records and the number of pages.
    * @throws       AuraHandledException if any exception occurs during the execution.
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static ResponseObject queryRecords(QueryRequest request) {
        try {
            String query = createQuery(request, request.columns);
            System.debug('# query: ' + query);
            List<SObject> allRecordsQueried = Database.query(query);

            // Custom search on each column
            List<SObject> searchCompatibleRecords = new List<SObject>();
            if(String.isNotBlank(request.searchKey)) {
                for(SObject record : allRecordsQueried) {
                    for(Column column : request.columns) {
                        if(record.get(column.searchField) != null && String.valueOf(record.get(column.searchField))?.containsIgnoreCase(request.searchKey)) {
                            searchCompatibleRecords.add(record);
                            break;
                        }
                    }
                }
            }

            Integer recordCount = String.isNotBlank(request.searchKey) ? searchCompatibleRecords.size() : allRecordsQueried.size();
            Integer endIndex = Math.min(request.currentPage * request.numberOfRecordsPerPage, recordCount);
            
            List<SObject> recordsToReturn = new List<SObject>();
            for (Integer i = request.offset; i< endIndex; i++) {
                recordsToReturn.add(String.isNotBlank(request.searchKey) ? searchCompatibleRecords[i] : allRecordsQueried[i]);
            }
           
            return new ResponseObject (recordsToReturn, (Integer) Math.ceil((Decimal) recordCount / (Decimal) request.numberOfRecordsPerPage));
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }


    /******************************************************************************************
    * @description  Creates a dynamic SOQL query based on the specified request parameters and columns.
    * @param        request - The QueryRequest object containing the query parameters.
    * @param        columns - The list of Column objects representing the fields to be queried.
    * @return       A string containing the dynamically constructed SOQL query.
    *******************************************************************************************/
    public static String createQuery(QueryRequest request, List<Column> columns) {    
        // Create SELECT portion of the query
       
        String query = 'SELECT';
        for(Column c : columns) {
            query += ' ' + c.fieldName + ',' + (c.fieldName != c.searchField ? ' ' + c.searchField + ',' : '');
        }
        query = query.removeEnd(',') + ' ';

        // Creating the FROM portion of the query
        query += 'FROM ' + request.objectType + ' ';

        // Creating the WHERE portion of the query
        query += 'WHERE';
        if (String.isNotBlank(request.recordTypes)) {
            List<String> recordTypesList = request.recordTypes.split(';');
            if (!recordTypesList.isEmpty()) {             
                query += ' RecordType.DeveloperName IN ' + 
                +'(\'' + String.join(recordTypesList, '\',\'') + '\') ';
            }
        }
        for(FilterSectionController.FilterPersistence filter : request.filters) {
            if((filter.fieldType == 'Number' || filter.fieldType == 'Boolean') && (String.isNotBlank(filter.filterValue) || filter.isNullable)) {
                // Create numeric filter statements
                try {
                    if (String.isBlank(filter.filterValue)) {
                        query += ' AND ' + filter.fieldName + ' ' + OPERATORS.get(filter.filterOperator) + ' ' + null;
                    }
                    else{
                        if(filter.filterValue=='Vera'){
                            filter.filterValue = 'true';
                        }
                        else if(filter.filterValue=='Falsa'){
                            filter.filterValue = 'false';
                        }
                        query += ' AND ' + filter.fieldName + ' ' + OPERATORS.get(filter.filterOperator) + ' ' + filter.filterValue;
                    }
                } catch (Exception e) {
                    System.debug('In the Number Type Fields: ' + e.getMessage());
                    throw new DataTableControllerException(e.getMessage());
                }
            }else if(filter.fieldType == 'Text' && filter.filterType == 'Multiselect with Suggestions' && String.isNotBlank(filter.filterValue)) {
                try {
                    List<String> valuesList = filter.filterValue.split(',');
                    String valuesString = '';
                    for(String s : valuesList) {
                        valuesString += '\'' + s + '\',';
                    }
                    valuesString = '(' + valuesString.removeEnd(',') + ')';
                    query += ' AND ' + filter.fieldName
                    + ' IN ' + valuesString;
	
                } catch (Exception e) {
                    System.debug('In the Multiselect with Suggestions Type Fields: ' + e.getMessage());
                    throw new DataTableControllerException(e.getMessage());
                }
            }
            else if(filter.fieldType == 'Text' && (String.isNotBlank(filter.filterValue) || filter.isNullable)) {
                try {
                    if(filter.filterOperator == CONTAINS) {
                        query += ' AND ' + filter.fieldName + ' ' + OPERATORS.get(filter.filterOperator) + ' \'%' + filter.filterValue + '%\'' ;
                    }else if(filter.filterOperator == NOT_CONTAINS) {
                        List<String> operatorParts = OPERATORS.get(filter.filterOperator).split(' ');
                        String notOperator = operatorParts[0];
                        String likeOperator = operatorParts[1];
                        query += ' AND ' + '(' + notOperator + ' ' + filter.fieldName + ' ' + likeOperator + ' \'%' + filter.filterValue + '%\'' +')';
                    }else{
                        filter.filterValue = filter.filterValue == null ? '' : filter.filterValue;
                        query += ' AND ' + filter.fieldName + ' ' + OPERATORS.get(filter.filterOperator) + ' \'' + filter.filterValue + '\'';
                    }
                } catch (Exception e) {
                    System.debug('In the Text Type Fields: ' + e.getMessage());
                    throw new DataTableControllerException(e.getMessage());
                } 
            }
            else if(filter.fieldType == 'Picklist' && filter.filterType == 'Multiselect' && String.isNotBlank(filter.filterValue)) {
                try {
                    // Create picklist filter
                    if(filter.filterValue!='Tutti'){
                    List<String> valuesList = filter.filterValue.split(',');
                    String valuesString = '';

                    for(String s : valuesList) {
                        valuesString += '\'' + s + '\',';
                    }
                    valuesString = '(' + valuesString.removeEnd(',') + ')';
                    query += ' AND ' + filter.fieldName 
                    + ' IN ' + valuesString;
                    }
                } catch (Exception e) {
                    System.debug('In the Multiselect Picklist Type Fields: ' + e.getMessage());
                    throw new DataTableControllerException(e.getMessage());
                }
            }
            else if(filter.fieldType == 'Multiselect' && filter.filterType == 'Multiselect' && String.isNotBlank(filter.filterValue)) {
                try {
                    List<String> valuesList = filter.filterValue.split(',');
                    String valuesString = '';
                    for(String s : valuesList) {
                        valuesString += '\'' + s + '\',';
                    }
                    valuesString = '(' + valuesString.removeEnd(',') + ')';
                    String operator = filter.filterOperator == EQUAL ? ' IN ' : ' INCLUDES ';
                    query += ' AND ' + filter.fieldName + operator + valuesString;
                } catch (Exception e) {
                    System.debug('In the Multiselect Type Fields: ' + e.getMessage());
                    throw new DataTableControllerException(e.getMessage());
                }  
            }
            else if(filter.fieldType == 'Date' && String.isNotBlank(filter.filterValue)) {
                try {
                    List<String> dateParts = filter.filterValue.split('-');
                    Datetime dateInstance;
                    if(filter.filterOperator == LESS_THAN || filter.filterOperator == LESS_OR_EQUAL) {
                        dateInstance = Datetime.newinstanceGMT(Integer.valueOf(dateParts[0]), Integer.valueOf(dateParts[1]), Integer.valueOf(dateParts[2]), 23, 59, 59);
                    }
                    else if(filter.filterOperator == GREATER_THAN || filter.filterOperator == GREATER_OR_EQUAL) {
                        dateInstance = Datetime.newinstanceGMT(Integer.valueOf(dateParts[0]), Integer.valueOf(dateParts[1]), Integer.valueOf(dateParts[2]));
                    }
                    else if(filter.filterOperator == GREATER_THAN ) {
                        dateInstance = Datetime.newinstanceGMT(Integer.valueOf(dateParts[0]), Integer.valueOf(dateParts[1]), Integer.valueOf(dateParts[2]), 23, 59, 59);
                    }
                    else if(filter.filterOperator == GREATER_OR_EQUAL) {
                        dateInstance = Datetime.newinstanceGMT(Integer.valueOf(dateParts[0]), Integer.valueOf(dateParts[1]), Integer.valueOf(dateParts[2]));
                    }
                    query += ' AND ' + filter.fieldName + ' ' + OPERATORS.get(filter.filterOperator) + ' ' + dateInstance.formatGMT('yyyy-MM-dd');
                } catch(DmlException e) {
                    System.debug('In the Date Type Fields: ' + e.getMessage());
                    throw new DataTableControllerException(e.getMessage());
                }
            }
            else if(filter.fieldType == 'Date/Time' && String.isNotBlank(filter.filterValue)) {
                try {
                    List<String> dateParts = filter.filterValue.split('-');
                    Datetime dateInstance;
                    if(filter.filterOperator == LESS_THAN ) {
                        dateInstance = Datetime.newinstanceGMT(Integer.valueOf(dateParts[0]), Integer.valueOf(dateParts[1]), Integer.valueOf(dateParts[2]), 00, 00, 00);
                    }
                    else if(filter.filterOperator == LESS_OR_EQUAL) {
                        dateInstance = Datetime.newinstanceGMT(Integer.valueOf(dateParts[0]), Integer.valueOf(dateParts[1]), Integer.valueOf(dateParts[2]), 23, 59, 59);
                       
                    }
                    else if(filter.filterOperator == GREATER_THAN ) {
                        dateInstance = Datetime.newinstanceGMT(Integer.valueOf(dateParts[0]), Integer.valueOf(dateParts[1]), Integer.valueOf(dateParts[2]), 23, 59, 59);
                    }
                    else if(filter.filterOperator == GREATER_OR_EQUAL) {
                        dateInstance = Datetime.newinstanceGMT(Integer.valueOf(dateParts[0]), Integer.valueOf(dateParts[1]), Integer.valueOf(dateParts[2]), 00, 00, 00);
                    }
                    query += ' AND ' + filter.fieldName + ' ' + OPERATORS.get(filter.filterOperator) + ' ' + dateInstance.formatGMT('yyyy-MM-dd\'T\'HH:mm:ss.SSS\'Z\'');
                } catch(DmlException e) {
                    System.debug('In the Date/Time Type Fields: ' + e.getMessage());
                    throw new DataTableControllerException(e.getMessage());
                }
            }
        }

        query = query.replace('WHERE AND', 'WHERE');
        query = query.removeEnd(' WHERE');

        // Creating the ORDER BY portion of the query - Datatable sorting
        if(String.isNotBlank(request.sortedBy) && String.isNotBlank(request.sortDirection)) {
            query += ' ORDER BY ' + request.sortedBy + ' ' + request.sortDirection + ' NULLS LAST,';
        }

        // Creating the ORDER BY portion of the query - Default sorting
        List<UniviewTableColumn__mdt> columnSortedByDefaultMetadata = getColumnsSortedByDefault(request.univiewKey);
        query = !query.contains('ORDER BY') ? query + ' ORDER BY' : query;
        for(UniviewTableColumn__mdt column : columnSortedByDefaultMetadata) {
            query  += ' ' + column.SortingField__c + ' ' + column.DefaultSortDirection__c + ' NULLS LAST,';
        }
        query = query.removeEnd(',');
        query = query.removeEnd('ORDER BY');
       
        return query;
    }

    /******************************************************************************************
    * @description  Fetches columns sorted by their default sort order.
    * @param        univiewKey - The key used to retrieve specific column metadata.
    * @return       List<UniviewTableColumn__mdt> - A list of column metadata sorted by default sort order.
    * @throws       DataTableControllerException - Throws an exception if there is an error in the SOQL query.
    *******************************************************************************************/
    public static List<UniviewTableColumn__mdt> getColumnsSortedByDefault(String univiewKey) {
        try {
            List<UniviewTableColumn__mdt> columnMetadata = [
                SELECT IsActive__c, DefaultSortDirection__c, DefaultSortOrder__c, IsSortedByDefault__c, SortingField__c
                FROM UniviewTableColumn__mdt 
                WHERE UniviewKey__c =: univiewKey AND IsSortedByDefault__c = true AND IsActive__c = true
                WITH USER_MODE
                ORDER BY DefaultSortOrder__c ASC
            ];
            return columnMetadata;
        } catch (Exception e) {
            throw new DataTableControllerException(e.getMessage());
        }
        
    }

    // With Sharing and WithoutSharing
    private static DataTableGetRecords getRecords {
        get {
            if (null == getRecords) {
                // Use with sharing by default
                getRecords = new WithoutSharing();
            }
            return getRecords;
        }
        set;
    }

    /******************************************************************************************
    * @description  Retrieves records for the data table based on the provided QueryRequest.
    *               This method uses either "with sharing" or "without sharing" context based on the request.
    * @param        request - The request object containing query parameters.
    * @return       ResponseObject - The result of the query including records and pagination info.
    * @throws       AuraHandledException - Throws an exception if there is an error in the query process.
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static ResponseObject getDataTableRecords(QueryRequest request) {
        try {
            if(request.scopingRule){
                getRecords = new WithSharing();
            }else {
                getRecords = new WithoutSharing();
            }
            return getRecords.getDataTableRecords(request,request.scopingRule);
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    // Abstract class for handling data table records with and without sharing context.
    private abstract class DataTableGetRecords {
        public ResponseObject getDataTableRecords (QueryRequest request,Boolean scopingRule) {
            try {
                if(scopingRule){
                    WithSharing withoutSharingInstance = new WithSharing();
                    return withoutSharingInstance.getRecs(request);
                }
                else{
                    WithoutSharing withoutSharingInstance = new WithoutSharing();
                    return withoutSharingInstance.getRecs(request);
                }
            } catch (Exception e) {
                throw new DataTableControllerException(e.getMessage());
            }
        }
    }

    // WithSharing class for handling data table records with sharing context.
    private with sharing class WithSharing extends DataTableGetRecords {
        public ResponseObject getRecs(QueryRequest request){
            return queryRecords(request);
        }
    }
    
    
    // WithoutSharing class for handling data table records without sharing context.
    private without sharing class WithoutSharing extends DataTableGetRecords {
        public ResponseObject getRecs(QueryRequest request){
            return queryRecords(request);
        }
    }

}
