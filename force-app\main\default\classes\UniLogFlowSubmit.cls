public without sharing class UniLogFlowSubmit {

    @InvocableMethod(label='Submit Flow Log' description='Submit a log from a flow component' category='Log Actions')
	public static void submitFlowLog(List<flowLog> logList) {

		for (flowLog log : logList) {

            //submit error
            UniLogger logHandler = new UniLogger(
                log.message,
                log.payload,
                2
                );

			if(log.logLevel != null )loghandler.setLoggingLevel(LoggingLevel.valueOf(log.logLevel));

			loghandler
                .setReferenceIdentifier(log.refId)
                .setLogSource(UniLogger.LogSource.Flow)
				.publishLog();
		}
    }

    public class FlowLog {
		@InvocableVariable(label='Flow Name' description='Name of the flow where the error was caught' required=false)
		public String flowName;

		@InvocableVariable(label='Action Name' description='Name of the flow action where the error was caught' required=true)
		public String actionName;

		@InvocableVariable(label='Message' description='Log Message' required=true)
		public String message;

        @InvocableVariable(label='Payload' description='Log Payload' required=false)
		public String payload;

		@InvocableVariable(label='RefId' description='Unique Id of the thing to target in this log' required=false)
		public String refId;

        @InvocableVariable(label='logLevel' description='the level of criticity of that log (INFO, DEBUG, WARN, ERROR)' required=false)
		public String logLevel;
	}
}