/*
* @cicd_tests CtrlFeiTabExtension_Test
*/
public without sharing class CtrlFeiTabExtension {
    
    public class FeiTabExtensionWrapper{
        @AuraEnabled public String chromeExtensionURL           {get; set;}
        @AuraEnabled public String safariExtensionURL           {get; set;}
        @AuraEnabled public Boolean extensionRequired           {get; set;}
        @AuraEnabled public String keepAliveTimeoutMS           {get; set;}
        @AuraEnabled public Boolean skipExtension               {get; set;}
        @AuraEnabled public String keepAliveURL                 {get; set;}
    }

    @AuraEnabled(cacheable=true)
    public static FeiTabExtensionWrapper getExtensionUrl(){

        FeiTabExtensionWrapper result = new FeiTabExtensionWrapper();

        FEI_Environment__c mySetting = FEI_Environment__c.getInstance();

        if(mySetting != null){

            result.chromeExtensionURL = mySetting.Chrome_Extension_URL__c;
            result.safariExtensionURL = mySetting.Safari_Extension_URL__c;
            result.extensionRequired = mySetting.Extension_Required__c;

            Organization currentOrganization = [SELECT Id, IsSandbox FROM Organization LIMIT 1];

            result.keepAliveTimeoutMS = (currentOrganization.IsSandbox) ? mySetting.NOPROD_KeepAlive_Timeout_ms__c : mySetting.PROD_KeepAlive_Timeout_ms__c;

            result.skipExtension = mySetting.Skip_Extension__c;
            result.keepAliveURL = mySetting.KeepAlive_URL__c;
            
        }

        return result;

    }

}