@IsTest
public without sharing class EventControllerTest {
    
    @TestSetup
    static void setupTestData() {

        // Crea un contatto
        Contact contact = new Contact(FirstName = 'Test', LastName = 'Contact');
        insert contact;

        // Crea un evento
        Event evt = new Event(
            Subject = 'Test Meeting',
            StartDateTime = System.now().addHours(1),
            EndDateTime = System.now().addHours(2),
            IsAllDayEvent = false,
            Location = 'Test Room',
            Telefono_Cliente__c = '1234567890',
            Mail__c = true,
            Modalit_di_esecuzione_incontro__c = 'Telefonico',
            IsPrivate = false,
            IsReminderSet = true,
            ShowAs = 'Busy'
        );
        insert evt;
    }

    @IsTest
    static void testGetEventRecord() {
        Event evt = [SELECT Id FROM Event LIMIT 1];
        Map<String, Object> result = EventController.getEventRecord(evt.Id);
    }

    @IsTest
    static void testUpdateEvent() {
        Event evt = [SELECT Id, OwnerId FROM Event LIMIT 1];

        Map<String, Object> fields = new Map<String, Object>{
            'Subject' => 'Updated Subject',
            'Description' => 'Updated Description',
            'Location' => 'Updated Location',
            'IsAllDayEvent' => false,
            'Telefono_Cliente__c' => '999999999',
            'Mail__c' => true,
            'Modalit_di_esecuzione_incontro__c' => 'Videochiamata',
            'OwnerId' => evt.OwnerId,
            'Service_Territory__c' => null,
            'Trattativa__c' => null,
            'Case__c' => null,
            'FinServ__Household__c' => null,
            'IsPrivate' => false,
            'IsReminderSet' => true,
            'ShowAs' => 'Free',
            'StartDateTime' => String.valueOf(Date.today()) + 'T07:45:00.000Z',
            'EndDateTime' => String.valueOf(Date.today()) + 'T08:45:00.000Z'
        };

        User u = [SELECT Id FROM User WHERE Id != :UserInfo.getUserId() LIMIT 1];

        List<String> attendees = new List<String>{ evt.OwnerId, u.Id };
        Map<String, Object> result = EventController.updateEvent(evt.Id, fields, attendees);
    }

    @IsTest
    static void testGetPicklistValues() {
        List<Map<String, String>> picklist = EventController.getPicklistValues('Event', 'ShowAs');
    }

    @IsTest
    static void testGetUserName() {
        User u = [SELECT Id FROM User WHERE IsActive = true LIMIT 1];
        String name = EventController.getUserName(u.Id);
    }

    @IsTest
    static void testGetContactName() {
        Contact c = [SELECT Id FROM Contact LIMIT 1];
        String name = EventController.getContactName(c.Id);
    }
}