@isTest
public with sharing class Assignment<PERSON><PERSON>Handler_Test {
    @isTest
    static void testGetTakenInChargeExpiryDateWithBH_OnSuccess(){
        AssignmentFlowHandler.FlowInputStructure input = new AssignmentFlowHandler.FlowInputStructure();
        List<BusinessHours> bh = new List<BusinessHours>();
        List<AssignmentFlowHandler.FlowInputStructure> inputList = new List<AssignmentFlowHandler.FlowInputStructure>();
        List<AssignmentFlowHandler.FlowOutputStructure> outputList = new List<AssignmentFlowHandler.FlowOutputStructure>();
        bh = [SELECT Id, Name FROM BusinessHours WHERE Name = 'Standard Office BH' LIMIT 1];
        if(!bh.isEmpty()){
            input.businessHoursId = bh.get(0).Id;
            input.startDate = Datetime.newInstance(2024, 3, 20, 12, 0, 0);
            input.remainingTime = '2';
            input.unitOfMeasure= 'day';
            input.hoursType = 'business';
            inputList.add(input);
            outputList = AssignmentFlowHandler.getTakenInChargeExpiryDateWithBH(inputList);
            System.assertNotEquals(0, outputList.size());
            System.assertEquals(Datetime.newInstance(2024, 3, 22, 12, 0, 0), outputList.get(0).expiryDate);
            System.assertEquals(true, outputList.get(0).isSuccess);
            System.assertEquals(null, outputList.get(0).errorMessage);
        }
    }

    @isTest
    static void testGetTakenInChargeExpiryDateWithBH_OnFail(){
        List<AssignmentFlowHandler.FlowOutputStructure> outputList = new List<AssignmentFlowHandler.FlowOutputStructure>();
        try{
            Test.startTest();
            outputList = AssignmentFlowHandler.getTakenInChargeExpiryDateWithBH(null);
            Test.stopTest();
        }catch(Exception e){
            System.assertEquals(false, outputList.get(0).isSuccess);
            System.assertEquals(e.getMessage(), outputList.get(0).errorMessage);
        }
    }
        
}
