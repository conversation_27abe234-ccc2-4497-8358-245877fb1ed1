/**
 * @File Name         : ErrLogMngClassTest.cls
 * @Description       : 
 * <AUTHOR> <EMAIL>
 * @Group             : 
 * @Last Modified On  : 02-01-2025
 * @Last Modified By  : <EMAIL>
**/
@isTest
private class ErrLogMngClassTest {
    
    @testSetup
    static void setup() {
        // Create any necessary test data here

        /*
        List<ErrorLogMng__c> lstErr = new List<ErrorLogMng__c>();

        ErrorLogMng__c ie1 = new ErrorLogMng__c();
        ie1.ReqMethod__c = 'POST';
        ie1.ReqEndPoint__c = 'https://www.salesforce.com';
        ie1.ReqBody__c = 'req.getBody()';
        //ie1.ReqTimeOut__c = Label.EM_TimeOutValue;

        ie1.ResStatus__c = 'res.getStatus()';
        ie1.ResStatusCode__c = '200';
        ie1.ResBody__c = 'res.getBody()';
        
        ie1.ErrLogCause__c = 'String.valueOf(exc.getCause())';
        ie1.ErrLogClassName__c = ErrLogMngClassTest.class.getName();
        ie1.ErrLogLineNumber__c = 'String.valueOf(exc.getLineNumber())';
        ie1.ErrLogMessage__c = 'exc.getMessage()';
        ie1.ErrLogTypeName__c = 'exc.getTypeName()';
        lstErr.add(ie1);
        
        ErrorLogMng__c dmle1 = new ErrorLogMng__c();
        dmle1.ErrLogCause__c = 'String.valueOf(dmlExc.getCause())';
        dmle1.ErrLogLineNumber__c = 'String.valueOf(dmlExc.getLineNumber())';
        dmle1.ErrLogMessage__c = 'dmlExc.getMessage()';
        dmle1.ErrLogClassName__c = ErrLogMngClassTest.class.getName();
        dmle1.ErrLogTypeName__c = 'dmlExc.getTypeName()';
        lstErr.add(dmle1);

        ErrorLogMng__c ie2 = new ErrorLogMng__c();
        ie2.ReqMethod__c = 'POST';
        ie2.ReqEndPoint__c = 'https://www.salesforce.com';
        ie2.ReqBody__c = 'req.getBody()';
        //ie2.ReqTimeOut__c = Label.EM_TimeOutValue;

        ie2.ResStatus__c = 'res.getStatus()';
        ie2.ResStatusCode__c = '200';
        ie2.ResBody__c = 'res.getBody()';
        
        ie2.ErrLogCause__c = 'String.valueOf(exc.getCause())';
        ie2.ErrLogClassName__c = ErrLogMngClassTest.class.getName();
        ie2.ErrLogLineNumber__c = 'String.valueOf(exc.getLineNumber())';
        ie2.ErrLogMessage__c = 'exc.getMessage()';
        ie2.ErrLogTypeName__c = 'exc.getTypeName()';
        lstErr.add(ie2);
        
        ErrorLogMng__c dmle2 = new ErrorLogMng__c();
        dmle2.ErrLogCause__c = 'String.valueOf(dmlExc.getCause())';
        dmle2.ErrLogLineNumber__c = 'String.valueOf(dmlExc.getLineNumber())';
        dmle2.ErrLogMessage__c = 'dmlExc.getMessage()';
        dmle2.ErrLogClassName__c = ErrLogMngClassTest.class.getName();
        dmle2.ErrLogTypeName__c = 'dmlExc.getTypeName()';
        lstErr.add(dmle2);

        insert lstErr;
        */
    }

    @isTest
    static void testIntegrationErrLog() {
        // Create mock HttpRequest and HttpResponse

        System.CalloutException cEx = new System.CalloutException();
        cEx.setMessage('callOutMessage');

        HttpRequest req = new HttpRequest();
        req.setMethod('POST');
        req.setEndpoint('https://example.com/api');
        req.setBody('{"key":"value"}');
        
        HttpResponse res = new HttpResponse();
        res.setStatus('success');
        res.setStatusCode(500);
        res.setBody('Internal Server Error');
        
        // Call the method
        Test.startTest();
        new ErrLogMngClass().integrationErrLog(cEx, req, res, ErrLogMngClassTest.class.getName());
        Test.stopTest();
        
        // Verify the log entry
        ErrorLogMng__c log = [SELECT Id, ReqMethod__c, ReqEndpoint__c, ReqBody__c, ResStatus__c, ResStatusCode__c, ResBody__c, ErrLogCause__c, ErrLogClassName__c, ErrLogLineNumber__c, ErrLogMessage__c, ErrLogTypeName__c FROM ErrorLogMng__c LIMIT 1];
        System.assertEquals('POST', log.ReqMethod__c);
        System.assertEquals('https://example.com/api', log.ReqEndpoint__c);
        //System.assertEquals('{"key":"value"}', String.valueOf(JSON.deserializeUntyped(log.ReqBody__c)));
        System.assertEquals('500', log.ResStatusCode__c);
        System.assertEquals('Internal Server Error', log.ResBody__c);
        System.assertEquals(ErrLogMngClassTest.class.getName(), log.ErrLogClassName__c);
        System.assertEquals('callOutMessage', log.ErrLogMessage__c);
    }
    
    @isTest
    static void testDmlErr() {
        // Create a DmlException
        DmlException dmlExc = new DmlException();
        dmlExc.setMessage('message');
        
        // Call the method
        Test.startTest();
        new ErrLogMngClass().dmlErr(dmlExc, ErrLogMngClassTest.class.getName());
        Test.stopTest();
        
        // Verify the log entry
        ErrorLogMng__c log = [SELECT Id, ErrLogCause__c, ErrLogClassName__c, ErrLogLineNumber__c, ErrLogMessage__c, ErrLogTypeName__c FROM ErrorLogMng__c LIMIT 1];
        System.assertEquals(ErrLogMngClassTest.class.getName(), log.ErrLogClassName__c);
        System.assertEquals('message', log.ErrLogMessage__c);
    }
}