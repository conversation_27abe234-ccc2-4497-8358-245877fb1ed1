public class AppointmentTopicTimeSlotDataLoader {
    public static void retrieveAndInsertRecords(){  
        List<AppointmentTopicTimeSlot> appointmentTopicTimeSlotList = new List<AppointmentTopicTimeSlot>();
        List<TimeSlot> timeSlots = [SELECT Id, WorkTypeGroupId FROM TimeSlot WHERE WorkTypeGroupId!=null];
        system.debug('Total No. of TimeSlot Records having WorkTypeGroup: '+ timeSlots.size());
        for(TimeSlot TS : timeSlots){
            AppointmentTopicTimeSlot ATTS = new AppointmentTopicTimeSlot(TimeSlotId = TS.Id,
                                                                         WorkTypeGroupId = TS.WorkTypeGroupId);
            appointmentTopicTimeSlotList.add(ATTS);
        }
        insertRecords(appointmentTopicTimeSlotList);
    }
    
    private static void insertRecords(List<AppointmentTopicTimeSlot> appointmentTopicTimeSlotList){
        Database.SaveResult[] saveResults = Database.insert(appointmentTopicTimeSlotList, false);
        Integer attsInserted = 0;
        for (Database.SaveResult sr : saveResults) {
            if (sr.isSuccess()) {
                attsInserted = attsInserted+1;
                system.debug('Record successfully inserted. AppointmentTopicTimeSlot ID: ' + sr.getId());
            }
            else {               
                for(Database.Error err : sr.getErrors()) {
                    System.debug('The following error has occurred.');                    
                    System.debug(err.getStatusCode() + ': ' + err.getMessage());
                    System.debug('AppointmentTopicTimeSlot fields that affected this error: ' + err.getFields());
                }
            }
        }
        system.debug('Total No. of AppointmentTopicTimeSlot records successfully inserted: '+ attsInserted);
    }
}