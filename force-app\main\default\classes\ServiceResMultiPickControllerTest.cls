@isTest
private class ServiceResMultiPickControllerTest {
    @testSetup
    static void setupTestData() {
        List<skill> skList = [SELECT id, MasterLabel FROM skill];
        // Crea un utente di test
        Profile userProfile = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1];
        User testUser = new User(
            FirstName = 'Test',
            LastName = 'User',
            Email = '<EMAIL>',
            Username = '<EMAIL>' + System.currentTimeMillis(),
            Alias = 'tuser',
            ProfileId = userProfile.Id,
            TimeZoneSidKey = 'GMT',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US'
        );
        insert testUser;

        // Crea una ServiceResource associata all'utente
        ServiceResource serviceResource = new ServiceResource(
            Name = 'Test Service Resource',
            IsActive = true,
            Attiva_per_clienti_e_prospect__c = 'Nessuno',
            ResourceType = Schema.SObjectType.ServiceResource.fields.ResourceType.picklistValues[0].getValue(),
            RelatedRecordId = testUser.Id // Associa lo User alla ServiceResource
        );
        insert serviceResource;

        List<ServiceResourceSkill> serviceResourceSkills = new List<ServiceResourceSkill>();

        for (Integer i = 0; i < 2; i++) {
            ServiceResourceSkill mockSkill = new ServiceResourceSkill(
                ServiceResourceId = serviceResource.Id,
                skillId = skList[i].Id,
                SkillLevel = 10,
                EffectiveStartDate = Date.today()
            );
            serviceResourceSkills.add(mockSkill);
        }
        insert serviceResourceSkills;
    }

    @isTest
    static void testGetSkillLabels() {
        Test.startTest();
        List<String> skillLabels = ServiceResMultiPickController.getSkillLabels();
        Test.stopTest();
    }

    @isTest
    static void testGetUserSkills() {
        ServiceResource serviceResource = [SELECT Id FROM ServiceResource LIMIT 1];

        Test.startTest();
        Map<Id, String> userSkills = ServiceResMultiPickController.getUserSkills(serviceResource.Id);
        Test.stopTest();
    }
    
     @isTest
    static void testGetServiceResourceData() {
        ServiceResource serviceResource = [SELECT Id FROM ServiceResource LIMIT 1];

        Test.startTest();
        ServiceResMultiPickController.getServiceResourceData(serviceResource.Id);
        Test.stopTest();
    }

    
    
    @isTest
    static void testUpdateSkills() {
        ServiceResource serviceResource = [SELECT Id FROM ServiceResource LIMIT 1];

        List<String> skillsToInsert = new List<String>{ 'MockSkill2' }; // Inserisci una nuova skill mock
        List<String> skillsToDelete = new List<String>{ 'MockSkill0' }; // Elimina una skill mock esistente

        Test.startTest();
        Boolean result = ServiceResMultiPickController.updateSkills(
            skillsToDelete,
            skillsToInsert,
            'Clienti',
            serviceResource.Id
        );
        Test.stopTest();
    }
    
     @isTest
    static void testUpdateServiceRes() {
        ServiceResource serviceResource = [SELECT Id FROM ServiceResource LIMIT 1];

        Test.startTest();
        ServiceResMultiPickController.updateServiceRes(serviceResource.Id, true, null);
        Test.stopTest();
    }
    
    @isTest
    static void checkAttivaClienteProspectTest(){
       ServiceResource serviceResource = [SELECT Id FROM ServiceResource LIMIT 1];
       Test.startTest();
       ServiceResMultiPickController.checkAttivaClienteProspect('Clienti',serviceResource.Id);
       
        Test.stopTest();
    }
}