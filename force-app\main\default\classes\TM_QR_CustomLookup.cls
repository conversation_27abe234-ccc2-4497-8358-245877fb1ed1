/**
 * @description       : TM_QR_CustomLookup.cls
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 03-02-2025
 * @last modified by  : <EMAIL>
 * @cicd_tests TM_QR_CustomLookupTst
**/

 public with sharing class TM_QR_CustomLookup {

    public static Map<String, Object> buildQuery (String searchKeyword, String objectApiName, String queryFields, String queryWhere, String filterSearchKeywordField,
                                                   String filterFields, String primaryDisplayField, String secondaryDisplayField, String orderByField,
                                                   String recordNumberLimit, String initialSelectedId) {
        System.debug('searchKeyword ' + searchKeyword + ' objectApiName ' + objectApiName +' queryFields '+ queryFields +' filterSearchKeywordField '+ filterSearchKeywordField +
             ' filterFields ' +filterFields + ' primaryDisplayField ' + primaryDisplayField + ' secondaryDisplayField ' +secondaryDisplayField +
             ' recordNumberLimit ' +recordNumberLimit+ ' initialSelectedId ' + initialSelectedId);
        Map<String, Object> returnValue = new Map<String, Object> ();
        returnValue.put('error', false);
        String query = '';
        Integer recordLimit = 5;
        if (!String.isBlank(recordNumberLimit)) {

            recordLimit = Integer.valueOf(recordNumberLimit);
        }
        try {

            List<String> searchFields = queryFields.split(',');
            for (Integer i = 0; i < searchFields.size(); i++) {

                searchFields[i] = searchFields[i].trim();
            }
            String searchKey = '';
            if (!String.isBlank(primaryDisplayField) && queryFields.contains(primaryDisplayField)) {

                Integer index = queryFields.indexOf(primaryDisplayField);
                queryFields.remove(primaryDisplayField);
            }
            if (!String.isBlank(secondaryDisplayField) && queryFields.contains(secondaryDisplayField)) {

                Integer index = queryFields.indexOf(secondaryDisplayField);
                queryFields.remove(secondaryDisplayField);
            }
            if (!String.isBlank(searchKeyword)) {

                if (filterSearchKeywordField == 'IsActive = true AND TM_CodiceArticolo__c'){
                    searchKey = '\'' + searchKeyword + '%\'';
                } else {
                    searchKey = '\'%' + searchKeyword + '%\'';
                }
            }
            if (!String.isBlank(queryFields)) {
                
                //Modified by Giuseppe Mario Pastore on 13/02/2020 because the query resulted in:  SELECT Id, Name, Industry,  FROM Account WHERE Id='0013E00001A0AGbQAN'  ORDER BY Industry ASC LIMIT 5
                queryFields = (String) String.valueOf(queryFields.charAt(queryFields.length()-1)) == ',' ? queryFields.subString(0,queryFields.length()-2) : queryFields;
                System.debug('queryFields after removing final comma -----> ' + queryFields);
                if (!String.isBlank(secondaryDisplayField)) {
                    System.debug('secondary not blank');

                    if (searchFields.contains(secondaryDisplayField)) {

                        //Modified by Giuseppe Mario Pastore, because if primaryDisplayField is blank and is not contained in searchFields, 
                        //it will still enters in the subsequent if and will build the string "query = 'SELECT Id, ' + queryFields + ', ' + primaryDisplayField + ' FROM ' + objectApiName;" 
                        //with empty primaryDisplay field, resulting in SELECT Id,Name, FROM Account (ERROR)
                        //if (searchFields.contains(primaryDisplayField) &&) { <--- this is the previous code modified to avoid that error
                        if (searchFields.contains(primaryDisplayField) || String.isBlank(primaryDisplayField)) {
                            if (!String.isBlank(searchKey)) {

                                query = 'SELECT Id, ' + queryFields + ' FROM ' + objectApiName + ' WHERE ' + filterSearchKeywordField +' LIKE ' + searchKey + ' ';
                            } else {

                                query = 'SELECT Id, ' + queryFields + ' FROM ' + objectApiName;
                            }
                        } else {

                            if (!String.isBlank(searchKey)) {

                                query = 'SELECT Id, ' + queryFields + ', ' + primaryDisplayField + ' FROM ' + objectApiName + ' WHERE ' + filterSearchKeywordField +' LIKE ' + searchKey + ' ';
                            } else {

                                query = 'SELECT Id, ' + queryFields + ', ' + primaryDisplayField + ' FROM ' + objectApiName;
                            }
                        }
                    } else {

                        if (!String.isBlank(primaryDisplayField) && searchFields.contains(primaryDisplayField)) {

                            if (!String.isBlank(searchKey)) {

                                query = 'SELECT Id, ' + queryFields + ', ' + secondaryDisplayField + ' FROM ' + objectApiName + ' WHERE ' + filterSearchKeywordField + ' LIKE ' + searchKey + ' ';
                            } else {

                                query = 'SELECT Id, ' + queryFields + ', ' + secondaryDisplayField + ' FROM ' + objectApiName;
                            }
                        } else if (!String.isBlank(primaryDisplayField) && !searchFields.contains(primaryDisplayField)) {

                            if (!String.isBlank(searchKey)) {

                                query = 'SELECT Id, ' + queryFields + ', ' + primaryDisplayField + ', ' + secondaryDisplayField + ' FROM ' + objectApiName + ' WHERE ' + filterSearchKeywordField + ' LIKE ' + searchKey + ' ';
                            } else {

                                query = 'SELECT Id, ' + queryFields + ', ' + primaryDisplayField + ', ' + secondaryDisplayField + ' FROM ' + objectApiName;
                            }
                        } else {

                            if (!String.isBlank(searchKey)) {

                                query = 'SELECT Id, ' + queryFields + ', ' + secondaryDisplayField + ' FROM ' + objectApiName + ' WHERE ' + filterSearchKeywordField + ' LIKE ' + searchKey + ' ';
                            } else {

                                query = 'SELECT Id, ' + queryFields + ', ' + secondaryDisplayField + ' FROM ' + objectApiName;
                            }
                        }
                    }
                } else{

                    if (!String.isBlank(searchKey)) {

                        query = 'SELECT Id, ' + queryFields + ' FROM ' + objectApiName + ' WHERE ' + filterSearchKeywordField +' LIKE ' + searchKey + ' ';
                    } else {

                        query = 'SELECT Id, ' + queryFields + ' FROM ' + objectApiName;
                    }
                }
            } else {

                if (!String.isBlank(secondaryDisplayField)) {

                    if (!String.isBlank(primaryDisplayField)) {

                        if (!String.isBlank(searchKey)) {

                            query = 'SELECT Id, Name, ' + primaryDisplayField + ', ' + secondaryDisplayField + ' FROM ' + objectApiName + ' WHERE ' + filterSearchKeywordField +' LIKE ' + searchKey + ' ';
                        } else {

                            query = 'SELECT Id, Name, ' + primaryDisplayField + ', ' + secondaryDisplayField + ' FROM ' + objectApiName;
                        }
                    } else {

                        if (!String.isBlank(searchKey)) {

                            query = 'SELECT Id, Name, ' + secondaryDisplayField + ' FROM ' + objectApiName + ' WHERE ' + filterSearchKeywordField +' LIKE ' + searchKey + ' ';
                        } else {

                            query = 'SELECT Id, Name, ' + secondaryDisplayField + ' FROM ' + objectApiName;
                        }
                    }
                } else {

                    if (!String.isBlank(primaryDisplayField)) {

                        if (!String.isBlank(searchKey)) {

                            query = 'SELECT Id, Name, ' + primaryDisplayField + ' FROM ' + objectApiName + ' WHERE ' + filterSearchKeywordField +' LIKE ' + searchKey + ' ';
                        } else {

                            query = 'SELECT Id, Name, ' + primaryDisplayField + ' FROM ' + objectApiName;
                        }
                    } else {

                        if (!String.isBlank(searchKey)) {

                            query = 'SELECT Id, Name FROM ' + objectApiName + ' WHERE ' + filterSearchKeywordField +' LIKE ' + searchKey + ' ';
                        } else {

                            query = 'SELECT Id, Name FROM ' + objectApiName;
                        }
                    }
                }
            }
            if (!String.isBlank(queryWhere) && String.isBlank(initialSelectedId)) {

                query += (!query.contains('WHERE') ? ' WHERE ' : ' AND ') + queryWhere + ' ';
            }
            if (searchKeyword != '') {

                if (filterFields != null && String.isBlank(initialSelectedId)) {

                    List<String> searchKeyWordFieldCell = filterFields.split(',');
                    for (Integer i = 0; i < searchKeyWordFieldCell.size(); i++) {

                        if (i < searchKeyWordFieldCell.size() - 1) {

                            if (!query.contains('WHERE')) {

                                query += ' WHERE ';
                            } else {

                                query += ' OR ';
                            }
                            query += searchKeyWordFieldCell[i] + ' LIKE ' + searchKey + ' OR ';
                        } else {

                            query += searchKeyWordFieldCell[i] + ' LIKE ' + searchKey + ' ';
                        }
                    }
                    if (!String.isBlank(queryWhere)) {

                        query += ' AND ' + queryWhere + ' ';
                    }
                }
            }
            if (!String.isBlank(initialSelectedId)) {

                query += ' WHERE Id=\'' + initialSelectedId +'\' ';
            }
            if (!String.isBlank(orderByField)) {

                query += ' ORDER BY ' + orderByField + ' ASC LIMIT ' + recordLimit;
            } else if (!String.isBlank(primaryDisplayField)) {

                query += ' ORDER BY ' + primaryDisplayField + ' ASC LIMIT ' + recordLimit;
            } else {

                query += ' ORDER BY CreatedDate DESC LIMIT ' + recordLimit;
            }
            System.debug('query from buildQuery: ' + query);
            returnValue.put('query', query);
        } catch (Exception e) {

            returnValue.put('error', true);
            returnValue.put('errorMessage', e.getMessage());
            returnValue.put('errorStackTraceString', e.getStackTraceString());
        }
        return returnValue;
    }

    public class MyException extends Exception {}

}