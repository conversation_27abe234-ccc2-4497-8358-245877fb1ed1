public without sharing class OpportunityFetchExpiredOpportunities {
    @InvocableMethod(
        label='Fetch Expired Opportunities'
        description='UNF-(...) query to fetch all opportunities that should be closed due to expiration'
        category='Opportunity'
    )
    public static List<List<Id>> OpportunityFetchExpiredOpportunities() {
        Set<Id> toCloseOpps = new Set<Id>();

        List<AggregateResult> myQuotesWithOppsToClose = [
            SELECT MAX(ExpirationDate) expDate, OpportunityId oppToClose
            FROM Quote
            WHERE Opportunity.IsClosed = FALSE AND Quote.isClosed__c = FALSE
            GROUP BY OpportunityId
            HAVING MAX(ExpirationDate) < TODAY
        ];
        if (myQuotesWithOppsToClose.isEmpty())
            return new List<List<Id>>{ new List<Id>(toCloseOpps) };

        for (AggregateResult myQuoteWithOppToClose : myQuotesWithOppsToClose)
            toCloseOpps.add((Id) myQuoteWithOppToClose.get('oppToClose'));

        return new List<List<Id>>{ new List<Id>(toCloseOpps) };
    }
}
