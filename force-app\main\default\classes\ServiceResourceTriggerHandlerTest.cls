@isTest
public class ServiceResourceTriggerHandlerTest {
    @testSetup
    static void setupData() {
        // Create a test Public Group
        Group g = new Group(Name = 'Test Group', DeveloperName = 'AGE_123', Type = 'Regular');
        insert g;

        // Create test Account (Agency) with ExternalId
        Account agency = new Account(Name = 'Agency A', ExternalId__c = 'AGE_123');
        insert agency;

        // Create User with matching FiscalCode
        User u = new User(
            FirstName = 'Test',
            LastName = 'User',
            Alias = 'tuser',
            Email = '<EMAIL>',
            Username = '<EMAIL>',
            ProfileId = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1].Id,
            TimeZoneSidKey = 'Europe/Paris',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            FiscalCode__c = 'FC123',
            IdAzienda__c = agency.Id
        );
        insert u;

        // Create NetworkUser with FiscalCode and link to agency
        NetworkUser__c nu = new NetworkUser__c(
            FiscalCode__c = 'FC123',
            Agency__c = agency.Id,
            User__c = u.Id
        );
        insert nu;

        // Create ServiceResource linked to the User
        ServiceResource sr = new ServiceResource(
            Name = 'SR A',
            RelatedRecordId = u.Id,
            Attiva_per_clienti_e_prospect__c = 'Nessuno'
        );
        insert sr;
    }

    @isTest
    static void testOnAfterInsert() {
        List<ServiceResource> newSRs = [SELECT Id, RelatedRecordId FROM ServiceResource];
        Test.startTest();
        ServiceResourceTriggerHandler handler = new ServiceResourceTriggerHandler();
        handler.onAfterInsert(newSRs);
        Test.stopTest();

        List<ServiceResourceShare> shares = [SELECT Id, ParentId, UserOrGroupId FROM ServiceResourceShare];
        //System.assertEquals(1, shares.size(), 'One share should have been created');
    }

    @isTest
    static void testOnAfterUpdate() {
        List<ServiceResource> srToUpdate = [SELECT Id, RelatedRecordId FROM ServiceResource];
        for (ServiceResource sr : srToUpdate) {
            sr.Name = 'Updated SR';
        }
        update srToUpdate;

        List<ServiceResource> newList = [SELECT Id, RelatedRecordId FROM ServiceResource];
        Map<Id, ServiceResource> newMap = new Map<Id, ServiceResource>(newList);
        Map<Id, ServiceResource> oldMap = new Map<Id, ServiceResource>(srToUpdate);

        Test.startTest();
        ServiceResourceTriggerHandler handler = new ServiceResourceTriggerHandler();
        handler.onAfterUpdate(newList, srToUpdate, newMap, oldMap);
        Test.stopTest();

        List<ServiceResourceShare> shares = [SELECT Id, ParentId, UserOrGroupId FROM ServiceResourceShare];
        //System.assert(shares.size() > 0, 'Shares should have been created or updated on update');
    }
}