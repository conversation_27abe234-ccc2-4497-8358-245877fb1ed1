global with sharing class TransformationDataEconomics implements Callable {

    /*global Boolean invokeMethod(String methodName, Map<String,Object> inputMap, Map<String,Object> outMap, Map<String,Object> options){
        if(methodName == 'checkDataEconomics'){
            checkDataEconomics(inputMap, outMap, options);
        }
        return true;
    }*/

    global Object call(String action, Map<String, Object> args)
    {
        System.debug(args);
        Map<String, Object> input = (Map<String, Object>)args.get('input');
        Map<String, Object> output = (Map<String, Object>)args.get('output');
        Map<String, Object> options = (Map<String, Object>)args.get('options');

        if(action == 'checkDataEconomics'){
            checkDataEconomics(input, output, options);
        }
        Object result = output.get('response');
        return result;
    }

    public void checkDataEconomics(Map<String,Object> inputMap, Map<String,Object> outMap, Map<String,Object> options){
        try{
            Map<String,Object> result = new Map<String,Object>();
            //system.debug('%%inputMap%%' + inputMap);
            //system.debug('%%inputMap%%' + inputMap.keySet());
            system.debug('%%inputMap%%' + inputMap.get('Result'));
            String accAccSocietyId;
            List<wrapClassDataEconomics> wrapList = (List<wrapClassDataEconomics>)JSON.deserialize(JSON.serialize(inputMap.get('Result')), List<wrapClassDataEconomics>.class);
            List<AccountKPI__c> kpiList = new List<AccountKPI__c>();
            List<String> accAccSocietyListSai = new List<String>();
            List<String> accAccSocietyListSal = new List<String>();
            if(!wrapList.isEmpty()){
                if(wrapList.size()==1){
                    accAccSocietyListSai.add(wrapList[0].AccountAccountSocietyId);
                }else{
                    for(wrapClassDataEconomics singleItem : wrapList){
                        if(singleItem.Name == 'UnipolSai'){
                            accAccSocietyListSai.add(singleItem.AccountAccountSocietyId);
                        }else if (singleItem.Name == 'UniSalute'){
                            accAccSocietyListSal.add(singleItem.AccountAccountSocietyId);
                        }
                    }            
                }    
            }
            if (!accAccSocietyListSai.isEmpty())  {
                kpiList = [SELECT Id, Key__c, Value__c FROM AccountKPI__c where MasterRecordId__c IN:accAccSocietyListSai];
                if(kpiList.size()>0){
                    result.put('idAccAccSocietyId', String.valueOf(accAccSocietyListSai[0]));    
                }else if(!accAccSocietyListSal.isEmpty()){
                    result.put('idAccAccSocietyId', String.valueOf(accAccSocietyListSal[0])); 
                }else{
                    result.put('idAccAccSocietyId','');
                }  
                outMap.put('response', result);  
            }
            system.debug('%%outMap%%' + outMap);
        } catch(Exception e) {
            System.debug('Error: ' + e.getMessage() + ' - AT LINE : ' + e.getLineNumber());
        }
    }

    public class wrapClassDataEconomics {
        public String Name {get; set;}
        public String AccountAccountSocietyId {get; set;}
    }
}