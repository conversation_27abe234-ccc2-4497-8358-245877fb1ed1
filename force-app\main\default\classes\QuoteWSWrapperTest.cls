@isTest
private class QuoteWSWrapperTest {
    @isTest
    static void testQuoteWSWrapperInstantiation() {
        QuoteWSWrapper wrapper = new QuoteWSWrapper();
        // Create a test Quote record to get a valid Id
        Quote testQuote = new Quote(Name = 'Test Quote');
        insert testQuote;
        wrapper.id = testQuote.Id;
        wrapper.externalId = 'EXT001';
        wrapper.domainType = 'TestDomain';
        wrapper.name = 'Test Quote';
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;
        wrapper.accountId = testAccount.Id;
        wrapper.status = 'Draft';
        wrapper.expirationDate = Date.today().addDays(30);
        wrapper.billingStreet = 'Via Roma 1';
        wrapper.billingCity = 'Milano';
        wrapper.billingState = 'MI';
        wrapper.billingPostalCode = '20100';
        wrapper.billingCountry = 'Italia';
        wrapper.billingLatitude = 45.4642;
        wrapper.billingLongitude = 9.19;
        wrapper.quoteToStreet = 'Via Milano 2';
        wrapper.quoteToCity = 'Torino';
        wrapper.quoteToState = 'TO';
        wrapper.quoteToPostalCode = '10100';
        wrapper.quoteToCountry = 'Italia';
        wrapper.quoteToLatitude = 45.0703;
        wrapper.quoteToLongitude = 7.6869;
        wrapper.billingName = 'Mario Rossi';
        wrapper.email = '<EMAIL>';
        wrapper.phone = '**********';
        wrapper.areasOfNeed = new List<String>{'Auto', 'Casa'};
        wrapper.createdDate = System.now();
        wrapper.quoteAmount = 1234.56;
        wrapper.linkUnica = 'http://test.link';
        wrapper.createdDateTPD = System.now();
        wrapper.folderId = 'FOL123';
        wrapper.engagementPoint = 'Web';
        wrapper.coverages = new List<CoverageWSWrapper>();
        
        QuoteWSWrapper.testCoverage = true;

        System.assertNotEquals(null, wrapper);
    }
}