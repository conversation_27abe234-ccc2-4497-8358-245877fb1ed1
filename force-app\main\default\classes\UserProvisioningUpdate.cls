/*
* @description This class is used to update a User after SSO Login
* @cicd_tests UserProvisioningHandler_Test
*/
public without sharing class UserProvisioningUpdate {

    public class UCAResult{
        public List<UserType> users = new List<UserType>();
    }

    public class UserType{
        public String userId                {get; set;}
        public List<String> groups          {get; set;}
        public List<String> cin             {get; set;}
    }
    
    /*
    * @description This method is called to get User Context (from UCA API) and to set Permission Set on the User
    * @param User u instance of the existing User
    * @param Map<String, String> attributes Map Key-->Value saml request attriute --> value of the attribute
    */
    public static void handleUser(User u, Map<String, String> attributes) {
        
        //List<QueueableUserProvisioning.InputWrapper> queueableInputs = new List<QueueableUserProvisioning.InputWrapper>();
        
        try{
            
            String fiscalCode = attributes.get('CF');
            System.debug('UserProvisioningUpdate.fiscalCode: '+fiscalCode);

            //String loginRACF = attributes.get('sub');
            
            // Callout here to get info from UCA API
            UserProvisioningAPI.UCAResponse UCAData = UserProvisioningAPI.getUCAData(fiscalCode);
            
            Map<String, UCA_Mapping__mdt> mappingUCA = UCA_Mapping__mdt.getAll();
            
            Map<String, Set<String>> mapCodePermissionSet = new Map<String, Set<String>>();
            Set<String> permissionSetNameToDelete = new Set<String>();
            Set<String> defaultPermissionSet = new Set<String>();
            for(UCA_Mapping__mdt uca : mappingUCA.values()){

                if(String.isBlank(uca.Permission_Set_Name__c)) continue;

                if(!uca.Default_Assignment__c){
                    if(String.isNotBlank(uca.Permission_Set_Name__c)){
                        permissionSetNameToDelete.addAll(uca.Permission_Set_Name__c.split(','));

                        if(mapCodePermissionSet.containsKey(uca.Code__c)){
                            mapCodePermissionSet.get(uca.Code__c).addAll(uca.Permission_Set_Name__c.split(','));
                        }else{
                            Set<String> tempSet = new Set<String>();
                            tempSet.addAll(uca.Permission_Set_Name__c.split(','));
                            mapCodePermissionSet.put(uca.COde__c, tempSet);
                        }
                    }
                }else{

                    defaultPermissionSet.addAll(uca.Permission_Set_Name__c.split(','));

                }
            }

            if(Test.isRunningTest()){
                mapCodePermissionSet.put('0000',new Set<String>{'UNIT_TEST_CLASS'});
            }
            
            Set<String> permissionSetToDo = new Set<String>();
            Map<String, Set<String>> mapUserCode = new Map<String, Set<String>>();

            UserProvisioningUpdate.UCAResult jsonParser = new UserProvisioningUpdate.UCAResult();
            jsonParser.users = new List<UserProvisioningUpdate.UserType>();

            for(UserProvisioningAPI.UCARequestItem uri : UCAData.result){

                UserProvisioningUpdate.UserType currentUser = new UserProvisioningUpdate.UserType();
                currentUser.userId = uri.utenza;
                currentUser.groups = uri.gruppi;
                currentUser.cin = new List<String>();

                if(uri.gruppi != null && !uri.gruppi.isEmpty()){

                    for(String s : uri.gruppi){

                        if(mapCodePermissionSet.containsKey(s)){
                            permissionSetToDo.addAll(mapCodePermissionSet.get(s));
                            
                            if(mapUserCode.containsKey(uri.utenza)){
                                mapUserCode.get(uri.utenza).addAll(mapCodePermissionSet.get(s));
                            }else{
                                Set<String> tempSet = new Set<String>();
                                tempSet.addAll(mapCodePermissionSet.get(s));
                                mapUserCode.put(uri.utenza, tempSet);
                            }
                        }

                    }

                }
                
                if(uri.embedded != null){
                
                    UserProvisioningAPI.EmbeddedItem embedded = uri.embedded;
                    
                    if(embedded.abilitazioniOperative != null){
                        
                        UserProvisioningAPI.AbilitazioniOperativeItem operative = embedded.abilitazioniOperative;
                        
                        if(operative.abilitazioni != null && !operative.abilitazioni.isEmpty()){
                            
                            for(UserProvisioningAPI.AbilitazioneItem abilitazione : operative.abilitazioni){

                                currentUser.cin.add(abilitazione.codice);
                                
                                if(!mapCodePermissionSet.containsKey(abilitazione.codice)) continue;

                                if(mapCodePermissionSet.containsKey(abilitazione.codice)) permissionSetToDo.addAll(mapCodePermissionSet.get(abilitazione.codice));
                                
                                if(mapUserCode.containsKey(uri.utenza)){
                                    mapUserCode.get(uri.utenza).addAll(mapCodePermissionSet.get(abilitazione.codice));
                                }else{
                                    Set<String> tempSet = new Set<String>();
                                    tempSet.addAll(mapCodePermissionSet.get(abilitazione.codice));
                                    mapUserCode.put(uri.utenza, tempSet);
                                }
                                
                            }
                            
                        }
                        
                    }
                    
                }

                jsonParser.users.add(currentUser);
                
            }

            Set<String> mergeSet = new Set<String>();
            if(!defaultPermissionSet.isEmpty()) mergeSet.addAll(defaultPermissionSet);
            if(!permissionSetToDo.isEmpty()) mergeSet.addAll(permissionSetToDo);
            
            if(!mergeSet.isEmpty()){
                
                List<PermissionSetAssignment> assignmentToInsert = new List<PermissionSetAssignment>();
            
                List<PermissionSet> listPermissionSet = [SELECT Id, Name FROM PermissionSet WHERE Name IN :mergeSet];
                Map<String, Id> mapPermissionSetId = new Map<String, Id>();
                for(PermissionSet ps : listPermissionSet){
                    mapPermissionSetId.put(ps.Name, ps.Id);
                }
                
                for(String s : mergeSet){
                    
                    if(mapPermissionSetId.containsKey(s)){
                        
                        assignmentToInsert.add(
                            UserProvisioningUtils.createPermissionSetAssignment(
                                u.Id, 
                                mapPermissionSetId.get(s)
                            )
                        );
                        
                    }
                }
                
                // Delete old permission set assignment
                List<PermissionSetAssignment> assignmentToDelete = [SELECT Id FROM PermissionSetAssignment WHERE AssigneeId = :u.Id AND PermissionSet.Name IN :permissionSetNameToDelete];
                if(!assignmentToDelete.isEmpty()) Database.delete(assignmentToDelete, false);

                // Insert new permission set assignment
                if(!assignmentToInsert.isEmpty()) Database.insert(assignmentToInsert, false);

                u.UCA_Permissions__c = JSON.serialize(jsonParser);
                //u.LastLoginRACFUserId__c = null;
                //u.LastLoginADUserId__c = loginRACF;
                update u;
                
            }
        }catch(Exception ex){
            System.debug('UserProvisioningUpdate Exception message: '+ex.getMessage());
            System.debug('UserProvisioningUpdate Exception stack trace: '+ex.getStackTraceString());
            //throw ex;
        }
        
    }

}