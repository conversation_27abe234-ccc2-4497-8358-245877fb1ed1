global without sharing class UniLogCleanLogBatch implements Database.Batchable<sObject>, Schedulable {



	
    global void execute(SchedulableContext sc){
        Database.executeBatch( new UniLogCleanLogBatch(), 200);
    }



	global Database.QueryLocator start(Database.BatchableContext bc) {
		Database.QueryLocator queryLocator;
		try {
			//Build the Query
            queryLocator = Database.getQueryLocator(
				'SELECT ID FROM Custom_Log__c Where Expiration_Date__c <= YESTERDAY AND Stop_Expiration__c != true' // beware that boolean can be null
			);
		} catch (Exception exc) {
            UniLogger.writeError('something went wrong with the batch to delete log records - start', exc);
			throw exc;
		}
		return queryLocator;
	}

	global void execute(Database.BatchableContext bc, List<Custom_Log__c> logList) {
		List<Database.DeleteResult> deleteResultList = Database.delete(logList, false);

		for (Database.DeleteResult deleteResult : deleteResultList) {
			if (!deleteResult.isSuccess()) {
				UniLogger.writeError( 'something went wrong with the batch to delete log records - execute', deleteResult);
			}
		}
	}

	global void finish(Database.BatchableContext bc) {
	}

	private class CleanLogsBatchException extends Exception {
	}
}