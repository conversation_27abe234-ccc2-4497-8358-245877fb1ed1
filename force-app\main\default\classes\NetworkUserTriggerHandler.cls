public with sharing class NetworkUserTriggerHandler {
    public void onAfterInsert(List<NetworkUser__c> newList){
        String type = String.valueOf(newList[0].getSObjectType());
        SharingStrategy strategy = SharingStrategyFactory.getStrategy(type);
        strategy.collectTargetRecords(newList);
        strategy.applySharing();
    }

    public void onAfterUpdate(List<NetworkUser__c> newList, List<NetworkUser__c> oldList, Map<Id,NetworkUser__c> newMap, Map<Id,NetworkUser__c> oldMap){
        String type = String.valueOf(newList[0].getSObjectType());
        SharingStrategy strategy = SharingStrategyFactory.getStrategy(type);
        strategy.collectTargetRecords(newList);
        strategy.applySharing();
    }
}