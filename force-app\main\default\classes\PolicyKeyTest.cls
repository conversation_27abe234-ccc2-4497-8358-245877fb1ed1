@isTest
public class PolicyKeyTest {
    @isTest
    static void testFromBusinessKey15() {
        String businessKey = '123456789012345';
        String leo = 'LEO1';
        PolicyKey pk = PolicyKey.fromBusinessKey(businessKey, leo);
        System.assertNotEquals(null, pk);
        System.assertEquals(businessKey, pk.policyNumber);
        System.assertEquals(leo, pk.leo);
        System.assertEquals(businessKey, pk.businessKey);
        System.assertEquals('(ReferencePolicyNumber = \'' + businessKey + '\')', pk.toSoqlCondition());
    }

    @isTest
    static void testFromBusinessKey23() {
        String businessKey = '11234512345123*********';
        String leo = 'LEO2';
        PolicyKey pk = PolicyKey.fromBusinessKey(businessKey, leo);
        System.assertNotEquals(null, pk);
        System.assertEquals('1', pk.companyCode);
        System.assertEquals('12345', pk.motherAgencyCode);
        System.assertEquals('12345', pk.agencyCode);
        System.assertEquals('123', pk.branchCode);
        System.assertEquals('*********', pk.policyNumber);
        System.assertEquals(leo, pk.leo);
        System.assertEquals(businessKey, pk.businessKey);
        System.assert(pk.toSoqlCondition().contains('CompanyCode__c = \'1\''));
    }

    @isTest
    static void testMatches15() {
        String businessKey = '123456789012345';
        String leo = 'LEO3';
        PolicyKey pk = PolicyKey.fromBusinessKey(businessKey, leo);
        // Mock InsurancePolicy
        InsurancePolicy policy = new InsurancePolicy();
        policy.ReferencePolicyNumber = businessKey;
        System.assert(pk.matches(policy));
        policy.ReferencePolicyNumber = 'other';
        System.assert(!pk.matches(policy));
    }

    @isTest
    static void testMatches23() {
        String businessKey = '11234512345123*********';
        String leo = 'LEO4';
        PolicyKey pk = PolicyKey.fromBusinessKey(businessKey, leo);
        InsurancePolicy policy = new InsurancePolicy();
        policy.CompanyCode__c = '1';
        policy.MotherAgencyCode__c = '12345';
        policy.AgencyCode__c = '12345';
        policy.PolicyBranchCode__c = '123';
        policy.ReferencePolicyNumber = '*********';
        System.assert(pk.matches(policy));
        policy.CompanyCode__c = '2';
        System.assert(!pk.matches(policy));
    }

    @isTest
    static void testGetReturnKey() {
        String businessKey = '123456789012345';
        String leo = 'LEO5';
        PolicyKey pk = PolicyKey.fromBusinessKey(businessKey, leo);
        System.assertEquals(leo + '-' + businessKey, pk.getReturnKey());
    }

    @isTest
    static void testAssignmentContextAndRuleMaps() {
        Map<String, String> cipMap = new Map<String, String>{'k1' => 'v1'};
        Map<String, Id> cipToGroupId = new Map<String, Id>{'k1' => UserInfo.getUserId()};
        PolicyKey.AssignmentContext ctx = new PolicyKey.AssignmentContext(new Case(), cipMap, cipToGroupId);
        System.assertEquals(cipMap, ctx.cipMap);
        System.assertEquals(cipToGroupId, ctx.cipToGroupId);

        PolicyKey.AssignmentRuleMaps maps = new PolicyKey.AssignmentRuleMaps(
            cipMap, cipToGroupId, cipMap, cipToGroupId
        );
        System.assertEquals(cipMap, maps.cipMapR01R07);
        System.assertEquals(cipToGroupId, maps.cipToGroupIdR01R07);
    }

    @isTest
    static void testAssignmentContextData() {
        Map<String, Id> cipToGroupId = new Map<String, Id>{'k1' => UserInfo.getUserId()};
        Map<String, User> userMap = new Map<String, User>();
        PolicyKey.AssignmentRuleMaps maps = new PolicyKey.AssignmentRuleMaps(
            new Map<String, String>(), new Map<String, Id>(), new Map<String, String>(), new Map<String, Id>()
        );
        PolicyKey.AssignmentContextData ctxData = new PolicyKey.AssignmentContextData(cipToGroupId, userMap, maps);
        System.assertEquals(cipToGroupId, ctxData.cipToGroupId);
        System.assertEquals(userMap, ctxData.userMap);
        System.assertEquals(maps, ctxData.ruleMaps);
    }

    @isTest
    static void testCaseClassificationResult() {
        PolicyKey.CaseClassificationResult res = new PolicyKey.CaseClassificationResult();
        res.cipSet.add('cip1');
        res.fiscalCodes.add('cf1');
        res.r01r07Cases.add(new Case());
        res.r02r08Cases.add(new Case());
        res.casesWithPolicy.add(new Case());
        System.assert(res.cipSet.contains('cip1'));
        System.assert(res.fiscalCodes.contains('cf1'));
        System.assertEquals(1, res.r01r07Cases.size());
        System.assertEquals(1, res.r02r08Cases.size());
        System.assertEquals(1, res.casesWithPolicy.size());
    }
}