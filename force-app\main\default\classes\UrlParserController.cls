public with sharing class UrlParserController implements Metadata.DeployCallback {
    public final static String TARGET_APP = 'c__Disputes';
    public boolean isNull {get; set;}
    
    public static void redirect() {
        String baseQuery = 'SELECT Id,  AccountNameFormula__c, JourneyStep__c, AreasOfNeedFormula__c, ' +
        'Amount, AccountValusFormula__c, CreatedDate, WorkingSLAExpiryDate__c, TemperatureFormula__c, TakenInChargeSLARemainingDays__c ' + 
        'FROM Opportunity WHERE StageName = \'Assegnato\'';
        String orderByQuery = ' ORDER BY TemperatureFormula__c, TakenInChargeSLARemainingDays__c';
        
        // Get URL Parameters
        String amount;// = ApexPages.currentPage().getParameters().get('amount');
        
        // Check URL Parameters and construct query
        if(String.isNotBlank(amount)) {
            baseQuery += ' AND Amount = ' + amount;
        }
        
        // Add ordering to the query
        baseQuery += orderByQuery;

        saveQuery(baseQuery);
    }

    private static void saveQuery(String queryString) {
        DateTime currentDatetime = DateTime.now();
        String unixTimeStamp = String.valueOf(currentDatetime.getTime());
        // ReportQuery__mdt reportQuery = new ReportQuery__mdt(
        //     DeveloperName = unixTimeStamp,
        //     MasterLabel = unixTimeStamp,
        //     Query__c = queryString,
        //     CreatedDate__c = currentDatetime
        // );
        // insert reportQuery;
        Metadata.CustomMetadata customMetadata =  new Metadata.CustomMetadata();
        customMetadata.fullName = 'ReportQuery.Test1';
        customMetadata.label = 'Test1';

        /* Create the Object of CustomMetadataValue */
        Metadata.CustomMetadataValue queryField = new Metadata.CustomMetadataValue();
        queryField.field = 'Query__c';
        queryField.value = queryString;
        customMetadata.values.add(queryField);

        Metadata.CustomMetadataValue createdDateField = new Metadata.CustomMetadataValue();
        createdDateField.field = 'CreatedDate__c';
        createdDateField.value = currentDatetime;
        customMetadata.values.add(createdDateField);

        // Create deployment containter and deploy metadata
        Metadata.DeployContainer metadataContainer = new Metadata.DeployContainer();
        metadataContainer.addMetadata(customMetadata);
        UrlParserController callback = new UrlParserController();
        Id jobId = Metadata.Operations.enqueueDeployment(metadataContainer, callback);
    }

    private PageReference getPageReference(String recordId, String sObjectType) {
        PageReference recordUrl = new PageReference('/lightning/app/' + TARGET_APP + '/r/' + sObjectType + '/' + recordId + '/view');
        recordUrl.setRedirect(true);
        return recordUrl;
    }

    public void handleResult(Metadata.DeployResult result,
                             Metadata.DeployCallbackContext context) {
        if (result.status == Metadata.DeployStatus.Succeeded) {
            System.debug('The metadata deployment was successful.');
        } else {
            System.debug('The metadata deployment was not successful.');
        }
    }
}
