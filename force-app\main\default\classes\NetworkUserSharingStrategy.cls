public without sharing class NetworkUserSharingStrategy implements SharingStrategy {
    private List<NetworkUser__c> networkUsers = new List<NetworkUser__c>();
    private List<NetworkUser__Share> shares = new List<NetworkUser__Share>();

    public void collectTargetRecords(List<SObject> sobjects) {
        for (SObject s : sobjects) {
            if (s instanceof NetworkUser__c) networkUsers.add((NetworkUser__c)s);
        }
    }

    public void applySharing() {
        Set<Id> userIds = new Set<Id>();
        Set<Id> agencyIds = new Set<Id>();
    
        for (NetworkUser__c nu : networkUsers) {
            if (nu.User__c != null) {
                userIds.add(nu.User__c);
            }
            if(nu.Agency__c != null){
                agencyIds.add(nu.Agency__c);
            }
        }

        Map<Id, String> agencyIdToExtId = new Map<Id, String>();
        for (Account a : [SELECT Id, ExternalId__c FROM Account WHERE Id IN :agencyIds]) {
            agencyIdToExtId.put(a.Id, a.ExternalId__c);
        }

        // Map each UserId to all their agency ExternalIds
        Map<Id, Set<String>> userToExtIds = new Map<Id, Set<String>>();
        for (NetworkUser__c nu : networkUsers) {
            if (!userToExtIds.containsKey(nu.User__c)) {
                userToExtIds.put(nu.User__c, new Set<String>());
            }
            userToExtIds.get(nu.User__c).add(agencyIdToExtId.get(nu.Agency__c));
        }
    
        // Get ServiceResources for these users only
        List<ServiceResource> relatedSRs = [
            SELECT Id, RelatedRecordId
            FROM ServiceResource
            WHERE RelatedRecordId IN :userIds
        ];
    
        // Get public groups for those external IDs
        Map<String, Group> groupMap = GroupHelper.getGroupsByExternalIds(new Set<String>(agencyIdToExtId.values()));
    
        // NetworkUser__Share creation
        List<NetworkUser__Share> nuShares = new List<NetworkUser__Share>();
        for (NetworkUser__c nu : networkUsers) {
            System.debug('nus: ' + agencyIdToExtId.get(nu.Agency__c));
            String extId = agencyIdToExtId.get(nu.Agency__c);
            Group g = groupMap.get(extId);
            if (g != null) {
                nuShares.add(new NetworkUser__Share(
                    ParentId = nu.Id,
                    UserOrGroupId = g.Id,
                    AccessLevel = 'Read',
                    RowCause = Schema.NetworkUser__Share.RowCause.Manual
                ));
            }
        }
    
        // UserShare creation
        List<UserShare> userShares = new List<UserShare>();
        for (Id userId : userToExtIds.keySet()) {
            for (String extId : userToExtIds.get(userId)) {
                System.debug('users: ' + extId);
                Group g = groupMap.get(extId);
                if (g != null && userId != null) {
                    userShares.add(new UserShare(
                        UserId = userId,
                        UserOrGroupId = g.Id,
                        UserAccessLevel = 'Read',
                        RowCause = Schema.UserShare.RowCause.Manual
                    ));
                }
            }
        }
    
        // ServiceResourceShare creation
        List<ServiceResourceShare> srShares = new List<ServiceResourceShare>();
        System.debug('relatedSRs: ' + relatedSRs);
        for (ServiceResource sr : relatedSRs) {
            Set<String> srExtIds = userToExtIds.get(sr.RelatedRecordId);
            System.debug('srExtIds: ' + srExtIds);
            if (srExtIds != null) {
                for (String extId : srExtIds) {
                    System.debug('service resources: ' + extId);
                    Group g = groupMap.get(extId);
                    if (g != null) {
                        srShares.add(new ServiceResourceShare(
                            ParentId = sr.Id,
                            UserOrGroupId = g.Id,
                            AccessLevel = 'Read',
                            RowCause = Schema.ServiceResourceShare.RowCause.Manual
                        ));
                    }
                }
            }
        }
    
        // Perform inserts
        if (!nuShares.isEmpty()) insert nuShares;
        if (!userShares.isEmpty()) insert userShares;
        if (!srShares.isEmpty()) insert srShares;
    }
}