@isTest
public class BatchSendCommunication_Test {
    
    @isTest
    static void test_batch_1(){

        Test.startTest();

        Campaign currentCampaign = new Campaign();
        currentCampaign.Name = 'Test';
        insert currentCampaign;

        Communication__c currentCommunication = new Communication__c();
        currentCommunication.CampaignId__c = currentCampaign.Id;
        insert currentCommunication;

        BatchSendCommunication.InputParameters params = new BatchSendCommunication.InputParameters();
        params.campaignId = currentCampaign.Id;

        try{

            BatchSendCommunication.executeBatchSendCommunication(new List<BatchSendCommunication.InputParameters>{params});

        }catch(Exception ex){}

        Test.stopTest();

    }

}
