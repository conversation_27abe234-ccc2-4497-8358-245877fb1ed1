global class EmptyBatchClass implements Database.Batchable<sObject> {
    global Database.QueryLocator start(Database.BatchableContext BC) {
        String query = 'SELECT Id FROM Account LIMIT 1';
        return Database.getQueryLocator(query);
    }

    global static void execute(Database.BatchableContext BC, List<sObject> scope) {
        System.debug('Empty Batch Class Running');
    }

    global void finish(Database.BatchableContext BC) {
        System.debug('Empty Batch Class Finished');
    }
}
