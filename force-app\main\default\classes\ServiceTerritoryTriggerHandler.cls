public with sharing class ServiceTerritoryTriggerHandler {
    public void onAfterInsert(List<ServiceTerritory> newList){
        String type = String.valueOf(newList[0].getSObjectType());
        SharingStrategy strategy = SharingStrategyFactory.getStrategy(type);
        strategy.collectTargetRecords(newList);
        strategy.applySharing();
    }

    public void onAfterUpdate(List<ServiceTerritory> newList, List<ServiceTerritory> oldList, Map<Id,ServiceTerritory> newMap, Map<Id,ServiceTerritory> oldMap){
        String type = String.valueOf(newList[0].getSObjectType());
        SharingStrategy strategy = SharingStrategyFactory.getStrategy(type);
        strategy.collectTargetRecords(newList);
        strategy.applySharing();
    }
}