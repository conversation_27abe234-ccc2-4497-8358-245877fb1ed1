global class UpdateOpportunitiesBatch implements Database.Batchable<SObject> {

    global Database.QueryLocator start(Database.BatchableContext BC) {
    String query = 
        'SELECT Id, StageName, TakenInChargeDate__c ' +
        'FROM Opportunity ' +
        'WHERE ' +
        '(' +
            'StageName = \'Assegnato\' ' +
            'AND RecordType.DeveloperName = \'Omnicanale\' ' +
            'AND TakenInChargeSLAExpiryDate__c < TODAY' +
        ') ' +
        'OR (' +
            'RecordType.DeveloperName = \'Prodotto\' ' +
            'AND Parent__r.StageName = \'Assegnato\' ' +
            'AND Parent__r.RecordType.DeveloperName = \'Omnicanale\' ' +
            'AND Parent__r.TakenInChargeSLAExpiryDate__c < TODAY' +
        ')';
        return Database.getQueryLocator(query);
    }

    global void execute(Database.BatchableContext BC, List<SObject> scope) {
        List<Opportunity> oppsToUpdate = new List<Opportunity>();

        for (SObject s : scope) {
            Opportunity opp = (Opportunity)s;
            opp.StageName = 'In gestione';
            opp.TakenInChargeDate__c = Date.today();
            oppsToUpdate.add(opp);
        }

        if (!oppsToUpdate.isEmpty()) {
            update oppsToUpdate;
        }
    }

    global void finish(Database.BatchableContext BC) {
        System.debug('Batch UpdateOpportunitiesBatch completato.');
    }
}