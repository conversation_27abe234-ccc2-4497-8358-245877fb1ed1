public without sharing class CtrlFeiRequestPayload {
    
    @AuraEnabled(cacheable=true)
    public static String getPayload(String recordId){

        String result = '';

        if(String.isBlank(recordId)) return result;

        System.debug('recordId '+recordId);

        FEI_Environment__c defaultOrganization = FEI_Environment__c.getInstance(UserInfo.getUserId());

        System.debug('@@@ '+defaultOrganization);

        InsurancePolicy currentPolicy = (Test.isRunningTest()) ? new InsurancePolicy(PolicyName = 'IP.INTERROGAZIONE.POLIZZA') : [SELECT Id, PolicyName FROM InsurancePolicy WHERE Id = :recordId LIMIT 1];

        FEI_Settings__mdt currentFEI = [SELECT Id, 
                                            Label,
                                            sendFEIRequest_Payload__c
                                        FROM FEI_Settings__mdt 
                                        WHERE Label = :currentPolicy.PolicyName
                                        AND Active__c = true 
                                        AND Environment__c = :defaultOrganization.Environment__c 
                                        LIMIT 1];

        if(String.isNotBlank(currentFEI.sendFEIRequest_Payload__c)){
            result = currentFEI.sendFEIRequest_Payload__c;
        }

        return result;

    }
 
}