public class DataTableControllerClone {
    public static String EQUAL = 'uguale a';
    public static String NOT_EQUAL = 'diverso';
    public static String GREATER_THAN = 'maggiore di';
    public static String LESS_THAN = 'minore di';
    public static String GREATER_OR_EQUAL = 'maggiore o uguale a';
    public static String LESS_OR_EQUAL = 'minore o uguale a';
    public static String CONTAINS = 'contiene';
    public static String NOT_CONTAINS = 'non contiene';
    public static Map<String, String> OPERATORS = new Map<String, String> {
        EQUAL => '=',
        NOT_EQUAL => '!=',
        GREATER_THAN => '>',
        LESS_THAN => '<',
        GREATER_OR_EQUAL => '>=',
        LESS_OR_EQUAL => '<=',
        CONTAINS => 'LIKE',
        NOT_CONTAINS => 'NOT LIKE'
    };
    
    @AuraEnabled(cacheable=false)
    public static List<Column> getFieldsFromMD(String univiewKey) {
        try {
            List<UniviewTableColumn__mdt> columnMetadata = [
                SELECT id,Object__c, FieldName__c, SearchField__c, ColumnLabel__c, ColumnType__c, FieldOrder__c, IsActive__c, IsSortable__c,
                ColumnYearFormat__c, ColumnMonthFormat__c, ColumnDayFormat__c, ColumnCurrencyCode__c, ColumnVariant__c, DefaultSortDirection__c,
                DefaultSortOrder__c, IsSortedByDefault__c, SortingField__c
                FROM UniviewTableColumn__mdt 
                WHERE UniviewKey__c =: univiewKey AND IsActive__c = true
                ORDER BY FieldOrder__c ASC
            ];

            List<Column> columns = new List<Column>();
            for(UniviewTableColumn__mdt column : columnMetadata) {
                columns.add(new Column(
                    column.Object__c,
                    column.FieldName__c,
                    column.SearchField__c,
                    column.ColumnLabel__c,
                    column.ColumnType__c,
                    column.ColumnYearFormat__c,
                    column.ColumnMonthFormat__c,
                    column.ColumnDayFormat__c,
                    column.ColumnCurrencyCode__c,
                    column.ColumnVariant__c,
                    (Integer) column.FieldOrder__c,
                    column.IsSortable__c,
                    column.IsActive__c,
                    column.DefaultSortDirection__c,
                    (Integer) column.DefaultSortOrder__c,
                    column.IsSortedByDefault__c,
                    column.SortingField__c
                ));
            }

            return columns;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    // @AuraEnabled(cacheable=false)
    // public static DataWrapper getFieldsFromMDa(String univiewKey) {
    //     try {
    //         List<UniviewTableColumn__mdt> columnMetadata = [
    //             SELECT id,Object__c, FieldName__c, SearchField__c, ColumnLabel__c, ColumnType__c, FieldOrder__c, IsActive__c, IsSortable__c,
    //             ColumnYearFormat__c, ColumnMonthFormat__c, ColumnDayFormat__c, ColumnCurrencyCode__c, ColumnVariant__c, DefaultSortDirection__c,
    //             DefaultSortOrder__c, IsSortedByDefault__c, SortingField__c
    //             FROM UniviewTableColumn__mdt 
    //             WHERE UniviewKey__c =: univiewKey AND IsActive__c = true
    //             ORDER BY FieldOrder__c ASC
    //         ];

            
    //         recordMap.put( column.FieldName__c , );
    //         recordMap.put('strChildName', 'Child Name');
    //         recordMap.put('strParentDesc', 'Parent Description');

    //         // wrapper
    //         Map<String, Object> recordMap = new Map<String, Object>();

    //         List<Column> columns = new List<Column>();
    //         for(UniviewTableColumn__mdt column : columnMetadata) {
    //             columns.add(new Column(
    //                 column.Object__c,
    //                 column.FieldName__c,
    //                 column.SearchField__c,
    //                 column.ColumnLabel__c,
    //                 column.ColumnType__c,
    //                 column.ColumnYearFormat__c,
    //                 column.ColumnMonthFormat__c,
    //                 column.ColumnDayFormat__c,
    //                 column.ColumnCurrencyCode__c,
    //                 column.ColumnVariant__c,
    //                 (Integer) column.FieldOrder__c,
    //                 column.IsSortable__c,
    //                 column.IsActive__c,
    //                 column.DefaultSortDirection__c,
    //                 (Integer) column.DefaultSortOrder__c,
    //                 column.IsSortedByDefault__c,
    //                 column.SortingField__c
    //             ));
    //             DynamicDataWrapper wrapper = new DynamicDataWrapper(recordMap);
    //         }
    //         return wrapper;
    //     } catch (Exception e) {
    //         throw new AuraHandledException(e.getMessage());
    //     }
    // }

    // public class DynamicDataWrapper {
    //     @AuraEnabled public Map<String, Column> record { get; set; }
    
    //     public DynamicDataWrapper(Map<String, Column> recordMap) {
    //         this.record = recordMap;
    //     }
    // }
    
    

    public class Column {
        @AuraEnabled
        public String objectType {get;set;}
        @AuraEnabled
        public String fieldName {get;set;}
        @AuraEnabled
        public String searchField {get;set;}
        @AuraEnabled
        public String columnLabel {get;set;}
        @AuraEnabled
        public String columnType {get;set;}
        @AuraEnabled
        public String columnYearFormat {get;set;}
        @AuraEnabled
        public String columnMonthFormat {get;set;}
        @AuraEnabled
        public String columnDayFormat {get;set;}
        @AuraEnabled
        public String columnCurrencyCode {get;set;}
        @AuraEnabled
        public String columnVariant {get;set;}
        @AuraEnabled
        public Integer fieldOrder {get;set;}
        @AuraEnabled
        public Boolean isSortable {get;set;}
        @AuraEnabled
        public Boolean isActive {get;set;}
        @AuraEnabled
        public String defaultSortDirection {get;set;}
        @AuraEnabled
        public Integer defaultSortOrder {get;set;}
        @AuraEnabled
        public Boolean isSortedByDefault {get;set;}
        @AuraEnabled
        public String sortingField {get;set;}

        public Column(
            String objectType,
            String fieldName,
            String searchField,
            String columnLabel,
            String columnType,
            String columnYearFormat,
            String columnMonthFormat,
            String columnDayFormat,
            String columnCurrencyCode,
            String columnVariant,
            Integer fieldOrder,
            Boolean isSortable,
            Boolean isActive,
            String defaultSortDirection,
            Integer defaultSortOrder,
            Boolean isSortedByDefault,
            String sortingField
        ) {
            this.objectType = objectType;
            this.fieldName = fieldname; //fieldname.contains('.') ? fieldName.replace('.', '_') : fieldName;
            this.searchField = searchField;
            this.columnLabel = columnLabel;
            this.columnType = columnType;
            this.columnYearFormat = columnYearFormat;
            this.columnMonthFormat = columnMonthFormat;
            this.columnDayFormat = columnDayFormat;
            this.columnCurrencyCode = columnCurrencyCode;
            this.columnVariant = columnVariant;
            this.fieldOrder = fieldOrder;
            this.isSortable = isSortable;
            this.isActive = isActive;
            this.defaultSortDirection = defaultSortDirection;
            this.defaultSortOrder = defaultSortOrder;
            this.isSortedByDefault = isSortedByDefault;
            this.sortingField = sortingField;
        }

        public Column() {}
    }

    public class DataWrapper {
        @AuraEnabled
        public Map<String, Object> data { get; set; }
    
        public DataWrapper(Map<String, Object> inputData) {
            data = new Map<String, Object>();
            data.putAll(inputData);
        }
    }

    public class ResponseObject {
        @AuraEnabled
        public map<String,list<map<String,Object>>>  records {get;set;}
        @AuraEnabled
        public Integer numberOfPages {get;set;}
        
        public ResponseObject( map<String,list<map<String,Object>>> records, Integer numberOfPages) {
            this.records = records;
            this.numberOfPages = numberOfPages;
        }
    }

    public class QueryRequest{
        @AuraEnabled
        public List<FilterSectionController.FilterPersistence> filters {get;set;}
        @AuraEnabled
        public List<Column> columns {get;set;}
        @AuraEnabled
        public Integer offset {get;set;}
        @AuraEnabled
        public Integer numberOfRecordsPerPage {get;set;}
        @AuraEnabled
        public Integer currentPage {get;set;}
        @AuraEnabled
        public String objectType {get;set;}
        @AuraEnabled
        public String univiewKey {get;set;}
        @AuraEnabled
        public String sortedBy {get;set;}
        @AuraEnabled
        public String sortDirection {get;set;}
        @AuraEnabled
        public String searchKey {get;set;}
        @AuraEnabled
        public Boolean scopingRule {get;set;}
        @AuraEnabled
        public String recordTypes {get;set;}
    }

    @AuraEnabled(cacheable=false)
    public static ResponseObject queryRecords(QueryRequest request) {
        try {
            //new
            map<String,list<map<String,Object>>> mapData = new map<String,list<map<String,Object>>>();
            list<map<String,Object>> listWithOutSeach  = new list<map<String,Object>>();
            String query = createQuery(request, request.columns);
            List<SObject> allRecordsQueried = Database.query(query);
            // Custom search on each column
            list<map<String,Object>>  searchCompatibleRecords = new list<map<String,Object>>();
                for(SObject record : allRecordsQueried) {
                    Map<String, Object> inputMap = new Map<String, Object>();
                    Boolean found =false;
                    for(Column column : request.columns) {
                        Object targetfield;
                        if(column.fieldName.contains('.')){
                            List<String> parts = column.searchField.split('\\.');
                            String objectName = parts[0];
                            String searchField = parts[1];
                            targetfield = record.getSObject(objectName).get(searchField);
                        }else{
                            targetfield = record.get(column.fieldName);
                        }
                        String a=column.fieldName.contains('.') ? column.fieldName.replace('.', '_') : column.fieldName;
                        inputMap.put(a , targetfield);
                        if(String.isNotBlank(request.searchKey)){
                            if(targetfield != null && String.valueOf(targetfield)?.containsIgnoreCase(request.searchKey) ) {
                                found=true;
                            }
                        } 
                    }
                    if(String.isNotBlank(request.searchKey)){
                        if(found){
                            DataWrapper wrapper = new DataWrapper(inputMap);
                            searchCompatibleRecords.add(wrapper.data);  
                        }
                    }else{
                        DataWrapper wrapper = new DataWrapper(inputMap);
                        listWithOutSeach.add(wrapper.data); 
                    }
                }
            Integer recordCount = String.isNotBlank(request.searchKey) ? searchCompatibleRecords.size() : listWithOutSeach.size();
            Integer endIndex = Math.min(request.currentPage * request.numberOfRecordsPerPage, recordCount);
            
            list<map<String,Object>> recordsToReturn = new list<map<String,Object>> ();
            for (Integer i = request.offset; i< endIndex; i++) {
                recordsToReturn.add(String.isNotBlank(request.searchKey) ? searchCompatibleRecords[i] : listWithOutSeach[i]);
            }

            mapData.put('Details',recordsToReturn);
           
            return new ResponseObject (mapData, (Integer) Math.ceil((Decimal) recordCount / (Decimal) request.numberOfRecordsPerPage));
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }


    public static String createQuery(QueryRequest request, List<Column> columns) {    
        // Create SELECT portion of the query
       
        String query = 'SELECT';
        for(Column c : columns) {
            query += ' ' + c.fieldName + ',' + (c.fieldName != c.searchField ? ' ' + c.searchField + ',' : '');
        }
        query = query.removeEnd(',') + ' ';

        // Creating the FROM portion of the query
        query += 'FROM ' + request.objectType + ' ';

        // Creating the WHERE portion of the query
        query += 'WHERE';
        if (String.isNotBlank(request.recordTypes)) {
            List<String> recordTypesList = request.recordTypes.split(';');
            if (!recordTypesList.isEmpty()) {                
                query += ' RecordType.DeveloperName IN ' + '(\'' + String.join(recordTypesList, '\',\'') + '\') ';
            }
        }
        for(FilterSectionController.FilterPersistence filter : request.filters) {
            if((filter.fieldType == 'Number' || filter.fieldType == 'Boolean') && (String.isNotBlank(filter.filterValue) || filter.isNullable)) {
                // Create numeric filter statements
                if (String.isBlank(filter.filterValue)) {
                    query += ' AND ' + filter.fieldName + ' ' + OPERATORS.get(filter.filterOperator) + ' ' + null;
                }
                else{
                    query += ' AND ' + filter.fieldName + ' ' + OPERATORS.get(filter.filterOperator) + ' ' + filter.filterValue;
                }
                
            }else if(filter.fieldType == 'Text' && filter.filterType == 'Multiselect with Suggestions' && String.isNotBlank(filter.filterValue)) {
                List<String> valuesList = filter.filterValue.split(',');
                String valuesString = '';
                for(String s : valuesList) {
                    valuesString += '\'' + s + '\',';
                }
                valuesString = '(' + valuesString.removeEnd(',') + ')';
                query += ' AND ' + filter.fieldName + ' IN ' + valuesString;
            }
            else if(filter.fieldType == 'Text' && (String.isNotBlank(filter.filterValue) || filter.isNullable)) {
                if(filter.filterOperator == CONTAINS) {
                    query += ' AND ' + filter.fieldName + ' ' + OPERATORS.get(filter.filterOperator) + ' \'%' + filter.filterValue + '%\'' ;
                }else if(filter.filterOperator == NOT_CONTAINS) {
                    List<String> operatorParts = OPERATORS.get(filter.filterOperator).split(' ');
                    String notOperator = operatorParts[0];
                    String likeOperator = operatorParts[1];
                    query += ' AND ' + '(' + notOperator + ' ' + filter.fieldName + ' ' + likeOperator + ' \'%' + filter.filterValue + '%\'' +')';
                }else{
                    filter.filterValue = filter.filterValue == null ? '' : filter.filterValue;
                    query += ' AND ' + filter.fieldName + ' ' + OPERATORS.get(filter.filterOperator) + ' \'' + filter.filterValue + '\'';
                }
            }
            else if(filter.fieldType == 'Picklist' && filter.filterType == 'Multiselect' && String.isNotBlank(filter.filterValue)) {
                // Create picklist filter
                if(filter.filterValue!='Tutti'){
                    List<String> valuesList = filter.filterValue.split(',');
                    String valuesString = '';

                    for(String s : valuesList) {
                        valuesString += '\'' + s + '\',';
                    }
                    valuesString = '(' + valuesString.removeEnd(',') + ')';
                    query += ' AND ' + filter.fieldName + ' IN ' + valuesString;
                }
                
            }
            else if(filter.fieldType == 'Multiselect' && filter.filterType == 'Multiselect' && String.isNotBlank(filter.filterValue)) {
                List<String> valuesList = filter.filterValue.split(',');
                String valuesString = '';
                for(String s : valuesList) {
                    valuesString += '\'' + s + '\',';
                }
                valuesString = '(' + valuesString.removeEnd(',') + ')';
                query += ' AND ' + filter.fieldName + ' INCLUDES ' + valuesString;
            }
            else if(filter.fieldType == 'Date' && String.isNotBlank(filter.filterValue)) {
                try {
                    List<String> dateParts = filter.filterValue.split('-');
                    Datetime dateInstance;
                    if(filter.filterOperator == LESS_THAN || filter.filterOperator == LESS_OR_EQUAL) {
                        dateInstance = Datetime.newinstanceGMT(Integer.valueOf(dateParts[0]), Integer.valueOf(dateParts[1]), Integer.valueOf(dateParts[2]), 23, 59, 59);
                    }
                    else if(filter.filterOperator == GREATER_THAN || filter.filterOperator == GREATER_OR_EQUAL) {
                        dateInstance = Datetime.newinstanceGMT(Integer.valueOf(dateParts[0]), Integer.valueOf(dateParts[1]), Integer.valueOf(dateParts[2]));
                    }
                    else if(filter.filterOperator == EQUAL || filter.filterOperator == NOT_EQUAL){
                        dateInstance = Datetime.newinstanceGMT(Integer.valueOf(dateParts[0]), Integer.valueOf(dateParts[1]), Integer.valueOf(dateParts[2]));
                    }
                    query += ' AND ' + filter.fieldName + ' ' + OPERATORS.get(filter.filterOperator) + ' ' + dateInstance.formatGMT('yyyy-MM-dd');
                } catch(DmlException e) {
                    System.debug('In the Date Type Fields: ' + e.getMessage());
                }
            }
            else if(filter.fieldType == 'Date/Time' && String.isNotBlank(filter.filterValue)) {
                try {
                    List<String> dateParts = filter.filterValue.split('-');
                    Datetime dateInstance;
                    if(filter.filterOperator == LESS_THAN ) {
                        dateInstance = Datetime.newinstanceGMT(Integer.valueOf(dateParts[0]), Integer.valueOf(dateParts[1]), Integer.valueOf(dateParts[2]), 00, 00, 00);
                    }
                    else if(filter.filterOperator == LESS_OR_EQUAL) {
                        dateInstance = Datetime.newinstanceGMT(Integer.valueOf(dateParts[0]), Integer.valueOf(dateParts[1]), Integer.valueOf(dateParts[2]), 23, 59, 59);
                       
                    }
                    else if(filter.filterOperator == GREATER_THAN || filter.filterOperator == GREATER_OR_EQUAL) {
                        dateInstance = Datetime.newinstanceGMT(Integer.valueOf(dateParts[0]), Integer.valueOf(dateParts[1]), Integer.valueOf(dateParts[2]));
                    }
                    query += ' AND ' + filter.fieldName + ' ' + OPERATORS.get(filter.filterOperator) + ' ' + dateInstance.formatGMT('yyyy-MM-dd\'T\'HH:mm:ss.SSS\'Z\'');
                } catch(DmlException e) {
                    System.debug('In the Date/Time Type Fields: ' + e.getMessage());
                }
            }
        }

        query = query.replace('WHERE AND', 'WHERE');
        query = query.removeEnd(' WHERE');

        // Creating the ORDER BY portion of the query - Datatable sorting
        if(String.isNotBlank(request.sortedBy) && String.isNotBlank(request.sortDirection)) {
            query += ' ORDER BY ' + request.sortedBy + ' ' + request.sortDirection + ' NULLS LAST,';
        }

        // Creating the ORDER BY portion of the query - Default sorting
        List<UniviewTableColumn__mdt> columnSortedByDefaultMetadata = getColumnsSortedByDefault(request.univiewKey);
        query = !query.contains('ORDER BY') ? query + ' ORDER BY' : query;
        for(UniviewTableColumn__mdt column : columnSortedByDefaultMetadata) {
            query  += ' ' + column.SortingField__c + ' ' + column.DefaultSortDirection__c + ' NULLS LAST,';
        }
        query = query.removeEnd(',');
        query = query.removeEnd('ORDER BY');
        
        System.debug(query);
        return query;
    }

    public static List<UniviewTableColumn__mdt> getColumnsSortedByDefault(String univiewKey) {
        List<UniviewTableColumn__mdt> columnMetadata = [
                SELECT IsActive__c, DefaultSortDirection__c, DefaultSortOrder__c, IsSortedByDefault__c, SortingField__c
                FROM UniviewTableColumn__mdt 
                WHERE UniviewKey__c =: univiewKey AND IsSortedByDefault__c = true AND IsActive__c = true
                ORDER BY DefaultSortOrder__c ASC
        ];
        return columnMetadata;
    }

    // With Sharing and WithoutSharing
    private static DataTableGetRecords getRecords {
        get {
            if (null == getRecords) {
                // Use with sharing by default
                getRecords = new WithoutSharing();
            }
            return getRecords;
        }
        set;
    }

    @AuraEnabled(cacheable=false)
    public static ResponseObject getDataTableRecords(QueryRequest request) {

        if(request.scopingRule){
            getRecords = new WithSharing();
        }else {
            getRecords = new WithoutSharing();
        }
        return getRecords.getDataTableRecords(request,request.scopingRule);
    }
    private abstract class DataTableGetRecords {
        public ResponseObject getDataTableRecords (QueryRequest request,Boolean scopingRule) {
            if(scopingRule){
                WithSharing withoutSharingInstance = new WithSharing();
                return withoutSharingInstance.getRecs(request);
            }
            else{
                WithoutSharing withoutSharingInstance = new WithoutSharing();
                return withoutSharingInstance.getRecs(request);
            }
        }
    }

    private with sharing class WithSharing extends DataTableGetRecords {
        public ResponseObject getRecs(QueryRequest request){
            return queryRecords(request);
        }
    }
    private without sharing class WithoutSharing extends DataTableGetRecords {
        public ResponseObject getRecs(QueryRequest request){
            return queryRecords(request);
        }
    }

}
