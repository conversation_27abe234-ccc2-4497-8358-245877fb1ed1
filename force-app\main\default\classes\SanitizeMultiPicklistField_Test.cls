@IsTest
public with sharing class SanitizeMultiPicklistField_Test
{
    @IsTest
    public static void sanitizeTest()
    {
        String input = 'Cane e Gatto;<PERSON>eicoli;Veicoli';

        Test.startTest();
        List<String> output = SanitizeMultiPicklistField.sanitizeField(new List<String> {input});
        Test.stopTest();
        System.assertEquals(1, output.size(), 'Amount of output data was different from expected');
        System.assertEquals('Cane e Gatto;Vei<PERSON>li', output.get(0), 'Output data was different from expected');
    }
}
