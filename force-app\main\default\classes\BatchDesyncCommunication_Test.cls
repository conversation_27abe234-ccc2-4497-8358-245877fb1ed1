@isTest
public class BatchDesyncCommunication_Test {
    
    @isTest
    static void test_batch_1(){

        Test.startTest();

        Campaign currentCampaign = new Campaign();
        currentCampaign.Name = 'Test';
        insert currentCampaign;

        Communication__c currentCommunication = new Communication__c();
        currentCommunication.CampaignId__c = currentCampaign.Id;
        currentCommunication.ReadyToSend__c = true;
        insert currentCommunication;

        BatchSendCommunication.InputParameters params = new BatchSendCommunication.InputParameters();
        params.campaignId = currentCampaign.Id;

        try{

            BatchDesyncCommunication.executeBatchDesyncCommunication();

        }catch(Exception ex){}

        Test.stopTest();

    }

}
