@isTest
private class OpportunityWSWrapperTest {
    @isTest
    static void testOpportunityWSWrapperInstantiation() {
        OpportunityWSWrapper wrapper = new OpportunityWSWrapper();
        wrapper.id = null;
        wrapper.externalId = 'EXT001';
        wrapper.recordTypeId = null;
        wrapper.domainType = 'TestDomain';
        wrapper.name = 'Test Opportunity';
        wrapper.accountId = null;
        wrapper.agency = 'Agency1';
        wrapper.cip = 'CIP001';
        wrapper.selectedByLocator = true;
        wrapper.leadSource = 'Web';
        wrapper.channel = 'Online';
        wrapper.engagementPoint = 'Landing';
        wrapper.areaOfNeed = new List<String>{'Auto', 'Casa'};
        wrapper.amount = 10000.50;
        wrapper.hasCallMeBack = false;
        wrapper.rating = 'Hot';
        wrapper.workingSLAExpiryDate = System.now().addDays(2);
        wrapper.takenInChargeDate = System.now();
        wrapper.takenInChargeSLAExpiryDate = System.now().addDays(1);
        wrapper.proposalId = 'PROP001';
        wrapper.proposalIssueDate = System.now();
        wrapper.bankBranch = 'Branch1';
        wrapper.bankAgent = 'Agent1';
        wrapper.quote = new QuoteWSWrapper();
        wrapper.appointmentDate = System.now().addDays(3);

        OpportunityWSWrapper.testCoverage = true;

        System.assertNotEquals(null, wrapper);
    }
    
    @isTest
    static void testCoverageWSWrapperInstantiation() {
        CoverageWSWrapper wrapper = new CoverageWSWrapper();
        wrapper.id = null;
        wrapper.externalId = 'EXT001';
        wrapper.name = 'Test Coverage';
        wrapper.asset = 'Auto';
        wrapper.stageName = 'New';
        wrapper.amount = 1500.00;
        wrapper.rating = 'A';
        wrapper.areaOfNeed = 'Auto';
        wrapper.conventionCode = 'CONV123';
        wrapper.firstName = 'Mario';
        wrapper.lastName = 'Rossi';
        wrapper.fiscalCode = '****************';
        wrapper.birthDate = Date.newInstance(1980, 1, 1);
        wrapper.email = '<EMAIL>';
        wrapper.mobilePhone = '**********';
        wrapper.fractionation = 'Annuale';
        wrapper.engagementPoint = 'Web';
        wrapper.description = 'Test description';

        CoverageWSWrapper.testCoverage = true;

        System.assertNotEquals(null, wrapper);
    }
}