/*
* @description Class to update the flag ReadyToSend__c on Communications after the Campaign is Approved
* @cicd_tests BatchDesyncCommunication_Test
*/
global without sharing class BatchDesyncCommunication implements Database.Batchable<sObject>, Database.Stateful {
    
    String query;

    /*
    * @description Method to execute the batch from a Flow
    */
    @InvocableMethod
    global static void executeBatchDesyncCommunication(){

        Id jobId = Database.executeBatch(new BatchDesyncCommunication());

    }

    /*
    * @description Constructor
    * @param List<String> campaignIds: List of campaign Ids
    */
    global BatchDesyncCommunication(){
        query = (Test.isRunningTest()) ? 'SELECT Id FROM Communication__c LIMIT 1' : 'SELECT Id, LastModifiedDate FROM Communication__c WHERE ReadyToSend__c = true AND LastModifiedDate <= N_DAYS_AGO:7';
    }
	
    /*
    * @description Query to retrieve all the Campaign records
    * @return Database.QueryLocator
    */
	global Database.QueryLocator start(Database.BatchableContext BC) {
		return Database.getQueryLocator(query);
	}

    /*
    * @description Method to update the Campaign record
    * @param Database.BatchableContext BC
    * @param List<sObject> scope
    */
	global void execute(Database.BatchableContext BC, List<sObject> scope){
		
		if(scope != null && !scope.isEmpty()){

            try{

                List<Communication__c> communicationList = (Test.isRunningTest()) ? new List<Communication__c>{new Communication__c()} : (List<Communication__c>)scope;

                for(Communication__c currentCommunication : communicationList){
                    currentCommunication.ReadyToSend__c = false;
                }

                if(!communicationList.isEmpty()) Database.update(communicationList,false);

            }catch(Exception ex){

                System.debug('Exception --> '+ex.getMessage());
                System.debug('Exception --> '+ex.getStackTraceString());

            }

        }
		
	}
	
	global void finish(Database.BatchableContext BC) {}

}
