/*********************************************************************************
* <AUTHOR>
* @description    This Class is used for managing time schedule of the users in the agencies
* @date           2024-05-21
**********************************************************************************/
public without sharing class SchedulerOperatingHours {
  /*********************************************************************************
  * <AUTHOR>
  * @description    This Class is used as Time Slot which store necessary information about Time and
  * connected Object in the datamodel
  * @date           2024-05-21
  **********************************************************************************/
  public class Slot {
    @AuraEnabled
    public String deleteRecord {get;set;}
    @AuraEnabled
    public String Id {get;set;}
    @AuraEnabled
    public String recId {get;set;}
    @AuraEnabled
    public String operatingHoursId {get;set;}
    @AuraEnabled
    public String dayOfWeek {get;set;}
    @AuraEnabled
    public String agencyId {get;set;}
    @AuraEnabled
    public String agencyName {get;set;}
    @AuraEnabled
    public String startTime {get;set;}
    @AuraEnabled
    public String endTime {get;set;}

    public Slot(Id recId,String dayOfWeek,String deleteRecord,Id operatingHoursId,Id agencyId, String agencyName,Time startTime, Time endTime){
      this.deleteRecord=deleteRecord;
      this.recId =recId;
      this.dayOfWeek = dayOfWeek;
      this.operatingHoursId=operatingHoursId;
      this.agencyId = agencyId;
      this.agencyName = agencyName;
      this.startTime = transformTime(startTime);
      this.endTime = transformTime(endTime);
    }

    public Slot(String recId,String deleteRecord, String dayOfWeek,String operatingHoursId, String startTime, String endTime){
      this.deleteRecord=deleteRecord;
      this.recId = recId;
      this.dayOfWeek =dayOfWeek;
      this.operatingHoursId = operatingHoursId;
      this.startTime = startTime;
      this.endTime = endTime;
    }
    /*********************************************************************
    * <AUTHOR>
    * @date         2022-04-06
    * @description  The method transform the time to string (format HH:MM)
    * @param        timeVar (Time): Time variable
    * @return       String: the transformed Time variable
    *********************************************************************/
    public String transformTime(Time timeVar){
      String transform;
      if(timeVar.hour()<10){
        transform = '0'+ String.valueOf(timeVar.hour());
      }else{
        transform = String.valueOf(timeVar.hour());
      }
      transform=transform+':';
      if(timeVar.minute()<10){
        transform = transform + '0'+ String.valueOf(timeVar.minute());
      }else{
        transform = transform + String.valueOf(timeVar.minute());
      }
      return transform;
    }
  }
  /*********************************************************************
  * <AUTHOR>
  * @date         2022-05-21
  * @description  The method is used to return the map of the days of the weeks with the List of Slots
  * @param        recordId (String): The user Id which taken form the record page
  * @return       Map<String, List<Slot>>: A map of Days of the week with the List ofSlots
  *********************************************************************/
  @AuraEnabled(cacheable=false)
  public static Map<String, List<Slot>> getTimeSlot(String recordId) {
    try {

      Map<Id, ServiceTerritoryMember> oper = new Map<Id, ServiceTerritoryMember>([SELECT Id,OperatingHoursId,ServiceResourceId,ServiceTerritoryId,ServiceTerritory.Name FROM ServiceTerritoryMember WHERE ServiceResourceId =: recordId AND OperatingHoursId!=null AND ServiceTerritory.IsActive=true ORDER BY ServiceTerritory.Name]);
      Map<Id, ServiceTerritoryMember> operatingHoursMap = new Map<Id, ServiceTerritoryMember>();

      for (ServiceTerritoryMember member : oper.values()) {
        operatingHoursMap.put(member.OperatingHoursId, member);
      }

      List<TimeSlot> timeSlots = [SELECT Id,OperatingHoursId,DayOfWeek,StartTime,EndTime FROM TimeSlot WHERE OperatingHoursId IN :operatingHoursMap.keySet() ORDER BY StartTime];

      Map<String, List<Slot>> timeSlotsByDay = new Map<String, List<Slot>>();
      List<String> daysOfWeek = new List<String>{'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'};
      for (String day : daysOfWeek){
        timeSlotsByDay.put(day,new List<Slot>());
      }

      for (TimeSlot ts : timeSlots) {
        if (!timeSlotsByDay.containsKey(ts.DayOfWeek)) {
          timeSlotsByDay.put(ts.DayOfWeek, new List<Slot>());
        }
        timeSlotsByDay.get(ts.DayOfWeek).add(new Slot(ts.Id,
        ts.DayOfWeek,
        '',
        operatingHoursMap.get(ts.OperatingHoursId).OperatingHoursId,
        operatingHoursMap.get(ts.OperatingHoursId).ServiceTerritoryId,
        operatingHoursMap.get(ts.OperatingHoursId).ServiceTerritory.Name,
        ts.StartTime,
        ts.EndTime));
      }

      return timeSlotsByDay;
    } catch (Exception e) {
      throw new AuraHandledException(e.getMessage());
    }
  }

  /*********************************************************************
  * <AUTHOR>
  * @date         2022-05-21
  * @description  The method is used to return the information of the Agencies
  * @param        recordId (String): The user Id which taken form the record page
  * @return       Map<String, ServiceTerritoryMember>: A map of Operating Hours Id with the ServiceTerritoryMember
  *********************************************************************/
  @AuraEnabled(cacheable=false)
  public static Map<Id, ServiceTerritoryMember> getAgency(String recordId) {
    try{
      Map<Id, ServiceTerritoryMember> serviceTerritoryMemberMap = new Map<Id, ServiceTerritoryMember>([SELECT  Id,OperatingHoursId,ServiceResourceId,ServiceTerritoryId,ServiceTerritory.Name FROM ServiceTerritoryMember WHERE ServiceResourceId =: recordId AND OperatingHoursId!=null]);
      Map<Id, ServiceTerritoryMember> ServiceTerritoryMap = new Map<Id, ServiceTerritoryMember>();

      for (ServiceTerritoryMember member : serviceTerritoryMemberMap.values()) {
        ServiceTerritoryMap.put(member.OperatingHoursId, member);
      }
      return ServiceTerritoryMap;
    }catch (Exception e) {
      throw new AuraHandledException(e.getMessage());
    }
  }
  /*********************************************************************
  * <AUTHOR>
  * @date         2022-05-21
  * @description  The method returns the Service Resource Id , the technical id of the user
  * @return       String: Service Resource Id
  *********************************************************************/
  @AuraEnabled(cacheable=false)
  public static String getServiceResource(){
    Id userId = UserInfo.getUserId();
    String ServiceResourceId;

    List<ServiceResource> ServiceResourceObejct = [SELECT Id FROM ServiceResource WHERE RelatedRecordId=:userId];
    if(ServiceResourceObejct.size()>0){
      ServiceResourceId = ServiceResourceObejct[0].Id;
    }

    return ServiceResourceId;
  }

  /*********************************************************************
  * <AUTHOR>
  * @date         2022-05-21
  * @description  The method deletes List of Ids of the TimeSlots
  * @param        RecordTobeRemoved (List<String>): List of Ids of the TimeSlots
  *********************************************************************/
  @AuraEnabled(cacheable=false)
  public static void recordTobeRemoved(List<String> RecordTobeRemoved){
    try{
      List<TimeSlot> timeSlotTobeRemoved =[SELECT ID FROM TimeSlot WHERE ID IN : RecordTobeRemoved];
      delete timeSlotTobeRemoved;
    }catch (Exception e) {
      throw new AuraHandledException(e.getMessage());
    }
  }

  /*********************************************************************
  * <AUTHOR>
  * @date         2022-05-21
  * @description  This methods returns the hours as Interger in the first position of the List
  * and the minutes as Integer in the second position of the List
  * @param        timeString (String): the Time as Stirng (format : HH:MM)
  * @return       List<Integer> : List of Interger about the Hours and the Minutes
  *********************************************************************/
  public static List<Integer> splitTime(String timeString) {
      List<Integer> timeList = new List<Integer>();
      String[] timeParts = timeString.split(':');
      if (timeParts.size() == 2) {
          Integer hours = Integer.valueOf(timeParts[0]);
          Integer minutes = Integer.valueOf(timeParts[1]);

          timeList.add(hours);
          timeList.add(minutes);
      } else {
          System.debug('Invalid time format.');
      }
      return timeList;
  }
  /*********************************************************************
  * <AUTHOR>
  * @date         2022-05-21
  * @description  This methods is used to do insert,update,delete for the TimeSlots
  * @param        changedItems (String): the payload from the frontend
  *********************************************************************/
  @AuraEnabled(cacheable=false)
  public static void upsertTimeSlots(String changedItems){
    try{
      List<Object> jsonArray = (List<Object>) JSON.deserializeUntyped(changedItems);

      List<TimeSlot> timeSlotsToInsert = new List<TimeSlot>();
      List<Slot> SlotsToUpdate = new List<Slot>();
      List<String> SlotsToBeDeleted = new List<String>();

      // Iterate over each JSON object in the array
      for (Object obj : jsonArray) {
        Map<String, Object> jsonData = (Map<String, Object>) obj;

        // Extract data from JSON map
        String recId = (String) jsonData.get('recId');
        String dayOfWeek = (String) jsonData.get('dayOfWeek');
        String deleteRecord = (String) jsonData.get('deleteRecord');
        String operatingHoursId = (String) jsonData.get('operatingHoursId');
        String agencyId = (String) jsonData.get('agencyId');
        String startTime = (String) jsonData.get('startTime');
        String endTime = (String) jsonData.get('endTime');
        System.debug('OK1'+operatingHoursId);
        if ( recId=='') {
          //insert
          TimeSlot timeSlot = new TimeSlot(
            DayOfWeek = dayOfWeek,
            operatingHoursId = operatingHoursId,
            StartTime = Time.newInstance(splitTime(startTime)[0], splitTime(startTime)[1], 0, 0),
            EndTime = transformEndTime(endTime)
          );

          timeSlotsToInsert.add(timeSlot);
        }else {
          if(deleteRecord=='delete' || deleteRecord=='update'){
            if(deleteRecord=='delete'){
              //delete
              SlotsToBeDeleted.add(recId);
            }
            if(deleteRecord=='update'){
              //delete
              //insert
              TimeSlot timeSlot = new TimeSlot(
                DayOfWeek = dayOfWeek,
                operatingHoursId = operatingHoursId,
                StartTime = Time.newInstance(splitTime(startTime)[0], splitTime(startTime)[1], 0, 0),
                EndTime = transformEndTime(endTime)
              );
              timeSlotsToInsert.add(timeSlot);
              SlotsToBeDeleted.add(recId);
            }
          }else{
            //update
            Slot timeSlot = new Slot(
              recId,
              '',
              dayOfWeek,
              operatingHoursId,
              startTime,
              endTime
            );
            SlotsToUpdate.add(timeSlot);
          }
        }
      }
      System.debug('OK2');
      // In this part , we want to find the TimeSlots which we want to update the fields StartTime and EndTime
      List<Id> ids = new List<Id>();

      for (Slot slot : SlotsToUpdate) {
        ids.add(slot.recId);
      }

      List<TimeSlot> timeSlotsToUpdate =[SELECT Id,OperatingHoursId,DayOfWeek,StartTime,EndTime FROM TimeSlot WHERE Id IN :ids];
      for (Integer i = 0; i < timeSlotsToUpdate.size(); i++) {
        for (Integer j = 0; j < SlotsToUpdate.size(); j++) {
          if(timeSlotsToUpdate[i].Id == SlotsToUpdate[j].recId){
            timeSlotsToUpdate[i].DayOfWeek = SlotsToUpdate[j].dayOfWeek;
            timeSlotsToUpdate[i].StartTime = Time.newInstance(splitTime(SlotsToUpdate[j].startTime)[0], splitTime(SlotsToUpdate[j].startTime)[1], 0, 0);
            timeSlotsToUpdate[i].EndTime = transformEndTime(SlotsToUpdate[j].endTime);
          }
        }
      }
      System.debug(timeSlotsToInsert);
      // Perform DML operation
      if (!SlotsToBeDeleted.isEmpty()) {
        recordTobeRemoved(SlotsToBeDeleted);
      }
      System.debug('ok3');
      if (!timeSlotsToInsert.isEmpty()) {
        insert timeSlotsToInsert;
      }
      System.debug('ok4');
      if (!timeSlotsToUpdate.isEmpty()) {
        update timeSlotsToUpdate;
      }
    }catch (Exception e) {
      throw new AuraHandledException(e.getMessage());
    }
  }
  /*********************************************************************
  * <AUTHOR>
  * @date         2022-05-21
  * @description  This methods is used to transform the end time when
  * it is "00:00" to "23:59:59"
  * @param        endTime (String): the end Time of the Slot
  * @return       Time : endTime
  *********************************************************************/
  public static Time transformEndTime(String endTime){
    Time transformEndTime;
    if(endTime == '00:00'){
      transformEndTime = Time.newInstance(23, 59, 59, 59);
    }else{
      transformEndTime = Time.newInstance(splitTime(endTime)[0], splitTime(endTime)[1], 0, 0);
    }
    return transformEndTime;
  }
  /*********************************************************************
  * <AUTHOR>
  * @date         2022-05-21
  * @description  This method gives us a warning if the Event interfere with TimeSlots
  * @param        recordId (String): the user id
  * @param        Payload (String): the payload from the frontend
  * @return       Boolean : Return true when we have a conflicted between Event and Time Slots
  *********************************************************************/
  @AuraEnabled(cacheable=false)
  public static Boolean getWarningsEvents(String recordId,String Payload){
    String userId = UserInfo.getUserId();
    Set<String> setModifiedTerritoriesId = getModifiedTerritories(Payload);
    Set<String> setModifiedDaysOfWeek = getModifieddDaysOfWeek(Payload);
    Set<String> setModifiedTerritoriesNames = getModifiedTerritoriesNames(Payload);

    Map<String, List<Slot>> mapSlots = initDataMap(recordId,setModifiedTerritoriesId,setModifiedDaysOfWeek);
    List<Event> eventList = getEvents(userId,setModifiedTerritoriesNames,setModifiedDaysOfWeek);

    for(Event event : eventList){
      String dayOfWeek = event.StartDateTime.format('EEEE');
      Boolean flag = false ;
      if(!mapSlots.get(dayOfWeek).isEmpty()){
        List<Slot> listOfSlots = mapSlots.get(dayOfWeek);
        if(listOfSlots.isEmpty()){
          //Event found , but no timeslot for this say of the week - Warning
          flag = true;
        }else{
          //Event found , timeslot found , check if event is inside of the timeslot
          for(Slot slot : listOfSlots){
            Time newTime = transformEndTime(slot.endTime);
            String slotStartTime=DateTime.newInstance(1997, 1, 31, splitTime(slot.startTime)[0],splitTime(slot.startTime)[1] , 0).format('HH:mm:ss');
            String slotEndTime=DateTime.newInstance(1997, 1, 31, newTime.hour(), newTime.minute(), 0).format('HH:mm:ss');
            String eventStartTime = event.StartDateTime.format('HH:mm:ss');
            String eventEndTime = event.EndDateTime.format('HH:mm:ss');
            System.debug(slotStartTime + 'dadsad'+eventStartTime);
            if(slotStartTime <= eventStartTime && eventEndTime <= slotEndTime){
              flag = false;
              break;
            }else{
              flag = true;
            }
          }
        }
      }
      if(flag)return true;
    }
    return false;
  }
  /*********************************************************************
  * <AUTHOR>
  * @date         2022-05-21
  * @description  This method filters the unmodified territory and merging two timeslots which have same start with end time
  * and it returns Map of the days of the week with with the respective Slots
  * @param        recordId (String): the user id
  * @param        setModifiedTerritoriesId (Set<String>): the Set of the IDs of the ModifiedTerritories
  * @param        setModifiedDaysOfWeek (Set<String>):  the Set of the names of the days of the week
  * @return       Map<String, List<Slot>> : Map of the days which has been modified with the respective Slots
  *********************************************************************/
  public static Map<String, List<Slot>> initDataMap(String recordId,Set<String> setModifiedTerritoriesId, Set<String> setModifiedDaysOfWeek){

    //filter out the unmodified territory
    Map<String, List<Slot>> mapSlots = getTimeSlot(recordId);
    System.debug('mapSlots Before Modified: '+ mapSlots);

    for(String key : mapSlots.keySet()){
      for (Integer i = mapSlots.get(key).size() - 1; i >= 0; i--){
        Slot slot =mapSlots.get(key)[i];
        if(!setModifiedDaysOfWeek.contains(slot.dayOfWeek)){
          mapSlots.get(key).remove(i);
        }
      }
      if(mapSlots.get(key).isEmpty()){
        mapSlots.remove(key);
      }
    }
    System.debug('mapSlots Modified: '+ mapSlots);
    //MERGE
    for (String key : mapSlots.keySet()) {
      List<Slot> slots = mapSlots.get(key);
      for (Integer i = 0; i < slots.size() - 1; i++) {
          Slot currentSlot = slots[i];
          if (i + 1 < slots.size()){
            Slot nextSlot = slots[i + 1];
            if (currentSlot.endTime == nextSlot.startTime && currentSlot.agencyId==nextSlot.agencyId){
              Slot timeSlot = new Slot(
                '',
                '',
                currentSlot.dayOfWeek,
                currentSlot.agencyId,
                currentSlot.startTime,
                nextSlot.endTime
              );
              slots.remove(i);
              slots.remove(i);
              slots.add(timeSlot);
              i=i-1;
            }
          }
      }
    }
    for(String key : mapSlots.keySet() ) {
      System.debug('Day of the week : ' + key);
      List<Slot> slots = mapSlots.get(key);
      System.debug('\t\tSlots: ' + slots);
    }
    return mapSlots;
  }
  /*********************************************************************
  * <AUTHOR>
  * @date         2022-05-21
  * @description  This method returns a List of event which are related with the user and
  * @param        userId (String): the user id
  * @param        setModifiedTerritoriesNames (Set<String>): the names of the Territory Names
  * @param        setModifiedDaysOfWeek (Set<String>): the names of the Week Days
  * @return       List<Event>: Events return
  *********************************************************************/
  public static List<Event> getEvents(String userId,Set<String> setModifiedTerritoriesNames, Set<String> setModifiedDaysOfWeek){

    datetime dateNow = datetime.now();

    List<Event> eventList = [SELECT Id, Subject, StartDateTime,EndDateTime, ServiceAppointment.ServiceTerritoryId , ServiceAppointment.ServiceTerritory.Name
    FROM Event WHERE (Event.ServiceAppointment.ServiceTerritory.Name IN :setModifiedTerritoriesNames OR Event.Location IN :setModifiedTerritoriesNames)
    AND StartDateTime > :dateNow
    AND OwnerId =: userId];

    Integer index=0;
    for(Event event : eventList){
      if(!setModifiedDaysOfWeek.contains(event.StartDateTime.format('EEEE'))){
        eventList.remove(index);
      }
      index++;
    }

    return eventList;
  }
  /*********************************************************************
  * <AUTHOR>
  * @date         2022-05-21
  * @description  This method extracts the ids of the Modified Territories from the payload
  * @param        changedItems (String): The payload of the Changed Items
  * @return       Set<String> : the ids of the Modified Territories
  *********************************************************************/
  public static Set<String> getModifiedTerritories(String changedItems){
    List<Object> jsonArray = (List<Object>) JSON.deserializeUntyped(changedItems);

    Set<String> setModifiedTerritoriesId = new Set<String>();
    for (Object obj : jsonArray) {
      // Extract data from JSON map
      Map<String, Object> jsonData = (Map<String, Object>) obj;
      String agencyId = (String) jsonData.get('agencyId');
      setModifiedTerritoriesId.add(agencyId);
    }
    return setModifiedTerritoriesId;
  }
  /*********************************************************************
  * <AUTHOR>
  * @date         2022-05-21
  * @description  This method extracts the names of the Modified Territories from the payload
  * @param        changedItems (String):  The payload of the Changed Items
  * @return       Set<String>  :  the names of the Modified Territories
  *********************************************************************/
  public static Set<String> getModifiedTerritoriesNames(String changedItems){
    List<Object> jsonArray = (List<Object>) JSON.deserializeUntyped(changedItems);

    Set<String> setModifiedTerritoriesNames = new Set<String>();
    for (Object obj : jsonArray) {
      // Extract data from JSON map
      Map<String, Object> jsonData = (Map<String, Object>) obj;
      String agencyName = (String) jsonData.get('agencyName');
      setModifiedTerritoriesNames.add(agencyName);
    }
    return setModifiedTerritoriesNames;
  }
  /*********************************************************************
  * <AUTHOR>
  * @date         2022-05-21
  * @description  This method extracts the days of the week which Time Slots have been modified
  * @param        changedItems (String):  The payload of the Changed Items
  * @return       Set<String> : Set of the days of the week
  *********************************************************************/
  public static Set<String> getModifieddDaysOfWeek(String changedItems){
    List<Object> jsonArray = (List<Object>) JSON.deserializeUntyped(changedItems);

    Set<String> setModifiedDaysOfWeek = new Set<String>();
    for (Object obj : jsonArray) {
      // Extract data from JSON map
      Map<String, Object> jsonData = (Map<String, Object>) obj;
      String dayOfWeek = (String) jsonData.get('dayOfWeek');
      setModifiedDaysOfWeek.add(dayOfWeek);
    }
    return setModifiedDaysOfWeek;
  }
}
