public with sharing class ServiceResourceTriggerHandler {
    public void onAfterInsert(List<ServiceResource> newList){
        String type = String.valueOf(newList[0].getSObjectType());
        SharingStrategy strategy = SharingStrategyFactory.getStrategy(type);
        strategy.collectTargetRecords(newList);
        strategy.applySharing();
    }

    public void onAfterUpdate(List<ServiceResource> newList, List<ServiceResource> oldList, Map<Id,ServiceResource> newMap, Map<Id,ServiceResource> oldMap){
        String type = String.valueOf(newList[0].getSObjectType());
        SharingStrategy strategy = SharingStrategyFactory.getStrategy(type);
        strategy.collectTargetRecords(newList);
        strategy.applySharing();
    }
}