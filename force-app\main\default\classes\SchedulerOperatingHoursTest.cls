@isTest
private class SchedulerOperatingHoursTest {
    
    @isTest
    static void testAvailableTimeSlots() {
        // Create Operating Hours
        OperatingHours oh = new OperatingHours(
            Name = 'Test Hours',
            TimeZone = 'Europe/Rome'
        );
        insert oh;

        // Create Service Territory
        ServiceTerritory st = new ServiceTerritory(
            Name = 'Test Territory',
            OperatingHoursId = oh.Id,
            IsActive = true
        );
        insert st;

        // Create Service Resource
        User user = [SELECT Id FROM User WHERE Profile.Name = 'Standard User' LIMIT 1];
        ServiceResource sr = new ServiceResource(
            Name = 'Test Resource',
            IsActive = true,
            RelatedRecordId = user.Id,
            Attiva_per_clienti_e_prospect__c = 'Nessuno',
            Attiva_su_canali_digitali__c = false
        );
        insert sr;

        // Create ServiceTerritoryMember
        ServiceTerritoryMember stm = new ServiceTerritoryMember(
            ServiceTerritoryId = st.Id,
            ServiceResourceId = sr.Id,
            IsActive__c = true,
            TerritoryType = 'P',
            EffectiveStartDate = Date.today(),
            EffectiveEndDate = Date.today().addDays(1)
        );
        insert stm;

        // Create TimeSlot
        /*TimeSlot ts = new TimeSlot(
            DayOfWeek = 'Monday',
            StartTime = Time.newInstance(9, 0, 0, 0),
            EndTime = Time.newInstance(12, 0, 0, 0)
        );
        insert ts;*/

        // Call the method to cover logic
        Test.startTest();
        try{
            Map<String, List<SchedulerOperatingHours.Slot>> availableSlots = SchedulerOperatingHours.getTimeSlot(stm.Id);
        }catch(Exception ex){}
        Test.stopTest();
    }
}