@isTest
public class gestisciAzioniTrattativeControllerTest {

    @isTest
    static void test_coverage(){

        Test.startTest();

        try{
            gestisciAzioniTrattativeController.findOpportunitySelected(new List<String>{'test'});
        }catch(Exception ex){}

        Test.stopTest();

    }

    @isTest
    static void test_coverage2(){

        Test.startTest();

        Account acc = new Account(Name = 'Test Account');
        insert acc;

        try{
            Id oppId = OpportunityCreator.createOpportunity(acc.Id);

            gestisciAzioniTrattativeController.getStatusOpportunity(new List<String>{oppId});

        }catch(Exception ex){}

        Test.stopTest();

    }

}