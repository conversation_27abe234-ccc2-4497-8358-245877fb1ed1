/**
 * @File Name         : CollaborativoController.cls
 * @Description       : 
 * <AUTHOR> <EMAIL>
 * @Group             : 
 * @Last Modified On  : 04-04-2025
 * @Last Modified By  : <EMAIL>
@cicd_tests CollaborativoController

**/

global without sharing class CollaborativoController implements System.Callable {
    public Object call(String action, Map<String, Object> args) {
        System.debug('***Start Call Method : ' + action);
        Map<String, Object> input = (Map<String, Object>) args.get('input');
        Map<String, Object> output = (Map<String, Object>) args.get('output');
        Map<String, Object> options = (Map<String, Object>) args.get('options');

        Object result = invokeMethod(action, input, output, options);
        System.debug('///Result: ' + result);

        return result;
    }

    global Boolean invokeMethod(
        String methodName,
        Map<String, Object> inputMap,
        Map<String, Object> outMap,
        Map<String, Object> options
    ) {
        Boolean result = true;
        try {
            if (methodName == 'loadData') {
                loadData('loadData', inputMap, outMap, options);
            } else if (methodName == 'preAddGroup') {
                preAddGroup('preAddGroup', inputMap, outMap, options);
            } else if (methodName == 'insertNewgroup') {
                insertNewgroup('insertNewgroup', inputMap, outMap, options);
            } else if (methodName == 'deleteGroup') {
                deleteGroup('deleteGroup', inputMap, outMap, options);
            } else if (methodName == 'modifyGroup') {
                modifyGroup('modifyGroup', inputMap, outMap, options);
            }
        } catch (Exception e) {
            System.debug('Error: ' + e.getMessage() + 'line: ' + e.getLineNumber());
            outMap.put('success', false);
            outMap.put('errorType', 0);
            outMap.put('message', 'Errore generico');
            result = false;
        }
        return result;
    }

    public static void loadData(
        String methodName,
        Map<String, Object> inputMap,
        Map<String, Object> outMap,
        Map<String, Object> options
    ) {
        System.debug('Start loadData');
        try {
            System.debug('inputMap: ' + inputMap);
            String userId = (String) inputMap.get('UserId');
            List<GroupInfo> listGroupInfo = new List<GroupInfo>();

            User us = [SELECT Id, Name, IdAzienda__c FROM User WHERE Id = :userId LIMIT 1];
            String idAzienda = us.IdAzienda__c;
            System.debug('idAzienda: ' + idAzienda);
            Id RTGRPCollaborativo = Schema.SObjectType.Group__c.getRecordTypeInfosByDeveloperName().get('UserGroup').getRecordTypeId();

            List<GroupMember> listGroupMemberWithUser = [
                SELECT id, UserOrGroup.name, Group.name, GroupId
                FROM GroupMember
                WHERE UserOrGroupId = :userId
            ];
            System.debug('listGroupMemberWithUser: ' + listGroupMemberWithUser);
            List<String> listGroupId = new List<String>();

            for (GroupMember gm : listGroupMemberWithUser) {
                listGroupId.add(gm.GroupId);
            }

            System.debug('listGroupId: ' + listGroupId + 'RTGRPCollaborativo : ' + RTGRPCollaborativo);
            List<Group__Share> listGroupShare = [
                SELECT Id, UserOrGroup.name, UserOrGroupId, AccessLevel, ParentId, Parent.Name, Parent.RecordType.DeveloperName, LastModifiedDate
                FROM Group__Share
                WHERE UserOrGroupId IN :listGroupId AND Parent.Agenzia__c = :idAzienda AND Parent.RecordTypeId = :RTGRPCollaborativo
            ];
            System.debug('listGroupShare: ' + listGroupShare);

            Set<String> idGroupToShare = new Set<String>();
            for (Group__Share gs : listGroupShare) {
                idGroupToShare.add(gs.ParentId);
            }

            List<Group__c> listCollaborativi = [
                SELECT Id, Name, RecordTypeId, Agenzia__c, PublicGroupId__c, Shares__c, TypeCip__c, Type__c, CIP__c, Component__c, Description__c, Society__c, isActive__c, ExternalId__c, CreatedDate 
                FROM Group__c WHERE Id IN :idGroupToShare
            ];
            System.debug('listCollaborativi: ' + listCollaborativi);

            System.debug(
                'Schema.sObjectType.GroupMember.isAccessible() :' + Schema.sObjectType.GroupMember.isAccessible()
            );
            if (Schema.sObjectType.GroupMember.isAccessible()) {
                List<AggregateResult> listGroupMember = [SELECT GroupId, Group.Name, COUNT(Id) MemberCount FROM GroupMember GROUP BY GroupId, Group.Name];
                System.debug('listGroupMember: ' + listGroupMember);
                for (Group__c gp : listCollaborativi) {
                    System.debug('ID gruppo : ' + gp.Id);
                    GroupInfo groupInfo = new GroupInfo(gp.Id, gp.Name, gp.Description__c, 0, gp.CreatedDate.date(), gp.PublicGroupId__c);
                    for (AggregateResult ar : listGroupMember) {
                        System.debug('(String)ar.get(GroupId) : ' + (String) ar.get('GroupId'));
                        if ((String) ar.get('GroupId') == gp.PublicGroupId__c) {
                            groupInfo.NumeroMembri = (Integer) ar.get('MemberCount');
                        }
                    }
                    listGroupInfo.add(groupInfo);
                }
            } else {
                System.debug('Insufficient permissions to access GroupMember object.');
                outMap.put('success', false);
                outMap.put('message', 'Insufficient permissions to access GroupMember object.');
            }

            outMap.put('success', true);
            outMap.put('response', JSON.serialize(listGroupInfo));
        } catch (Exception e) {
            System.debug('Error: ' + e.getMessage() + 'line: ' + e.getLineNumber());
            outMap.put('success', false);
            outMap.put('message', 'Errore generico');
        }
    }
    @AuraEnabled(cacheable=true)
    public static void preAddGroup(
        String methodName,
        Map<String, Object> inputMap,
        Map<String, Object> outMap,
        Map<String, Object> options
    ) {
        System.debug('Start preAddGroup');
        try {
            System.debug('inputMap: ' + inputMap);
            String userId = (String) inputMap.get('UserId');
            List<GroupInfo> listGroupInfo = new List<GroupInfo>();

            User us = [SELECT Id, Name, IdAzienda__c FROM User WHERE Id = :userId LIMIT 1];
            List<User> listUser = [
                SELECT Id, Name, IdAzienda__c
                FROM User
                WHERE IdAzienda__c = :us.IdAzienda__c AND IsActive = TRUE
            ];

            List<UserAdded> listUserAdded = new List<UserAdded>();
            for (User u : listUser) {
                UserAdded userAdded = new UserAdded(u.Id, u.Name, false);
                listUserAdded.add(userAdded);
            }

            outMap.put('success', true);
            outMap.put('response', JSON.serialize(listUserAdded));
        } catch (Exception e) {
            System.debug('Error: ' + e.getMessage() + 'line: ' + e.getLineNumber());
            outMap.put('success', false);
            outMap.put('message', 'Errore generico');
        }
    }

    @AuraEnabled(cacheable=true)
    public static void insertNewgroup(
        String methodName,
        Map<String, Object> inputMap,
        Map<String, Object> outMap,
        Map<String, Object> options
    ) {
        System.debug('Start insertNewgroup');
        System.debug('inputMap: ' + inputMap);
        System.debug('listUser : ' + inputMap.get('ListUser'));
        Savepoint sp = Database.setSavepoint();
        try {
            String userId = (String) inputMap.get('UserId');
            String groupName = (String) inputMap.get('GroupName') != null ? (String) inputMap.get('GroupName') : '';
            String descrizione = (String) inputMap.get('Descrizione') != null
                ? (String) inputMap.get('Descrizione') : '';
            String listUserSerialized = JSON.serialize(inputMap.get('ListUser')).startsWith('[')
                ? JSON.serialize(inputMap.get('ListUser')) : '[' + JSON.serialize(inputMap.get('ListUser')) + ']';

            Boolean selectAll = false;
            if (inputMap.containsKey('SelectAll')) {
                Object selectAllValue = inputMap.get('SelectAll');
                if (selectAllValue instanceof Boolean) {
                    selectAll = (Boolean)selectAllValue;
                } else if (selectAllValue instanceof String) {
                    selectAll = 'true'.equalsIgnoreCase((String)selectAllValue);
                }
            }
            System.debug('selectAll: ' + selectAll);
            List<UserWrapper> listUser = inputMap.get('ListUser') != null
                ? (List<UserWrapper>) JSON.deserialize(listUserSerialized, List<UserWrapper>.class) : null;
            System.debug('groupName: ' + groupName);
            System.debug('descrizione: ' + descrizione);
            System.debug('listUser: ' + listUser);

            Id RTGRPCollaborativo = Schema.SObjectType.Group__c.getRecordTypeInfosByDeveloperName()
                .get('UserGroup')
                .getRecordTypeId();
            List<Group__c> listGroupsToCheck = [
                SELECT id, Name, RecordType.DeveloperName
                FROM Group__c
                WHERE RecordTypeId = :RTGRPCollaborativo AND Name = :groupName
            ];
            System.debug('listGroupsToCheck: ' + listGroupsToCheck);
            if (!listGroupsToCheck.isEmpty()) {
                System.debug('Collaborativo già esistente');
                outMap.put('success', false);
                outMap.put('message', 'Collaborativo già esistente');
                return;
            }

            User us = [SELECT Id, Name, IdAzienda__c FROM User WHERE Id = :userId LIMIT 1];
            System.debug('us: ' + us);
            Account acc = [SELECT Id, Name, ExternalId__c FROM Account WHERE Id = :us.IdAzienda__c LIMIT 1];
            System.debug('acc: ' + acc);
            if (selectAll) {
                System.debug('Select All');
                Group gp = new Group();
                gp.Name = 'COLL_' + acc.ExternalId__c;
                gp.Description = descrizione;
                insert gp;

                List<User> listUserToAdd = [
                    SELECT Id, Name, IdAzienda__c
                    FROM User
                    WHERE IdAzienda__c = :us.IdAzienda__c AND IsActive = TRUE
                ];
                for (User u : listUserToAdd) {
                    GroupMember gm = new GroupMember();
                    gm.GroupId = gp.Id;
                    gm.UserOrGroupId = u.Id;
                    insert gm;
                }

                // Chiamata asincrona per creare il record Group__c
                createCustomGroupAsync(groupName, descrizione, acc.ExternalId__c, acc.Id, gp.Id);
                outMap.put('success', true);
                outMap.put('message', 'Gruppo creato con successo');
            } else if (listUser != null && !listUser.isEmpty()) {
                System.debug('List User');
                Group gp = new Group();
                gp.Name = 'COLL_' + acc.ExternalId__c;
                gp.Description = descrizione;
                insert gp;

                for (UserWrapper u : listUser) {
                    GroupMember gm = new GroupMember();
                    gm.GroupId = gp.Id;
                    gm.UserOrGroupId = u.Id;
                    insert gm;
                }

                // Chiamata asincrona per creare il record Group__c
                createCustomGroupAsync(groupName, descrizione, acc.ExternalId__c, acc.Id, gp.Id);
                outMap.put('success', true);
                outMap.put('message', 'Gruppo creato con successo');
            } else {
                outMap.put('success', false);
                outMap.put('message', 'Selezionare almeno un utente');
                return;
            }
        } catch (Exception e) {
            Database.rollback(sp);
            System.debug('Error: ' + e.getMessage() + 'line: ' + e.getLineNumber());
            outMap.put('success', false);
            outMap.put('message', 'Errore generico');
        }
    }

    public static void modifyGroup(
        String methodName,
        Map<String, Object> inputMap,
        Map<String, Object> outMap,
        Map<String, Object> options
    ) {
        System.debug('Start modifyGroup');
        System.debug('inputMap: ' + inputMap);
        try {
            String groupId = (String) inputMap.get('GroupId');
            String groupName = (String) inputMap.get('GroupName') != null ? (String) inputMap.get('GroupName') : '';
            String descrizione = (String) inputMap.get('Description') != null
                ? (String) inputMap.get('Description')
                : '';
            List<String> listUser = (List<String>) JSON.deserialize(
                JSON.serialize(inputMap.get('ListUser')),
                List<String>.class
            );

            List<User> userList = [SELECT Id, Name FROM User WHERE Id IN :listUser];
            System.debug('groupId: ' + groupId);
            System.debug('groupName: ' + groupName);
            System.debug('descrizione: ' + descrizione);
            System.debug('listUser: ' + listUser);

            // Aggiorna il record Group__c
            Group__c gp = [SELECT Id, Name, Description__c, PublicGroupId__c FROM Group__c WHERE Id = :groupId LIMIT 1];
            gp.Name = groupName;
            gp.Description__c = descrizione;
            update gp;

            // Esegui l'operazione sui GroupMember in modo asincrono
            updateGroupMembersAsync(gp.PublicGroupId__c, listUser);

            outMap.put('success', true);
            outMap.put('message', 'Gruppo modificato con successo');
        } catch (Exception e) {
            System.debug('Error: ' + e.getMessage() + 'line: ' + e.getLineNumber());
            outMap.put('success', false);
            outMap.put('message', 'Errore generico');
        }
    }

    @AuraEnabled(cacheable=true)
    public static List<UserAdded> getListUserAdded(String groupId, String userId) {
        System.debug('Start getListUserAdded');
        System.debug('groupId: ' + groupId);
        System.debug('userId: ' + userId);
        List<UserAdded> listUserAdded = new List<UserAdded>();
        try {
            User us = [SELECT Id, Name, IdAzienda__c FROM User WHERE Id = :userId LIMIT 1];
            List<User> listUser = [
                SELECT Id, Name, IdAzienda__c
                FROM User
                WHERE IdAzienda__c = :us.IdAzienda__c AND IsActive = TRUE
            ];
            List<Group__c> listCollaborativi = [
                SELECT Id, Name, RecordTypeId, Agenzia__c, PublicGroupId__c, Shares__c, TypeCip__c, Type__c, CIP__c, Component__c, Description__c, Society__c, isActive__c, ExternalId__c, CreatedDate
                FROM Group__c
                WHERE Id = :groupId
            ];
            System.debug('listUser: ' + listUser);
            List<GroupMember> listGroupMember = [
                SELECT Id, UserOrGroupId, UserOrGroup.Name
                FROM GroupMember
                WHERE GroupId = :listCollaborativi[0].PublicGroupId__c
            ];
            System.debug('listGroupMember: ' + listGroupMember);

            for (User usr : listUser) {
                UserAdded userAdded = new UserAdded(usr.Id, usr.Name, false);
                for (GroupMember gm : listGroupMember) {
                    if (gm.UserOrGroupId == usr.Id) {
                        userAdded.isSelected = true;
                    }
                }
                listUserAdded.add(userAdded);
            }
        } catch (Exception e) {
            System.debug('Error: ' + e.getMessage() + 'line: ' + e.getLineNumber());
        }
        System.debug('listUserAdded: ' + listUserAdded);
        return listUserAdded;
    }

    public static void deleteGroup(
        String methodName,
        Map<String, Object> inputMap,
        Map<String, Object> outMap,
        Map<String, Object> options
    ) {
        System.debug('Start deleteGroup');
        System.debug('inputMap: ' + inputMap);
        Savepoint sp = Database.setSavepoint();

        try {
            String groupId = (String) inputMap.get('GroupId');
            System.debug('groupId: ' + groupId);
            String publicGroupId = (String) inputMap.get('PublicGroupId');
            System.debug('publicGroupId: ' + publicGroupId);
            List<OpportunityShare> listOpportunityShare = [
                SELECT Id
                FROM OpportunityShare
                WHERE UserOrGroupId = :publicGroupId
            ];
            List<CaseShare> listCaseShare = [SELECT Id FROM CaseShare WHERE UserOrGroupId = :publicGroupId];

            System.debug('listOpportunityShare size: ' + listOpportunityShare.size());
            System.debug('listCaseShare size: ' + listCaseShare.size());

            List<Group> listPublicGroup = [SELECT Id FROM Group WHERE Id = :publicGroupId];

            if (!(listOpportunityShare.isEmpty() && listCaseShare.isEmpty())) {
                outMap.put('success', false);
                outMap.put('message', 'Il gruppo non può essere eliminato perchè sono presenti condivisioni');
                outMap.put('groupId', groupId);
                return;
            } else {
                System.debug('Blocco delete');
                // Elimina il record di Group
                delete listPublicGroup;
                // Elimina il record di Group__c in modo asincrono
                deleteCustomGroupAsync(groupId);
                outMap.put('success', true);
                outMap.put('message', 'Gruppo eliminato con successo');
            }
        } catch (Exception e) {
            Database.rollback(sp);
            System.debug('Error: ' + e.getMessage() + 'line: ' + e.getLineNumber());
            outMap.put('success', false);
            outMap.put('message', 'Errore generico');
        }
    }

    @future
    public static void createCustomGroupAsync(
        String groupName,
        String descrizione,
        String externalId,
        Id accountId,
        Id publicGroupId
    ) {
        System.debug('Start createCustomGroupAsync');
        try {
            Id RTGRPCollaborativo = Schema.SObjectType.Group__c.getRecordTypeInfosByDeveloperName()
                .get('UserGroup')
                .getRecordTypeId();
            Integer rand = Math.round(Math.random() * 1000);
            Group__c collaborativo = new Group__c();
            collaborativo.Name = groupName;
            collaborativo.RecordTypeId = RTGRPCollaborativo;
            collaborativo.ExternalId__c = externalId + String.valueOf(rand);
            collaborativo.Agenzia__c = accountId;
            collaborativo.PublicGroupId__c = publicGroupId;
            collaborativo.Description__c = descrizione;

            insert collaborativo;

            String groupResp = 'R_' + externalId;
            String groupAge = externalId;
            List<String> listGroup = new List<String>();
            listGroup.add(groupResp);
            listGroup.add(groupAge);
            List<Group> listGroupToShare = [
                SELECT Id, Name, DeveloperName
                FROM Group
                WHERE DeveloperName IN :listGroup
            ];
            System.debug('listGroupToShare: ' + listGroupToShare);
            List<Group__Share> listGroupShare = new List<Group__Share>();
            for (Group g : listGroupToShare) {
                if (groupResp.equalsIgnoreCase(g.DeveloperName)) {
                    Group__Share groupShare = new Group__Share();
                    groupShare.ParentId = collaborativo.Id;
                    groupShare.UserOrGroupId = g.Id;
                    groupShare.AccessLevel = 'Edit';
                    listGroupShare.add(groupShare);
                } else if (groupAge.equalsIgnoreCase(g.DeveloperName)) {
                    Group__Share groupShare = new Group__Share();
                    groupShare.ParentId = collaborativo.Id;
                    groupShare.UserOrGroupId = g.Id;
                    groupShare.AccessLevel = 'Read';
                    listGroupShare.add(groupShare);
                }
            }
            System.debug('listGroupShare : ' + listGroupShare);
            insert listGroupShare;
        } catch (Exception e) {
            System.debug('Error in createCustomGroupAsync: ' + e.getMessage());
        }
    }

    @future
    public static void updateGroupMembersAsync(String publicGroupId, List<String> listUser) {
        try {
            // Elimina i GroupMember esistenti
            List<GroupMember> listGroupMember = [SELECT Id FROM GroupMember WHERE GroupId = :publicGroupId];
            if (!listGroupMember.isEmpty()) {
                delete listGroupMember;
            }

            if (!listUser.isEmpty()) {
                // Inserisci i nuovi GroupMember
                List<GroupMember> newGroupMembers = new List<GroupMember>();
                for (String userId : listUser) {
                    GroupMember gm = new GroupMember();
                    gm.GroupId = publicGroupId;
                    gm.UserOrGroupId = userId;
                    newGroupMembers.add(gm);
                }
                insert newGroupMembers;
            }
        } catch (Exception e) {
            System.debug('Error in updateGroupMembersAsync: ' + e.getMessage());
        }
    }

    @future
    public static void deleteCustomGroupAsync(String groupId) {
        try {
            List<Group__c> listGroup = [SELECT Id FROM Group__c WHERE Id = :groupId];
            delete listGroup;
        } catch (Exception e) {
            System.debug('Error in deleteCustomGroupAsync: ' + e.getMessage());
        }
    }

    // Classe innestata
    public class GroupInfo {
        public String Id { get; set; }
        public String NomeGruppo { get; set; }
        public String Descrizione { get; set; }
        public Integer NumeroMembri { get; set; }
        public Date DataCreazione { get; set; }
        public String PublicGroupId { get; set; }

        // Costruttore
        public GroupInfo(
            String id,
            String nomeGruppo,
            String descrizione,
            Integer numeroMembri,
            Date dataCreazione,
            String publicGroupId
        ) {
            this.Id = id;
            this.NomeGruppo = nomeGruppo;
            this.Descrizione = descrizione;
            this.NumeroMembri = numeroMembri;
            this.DataCreazione = dataCreazione;
            this.PublicGroupId = publicGroupId;
        }

        // Costruttore senza parametri
        public GroupInfo() {
        }
    }

    public class UserAdded {
        @AuraEnabled
        public String Id { get; set; }
        @AuraEnabled
        public String Nome { get; set; }
        @AuraEnabled
        public Boolean isSelected { get; set; }
        // Costruttore
        public UserAdded(String id, String nome, Boolean isSelected) {
            this.Id = id;
            this.Nome = nome;
            this.isSelected = isSelected;
        }

        // Costruttore senza parametri
        public UserAdded() {
        }
    }

    public class UserWrapper {
        public String Id { get; set; }
        public String Name { get; set; }

        // Costruttore
        public UserWrapper(String id, String name) {
            this.Id = id;
            this.Name = name;
        }

        // Costruttore senza parametri
        public UserWrapper() {
        }
    }
}