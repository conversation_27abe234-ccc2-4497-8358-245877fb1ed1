public without sharing class ServiceTerritorySharingStrategy implements SharingStrategy {
    private List<ServiceTerritory> territories = new List<ServiceTerritory>();
    private List<ServiceTerritoryShare> shares = new List<ServiceTerritoryShare>();

    public void collectTargetRecords(List<SObject> sobjects) {
        for (SObject s : sobjects) {
            if (s instanceof ServiceTerritory) territories.add((ServiceTerritory)s);
        }
    }

    public void applySharing() {
        Set<String> agencyIds = new Set<String>();
        for (ServiceTerritory st : territories) {
            if (st.Agency__c != null){
                agencyIds.add(st.Agency__c);
            }
        }

        Map<Id, String> agencyIdToExtId = new Map<Id, String>();
        for (Account a : [SELECT Id, ExternalId__c FROM Account WHERE Id IN :agencyIds]) {
            agencyIdToExtId.put(a.Id, a.ExternalId__c);
        }

        Map<String, Group> groupMap = GroupHelper.getGroupsByExternalIds(new Set<String>(agencyIdToExtId.values()));

        for (ServiceTerritory st : territories) {
            String extId = agencyIdToExtId.get(st.Agency__c);
            Group publicGroup = groupMap.get(extId);
            if (publicGroup == null) continue;

            shares.add(new ServiceTerritoryShare(
                ParentId = st.Id,
                UserOrGroupId = publicGroup.Id,
                AccessLevel = 'Edit',
                RowCause = Schema.ServiceTerritoryShare.RowCause.Manual
            ));
        }

        if (!shares.isEmpty()) insert shares;
    }
}