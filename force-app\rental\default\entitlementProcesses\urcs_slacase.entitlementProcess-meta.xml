tadat<?xml version="1.0" encoding="UTF-8"?>
<EntitlementProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <SObjectType>Case</SObjectType>
    <active>true</active>
    <businessHours>Unipol Rental CS - Orari di lavoro</businessHours>
    <entryStartDateField>Case.CreatedDate</entryStartDateField>
    <exitCriteriaFilterItems>
        <field>Case.IsClosed</field>
        <operation>equals</operation>
        <value>true</value>
    </exitCriteriaFilterItems>
    <milestones>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Richiesta autorizzazione rent</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>notEqual</operation>
            <value>Supporto Area Riservata</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>notEqual</operation>
            <value>Reclamo Chiusura Contratto</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>notEqual</operation>
            <value>Reclamo Consegna Veicolo Nuovo</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>notEqual</operation>
            <value>Reclamo Consegna Veicolo Preassegnazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>notEqual</operation>
            <value>Reclamo Consegna Veicolo Sostitutiva</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>notEqual</operation>
            <value>Reclamo Manutenzione Veicolo</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>notEqual</operation>
            <value>Reclamo Pneumatici</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>notEqual</operation>
            <value>Reclamo Sinistro - carrozzeria</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>notEqual</operation>
            <value>Reclamo Soccorso Stradale</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Status</field>
            <operation>notEqual</operation>
            <value>Chiuso - Risolto</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Status</field>
            <operation>notEqual</operation>
            <value>Chiuso - Annullato</value>
        </milestoneCriteriaFilterItems>
        <milestoneName>Goal</milestoneName>
        <minutesCustomClass>urcs_CalculateTimeCaseSLA</minutesCustomClass>
        <timeTriggers>
            <actions>
                <name>violazioneGoal</name>
                <type>FieldUpdate</type>
            </actions>
            <timeLength>0</timeLength>
            <workflowTimeTriggerUnit>Minutes</workflowTimeTriggerUnit>
        </timeTriggers>
        <useCriteriaStartTime>true</useCriteriaStartTime>
    </milestones>
    <milestones>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>notEqual</operation>
            <value>Supporto Area Riservata</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>notEqual</operation>
            <value>Reclamo Chiusura Contratto</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>notEqual</operation>
            <value>Reclamo Consegna Veicolo Nuovo</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>notEqual</operation>
            <value>Reclamo Consegna Veicolo Preassegnazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>notEqual</operation>
            <value>Reclamo Consegna Veicolo Sostitutiva</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>notEqual</operation>
            <value>Reclamo Manutenzione Veicolo</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>notEqual</operation>
            <value>Reclamo Pneumatici</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>notEqual</operation>
            <value>Reclamo Sinistro - carrozzeria</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>notEqual</operation>
            <value>Reclamo Soccorso Stradale</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Status</field>
            <operation>notEqual</operation>
            <value>Chiuso - Risolto</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Status</field>
            <operation>notEqual</operation>
            <value>Chiuso - Annullato</value>
        </milestoneCriteriaFilterItems>
        <milestoneName>Deadline</milestoneName>
        <minutesCustomClass>urcs_CalculateTimeCaseSLA</minutesCustomClass>
        <timeTriggers>
            <actions>
                <name>violazioneDeadline</name>
                <type>FieldUpdate</type>
            </actions>
            <timeLength>0</timeLength>
            <workflowTimeTriggerUnit>Minutes</workflowTimeTriggerUnit>
        </timeTriggers>
        <useCriteriaStartTime>true</useCriteriaStartTime>
    </milestones>
</EntitlementProcess>
