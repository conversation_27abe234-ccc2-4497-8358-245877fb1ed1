@isTest
private class BenchMarkUpdateSubscribeTest {
    @isTest
    static void testExecuteBatch() {
        // Simula l'invocazione del metodo con un nome batch
        List<String> batchNames = new List<String>{'TestBatchName'};

        Test.startTest();
        BenchMarkUpdateSubscribe.ExecuteBatch(batchNames);
        Test.stopTest();

        // Non possiamo verificare direttamente la pubblicazione dell'evento,
        // ma possiamo almeno assicurarci che il metodo venga eseguito senza errori.
        System.assert(true, 'Metodo ExecuteBatch eseguito correttamente');
    }
}