@isTest
private class EmptyBatchClassTest {

    @testSetup
    static void setupData() {
        // Crea almeno un record per garantire che il batch abbia un risultato
        insert new Account(Name = 'Test Account');
    }

    @isTest
    static void testBatchExecution() {
        Test.startTest();

        // Istanzia e avvia il batch
        EmptyBatchClass batch = new EmptyBatchClass();
        Database.executeBatch(batch, 1); // Esegui con scope di 1 per testare più facilmente
        Test.stopTest();
    }
}