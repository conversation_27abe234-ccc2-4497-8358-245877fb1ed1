public with sharing class OpportunityController {
    private Static String DEFAULT_QUERY = 'SELECT Id, AccountRecordTypeFormula__c, AccountNameFormula__c, JourneyStep__c, HasCallMeBackFormula__c, AreasOfNeedFormula__c, TakenInChargeSLARemainingHours__c, ' +
    'Amount, Name, AccountValusFormula__c, CreatedDate, WorkingSLAExpiryDate__c, TemperatureFormula__c, AreaOfNeed__c, Rating__c, TakenInChargeSLARemainingDays__c ' + 
    'FROM Opportunity WHERE StageName = \'Assegnato\' ';
    private Static String ORDER_BY_SUBQUERY = ' ORDER BY TemperatureFormula__c, TakenInChargeSLARemainingHours__c';
    // private Static String DEFAULT_QUERY = 'SELECT Id, AccountRecordTypeFormula__c, AccountNameFormula__c, JourneyStep__c, HasCallMeBackFormula__c, AreasOfNeedFormula__c, TakenInChargeSLARemainingHours__c, ' +
    // 'Amount, Name, AccountValusFormula__c, CreatedDate, WorkingSLAExpiryDate__c, TemperatureFormula__c, AreaOfNeed__c, Rating__c, TakenInChargeSLARemainingDays__c ' + 
    // 'FROM Opportunity WHERE StageName = \'Assegnato\' AND OwnerId = \'' + UserInfo.getUserId() + '\'';

    public class URLParameters {
        @AuraEnabled
        public Decimal amountFrom {get;set;}
        @AuraEnabled
        public Decimal amountTo {get;set;}
        @AuraEnabled
        public String clientType {get;set;}
        @AuraEnabled
        public String areasOfNeed {get;set;}
        @AuraEnabled
        public String temperature {get;set;}
        @AuraEnabled
        public String dateFrom {get;set;}
        @AuraEnabled
        public String dateTo {get;set;}
        @AuraEnabled
        public Integer takenInChargeSLARemainingHours {get;set;}
        @AuraEnabled
        public String opportunityName {get;set;}
        @AuraEnabled
        public String accountName {get;set;}
        @AuraEnabled
        public String journeyStep {get;set;}
        @AuraEnabled
        public String owner {get;set;}
        @AuraEnabled
        public Boolean callMeBack {get;set;}
        @AuraEnabled
        public Integer valus {get;set;}
        @AuraEnabled
        public String workingSLAExpiryDateFrom {get;set;}
        @AuraEnabled
        public String workingSLAExpiryDateTo {get;set;}
    }

    public class ReloadOpportunitiesWrapper {
        @AuraEnabled
        public URLParameters parameters {get;set;}
        @AuraEnabled
        public List<Opportunity> opportunities {get;set;}

        public ReloadOpportunitiesWrapper(URLParameters parameters, List<Opportunity> opportunities) {
            this.parameters = parameters;
            this.opportunities = opportunities;
        }
        
    }
    
    @AuraEnabled(cacheable=false)
    public static List<Opportunity> getReportOpportunities(URLParameters params, Boolean resetButtonClicked) {
        try {
            String query = createQuery(params, resetButtonClicked);

            return Database.query(query);
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    public static String createQuery(URLParameters params, Boolean resetButtonClicked) {
        System.debug('# Parameters: ' + params);
        String query = DEFAULT_QUERY;
        
        if(params.amountFrom != null) {
            query += ' AND Amount >= ' + params.amountFrom;
        }
        if(params.amountTo != null) {
            query += ' AND Amount <= ' + params.amountTo;
        }
        if(String.isNotBlank(params.clientType)) {
            query += ' AND AccountRecordTypeFormula__c = \'' + params.clientType + '\'';
        }
        if(String.isNotBlank(params.dateFrom)) {
            List<String> dateParts = params.dateFrom.split('-');
            Datetime dateFrom = Datetime.newinstanceGMT(Integer.valueOf(dateParts[0]), Integer.valueOf(dateParts[1]), Integer.valueOf(dateParts[2]));
            query += ' AND CreatedDate >= ' + dateFrom.formatGMT('yyyy-MM-dd\'T\'HH:mm:ss.SSS\'Z\'');
        }
        if(String.isNotBlank(params.dateTo)) {
            List<String> dateParts = params.dateTo.split('-');
            Datetime dateTo = Datetime.newinstanceGMT(Integer.valueOf(dateParts[0]), Integer.valueOf(dateParts[1]), Integer.valueOf(dateParts[2]), 23, 59, 59);
            query += ' AND CreatedDate <= ' + dateTo.formatGMT('yyyy-MM-dd\'T\'HH:mm:ss.SSS\'Z\'');
        }
        if(String.isNotBlank(params.areasOfNeed)) {
            List<String> areasOfNeed = params.areasOfNeed.split(';');
            String areasOfNeedString = '';
            for(String s : areasOfNeed) {
                areasOfNeedString += '\'' + s + '\',';
            }
            areasOfNeedString = '(' + areasOfNeedString.removeEnd(',') + ')';
            query += ' AND AreaOfNeed__c INCLUDES ' + areasOfNeedString;
        }
        if(String.isNotBlank(params.temperature)) {
            List<String> temperatures = params.temperature.split(';');
            String temperatureString = '';
            for(String s : temperatures) {
                temperatureString += '\'' + s + '\',';
            }
            temperatureString = '(' + temperatureString.removeEnd(',') + ')';
            query += ' AND Rating__c IN ' + temperatureString;
        }
        if(params.takenInChargeSLARemainingHours != null) {
            query += ' AND TakenInChargeSLARemainingHours__c = ' + params.takenInChargeSLARemainingHours;
        }
        if(String.isNotBlank(params.opportunityName)) {
            query += ' AND Name = \'' + params.opportunityName + '\'';
        }
        if(String.isNotBlank(params.accountName)) {
            query += ' AND Account.Name = \'' + params.accountName + '\'';
        }
        if(String.isNotBlank(params.journeyStep)) {
            query += ' AND JourneyStep__c = \'' + params.journeyStep + '\'';
        }
        if(String.isNotBlank(params.owner)) {
            query += ' AND Owner.Name = \'' + params.owner + '\'';
        }
        if(params.callMeBack != null) {
            query += ' AND HasCallMeBack__c = ' + params.callMeBack;
        }
        if(params.valus != null) {
            query += ' AND AccountValusFormula__c = \'' + String.valueOf(params.valus) + '\'';
        }
        if(String.isNotBlank(params.workingSLAExpiryDateFrom)) {
            List<String> dateParts = params.workingSLAExpiryDateFrom.split('-');
            Datetime workingSLAExpiryDateFrom = Datetime.newinstanceGMT(Integer.valueOf(dateParts[0]), Integer.valueOf(dateParts[1]), Integer.valueOf(dateParts[2]));
            query += ' AND WorkingSLAExpiryDate__c >= ' + workingSLAExpiryDateFrom.formatGMT('yyyy-MM-dd\'T\'HH:mm:ss.SSS\'Z\'');
        }
        if(String.isNotBlank(params.workingSLAExpiryDateTo)) {
            List<String> dateParts = params.workingSLAExpiryDateTo.split('-');
            Datetime workingSLAExpiryDateTo = Datetime.newinstanceGMT(Integer.valueOf(dateParts[0]), Integer.valueOf(dateParts[1]), Integer.valueOf(dateParts[2]), 23, 59, 59);
            query += ' AND WorkingSLAExpiryDate__c <= ' + workingSLAExpiryDateTo.formatGMT('yyyy-MM-dd\'T\'HH:mm:ss.SSS\'Z\'');
        }
        query += ORDER_BY_SUBQUERY;
        System.debug('# query: ' + query);
        if(!resetButtonClicked) {
            //saveQuery(query, params);
        }

        return query;
    }

    // private static void saveQuery(String query, URLParameters params) {
    //     List<ReportQuery__c> latestQuery = getLatestQuery();

    //     // If a query for the specific report does not exists from the user insert it
    //     // otherwise update his latest query
    //     if(latestQuery.isEmpty()) {
    //         ReportQuery__c newQuery = new ReportQuery__c(
    //             Query__c = query,
    //             QueryParameters__c = JSON.serialize(params),
    //             OwnerId = UserInfo.getUserId(),
    //             ReportDeveloperName__c = 'Trattative_Attivit_VLw'
    //         );
    //         insert newQuery;
    //     }
    //     else {
    //         ReportQuery__c queryToUpdate = latestQuery[0];
    //         queryToUpdate.Query__c = query;
    //         queryToUpdate.QueryParameters__c = JSON.serialize(params);
    //         update queryToUpdate;
    //     }
    // }

    // @AuraEnabled(cacheable=false)
    // public static List<Opportunity> reloadReportOpportunities(Boolean resetButtonClicked) {
    //     try {
    //         if(resetButtonClicked) {
    //             return Database.query(DEFAULT_QUERY + ORDER_BY_SUBQUERY);
    //         }
    //         return Database.query(getLatestQueryString());
    //     } catch (Exception e) {
    //         throw new AuraHandledException(e.getMessage());
    //     }
    // }    

    // @AuraEnabled(cacheable=false)
    // public static ReloadOpportunitiesWrapper reloadReportOpportunities(Boolean resetButtonClicked) {
    //     try {
    //         if(resetButtonClicked) {
    //             return new ReloadOpportunitiesWrapper(
    //                 new URLParameters(), 
    //                 Database.query(DEFAULT_QUERY + ORDER_BY_SUBQUERY)
    //             );
    //         }
    //         return getLatestReloadOpportunitiesResponse();
    //     } catch (Exception e) {
    //         throw new AuraHandledException(e.getMessage());
    //     }
    // }    

    // private static String getLatestQueryString() {
    //     List<ReportQuery__c> latestQuery = getLatestQuery();
        
    //     if(latestQuery.isEmpty()) {
    //         return DEFAULT_QUERY + ORDER_BY_SUBQUERY;
    //     }
    //     return latestQuery[0].Query__c;
    // }

    // private static ReloadOpportunitiesWrapper getLatestReloadOpportunitiesResponse() {
    //     List<ReportQuery__c> latestQuery = getLatestQuery();
        
    //     if(latestQuery.isEmpty()) {
    //         return new ReloadOpportunitiesWrapper(
    //             new URLParameters(), 
    //             Database.query(DEFAULT_QUERY + ORDER_BY_SUBQUERY)
    //         );
    //     }
    //     return new ReloadOpportunitiesWrapper(
    //             (URLParameters) JSON.deserialize(latestQuery[0].QueryParameters__c, URLParameters.class), 
    //             Database.query(latestQuery[0].Query__c)
    //     );
    // }

    // private static List<ReportQuery__c> getLatestQuery() {
    //     return [
    //         SELECT Query__c, CreatedDate, QueryParameters__c
    //         FROM ReportQuery__c 
    //         WHERE OwnerId = :UserInfo.getUserId() AND ReportDeveloperName__c = 'Trattative_Attivit_VLw'
    //         ORDER BY CreatedDate DESC
    //     ];
    // }

    @AuraEnabled(cacheable=false)
    public static List<String> getOpportunityNames() {
        try {
            Set<String> uniqueOpportunityNames = new Set<String>();
            // for(Opportunity currentOpportunity : [SELECT Name FROM Opportunity WHERE StageName = 'Assegnato' AND OwnerId = :UserInfo.getUserId() ORDER BY Name ASC]) {
            for(Opportunity currentOpportunity : [SELECT Name FROM Opportunity WHERE StageName = 'Assegnato' ORDER BY Name ASC]) {
                uniqueOpportunityNames.add(currentOpportunity.Name);
            }

            return new List<String>(uniqueOpportunityNames);
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled(cacheable=false)
    public static List<String> getAccountNames() {
        try {
            Set<String> uniqueAccountNames = new Set<String>();
            // for(Opportunity currentOpportunity : [SELECT Name FROM Opportunity WHERE StageName = 'Assegnato' AND OwnerId = :UserInfo.getUserId() ORDER BY Account.Name ASC]) {
            for(Opportunity currentOpportunity : [SELECT Account.Name FROM Opportunity WHERE StageName = 'Assegnato' ORDER BY Account.Name ASC]) {
                uniqueAccountNames.add(currentOpportunity.Account?.Name);
            }

            return new List<String>(uniqueAccountNames);
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled(cacheable=false)
    public static List<String> getOpportunityOwners() {
        try {
            Set<String> uniqueOwnerNames = new Set<String>();
            // for(Opportunity currentOpportunity : [SELECT Owner.Name FROM Opportunity WHERE StageName = 'Assegnato' AND OwnerId = :UserInfo.getUserId() ORDER BY Owner.Name ASC]) {
            for(Opportunity currentOpportunity : [SELECT Owner.Name FROM Opportunity WHERE StageName = 'Assegnato' ORDER BY Owner.Name ASC]) {
                uniqueOwnerNames.add(currentOpportunity.Owner?.Name);
            }

            return new List<String>(uniqueOwnerNames);
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
}