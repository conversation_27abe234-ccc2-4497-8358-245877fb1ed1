@isTest
public class OpportunityStageUpdateBatch_Test {
	@testSetup
    public static void setupTestData(){
        List<Opportunity> opportunities = new List<Opportunity>();
        for(Integer i = 0; i < 100; i++){
            opportunities.add(new Opportunity(
                Name = 'Test Opportunity ' + i,
                RecordTypeId = SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('InterestShow').getRecordTypeId(), 
                StageName = 'Assegnato',
                CloseDate=Date.today().addDays(3),
                WorkingSLAExpiryDate__c = Date.today().addDays(-2)
            ));
        }
        insert opportunities;
    }
    @isTest
    static void testBatchProcessing() {
		
        Test.startTest();
        OpportunityStageUpdateBatch batch = new OpportunityStageUpdateBatch();
        Database.executeBatch(batch);
        Test.stopTest();

        List<Opportunity> updatedOpportunities = [SELECT RecordTypeId, StageName,ClosureSubstatus__c  FROM Opportunity WHERE (RecordType.Name = 'Interest Show' AND StageName= 'Chiuso' AND ClosureSubstatus__c='Fuori SLA' AND RecordType.Name ='Interest Show')];
        System.assertEquals(100, updatedOpportunities.size(), 'Expected 100 opportunities to be processed.');


    }
}
