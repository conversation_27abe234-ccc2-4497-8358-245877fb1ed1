@IsTest
public without sharing class OpportunityRelatedListControllerTest {
    
    @TestSetup
    static void makeData(){
            
        Account a = new Account(
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('PersonAccount').getRecordTypeId(),
            FirstName = 'First',
            LastName = 'Last'
        );

        insert a;

        Id omnicanaleRT = Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Omnicanale').getRecordTypeId();

        Opportunity o = new Opportunity(
                    RecordTypeId = omnicanaleRT,
                    Name = 'Opportunity Test',
                    StageName = 'Nuovo',
                    CloseDate = Date.today().addDays(3),
                    AccountId = a.Id
                );
        insert o;

        Opportunity o1 = new Opportunity(
                    RecordTypeId = omnicanaleRT,
                    Name = 'Opportunity Test1',
                    StageName = 'Nuovo',
                    CloseDate = Date.today().addDays(3),
                    AccountId = a.Id
                );
        insert o1;



        Account account1 = new Account(
            Name = 'Test Agency 1',
            ExternalId__c = 'Test1',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId()
        );

        insert account1;
    }


    @IsTest
    static void getOpportunitiesTest(){

        Account a = [SELECT Id FROM Account LIMIT 1];
        Opportunity o = [SELECT Id, StageName FROM Opportunity LIMIT 1];

        o.StageName = 'Chiuso';
        update o;

        List<String> stageNames = new List<String>{'Nuovo', 'Assegnato', 'In gestione'};
        List<String> stageNamesChiuso = new List<String>{'Chiuso'};
        
        Test.startTest();
            OpportunityRelatedListController oc = new OpportunityRelatedListController();
            List<Opportunity> oppList = OpportunityRelatedListController.getRelatedOpportunities(a.Id, stageNames);
            List<Opportunity> oppListChiuso = OpportunityRelatedListController.getRelatedOpportunities(a.Id, stageNamesChiuso);
        Test.stopTest();

        System.assertEquals(1, oppList.size());
        System.assertEquals(1, oppListChiuso.size());
    }

    @IsTest
    static void createOpportunityTest(){
        Account person = [SELECT Id FROM Account WHERE RecordType.DeveloperName = 'PersonAccount' LIMIT 1];
        Account agency = [SELECT Id FROM Account WHERE RecordType.DeveloperName = 'Agency' LIMIT 1];


        Test.startTest();
            String oppId = OpportunityRelatedListControllerNS.createOpportunity(person.Id, System.today(), /*365,*/ agency.Id);
        Test.stopTest();

        System.assertEquals(true, String.isNotBlank(oppId));
    }

    @IsTest
    static void updateOpportunityTest(){
        Opportunity o = [SELECT Id, Name FROM Opportunity LIMIT 1];

        Test.startTest();
            OpportunityRelatedListControllerNS.updateOpportunity(o.Id, 'AGE0000003440');
        Test.stopTest();
    }

    @IsTest
    static void getAccountInfoTest(){
        Account a = [SELECT Id FROM Account WHERE RecordType.DeveloperName = 'PersonAccount' LIMIT 1];

        Test.startTest();
            a = OpportunityRelatedListController.getAccountInfo(a.Id);
        Test.stopTest();

        System.assertEquals('First Last', a.Name);
    }

    @IsTest
    static void getUserInfoTest(){
        Test.startTest();
            User u = OpportunityRelatedListController.getUserInfo(UserInfo.getUserId());
        Test.stopTest();

        System.assertEquals(UserInfo.getName(), u.Name);
    }

    
    @IsTest
    static void getTempiLavorazioneTest(){
        String accountId = [SELECT Id FROM Account WHERE RecordType.DeveloperName = 'PersonAccount' LIMIT 1].Id;

        Test.startTest();
            Integer giorni = OpportunityRelatedListControllerNS.getTempiLavorazione(accountId, '');
        Test.stopTest();
    }
}