global class KPIUpdateSimulation implements Database.Batchable<sObject>, Schedulable {
    global Database.QueryLocator start(Database.BatchableContext BC) {
        String query = 'SELECT Id FROM KPI__c limit 50000';
        return Database.getQueryLocator(query);
    }

    global void execute(Database.BatchableContext BC, List<KPI__c> scope) {
        final Integer GENERAL_VALUE = 700;
        final Integer BENCHMARK_VALUE = 400;
        final Integer WEIGHT_VALUE = 200;
        final Integer SCORE_VALUE = 200;
        final Integer GAP_VALUE = 300;


        if (scope != null && !scope.isEmpty()) {
            
            for (KPI__c kpiRecord : scope) {
                // Information
                //kpiRecord.NumberPoliciesByContact__c = Math.round(Math.random() * GENERAL_VALUE);
                //kpiRecord.NumberContactableClients__c = Math.round(Math.random() * GENERAL_VALUE);
                //kpiRecord.NumberClientsPortfolio__c = Math.round(Math.random() * GENERAL_VALUE);
                //kpiRecord.ContactActivitiesProcessedClosed__c = Math.round(Math.random() * GENERAL_VALUE);
                //kpiRecord.ContactActivitiesAssignedPeriod__c = Math.round(Math.random() * GENERAL_VALUE);
                
                // Lavorazione
                // kpiRecord.ContactActivitiesProcessingAssigned__c = Math.round(Math.random() * GENERAL_VALUE);
                kpiRecord.BenchmarkContactActivitiesAssigned__c = Math.round(Math.random() * BENCHMARK_VALUE);
                kpiRecord.WeightContactActivitiesAssigned__c = Math.round(Math.random() * WEIGHT_VALUE);
                kpiRecord.ScoreProcessingAssignedActivities__c = Math.round(Math.random() * SCORE_VALUE);
                kpiRecord.GapContactActivitiesAssigned__c = Math.round(Math.random() * GAP_VALUE);
                
                // Connversione
                // kpiRecord.ConversionAssignedContactActivities__c = Math.round(Math.random() * GENERAL_VALUE);
                kpiRecord.BenchmarkConversionAssignedActivities__c = Math.round(Math.random() * BENCHMARK_VALUE);
                kpiRecord.WeightConversionAssignedActivities__c = Math.round(Math.random() * WEIGHT_VALUE);
                kpiRecord.ScoreConversionAssignedActivities__c = Math.round(Math.random() * SCORE_VALUE);
                kpiRecord.GapConversionAssignedActivities__c = Math.round(Math.random() * GAP_VALUE);
                
                // Tempo di lavorazione
                kpiRecord.AverageLeadProcessingTime__c = Math.round(Math.random() * GENERAL_VALUE);
                kpiRecord.BenchmarkAverageLeadProcessingTime__c = Math.round(Math.random() * BENCHMARK_VALUE);
                kpiRecord.WeightAverageLeadProcessingTime__c = Math.round(Math.random() * WEIGHT_VALUE);
                kpiRecord.ScoreAverageLeadProcessingTime__c = Math.round(Math.random() * SCORE_VALUE);
                kpiRecord.GapAverageLeadProcessingTime__c = Math.round(Math.random() * GAP_VALUE);
                
                // Penetrazione digitale
                kpiRecord.DigitalPenetration__c = Math.round(Math.random() * GENERAL_VALUE);
                kpiRecord.BenchmarkDigitalPenetration__c = Math.round(Math.random() * BENCHMARK_VALUE);
                kpiRecord.WeightDigitalPenetration__c = Math.round(Math.random() * WEIGHT_VALUE);
                kpiRecord.ScoreDigitalPenetration__c = Math.round(Math.random() * SCORE_VALUE);
                kpiRecord.GapDigitalPenetration__c = Math.round(Math.random() * GAP_VALUE);
                
                // Registrazione Area Riservata
                kpiRecord.PrivateAreaRegistration__c = Math.round(Math.random() * GENERAL_VALUE);
                kpiRecord.BenchmarkPrivateAreaRegistration__c = Math.round(Math.random() * BENCHMARK_VALUE);
                kpiRecord.WeightPrivateAreaRegistration__c = Math.round(Math.random() * WEIGHT_VALUE);
                kpiRecord.ScorePrivateAreaRegistration__c = Math.round(Math.random() * SCORE_VALUE);
                kpiRecord.GapPrivateAreaRegistration__c = Math.round(Math.random() * GAP_VALUE);
                
                // Preventivi Omnicanale
                kpiRecord.OmnichannelQuotes__c = Math.round(Math.random() * GENERAL_VALUE);
                kpiRecord.BenchmarkOmnichannelQuotes__c = Math.round(Math.random() * BENCHMARK_VALUE);
                kpiRecord.WeightOmnichannelQuotes__c = Math.round(Math.random() * WEIGHT_VALUE);
                kpiRecord.ScoreOmnichannelQuotes__c = Math.round(Math.random() * SCORE_VALUE);
                kpiRecord.GapOmnichannelQuotes__c = Math.round(Math.random() * GAP_VALUE);
            }

            update scope;
        }
    }

    global void finish(Database.BatchableContext BC) {
    }

    global void execute(SchedulableContext sc) {
        KPIUpdateSimulation batchJob = new KPIUpdateSimulation();
        Database.executeBatch(batchJob);
    }
}
