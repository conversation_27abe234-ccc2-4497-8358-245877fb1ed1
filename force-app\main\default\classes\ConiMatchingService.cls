/**
 * @File Name         : ClassName.cls
 * @Description       :
 * <AUTHOR> <EMAIL>
 * @Group             :
 * @Last Modified On  : 12-11-2024
 * @Last Modified By  : <EMAIL>
 **/
public with sharing class ConiMatchingService {
    @TestVisible
    private static Boolean testCoverage { get; set; }
    // private static List<AccountAgencyDetails__c> accountAgencyDetails;
    // private static List<String> childRelationshipNames = new List<String>{
    //     'InsurancePolicies',
    //     'Opportunities',
    //     'ServiceAppointments'
    // };

    // Scenario: group is newly matched with account so share the account's child records with that group
    // public static void shareClientsWithGroups(Map<Id, Set<Id>> accountAgencyDetailsIdsToGroupIds) {
    //     List<SObject> shareRecords = buildShareRecordsForClients(accountAgencyDetailsIdsToGroupIds);
    //     // TODO: pass to queueable instead
    //     insert shareRecords;
    // }

    // Scenario: group is no longer matched with account so remove the share records
    // TODO: IMPLEMENT
    // public static void unshareClientsFromGroups(Map<Id, Set<Id>> accountAgencyDetailsIdsToGroupIds) {
    //     return;
    // }

    // Scenario: new child records are inserted for existing client accounts with existing matched groups
    // public static void shareRecordsWithExistingGroups(Set<Id> childRecordIds) {
    //     // Get matched groups
    //     // Insert new
    //     return;
    // }

    // public static Map<Id, Set<Id>> getMatchedGroupIdsForAccountAgencyDetailsIds(Set<Id> accountAgencyDetailsIds) {
    //     List<AccountAgencyDetails__c> AADs = getAccountAgencyDetailsWithChildrenWithinAgency(accountAgencyDetailsIds);

    //     Map<Id, Set<Id>> accountAgencyDetailsIdsToMatchedGroupIds = new Map<Id, Set<Id>>();

    //     for (AccountAgencyDetails__c AAD : AADs) {

    //         Set<Id> matchedGroupIds = new Set<Id>();

    //         // For each child object, iterate over its records, get those records' shares, and add them to the shares list
    //         for (String childObjectName : childRelationshipNames) {
    //             for (SObject childRecord : AAD.getSObjects(childObjectName)) {
    //                 for(Sobject childRecordShare : childRecord.getSObjects('Shares')) {
    //                     Id userOrGroupId = (Id)childRecordShare.get('UserOrGroupId');
    //                     Boolean isGroupId = userOrGroupId.getSObjectType().getDescribe().getName() == 'Group';
    //                     if (isGroupId) {
    //                         matchedGroupIds.add(userOrGroupId);
    //                     }
    //                 }
    //             }
    //         }

    //         if (!matchedGroupIds.isEmpty()) {
    //             accountAgencyDetailsIdsToMatchedGroupIds.put(AAD.Id, matchedGroupIds);
    //         }
    //     }
    //     return accountAgencyDetailsIdsToMatchedGroupIds;
    // }

    // private static List<SObject> buildShareRecordsForClients(Map<Id, Set<Id>> accountAgencyDetailsIdsToGroupIds) {
    //     // Get existing child records
    //     // TODO: this must change to also share child records from outside the agency, e.g. all policies across all agencies
    //     List<AccountAgencyDetails__c> accountAgencyDetailsWithChildren = getAccountAgencyDetailsWithChildrenWithinAgency(accountAgencyDetailsIdsToGroupIds.keySet());

    //     // Get all child record IDs into a set
    //     Map<Id, Set<Id>> accountAgencyDetailsIdsToChildRecordIds = getAccountAgencyDetailIdsToChildRecordIds(accountAgencyDetailsWithChildren);

    //     List<SObject> shareRecords = new List<Sobject>();

    //     // Build shares for those child records
    //     // For each client...
    //     for (Id accountAgencyDetailsId : accountAgencyDetailsIdsToChildRecordIds.keySet()) {
    //         // For each child record of that client...
    //         for (Id childRecordId : accountAgencyDetailsIdsToChildRecordIds.get(accountAgencyDetailsId)) {
    //             // For each group that needs access to that child record...
    //             for (Id groupId : accountAgencyDetailsIdsToGroupIds.get(accountAgencyDetailsId)) {
    //                 shareRecords.add(buildShareRecord(childRecordId, groupId));
    //             }
    //         }
    //     }

    //     return shareRecords;
    // }

    // private static Map<Id, Set<Id>> getAccountAgencyDetailIdsToChildRecordIds(List<AccountAgencyDetails__c> accountAgencyDetails) {
    //     Map<Id, Set<Id>> accountAgencyDetailIdsToChildRecordIds = new Map<Id, Set<Id>>();

    //     for (AccountAgencyDetails__c AAD : accountAgencyDetails) {
    //         Set<Id> childRecordIds = new Set<Id>();

    //         for (String childRelationshipName : childRelationshipNames) {
    //             for (SObject childRecord : AAD.getSObjects(childRelationshipName)) {
    //                 childRecordIds.add(childRecord.Id);
    //             }
    //         }

    //         if (!childRecordIds.isEmpty()) {
    //             accountAgencyDetailIdsToChildRecordIds.put(AAD.Id, childRecordIds);
    //         }
    //     }

    //     return accountAgencyDetailIdsToChildRecordIds;
    // }

    // private static SObject buildShareRecord(Id recordId, Id groupId) {
    //     String shareObjectName = recordId.getSObjectType().getDescribe().getName() + 'Share';

    //     SObject shareRecord = (sObject)Type.forName(shareObjectName).newInstance();

    //     if (shareObjectName == 'OpportunityShare') {
    //         shareRecord.put('OpportunityAccessLevel', 'Read');
    //         shareRecord.put('OpportunityId', recordId);
    //         shareRecord.put('UserOrGroupId', groupId);
    //     } else {
    //         shareRecord.put('AccessLevel', 'Read');
    //         shareRecord.put('ParentId', recordId);
    //         shareRecord.put('UserOrGroupId', groupId);
    //     }

    //     return shareRecord;
    // }

    // NOTE: WE NEED TWO QUERIES: ONE TO GET THE SHARING LEVEL WITHIN THE AGENCY (TO KNOW WHO IS ASSIGNED)
    // AND ANOTHER TO GET CHILD RECORDS ACROSS AGENCIES (TO KNOW ALL THE RECORDS THEY SHOULD ACCESS, INCLUDING CROSS-AGENCY RECORDS LIKE POLICIES)
    // THIS IS THE FIRST QUERY.  TODO: CREATE A SECOND METHOD WITH THE SECOND QUERY
    // private static List<AccountAgencyDetails__c> getAccountAgencyDetailsWithChildrenWithinAgency(Set<Id> accountAgencyDetailsIds) {
    //     // Uncomment below for caching
    //     // if (accountAgencyDetails == null){
    //         /*
    //         accountAgencyDetails = [SELECT Id, Name,
    //             (SELECT Id, Name, AccountId,
    //                 (SELECT Id, UserOrGroupId, UserOrGroup.Name, OpportunityAccessLevel, RowCause
    //                 FROM Shares
    //                 WHERE OpportunityAccessLevel = 'Edit')
    //             FROM Opportunities__r),
    //             (SELECT Id, Name,
    //                 (SELECT Id, UserOrGroupId, UserOrGroup.Name, AccessLevel, RowCause
    //                 FROM Shares
    //                 WHERE AccessLevel = 'Edit')
    //             FROM InsurancePolicies__r),
    //             (SELECT Id, AccountId,
    //                 (SELECT Id, UserOrGroupId, UserOrGroup.Name, AccessLevel, RowCause
    //                 FROM Shares
    //                 WHERE AccessLevel = 'Edit')
    //             FROM ServiceAppointments__r)
    //         FROM AccountAgencyDetails__c
    //         WHERE Id IN :accountAgencyDetailsIds];
    //         */
    //     // }

    //     return accountAgencyDetails;
    // }
}
