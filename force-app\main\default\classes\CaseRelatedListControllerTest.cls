@IsTest
public without sharing class CaseRelatedListControllerTest {
   
    @TestSetup
    static void makeData(){
        
        Account a = new Account(
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('PersonAccount').getRecordTypeId(),
            FirstName = 'First',
            LastName = 'Last'
        );

        insert a;

        Id attivitaRT = Schema.SObjectType.Case.getRecordTypeInfosByDeveloperName().get('AttivitaContatto').getRecordTypeId();

        Case c = new Case(
            RecordTypeId = attivitaRT,
            AccountId = a.Id,
            dueDate__c = Date.today().addDays(10)
        );

        insert c;

        Case c1 = new Case(
            RecordTypeId = attivitaRT,
            AccountId = a.Id,
            dueDate__c = Date.today().addDays(10)
        );

        insert c1;
    }

    @IsTest
    static void getCasesTest(){
        
        Account a = [SELECT Id FROM Account LIMIT 1];
        Case c = [SELECT Id, Status FROM Case LIMIT 1];

        c.Status = 'Closed';
        update c;

        Test.startTest();
        CaseRelatedListController cc = new CaseRelatedListController(); 
        List<Case> listCase = CaseRelatedListController.getRelatedCases(a.Id, '');
        List<Case> listCaseClosed = CaseRelatedListController.getRelatedCases(a.Id, 'Closed');
        Test.stopTest();

        System.assertEquals(1, listCase.size());
        System.AssertEquals(1, listCaseClosed.size());
    }
}