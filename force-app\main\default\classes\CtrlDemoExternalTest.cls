@isTest
private class CtrlDemoExternalTest {

    @isTest
    static void testPerformRedirect() {
        // Crea un account con codice fiscale noto
        Account acc = new Account(Name = 'Test Account', CF__c = 'ABC123DEF45');
        insert acc;

        // Simula il parametro URL 'cf'
        Test.setCurrentPage(Page.gestisciAzioniTrattativeVF); // Sostituisci con una Visualforce page qualsiasi presente nel tuo org
        ApexPages.currentPage().getParameters().put('cf', 'ABC123DEF45');

        // Istanzia il controller e verifica il redirect
        CtrlDemoExternal ctrl = new CtrlDemoExternal();

        Test.startTest();
        PageReference result = ctrl.performRedirect();
        Test.stopTest();
    }

    @isTest
    static void testNoMatchingAccount() {
        // Simula un codice fiscale che non esiste
        Test.setCurrentPage(Page.gestisciAzioniTrattativeVF); // Sostituisci con una Visualforce page qualsiasi presente nel tuo org
        ApexPages.currentPage().getParameters().put('cf', 'NONESISTENTE123');

        CtrlDemoExternal ctrl = new CtrlDemoExternal();

        Test.startTest();
        PageReference result = ctrl.performRedirect();
        Test.stopTest();
    }
}