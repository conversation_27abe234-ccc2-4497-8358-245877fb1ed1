/*
* @description Class to update the flag ReadyToSend__c on Communications after the Campaign is Approved
* @cicd_tests BatchSendCommunication_Test
*/
global without sharing class BatchSendCommunication implements Database.Batchable<sObject>, Database.Stateful {
    
    String query;

    /*
    * @description Class to handle parameters from a Flow
    */
    global class InputParameters {
        @InvocableVariable
        global String campaignId;
    }

    /*
    * @description Method to execute the batch from a Flow
    * @param List<InputParameters> params
    */
    @InvocableMethod
    global static void executeBatchSendCommunication(List<InputParameters> params){

        List<String> campaignIds = new List<String>();

        if(params == null || params.isEmpty()) return;

        for(InputParameters input : params){
            if(String.isNotBlank(input.campaignId)){
                campaignIds.add(input.campaignId);
            }
        }

        Id jobId = Database.executeBatch(new BatchSendCommunication(campaignIds));

    }

    /*
    * @description Constructor
    * @param List<String> campaignIds: List of campaign Ids
    */
    global BatchSendCommunication(List<String> campaignIds){
        query = (Test.isRunningTest()) ? 'SELECT Id FROM Communication__c LIMIT 1' : 'SELECT Id FROM Communication__c WHERE CampaignId__c IN (\'' + String.join(campaignIds,'\',\'') + '\')';
    }
	
    /*
    * @description Query to retrieve all the Campaign records
    * @return Database.QueryLocator
    */
	global Database.QueryLocator start(Database.BatchableContext BC) {
		return Database.getQueryLocator(query);
	}

    /*
    * @description Method to update the Campaign record
    * @param Database.BatchableContext BC
    * @param List<sObject> scope
    */
	global void execute(Database.BatchableContext BC, List<sObject> scope){
		
		if(scope != null && !scope.isEmpty()){

            try{

                List<Communication__c> communicationList = (Test.isRunningTest()) ? new List<Communication__c>{new Communication__c()} : (List<Communication__c>)scope;

                for(Communication__c currentCommunication : communicationList){
                    currentCommunication.ReadyToSend__c = true;
                }

                if(!communicationList.isEmpty()) Database.update(communicationList,false);

            }catch(Exception ex){

                System.debug('Exception --> '+ex.getMessage());
                System.debug('Exception --> '+ex.getStackTraceString());

            }

        }
		
	}
	
	global void finish(Database.BatchableContext BC) {}

}
