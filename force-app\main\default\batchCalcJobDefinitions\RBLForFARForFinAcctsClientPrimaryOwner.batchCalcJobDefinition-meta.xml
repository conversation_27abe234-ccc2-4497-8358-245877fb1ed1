<?xml version="1.0" encoding="UTF-8"?>
<BatchCalcJobDefinition xmlns="http://soap.sforce.com/2006/04/metadata">
    <aggregates>
        <fields>
            <aggregateFunction>Sum</aggregateFunction>
            <alias>BalanceSum</alias>
            <sourceFieldName>FinancialAccount_Balance</sourceFieldName>
        </fields>
        <groupBy>FinServ_RelatedAccount_c</groupBy>
        <label>aggregate_FinancialAccount_Balance</label>
        <name>aggregate_FinancialAccount_Balance</name>
        <sourceName>FinServ_FinancialAccountRole_c_Filter</sourceName>
    </aggregates>
    <datasources>
        <fields>
            <alias>Id</alias>
            <dataType>Text</dataType>
            <name>Id</name>
        </fields>
        <fields>
            <alias>Name</alias>
            <dataType>Text</dataType>
            <name>Name</name>
        </fields>
        <label>Account</label>
        <name>Account</name>
        <sourceName>Account</sourceName>
        <type>StandardObject</type>
    </datasources>
    <datasources>
        <fields>
            <alias>FinancialAccount_Balance</alias>
            <dataType>Numeric</dataType>
            <name>FinServ__FinancialAccount__r.FinServ__Balance__c</name>
        </fields>
        <fields>
            <alias>FinServ_RelatedAccount_c</alias>
            <dataType>Text</dataType>
            <name>FinServ__RelatedAccount__c</name>
        </fields>
        <fields>
            <alias>FinServ_Role_c</alias>
            <dataType>Text</dataType>
            <name>FinServ__Role__c</name>
        </fields>
        <label>FinServ_FinancialAccountRole_c</label>
        <name>FinServ_FinancialAccountRole_c</name>
        <sourceName>FinServ__FinancialAccountRole__c</sourceName>
        <type>StandardObject</type>
    </datasources>
    <description>Client-level aggregation of all Financial Accounts balances where Client is designated as the Primary Owner on Financial Account Role.</description>
    <executionPlatformType>CRMA</executionPlatformType>
    <filters>
        <criteria>
            <operator>Equals</operator>
            <sequence>1</sequence>
            <sourceFieldName>FinServ_Role_c</sourceFieldName>
            <value>Primary Owner</value>
        </criteria>
        <filterCondition>1</filterCondition>
        <isDynamicFilter>false</isDynamicFilter>
        <label>FinServ_FinancialAccountRole_c_Filter</label>
        <name>FinServ_FinancialAccountRole_c_Filter</name>
        <sourceName>FinServ_FinancialAccountRole_c</sourceName>
    </filters>
    <isTemplate>false</isTemplate>
    <joins>
        <fields>
            <alias>BalanceSum</alias>
            <sourceFieldName>BalanceSum</sourceFieldName>
            <sourceName>aggregate_FinancialAccount_Balance</sourceName>
        </fields>
        <fields>
            <alias>Id</alias>
            <sourceFieldName>Id</sourceFieldName>
            <sourceName>Account</sourceName>
        </fields>
        <joinKeys>
            <primarySourceFieldName>Id</primarySourceFieldName>
            <secondarySourceFieldName>FinServ_RelatedAccount_c</secondarySourceFieldName>
        </joinKeys>
        <label>Account_aggregate_FinancialAccount_Balance</label>
        <name>Account_aggregate_FinancialAccount_Balance</name>
        <primarySourceName>Account</primarySourceName>
        <secondarySourceName>aggregate_FinancialAccount_Balance</secondarySourceName>
        <type>LeftOuter</type>
    </joins>
    <label>RBLForFARForFinAcctsClientPrimaryOwner</label>
    <processType>DataProcessingEngine</processType>
    <status>Inactive</status>
    <writebacks>
        <fields>
            <runtimeParameter>false</runtimeParameter>
            <sourceFieldName>Id</sourceFieldName>
            <targetFieldName>Id</targetFieldName>
        </fields>
        <fields>
            <runtimeParameter>false</runtimeParameter>
            <sourceFieldName>BalanceSum</sourceFieldName>
            <targetFieldName>FinServ__TotalFinAcctsPrimaryOwner__c</targetFieldName>
        </fields>
        <isChangedRow>false</isChangedRow>
        <label>RBLForFARForFinAcctsClientPrimaryOwnerExport</label>
        <name>RBLForFARForFinAcctsClientPrimaryOwnerExport</name>
        <operationType>Update</operationType>
        <sourceName>Account_aggregate_FinancialAccount_Balance</sourceName>
        <storageType>sObject</storageType>
        <targetObjectName>Account</targetObjectName>
        <writebackSequence>1</writebackSequence>
        <writebackUser>0057Q00000A7swHQAR</writebackUser>
    </writebacks>
</BatchCalcJobDefinition>
