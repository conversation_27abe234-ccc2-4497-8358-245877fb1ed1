@isTest
public class OpportunityStageUpdateSchedule_Test {

    @isTest
   	static void testOpportunityStageUpdateSchedule() {
        Test.startTest();

        
        String cronExpression = '0 0 22 * * ?'; // 10:00 PM every day
        System.schedule('TestOpportunityStageUpdateJob', cronExpression, new OpportunityStageUpdateSchedule());

       
        Test.stopTest();

        List<Opportunity> updatedOpportunities = [SELECT Id, StageName, ClosureSubstatus__c FROM Opportunity WHERE StageName = 'Chiuso' AND ClosureSubstatus__c = 'Fuori SLA'];
        
        
        System.assertEquals(0, updatedOpportunities.size(), 'Opportunities were not updated as expected.');
    }
}
