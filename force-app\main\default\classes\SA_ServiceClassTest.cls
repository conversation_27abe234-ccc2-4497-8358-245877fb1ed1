@isTest
private class SA_ServiceClassTest {

    @testSetup
    static void setupData() {
        // Crea utente di test
        Profile p = [SELECT Id FROM Profile WHERE Name='Standard User' LIMIT 1];
        User testUser = new User(
            Username = '<EMAIL>',
            Alias = 'tuser',
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8',
            LastName = 'Test',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            TimeZoneSidKey = 'Europe/Paris',
            ProfileId = p.Id
        );
        insert testUser;

        // Crea ServiceResource collegato all'utente
        ServiceResource sr = new ServiceResource(
            Name = 'Test SR',
            RelatedRecordId = testUser.Id,
            Attiva_su_canali_digitali__c = false,
            Attiva_per_clienti_e_prospect__c = 'Nessuno',
            IsActive = true
        );
        insert sr;
        
        OperatingHours op = new OperatingHours(Name = 'TestOp');
        insert op;

        // Crea ServiceTerritory
        ServiceTerritory st = new ServiceTerritory(
            Name = 'Test ST',
            OperatingHoursId= op.Id,
            isActive = true
        );
        insert st;

        // Crea membro del territorio
        ServiceTerritoryMember stm = new ServiceTerritoryMember(
            ServiceResourceId = sr.Id,
            ServiceTerritoryId = st.Id,
            IsActive__c = true,
            EffectiveStartDate = Date.today()
        );
        insert stm;
    }

    @isTest
    static void test_getRelatedServiceResources() {
        // Simula contesto utente
        User testUser = [SELECT Id FROM User WHERE Username = '<EMAIL>' LIMIT 1];
        System.runAs(testUser) {
            SA_ServiceClass svc = new SA_ServiceClass();
            Map<String, Object> inputMap = new Map<String, Object>();
            Map<String, Object> outputMap = new Map<String, Object>();
            Map<String, Object> options = new Map<String, Object>();
            try{
	            Boolean result = svc.invokeMethod('getRelatedServiceResources', inputMap, outputMap, options);

    	        List<Object> resources = (List<Object>)outputMap.get('serviceResources');
            }catch(Exception ex){}
        }
    }

    @isTest
    static void test_getRelatedServiceTerritories() {
        // Simula contesto utente
        User testUser = [SELECT Id FROM User WHERE Username = '<EMAIL>' LIMIT 1];
        System.runAs(testUser) {
            SA_ServiceClass svc = new SA_ServiceClass();
            Map<String, Object> inputMap = new Map<String, Object>();
            Map<String, Object> outputMap = new Map<String, Object>();
            Map<String, Object> options = new Map<String, Object>();
			
            try{
	            Boolean result = svc.invokeMethod('getRelatedServiceTerritories', inputMap, outputMap, options);

    	        List<Object> territories = (List<Object>)outputMap.get('serviceTerritories');
            }catch(Exception ex){}
        }
    }

    @isTest
    static void test_error_case_no_resource() {
        // Crea un nuovo utente senza ServiceResource
        User u = new User(
            Username = '<EMAIL>',
            Alias = 'nouser',
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8',
            LastName = 'NoRes',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            TimeZoneSidKey = 'Europe/Paris',
            ProfileId = [SELECT Id FROM Profile WHERE Name='Standard User' LIMIT 1].Id
        );
        insert u;

        System.runAs(u) {
            SA_ServiceClass svc = new SA_ServiceClass();
            Map<String, Object> inputMap = new Map<String, Object>();
            Map<String, Object> outputMap = new Map<String, Object>();
            Map<String, Object> options = new Map<String, Object>();
            try{
	            svc.invokeMethod('getRelatedServiceResources', inputMap, outputMap, options);
            }catch(Exception ex){}
        }
    }
}