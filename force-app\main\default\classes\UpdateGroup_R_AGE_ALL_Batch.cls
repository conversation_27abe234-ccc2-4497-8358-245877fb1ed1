global class UpdateGroup_R_AGE_ALL_Batch implements Database.Batchable<SObject>, Schedulable, Database.Stateful {
    //Instance variable
    private Id ID_groupR_AGE_ALL;
    private Map<Id, GroupMember> groupMemberMapR_AGE_ALL;
    
    global UpdateGroup_R_AGE_ALL_Batch() {
        initializeGroupContext();
    }
    
    private void initializeGroupContext() {
        // Recupero ID del gruppo 'R_AGE_ALL'
        List<Group> groupR_AGE_ALL = [SELECT Id FROM Group WHERE DeveloperName = 'R_AGE_ALL' LIMIT 1];
        if (!groupR_AGE_ALL.isEmpty()) {
        	ID_groupR_AGE_ALL = groupR_AGE_ALL[0].id;
        }

        // Recupero membri esistenti del gruppo 'R_AGE_ALL'
        groupMemberMapR_AGE_ALL = new Map<Id, GroupMember>();
        for (GroupMember gm : [SELECT Id, UserOrGroupId FROM GroupMember WHERE GroupId = :ID_groupR_AGE_ALL]) {
            groupMemberMapR_AGE_ALL.put(gm.UserOrGroupId, gm);
        }
    }
    
    global Database.QueryLocator start(Database.BatchableContext BC) {
    String baseQuery = 'SELECT Id, DeveloperName FROM Group WHERE DeveloperName LIKE \'R_AGE%\' AND DeveloperName != \'R_AGE_ALL\'';
    
        // LIMIT only in test executions
        if (Test.isRunningTest()) {
           baseQuery += ' LIMIT 100';
        }
    
        return Database.getQueryLocator(baseQuery);
	}
   
    global void execute(Database.BatchableContext BC, List<group> scope) {
         
        List<GroupMember>  groupMemberToAdd = new List<GroupMember> ();
        // List<GroupMember>  groupMemberToRemove = new List<GroupMember> ();
      
		if (scope.isEmpty() || ID_groupR_AGE_ALL == null) return;
        
        // Creo un set di ID dalla lista
		Set<Id> groupR_AGE_ALLListIds = new Set<Id>();
		for (Group g : scope) {
    		groupR_AGE_ALLListIds .add(g.Id);
            if ( !groupMemberMapR_AGE_ALL.containsKey(g.id) ) { 
                GroupMember gmToAdd = new GroupMember(UserOrGroupId = g.id, GroupId = ID_groupR_AGE_ALL);
            	groupMemberToAdd.add(gmToAdd);	 
                System.debug('GroupMember To ADD: ' + gmToAdd );
            }
		}
        if (!groupMemberToAdd.isEmpty()) {
            insert groupMemberToAdd; 
        }
    }
     global void finish(Database.BatchableContext BC) {
    }
	global void execute(SchedulableContext SC) {
        Database.executeBatch(new UpdateGroup_R_AGE_ALL_Batch());
    }

}