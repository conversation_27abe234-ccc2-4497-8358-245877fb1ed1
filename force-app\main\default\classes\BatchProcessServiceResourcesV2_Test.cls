@isTest
public class BatchProcessServiceResourcesV2_Test {
    @testSetup
    static void makeData() {
        Id recordTypeAgency = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId();
        Account agency = new Account(Name = 'TestAgency', RecordTypeId = recordTypeAgency);
        insert agency;

        User testUser = UTL_Datafactory.createUser('tusr', '<EMAIL>', 'User Test', 'Unipol Standard User', '', '<EMAIL>', true);
        testUser.FederationIdentifier = '****************';
        testUser.ExternalId__c = '****************';
        update testUser;

        NetworkUser__c nu = new NetworkUser__c();
        nu.NetworkUser__c = '000000';
        nu.FiscalCode__c = '****************';
        nu.IsActive__c = true;
        nu.Profile__c = 'A';
        nu.Society__c = 'SOC_1';
        nu.Agency__c = agency.Id;
        insert nu;
    }

    @isTest
    static void test_batch_1() {
        PermissionSet ps = [SELECT Id, Name FROM PermissionSet WHERE Name = 'MandatoUnipolRental'];
        User usr = [SELECT Id FROM User WHERE UserName = '<EMAIL>'];

        PermissionSetAssignment psa = new PermissionSetAssignment(AssigneeId = usr.Id, PermissionSetId = ps.Id);
        insert psa;

        Test.startTest();
        Database.executeBatch(new BatchProcessServiceResourcesV2(), 200);
        Database.executeBatch(new BatchProcessServiceResourcesV2(true), 200);
        Database.executeBatch(new BatchProcessServiceResourcesV2(new Set<String>{ '****************' }), 200);
        Database.executeBatch(new BatchProcessServiceResourcesV2(Date.today(), Date.today()), 200);
        Test.stopTest();
    }
}
