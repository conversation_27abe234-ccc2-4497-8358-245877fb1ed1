/***************************************************************************************************************************************************
* <AUTHOR>
* @description   A global batch class to calculate and update KPI benchmark values.
*                Implements Database.Batchable to run the batch process and Schedulable to schedule the batch.
* @date           2024-02-17
****************************************************************************************************************************************************/
global class KPIBenchmarkBatch implements Database.Batchable<sObject>, Schedulable {

    /******************************************************************************************
    * @description   Start method to define the scope of the batch process.
    * @param         BC - The Batchable context.
    * @return        Database.QueryLocator - The query locator to fetch KPI records.
    *******************************************************************************************/
    global Database.QueryLocator start(Database.BatchableContext BC) {
        String query = 'SELECT Id, Agency__r.Size__c, Agency__r.Cluster__c, BenchmarkContactActivitiesAssigned__c, BenchmarkConversionAssignedActivities__c, BenchmarkAverageLeadProcessingTime__c, BenchmarkDigitalPenetration__c, BenchmarkOmnichannelQuotes__c, BenchmarkPrivateAreaRegistration__c  FROM KPI__c limit 50000';
        return Database.getQueryLocator(query);
    }

    /******************************************************************************************
    * @description   Execute method to process each batch of records.
    * @param         BC - The Batchable context.
    * @param         scope - The list of KPI records in the current batch.
    *******************************************************************************************/
    global void execute(Database.BatchableContext BC, List<KPI__c> scope) {
        System.debug(JSON.serialize(scope));
        if (scope != null && !scope.isEmpty()) {

            Map<String, Decimal> mapBenchmark = new Map<String, Decimal>();
            List<AggregateResult> listKpi = [SELECT Agency__r.Size__c, 
                                                    Agency__r.Cluster__c,
                                                    AVG(AverageLeadProcessingTime__c) AvgAverageLeadProcessingTime,
                                                    AVG(ContactActivitiesProcessingAssigned__c) AvgContactActivitiesAssigned,
                                                    AVG(ConversionAssignedContactActivities__c) AvgConversionAssignedActivities,
                                                    AVG(DigitalPenetration__c) AvgDigitalPenetration,
                                                    AVG(PrivateAreaRegistration__c) AvgPrivateAreaRegistration,
                                                    AVG(OmnichannelQuotes__c) AvgOmnichannelQuotes
                                             
                                            FROM KPI__c
                                            GROUP BY Agency__r.Size__c, 
                                                     Agency__r.Cluster__c];
                        
            // Populate the map with benchmark values
            for(AggregateResult ar : listKpi){
                mapBenchmark.put(ar.get('Size__c') + '__' + ar.get('Cluster__c') + '__AvgAverageLeadProcessingTime', (Decimal)ar.get('AvgAverageLeadProcessingTime'));
                mapBenchmark.put(ar.get('Size__c') + '__' + ar.get('Cluster__c') + '__AvgContactActivitiesAssigned', (Decimal)ar.get('AvgContactActivitiesAssigned'));
                mapBenchmark.put(ar.get('Size__c') + '__' + ar.get('Cluster__c') + '__AvgConversionAssignedActivities', (Decimal)ar.get('AvgConversionAssignedActivities'));
                mapBenchmark.put(ar.get('Size__c') + '__' + ar.get('Cluster__c') + '__AvgDigitalPenetration', (Decimal)ar.get('AvgDigitalPenetration'));
                mapBenchmark.put(ar.get('Size__c') + '__' + ar.get('Cluster__c') + '__AvgPrivateAreaRegistration', (Decimal)ar.get('AvgPrivateAreaRegistration'));
                mapBenchmark.put(ar.get('Size__c') + '__' + ar.get('Cluster__c') + '__AvgOmnichannelQuotes', (Decimal)ar.get('AvgOmnichannelQuotes'));
            }

            // Update the KPI records with the benchmark values
            for (KPI__c kpiRecord : scope) {
                kpiRecord.BenchmarkAverageLeadProcessingTime__c = (Decimal) mapBenchmark.get(kpiRecord.Agency__r.Size__c + '__' + kpiRecord.Agency__r.Cluster__c + '__AvgAverageLeadProcessingTime');
                kpiRecord.BenchmarkContactActivitiesAssigned__c = (Decimal) mapBenchmark.get(kpiRecord.Agency__r.Size__c + '__' + kpiRecord.Agency__r.Cluster__c + '__AvgContactActivitiesAssigned');
                kpiRecord.BenchmarkConversionAssignedActivities__c = (Decimal) mapBenchmark.get(kpiRecord.Agency__r.Size__c + '__' + kpiRecord.Agency__r.Cluster__c + '__AvgConversionAssignedActivities');
                kpiRecord.BenchmarkDigitalPenetration__c = (Decimal) mapBenchmark.get(kpiRecord.Agency__r.Size__c + '__' + kpiRecord.Agency__r.Cluster__c + '__AvgDigitalPenetration');
                kpiRecord.BenchmarkPrivateAreaRegistration__c = (Decimal) mapBenchmark.get(kpiRecord.Agency__r.Size__c + '__' + kpiRecord.Agency__r.Cluster__c + '__AvgPrivateAreaRegistration');
                kpiRecord.BenchmarkOmnichannelQuotes__c = (Decimal) mapBenchmark.get(kpiRecord.Agency__r.Size__c + '__' + kpiRecord.Agency__r.Cluster__c + '__AvgOmnichannelQuotes');
            }

            // Update the KPI records in the database
            update scope;
        }
    }

    /******************************************************************************************
    * @description   Finish method to perform any post-processing after the batch completes.
    * @param         BC - The Batchable context.
    *******************************************************************************************/
    global void finish(Database.BatchableContext BC) {
        String batchClassName = 'BenchmarkScoreBatch';
    	Type batchType = Type.forName(batchClassName);
        if (batchType != null) {
           Database.Batchable<sObject> batchInstance = (Database.Batchable<sObject>) batchType.newInstance();
           Database.executeBatch(batchInstance);
        }
    }

    /******************************************************************************************
    * @description   Execute method for the Schedulable interface to schedule the batch process.
    * @param         sc - The Schedulable context.
    *******************************************************************************************/
    global void execute(SchedulableContext sc) {
        KPIBenchmarkBatch batchJob = new KPIBenchmarkBatch();
        Database.executeBatch(batchJob);
    }
    
}
