<?xml version="1.0" encoding="UTF-8"?>
<BatchCalcJobDefinition xmlns="http://soap.sforce.com/2006/04/metadata">
    <aggregates>
        <fields>
            <aggregateFunction>Count</aggregateFunction>
            <alias>FinancialAccountCount</alias>
            <sourceFieldName>FinServ_FinancialAccount_c</sourceFieldName>
        </fields>
        <groupBy>FinServ_RelatedAccount_c</groupBy>
        <label>aggregate_FinServ_FinancialAccount_c</label>
        <name>aggregate_FinServ_FinancialAccount_c</name>
        <sourceName>FinServ_FinancialAccountRole_c_Filter</sourceName>
    </aggregates>
    <datasources>
        <fields>
            <alias>Id</alias>
            <dataType>Text</dataType>
            <name>Id</name>
        </fields>
        <fields>
            <alias>Name</alias>
            <dataType>Text</dataType>
            <name>Name</name>
        </fields>
        <label>Account</label>
        <name>Account</name>
        <sourceName>Account</sourceName>
        <type>StandardObject</type>
    </datasources>
    <datasources>
        <fields>
            <alias>FinServ_FinancialAccount_c</alias>
            <dataType>Text</dataType>
            <name>FinServ__FinancialAccount__c</name>
        </fields>
        <fields>
            <alias>FinServ_RelatedAccount_c</alias>
            <dataType>Text</dataType>
            <name>FinServ__RelatedAccount__c</name>
        </fields>
        <fields>
            <alias>FinServ_Role_c</alias>
            <dataType>Text</dataType>
            <name>FinServ__Role__c</name>
        </fields>
        <label>FinServ_FinancialAccountRole_c</label>
        <name>FinServ_FinancialAccountRole_c</name>
        <sourceName>FinServ__FinancialAccountRole__c</sourceName>
        <type>StandardObject</type>
    </datasources>
    <description>Client-level summary of the number of all associated Financial Accounts where Client is designated as a Joint Owner on Financial Account Role. Multiple Joint Owners supported.</description>
    <executionPlatformType>CRMA</executionPlatformType>
    <filters>
        <criteria>
            <operator>Equals</operator>
            <sequence>1</sequence>
            <sourceFieldName>FinServ_Role_c</sourceFieldName>
            <value>Joint Owner</value>
        </criteria>
        <filterCondition>1</filterCondition>
        <isDynamicFilter>false</isDynamicFilter>
        <label>FinServ_FinancialAccountRole_c_Filter</label>
        <name>FinServ_FinancialAccountRole_c_Filter</name>
        <sourceName>FinServ_FinancialAccountRole_c</sourceName>
    </filters>
    <isTemplate>false</isTemplate>
    <joins>
        <fields>
            <alias>FinancialAccountCount</alias>
            <sourceFieldName>FinancialAccountCount</sourceFieldName>
            <sourceName>aggregate_FinServ_FinancialAccount_c</sourceName>
        </fields>
        <fields>
            <alias>Id</alias>
            <sourceFieldName>Id</sourceFieldName>
            <sourceName>Account</sourceName>
        </fields>
        <joinKeys>
            <primarySourceFieldName>Id</primarySourceFieldName>
            <secondarySourceFieldName>FinServ_RelatedAccount_c</secondarySourceFieldName>
        </joinKeys>
        <label>Account_aggregate_FinServ_FinancialAccount_c</label>
        <name>Account_aggregate_FinServ_FinancialAccount_c</name>
        <primarySourceName>Account</primarySourceName>
        <secondarySourceName>aggregate_FinServ_FinancialAccount_c</secondarySourceName>
        <type>LeftOuter</type>
    </joins>
    <label>RBLForFARForTotalNumberOfAccountsJointOwner</label>
    <processType>DataProcessingEngine</processType>
    <status>Inactive</status>
    <writebacks>
        <fields>
            <runtimeParameter>false</runtimeParameter>
            <sourceFieldName>Id</sourceFieldName>
            <targetFieldName>Id</targetFieldName>
        </fields>
        <fields>
            <runtimeParameter>false</runtimeParameter>
            <sourceFieldName>FinancialAccountCount</sourceFieldName>
            <targetFieldName>FinServ__TotalNumberOfFinAccountsJointOwner__c</targetFieldName>
        </fields>
        <isChangedRow>false</isChangedRow>
        <label>RBLForFARForTotalNumberOfAccountsJointOwnerExport</label>
        <name>RBLForFARForTotalNumberOfAccountsJointOwnerExport</name>
        <operationType>Update</operationType>
        <sourceName>Account_aggregate_FinServ_FinancialAccount_c</sourceName>
        <storageType>sObject</storageType>
        <targetObjectName>Account</targetObjectName>
        <writebackSequence>1</writebackSequence>
        <writebackUser>0057Q00000A7swHQAR</writebackUser>
    </writebacks>
</BatchCalcJobDefinition>
