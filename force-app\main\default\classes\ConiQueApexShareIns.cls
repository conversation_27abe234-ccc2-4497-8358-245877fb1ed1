/**
 * @File Name         : ConiQueApexShareIns.cls
 * @Description       :
 * <AUTHOR> <EMAIL>
 * @Group             :
 * @Last Modified On  : 29-10-2024
 * @Last Modified By  : <EMAIL>
 **/
public with sharing class ConiQueApexShareIns /*implements Queueable*/ {
    @TestVisible
    private static String jsonString { get; set; }

    // private List<SObject> lstShares;

    // public ConiQueApexShareIns(List<SObject> lstSObjectShares) {

    //     this.lstShares = lstSObjectShares;
    // }

    // public void execute(QueueableContext qc) {

    //     if (!lstShares.isEmpty()) {
    //         System.debug('lstShares = ' + lstShares);
    //         try {
    //             Boolean isSuccess = true;
    //             Database.SaveResult[] lstInsRsl = Database.insert(lstShares, true);
    //             for (Database.SaveResult sr : lstInsRsl) {
    //                 if (!sr.isSuccess()) {
    //                     System.debug('##### DEV ===>>> Insert KO!');
    //                     isSuccess = false;
    //                 }
    //                 else {
    //                     System.debug('##### DEV ===>>> sr: ' + sr.Id);
    //                 }
    //             }
    //             if (isSuccess) {
    //                 System.debug('Statement insert with success!');
    //                 //ConiQueUpdGroups updGroups = new ConiQueUpdGroups(jsonString);
    //                 //System.enqueueJob(updGroups);
    //             }
    //         } catch(Exception e) {
    //             System.debug('The following exception has occurred: ' + e.getMessage());
    //         }
    //     }
    // }
}
