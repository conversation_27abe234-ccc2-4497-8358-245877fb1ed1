/**
 * @description Utility Class for ManageAccountAccountRelationBatch
 * <AUTHOR>
 * @since       30.04.2025
 */
public with sharing class ManageAccountAccountRelationUtility {
    public static void manageAgencyAAR(List<FinServ__AccountAccountRelation__c> listAgencyAAR, WrapperShare wrpShare) {
        for (FinServ__AccountAccountRelation__c aar : listAgencyAAR) {
            FinServ__AccountAccountRelation__Share accAccRelAgencyShare = new FinServ__AccountAccountRelation__Share();
            accAccRelAgencyShare.ParentId = aar.Id;
            accAccRelAgencyShare.UserOrGroupId = wrpShare.mapGroupAgency.get(
                Label.AgencyExtIdAAR + aar.FinServ__RelatedAccount__r.ExternalId__c
            );
            accAccRelAgencyShare.AccessLevel = 'Read';
            wrpShare.listShare.add(accAccRelAgencyShare);
        }
    }

    public static void manageSocietyAAR(
        List<FinServ__AccountAccountRelation__c> listSocietyAAR,
        WrapperShare wrpShare
    ) {
        for (FinServ__AccountAccountRelation__c aar : listSocietyAAR) {
            FinServ__AccountAccountRelation__Share accAccRelAgencyShare = new FinServ__AccountAccountRelation__Share();
            accAccRelAgencyShare.ParentId = aar.Id;
            accAccRelAgencyShare.UserOrGroupId = wrpShare.mapGroupSociety.get(
                aar.FinServ__RelatedAccount__r.ExternalId__c
            );
            accAccRelAgencyShare.AccessLevel = 'Read';
            wrpShare.listShare.add(accAccRelAgencyShare);
            AccountShare accShare = new AccountShare();
            accShare.AccountId = aar.FinServ__Account__c;
            accShare.UserOrGroupId = wrpShare.mapGroupSociety.get(aar.FinServ__RelatedAccount__r.ExternalId__c);
            accShare.AccountAccessLevel = 'Read';
            accShare.OpportunityAccessLevel = 'None';
            wrpShare.listAccountShare.add(accShare);
        }
    }

    public static void manageAgencySocietyAAR(
        List<FinServ__AccountAccountRelation__c> listAgencySocietyAAR,
        WrapperShare wrpShare
    ) {
        for (FinServ__AccountAccountRelation__c aar : listAgencySocietyAAR) {
            FinServ__AccountAccountRelation__Share accAccRelAgencySocietyShare = new FinServ__AccountAccountRelation__Share();
            accAccRelAgencySocietyShare.ParentId = aar.Id;
            accAccRelAgencySocietyShare.UserOrGroupId = wrpShare.mapGroupAgencySociety.get(
                aar.FinServ__Account__r.ExternalId__c
            );
            accAccRelAgencySocietyShare.AccessLevel = 'Read';
            wrpShare.listShare.add(accAccRelAgencySocietyShare);
        }
    }

    public static void manageGroup(Set<String> groupValue, WrapperShare wrpShare) {
        for (Group gr : [SELECT Id, DeveloperName FROM Group WHERE DeveloperName IN :groupValue WITH USER_MODE]) {
            if (gr.DeveloperName.startsWith(Label.AgencyExtIdAAR)) {
                wrpShare.mapGroupAgency.put(gr.DeveloperName, gr.Id);
            } else if (gr.DeveloperName.startsWith('AGE_')) {
                wrpShare.mapGroupAgencySociety.put(gr.DeveloperName, gr.Id);
            } else {
                wrpShare.mapGroupSociety.put(gr.DeveloperName, gr.Id);
            }
        }
    }

    public class WrapperShare {
        public List<FinServ__AccountAccountRelation__Share> listShare;
        public List<AccountShare> listAccountShare;
        public Map<String, Id> mapGroupAgency;
        public Map<String, Id> mapGroupSociety;
        public Map<String, Id> mapGroupAgencySociety;
    }
}
