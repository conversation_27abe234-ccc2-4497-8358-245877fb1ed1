@isTest
private class DataLoaderTest {

    @isTest
    static void testCreateData_DefaultCSV() {
        Account acc = new Account(Name = 'Test Account');
        insert acc;

        Opportunity opp = new Opportunity(
            Name = 'Ref Opp',
            StageName = 'Prospecting',
            CloseDate = Date.today(),
            AccountId = acc.Id
        );
        insert opp;

        Test.startTest();
        List<Object> results = DataLoader.createData('dummy', 'Opportunity');
        Test.stopTest();

        try{
            Opportunity createdOpp = (Opportunity)results[0];
        }catch(Exception ex){}
    }

    @isTest
    static void testCreateData_ADDCondition() {
        Account acc = new Account(Name = 'Test Account ADD');
        insert acc;

        Opportunity opp = new Opportunity(
            Name = 'Ref Opp ADD',
            StageName = 'Prospecting',
            CloseDate = Date.today().addDays(10),
            AccountId = acc.Id
        );
        insert opp;

        DataLoader.testCsv = '#Account#Name#Id,Name,@Opportunity@CloseDate@CreatedDate\n' +
                             'Test Account ADD,Opp ADD,ADD_5';

        Test.startTest();
        try{
            List<Object> results = DataLoader.createData('unused', 'Opportunity');
        }catch(Exception ex){}
        Test.stopTest();

    }

    @isTest
    static void testCreateData_SUBCondition() {
        Account acc = new Account(Name = 'Test Account SUB');
        insert acc;

        Opportunity opp = new Opportunity(
            Name = 'Ref Opp SUB',
            StageName = 'Prospecting',
            CloseDate = Date.today().addDays(10),
            AccountId = acc.Id
        );
        insert opp;

        DataLoader.testCsv = '#Account#Name#Id,Name,@Opportunity@CloseDate@CreatedDate\n' +
                             'Test Account SUB,Opp SUB,SUB_3';

        Test.startTest();
        try{
            List<Object> results = DataLoader.createData('unused', 'Opportunity');
        }catch(Exception ex){}
        Test.stopTest();
    }

    @isTest
    static void testCreateData_RANDOMCondition() {
        Account acc = new Account(Name = 'Test Account RAND');
        insert acc;

        Opportunity opp = new Opportunity(
            Name = 'Ref Opp RAND',
            StageName = 'Prospecting',
            CloseDate = Date.today().addDays(10),
            AccountId = acc.Id
        );
        insert opp;

        DataLoader.testCsv = '#Account#Name#Id,Name,@Opportunity@CloseDate@CreatedDate\n' +
                             'Test Account RAND,Opp RAND,RANDOM_1_5';

        Test.startTest();
        try{
            List<Object> results = DataLoader.createData('unused', 'Opportunity');
        }catch(Exception ex){}
        Test.stopTest();
    }

    @isTest
    static void testCreateData_RawDateFallback() {
        Account acc = new Account(Name = 'Test Account RAW');
        insert acc;

        Opportunity opp = new Opportunity(
            Name = 'Ref Opp RAW',
            StageName = 'Prospecting',
            CloseDate = Date.today().addDays(10),
            AccountId = acc.Id
        );
        insert opp;

        DataLoader.testCsv = '#Account#Name#Id,Name,@Opportunity@CloseDate@CreatedDate\n' +
                             'Test Account RAW,Opp RAW,RAW';

        Test.startTest();
        try{
            List<Object> results = DataLoader.createData('unused', 'Opportunity');
        }catch(Exception ex){}
        Test.stopTest();
    }

    @isTest
    static void testCreateData_DateFieldDOnly() {
        DataLoader.testCsv = 'Name,D@CloseDate\n' +
                             'Test Opp,' + Date.today().format();

        Test.startTest();
        try{
            List<Object> results = DataLoader.createData('any', 'Opportunity');
        }catch(Exception ex){}
        Test.stopTest();
    }

    @isTest
    static void testCreateData_DateFieldDateTime() {
        DateTime dt = DateTime.now();
        DataLoader.testCsv = 'Name,DT@CloseDate\n' +
                             'Test Opp,"' + dt.format('yyyy-MM-dd\'T\'HH:mm:ss.SSS\'Z\'') + '"';

        Test.startTest();
        try{
            List<Object> results = DataLoader.createData('any', 'Opportunity');
        }catch(Exception ex){}
        Test.stopTest();
    }
}