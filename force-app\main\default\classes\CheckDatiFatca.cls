/**
 * @File Name         : CheckDatiFatca.cls
 * @Description       : 
 * <AUTHOR> <EMAIL>
 * @Group             : 
 * @Last Modified On  : 26-11-2024
 * @Last Modified By  : <EMAIL>
@cicd_tests CheckDatiFatcaTest
**/


global with sharing class CheckDatiFatca implements System.Callable{
    public static final String ND = 'N/D';
    public static final String Z404 = 'Z404';
    public static final String USA = 'USA';
    public static final String AMMI = 'AMMI';
    public static final List<String> tipologiaSocietaFatcaListNotGiin = new List<String>{'3', '4', '5', '6'};

    public Object call(String action, Map<String, Object> args) {
        //System.debug('***Start Call Method : ' + action);
        Map<String, Object> input = (Map<String, Object>)args.get('input');
        Map<String, Object> output = (Map<String, Object>)args.get('output');
        Map<String, Object> options = (Map<String, Object>)args.get('options');

        Object result = invokeMethod(action, input, output,options);
        //System.debug('///Result: ' + result);
        
        return result;
    }

    global Boolean invokeMethod(String methodName, Map<String,Object> inputMap, Map<String,Object> outMap, Map<String,Object> options){
        
        Boolean result = true;
        try{
            if(methodName == 'checkModal'){
                checkModal ('checkModal', inputMap, outMap, options);
            }        
            else if(methodName == 'deleteResidenza'){
                deleteResidenza ('deleteResidenza', inputMap, outMap, options);
            }
            else if(methodName == 'checkModalPG'){
                checkModalPG ('checkModalPG', inputMap, outMap, options);
            }
            else if(methodName == 'insertResidenza'){
                insertResidenza ('insertResidenza', inputMap, outMap, options);
            }
            else if(methodName == 'modifyFatcaPf'){
                modifyFatcaPf ('modifyFatcaPf', inputMap, outMap, options);
            }
            else if(methodName == 'modifyFatcaPg'){
                modifyFatcaPg ('modifyFatcaPg', inputMap, outMap, options);
            }
            else if(methodName == 'modifyResidenza'){
                modifyResidenza ('modifyResidenza', inputMap, outMap, options);
            }
            else if(methodName == 'insertFatcaPF'){
                insertFatcaPF ('insertFatcaPF', inputMap, outMap, options);
            }
            else if(methodName == 'insertFatcaPG'){
                insertFatcaPG ('insertFatcaPG', inputMap, outMap, options);
            }
        }catch(Exception e){
            //System.debug('Error: ' + e.getMessage() + 'line: ' + e.getLineNumber());
            outMap.put('success', false);
            outMap.put('errorType', 0);
            outMap.put('message', 'Errore generico');
            result = false;
        }
        
        
        return result;
    }

    
    private void checkModal(String methodName, Map<String,Object> inputMap, Map<String,Object> outMap, Map<String,Object> options){
        
        try{
            System.debug('methodName: ' + methodName);
            //System.debug('inputMap: ' + inputMap);
            
            DatiFatcaPF datiFatcaPF = new DatiFatcaPF();
            datiFatcaPF = (DatiFatcaPF) JSON.deserialize(JSON.serialize(inputMap), DatiFatcaPF.class);
            //System.debug('datiFatcaPF: ' + datiFatcaPF);

            datiFatcaPF.CittadinanzaUsaError = false;
            datiFatcaPF.ResidenzaFiscaleUsaError = false;
            datiFatcaPF.TinUSAError = false;
            datiFatcaPF.StatoCittadinanzeError = false;

            List<Cittadinanza> cittadinanze = new List<Cittadinanza>();
            cittadinanze = datiFatcaPF.Cittadinanze != null ? datiFatcaPF.Cittadinanze : new List<Cittadinanza>();
            //System.debug('cittadinanze: ' + cittadinanze);

            List<String> cittadinzeString = new List<String>();
            String actualCittadinanzeString = '';
            String valueCitt = '';
            for(Cittadinanza c : cittadinanze){
                cittadinzeString.add(c.codice);
                valueCitt += c.descrizione + '; ';
                actualCittadinanzeString += c.codice + ';';
            }
            valueCitt = valueCitt != '' ? valueCitt.substring(0, valueCitt.length() - 2) : '';
            datiFatcaPF.ActualCittadinanzeString = actualCittadinanzeString != '' ? actualCittadinanzeString.substring(0, actualCittadinanzeString.length() - 1) : '';
            datiFatcaPF.ActualCittadinanze = cittadinzeString;
            datiFatcaPF.StringaCittadinanze = valueCitt;
            //System.debug('cittadinzeString: ' + cittadinzeString);
            //System.debug('valueCitt: ' + valueCitt);

            //Check cittadinanza estera usa
            Boolean checkUsa = false;
            
            List<Estero> listEstero = new List<Estero>();
            listEstero = datiFatcaPF.Estero != null ? datiFatcaPF.Estero : new List<Estero>();
            //System.debug('listEstero: ' + listEstero );
            
            if (!listEstero.isEmpty() && listEstero.size() == 1 && String.isBlank(listEstero[0].CodiceBelfiore)  ) {
                //System.debug('Svuota la lista estero');
                datiFatcaPF.Estero = new List<Estero>();
            } 

            for(String citt : cittadinzeString){
                if('536'.equalsIgnoreCase(citt)){
                    checkUsa = true;
                    break;
                }
            }

            //System.debug('checkUsa: ' + checkUsa);
            datiFatcaPF.DisableCittadinanzaUSA = false;
            datiFatcaPF.DisableResidenzaUSA = false;
            datiFatcaPF.DisableResidenzaEstero = false;
            datiFatcaPF.NifTin = false;
            datiFatcaPF.DisableNifTin = true;

            //Check residenza fiscale USA
            if(checkUsa || datiFatcaPF.PoteriFirmaUSA){
                //datiFatcaPF.DisableResidenzaUSA = false;
                datiFatcaPF.ResidenzaFiscaleUSA = true;
            }else{
                //datiFatcaPF.DisableResidenzaUSA = true;
            }
            
            //Check residenza fiscale estera
            if(!checkUsa || datiFatcaPF.PoteriFirmaEstero){
                //datiFatcaPF.DisableResidenzaEstero = false;
                datiFatcaPF.ResidenzaFiscaleEstero = true;
            }else{    
                //datiFatcaPF.DisableResidenzaEstero = true;
            }

            
            //System.debug('datiFatcaPF: ' + datiFatcaPF);
            outMap.put('response', JSON.serialize(datiFatcaPF));
        }catch(Exception e){
            //System.debug('Error: ' + e.getMessage() + 'line: ' + e.getLineNumber());
        }
    }

    private void checkModalPG(String methodName, Map<String,Object> inputMap, Map<String,Object> outMap, Map<String,Object> options){

        //System.debug('inputMap: ' + inputMap);
        System.debug('methodName: ' + methodName);
        //System.debug('options: ' + options);
        try{
            DatiFatcaPG datiFatcaPG = new DatiFatcaPG();
            datiFatcaPG = (DatiFatcaPG) JSON.deserialize(JSON.serialize(inputMap), DatiFatcaPG.class);
            //System.debug('datiFatcaPG: ' + datiFatcaPG);
            List<Estero> estero = new List<Estero>();
            estero = datiFatcaPG.Estero;
            
            String giin = String.valueOf(inputMap.get('Giin')) != null ? String.valueOf(inputMap.get('Giin')) : '';
            if(String.isNotBlank(giin) && (giin.length() == 19)){
                datiFatcaPG.Giin1 = giin.substring(0, 6); 
                datiFatcaPG.Giin2 = giin.substring(7, 12);
                datiFatcaPG.Giin3 = giin.substring(13, 15);
                datiFatcaPG.Giin4 = giin.substring(16, 19);

            }else{
                datiFatcaPG.Giin1 = '';
                datiFatcaPG.Giin2 = '';
                datiFatcaPG.Giin3 = '';
                datiFatcaPG.Giin4 = '';
            }

            for(Estero e : datiFatcaPG.Estero){
                //System.debug('e: ' + e);
                if(String.isNotBlank(String.valueOf(e.NumeroIdentificazioneFiscale))){
                    e.FlagIdentificazione = true;
                }
            }
            
            //System.debug('datiFatcaPG.TipoIndirizzo: ' + datiFatcaPG.TipoIndirizzo);
            if(String.isNotBlank(datiFatcaPG.TipoIndirizzo) && AMMI.equalsIgnoreCase(datiFatcaPG.TipoIndirizzo)){
                datiFatcaPG.FlagSedeLegale = true;
            }else{
                datiFatcaPG.FlagSedeLegale = false;
            }

            //System.debug('TipologiaSocietaFatca: ' + datiFatcaPG.TipologiaSocietaFatca);
            if(tipologiaSocietaFatcaListNotGiin.contains(datiFatcaPG.TipologiaSocietaFatca)){
                datiFatcaPG.FlagGiin = true;
                datiFatcaPG.MinLenghtGiin = 0;
            }else{
                datiFatcaPG.FlagGiin = false;
                datiFatcaPG.MinLenghtGiin = 19;
            }

            if(datiFatcaPG.ResidenzaFiscaleUsa){
                datiFatcaPG.MinLenghtTin = 9;
            }else{
                datiFatcaPG.MinLenghtTin = 0;
            }
            
            List<Estero> listEstero = new List<Estero>();
            listEstero = datiFatcaPG.Estero != null ? datiFatcaPG.Estero : new List<Estero>();
            //System.debug('listEstero: ' + listEstero );
            
            if (!listEstero.isEmpty() && listEstero.size() == 1 && String.isBlank(listEstero[0].CodiceBelfiore)  ) {
                //System.debug('Svuota la lista estero');
                datiFatcaPG.Estero = new List<Estero>();
            }

            List<Option> statiEstero = new List<Option>();
            for(Option o : datiFatcaPG.OptionsStati){
                //System.debug('o: ' + o);
                if(!'Z000'.equalsIgnoreCase(o.value) && !'Z404'.equalsIgnoreCase(o.value) ){
                    statiEstero.add(o);
                }
            }

            datiFatcaPG.OptionsStati = statiEstero;
            
            for(Estero e : datiFatcaPG.Estero){
                e.OptionsStati = statiEstero;
            }
            datiFatcaPG.NifTin = false;
            datiFatcaPG.DisableNifTin = true; 
            
            outMap.put('response', JSON.serialize(datiFatcaPG));
        }catch(Exception e){
            //System.debug('Error: ' + e.getMessage() + 'line: ' + e.getLineNumber());
        }
        
    }

    private void insertResidenza(String methodName, Map<String,Object> inputMap, Map<String,Object> outMap, Map<String,Object> options){   
        System.debug('methodName: ' + methodName);
        //System.debug('inputMap: ' + Json.serializePretty(inputMap));

        try{

            String username = (String)inputMap.get('Username') != null ? (String)inputMap.get('Username') : '';
            String userId = (String)inputMap.get('UserId') != null ? (String)inputMap.get('UserId') : '';
            //System.debug('username: ' + username + ' - userId: ' + userId);

            String numIde = String.valueOf(inputMap.get('NumIde')) != null? String.valueOf(inputMap.get('NumIde')) : '';
            //System.debug('numIde: ' + numIde);
            String codNazione = String.valueOf(inputMap.get('CodNazione'));
            //System.debug('codNazione: ' + codNazione);
            //System.debug('Request: ' + inputMap.get('Request'));
            //System.debug('NifTin: ' + inputMap.get('NifTin'));
            Boolean nifTin = (Boolean)inputMap.get('NifTin');

            if(nifTin && String.isBlank(numIde)){
                //System.debug('Errore: numero identificazione fiscale non valorizzato');
                outMap.put('success', false);
                outMap.put('errorType', 1);
                outMap.put('message', 'Numero identificazione fiscale non valorizzato');
                //System.debug('outMap: ' + outMap);
                return;
            }
            RequestBodyFatca requestBodyFatca = new RequestBodyFatca();
            requestBodyFatca = (RequestBodyFatca) JSON.deserialize(JSON.serialize(inputMap.get('Request')), RequestBodyFatca.class);

            ResidenzaFiscaleEstera es = new ResidenzaFiscaleEstera();
            es.residenzaFiscaleEstera = codNazione;
            es.numeroIdentificazioneFiscale = numIde;
            List<ResidenzaFiscaleEstera> residenzeFiscaliEstere = new List<ResidenzaFiscaleEstera>();
            residenzeFiscaliEstere = requestBodyFatca.residenzeFiscaliEstere;
            for(ResidenzaFiscaleEstera rfe : residenzeFiscaliEstere){
                if(String.isBlank(rfe.numeroIdentificazioneFiscale)){
                    rfe.numeroIdentificazioneFiscale = '';
                }
            } 
            residenzeFiscaliEstere.add(es);
            //System.debug('residenzeFiscaliEstere: ' + JSON.serializePretty(residenzeFiscaliEstere)  );
            List<String> actualCittadinanze = new List<String>();
            List<Cittadinanza> listCittadinazne = requestBodyFatca.cittadinanze;
            for(Cittadinanza c : listCittadinazne){
                actualCittadinanze.add(c.codice);
            }

            //System.debug('requestBodyFatca: ' + JSON.serialize(requestBodyFatca));

            //System.debug('requestBodyFatca.indirizzo : '+ requestBodyFatca.indirizzo);
            IndirizzoSend indToSend = new IndirizzoSend();
            if(requestBodyFatca.indirizzo != null ){
                indToSend.abbreviazioneProvincia = requestBodyFatca.indirizzo.abbreviazioneProvincia;
                indToSend.codiceBelfioreComune = requestBodyFatca.indirizzo.codiceBelfioreComune;
                indToSend.codiceBelfioreStato = requestBodyFatca.indirizzo.codiceBelfioreStato;
                indToSend.codicePostale = requestBodyFatca.indirizzo.codicePostale;
                indToSend.indirizzoCompleto = requestBodyFatca.indirizzo.indirizzoCompleto;
                indToSend.localita = requestBodyFatca.indirizzo.localita;
                indToSend.numeroCivico = requestBodyFatca.indirizzo.numeroCivico;
                indToSend.tipoIndirizzo = requestBodyFatca.indirizzo.tipoIndirizzo;
                indToSend.tipoNormalizzato = requestBodyFatca.indirizzo.tipoNormalizzato;
                indToSend.id = requestBodyFatca.indirizzo.id;
                
            }else{
                indToSend = null;
            }

            SendBodyFatca sendBodyFatca = new SendBodyFatca();
            sendBodyFatca.id = requestBodyFatca.id;
            sendBodyFatca.compagnia = requestBodyFatca.compagnia;
            sendBodyFatca.ciu = requestBodyFatca.ciu;
            sendBodyFatca.tipologiaSocietaFatca = requestBodyFatca.tipologiaSocietaFatca;
            sendBodyFatca.residenzaFiscaleUSA =  requestBodyFatca.residenzaFiscaleUSA;
            sendBodyFatca.cittadinanzaUSA =  requestBodyFatca.cittadinanzaUSA;
            sendBodyFatca.poteriFirmaUSA = requestBodyFatca.poteriFirmaUSA;
            sendBodyFatca.tin = requestBodyFatca.tin;
            sendBodyFatca.cittadinanze = actualCittadinanze;
            sendBodyFatca.poteriFirma =  requestBodyFatca.poteriFirma;
            sendBodyFatca.residenzaFiscale =  true;
            sendBodyFatca.giin = requestBodyFatca.giin;
            sendBodyFatca.indirizzo = null;
            sendBodyFatca.residenzeFiscaliEstere = residenzeFiscaliEstere;
            sendBodyFatca.potCittadinanzaUsa = requestBodyFatca.potCittadinanzaUsa;
            sendBodyFatca.tipologiaSocietaFatca = requestBodyFatca.tipologiaSocietaFatca;
            sendBodyFatca.userId = userId;
            sendBodyFatca.username = username;

            //System.debug('sendBodyFatca: ' + JSON.serialize(sendBodyFatca));
            outMap.put('response', JSON.serialize(sendBodyFatca));
            outMap.put('success', true);
        }catch(Exception e){
            //System.debug(methodName+ ' Error: ' + e.getMessage() + 'line: ' + e.getLineNumber());
        }

    }

    private void modifyResidenza(String methodName, Map<String,Object> inputMap, Map<String,Object> outMap, Map<String,Object> options){
        System.debug('methodName: ' + methodName);
        //System.debug('inputMap: ' + JSON.serializePretty(inputMap));
        try{

            String username = (String)inputMap.get('Username') != null ? (String)inputMap.get('Username') : '';
            String userId = (String)inputMap.get('UserId') != null ? (String)inputMap.get('UserId') : '';
            //System.debug('username: ' + username + ' - userId: ' + userId);
            RequestBodyFatca requestBodyFatca = new RequestBodyFatca();
            requestBodyFatca = (RequestBodyFatca) JSON.deserialize(JSON.serialize(inputMap.get('Request')), RequestBodyFatca.class);

            //System.debug('requestBodyFatca: ' + requestBodyFatca);
            StatoEstero statoEstero = new StatoEstero();
            statoEstero = (StatoEstero) JSON.deserialize(JSON.serialize(inputMap.get('CodiciNuovoStato')), StatoEstero.class);
            //System.debug('statoEstero: ' + statoEstero);

            String codNazione = String.valueOf(inputMap.get('CodiceBelfiore'));
            String codNazioneOld = String.valueOf(inputMap.get('CodiceBelfioreOld'));
            String flagIdentificazione = String.valueOf (inputMap.get('FlagIdentificazione'));
            String numeroIdentificazioneFiscale = (String.valueOf(inputMap.get('NumeroIdentificazioneFiscale')) != null && !'-'.equals(String.valueOf(inputMap.get('NumeroIdentificazioneFiscale'))))   ? String.valueOf(inputMap.get('NumeroIdentificazioneFiscale')) : '';
            String compagnia = String.valueOf(inputMap.get('Compagnia')) != null ? String.valueOf(inputMap.get('Compagnia')) : '';
            //System.debug('codNazione: ' + codNazione);
            //System.debug('codNazioneOld: ' + codNazioneOld);
            //System.debug('flagIdentificazione: ' + flagIdentificazione);
            //System.debug('numeroIdentificazioneFiscale: ' + numeroIdentificazioneFiscale);
            //System.debug('requestBodyFatca: ' + requestBodyFatca.residenzeFiscaliEstere);
            //System.debug('compagnia: ' + compagnia);
            
            if('true'.equalsIgnoreCase(flagIdentificazione)  && (String.isBlank(numeroIdentificazioneFiscale) || '-'.equalsIgnoreCase(numeroIdentificazioneFiscale))){
                //System.debug('Errore: numero identificazione fiscale non valorizzato');
                outMap.put('success', false);
                outMap.put('errorType', 0);
                outMap.put('message', 'Numero identificazione fiscale non valorizzato');
                return;
            }

            List<String> actualCittadinanze = new List<String>();
            List<Cittadinanza> listCittadinazne = requestBodyFatca.cittadinanze;
            for(Cittadinanza c : listCittadinazne){
                actualCittadinanze.add(c.codice);
            }

            List<ResidenzaFiscaleEstera> residenzeFiscaliEstere = new List<ResidenzaFiscaleEstera>();
            residenzeFiscaliEstere = requestBodyFatca.residenzeFiscaliEstere;

            for(ResidenzaFiscaleEstera r : residenzeFiscaliEstere){
                if(r.residenzaFiscaleEstera == codNazioneOld){
                    r.residenzaFiscaleEstera = codNazione;
                    r.numeroIdentificazioneFiscale = numeroIdentificazioneFiscale;
                }
            }
            //System.debug('residenzeFiscaliEstere: ' + residenzeFiscaliEstere);
            //System.debug('requestBodyFatca: ' + JSON.serialize(requestBodyFatca));

            IndirizzoSend indToSend = new IndirizzoSend();
            if(requestBodyFatca.indirizzo != null ){
                indToSend.abbreviazioneProvincia = requestBodyFatca.indirizzo.abbreviazioneProvincia;
                indToSend.codiceBelfioreComune = requestBodyFatca.indirizzo.codiceBelfioreComune;
                indToSend.codiceBelfioreStato = requestBodyFatca.indirizzo.codiceBelfioreStato;
                indToSend.codicePostale = requestBodyFatca.indirizzo.codicePostale;
                indToSend.indirizzoCompleto = requestBodyFatca.indirizzo.indirizzoCompleto;
                indToSend.localita = requestBodyFatca.indirizzo.localita;
                indToSend.numeroCivico = requestBodyFatca.indirizzo.numeroCivico;
                indToSend.tipoIndirizzo = requestBodyFatca.indirizzo.tipoIndirizzo;
                indToSend.tipoNormalizzato = requestBodyFatca.indirizzo.tipoNormalizzato;
                indToSend.id = requestBodyFatca.indirizzo.id;
                
            }else{
                indToSend = null;
            }

            SendBodyFatca sendBodyFatca = new SendBodyFatca();
            sendBodyFatca.id = requestBodyFatca.id;
            sendBodyFatca.compagnia = compagnia;
            sendBodyFatca.ciu = requestBodyFatca.ciu;
            sendBodyFatca.tipologiaSocietaFatca = requestBodyFatca.tipologiaSocietaFatca;
            sendBodyFatca.residenzaFiscaleUSA =  requestBodyFatca.residenzaFiscaleUSA;
            sendBodyFatca.cittadinanzaUSA =  requestBodyFatca.cittadinanzaUSA;
            sendBodyFatca.poteriFirmaUSA = requestBodyFatca.poteriFirmaUSA;
            sendBodyFatca.tin = requestBodyFatca.tin;
            sendBodyFatca.cittadinanze = actualCittadinanze;
            sendBodyFatca.poteriFirma =  requestBodyFatca.poteriFirma;
            sendBodyFatca.residenzaFiscale =  requestBodyFatca.residenzaFiscale;
            sendBodyFatca.giin = requestBodyFatca.giin;
            sendBodyFatca.indirizzo = null;
            sendBodyFatca.residenzeFiscaliEstere = residenzeFiscaliEstere;
            sendBodyFatca.potCittadinanzaUsa = requestBodyFatca.potCittadinanzaUsa;
            sendBodyFatca.tipologiaSocietaFatca = requestBodyFatca.tipologiaSocietaFatca;
            sendBodyFatca.userId = userId;
            sendBodyFatca.username = username;

            //System.debug('sendBodyFatca: ' + JSON.serialize(sendBodyFatca));
            outMap.put('response', JSON.serialize(sendBodyFatca));
            outMap.put('success', true);

        }catch(Exception e){
            //System.debug(methodName+ ' Error: ' + e.getMessage() + 'line: ' + e.getLineNumber());
        }
    }

    private void deleteResidenza(String methodName, Map<String,Object> inputMap, Map<String,Object> outMap, Map<String,Object> options){
        System.debug('methodName: ' + methodName);
        //System.debug('inputMap: ' + JSON.serializePretty(inputMap));
        try{

            String username = (String)inputMap.get('Username') != null ? (String)inputMap.get('Username') : '';
            String userId = (String)inputMap.get('UserId') != null ? (String)inputMap.get('UserId') : '';
            //System.debug('username: ' + username + ' - userId: ' + userId);
            String codNazione = String.valueOf(inputMap.get('CodNazione'));
            //System.debug('codNazione: ' + codNazione);
            RequestBodyFatca requestBodyFatca = new RequestBodyFatca();
            requestBodyFatca = (RequestBodyFatca) JSON.deserialize(JSON.serialize(inputMap.get('Request')), RequestBodyFatca.class);
            List<ResidenzaFiscaleEstera> residenzeFiscaliEstere = new List<ResidenzaFiscaleEstera>();
            residenzeFiscaliEstere = requestBodyFatca.residenzeFiscaliEstere;

            for(Integer i = 0; i < residenzeFiscaliEstere.size(); i++){
                ResidenzaFiscaleEstera es = residenzeFiscaliEstere[i];
                //System.debug('es: ' + es);

                es.numeroIdentificazioneFiscale = es.numeroIdentificazioneFiscale != null ? es.numeroIdentificazioneFiscale : '';
                if(es.residenzaFiscaleEstera == codNazione){
                    residenzeFiscaliEstere.remove(i);
                }
            }

            List<String> actualCittadinanze = new List<String>();
            List<Cittadinanza> listCittadinazne = requestBodyFatca.cittadinanze;
            for(Cittadinanza c : listCittadinazne){
                actualCittadinanze.add(c.codice);
            }

            IndirizzoSend indToSend = new IndirizzoSend();
            if(requestBodyFatca.indirizzo != null ){
                indToSend.abbreviazioneProvincia = requestBodyFatca.indirizzo.abbreviazioneProvincia;
                indToSend.codiceBelfioreComune = requestBodyFatca.indirizzo.codiceBelfioreComune;
                indToSend.codiceBelfioreStato = requestBodyFatca.indirizzo.codiceBelfioreStato;
                indToSend.codicePostale = requestBodyFatca.indirizzo.codicePostale;
                indToSend.indirizzoCompleto = requestBodyFatca.indirizzo.indirizzoCompleto;
                indToSend.localita = requestBodyFatca.indirizzo.localita;
                indToSend.numeroCivico = requestBodyFatca.indirizzo.numeroCivico;
                indToSend.tipoIndirizzo = requestBodyFatca.indirizzo.tipoIndirizzo;
                indToSend.tipoNormalizzato = requestBodyFatca.indirizzo.tipoNormalizzato;
                indToSend.id = requestBodyFatca.indirizzo.id;
                
            }else{
                indToSend = null;
            }

            SendBodyFatca sendBodyFatca = new SendBodyFatca();
            sendBodyFatca.id = requestBodyFatca.id;
            sendBodyFatca.compagnia = requestBodyFatca.compagnia;
            sendBodyFatca.ciu = requestBodyFatca.ciu;
            sendBodyFatca.tipologiaSocietaFatca = requestBodyFatca.tipologiaSocietaFatca;
            sendBodyFatca.residenzaFiscaleUSA =  requestBodyFatca.residenzaFiscaleUSA;
            sendBodyFatca.cittadinanzaUSA =  requestBodyFatca.cittadinanzaUSA;
            sendBodyFatca.poteriFirmaUSA = requestBodyFatca.poteriFirmaUSA;
            sendBodyFatca.tin =  '-'.equalsIgnoreCase(requestBodyFatca.tin) ? null : requestBodyFatca.tin;
            sendBodyFatca.cittadinanze = actualCittadinanze;
            sendBodyFatca.poteriFirma =  requestBodyFatca.poteriFirma;
            sendBodyFatca.residenzaFiscale =  residenzeFiscaliEstere.isEmpty() ? false : true;
            sendBodyFatca.giin = requestBodyFatca.giin;
            sendBodyFatca.indirizzo = null;
            sendBodyFatca.residenzeFiscaliEstere = residenzeFiscaliEstere;
            sendBodyFatca.potCittadinanzaUsa = requestBodyFatca.potCittadinanzaUsa;
            sendBodyFatca.tipologiaSocietaFatca = requestBodyFatca.tipologiaSocietaFatca;
            sendBodyFatca.userId = userId;
            sendBodyFatca.username = username;

            
            //System.debug('sendBodyFatca: ' + JSON.serialize(sendBodyFatca));
            outMap.put('response', JSON.serialize(sendBodyFatca));
        }catch(Exception e){
            //System.debug(methodName+ ' Error: ' + e.getMessage() + 'line: ' + e.getLineNumber());
        }
    }

    private void modifyFatcaPf(String methodName, Map<String,Object> inputMap, Map<String,Object> outMap, Map<String,Object> options){
        System.debug('methodName: ' + methodName);
        //System.debug('inputMap: ' + JSON.serializePretty(inputMap));
        //System.debug('Request: ' + inputMap.get('Request'));
        try{

            String username = (String)inputMap.get('Username') != null ? (String)inputMap.get('Username') : '';
            String userId = (String)inputMap.get('UserId') != null ? (String)inputMap.get('UserId') : '';
            //System.debug('username: ' + username + ' - userId: ' + userId);
            RequestBodyFatca requestBodyFatca = new RequestBodyFatca();
            requestBodyFatca = (RequestBodyFatca) JSON.deserialize(JSON.serialize(inputMap.get('Request')), RequestBodyFatca.class);
            //System.debug('requestBodyFatca: ' + requestBodyFatca);

            String accountDetailId = String.valueOf(inputMap.get('AccountDetailId'));
            //System.debug('accountDetailId: ' + accountDetailId);
            Boolean cittadinanzaUSA = (Boolean)inputMap.get('CittadinanzaUSA');
            //System.debug('cittadinanzaUSA: ' + cittadinanzaUSA);
            Boolean residenzaFiscaleUSA = (Boolean)inputMap.get('ResidenzaFiscaleUSA');
            //System.debug('residenzaFiscaleUSA: ' + residenzaFiscaleUSA);
            Boolean poteriFirmaUSA = (Boolean)inputMap.get('PoteriFirmaUSA');
            //System.debug('poteriFirmaUSA: ' + poteriFirmaUSA);
            String tinUSA = String.valueOf(inputMap.get('TinUSA')) != null ? String.valueOf(inputMap.get('TinUSA')) : '';
            //System.debug('tinUSA: ' + tinUSA);
            ////System.debug('tinUSA.length(): ' + tinUSA.length());
            //System.debug('String.isNotBlank(tinUSA): ' + String.isNotBlank(tinUSA));
            String cittadinaze = String.valueOf(inputMap.get('ActualCittadinanze'));
            ////System.debug('cittadinaze: ' + cittadinaze);
            List<String> actualCittadinanze = cittadinaze != null ? cittadinaze.split(';') : new List<String>();
            Boolean poteriFirmaEstero = (Boolean)inputMap.get('PoteriFirmaEstero');
            Boolean residenzaFiscaleEstero = (Boolean)inputMap.get('ResidenzaFiscaleEstero');
            List<Cittadinanza> cittadinanzeList = (List<Cittadinanza>)JSON.deserialize(JSON.serialize(inputMap.get('AllCittadinanze')), List<Cittadinanza>.class);
            //System.debug('actualCittadinanze: ' + actualCittadinanze);
            //System.debug('actualCittadinanze.size(): ' + actualCittadinanze.size());

            List<ResidenzaFiscaleEstera> residenzeFiscaliEstere = new List<ResidenzaFiscaleEstera>();
            residenzeFiscaliEstere = requestBodyFatca.residenzeFiscaliEstere;
            for(ResidenzaFiscaleEstera rfe : residenzeFiscaliEstere){
                if(String.isBlank(rfe.numeroIdentificazioneFiscale)){
                    rfe.numeroIdentificazioneFiscale = '';
                }
            } 

            List<AccountDetails__c> accdetail = [SELECT Id, BirthCountry__c, Country__c, Relation__r.FinServ__RelatedAccount__r.Name, SourceSystemIdentifier__c FROM AccountDetails__c WHERE Id = :accountDetailId LIMIT 1];

            SendBodyFatca sendBodyFatca = new SendBodyFatca();
            sendBodyFatca.id = requestBodyFatca.id;
            sendBodyFatca.compagnia = requestBodyFatca.compagnia;
            sendBodyFatca.ciu = requestBodyFatca.ciu;
            sendBodyFatca.tipologiaSocietaFatca = requestBodyFatca.tipologiaSocietaFatca;
            sendBodyFatca.residenzaFiscaleUSA = residenzaFiscaleUSA != null ? residenzaFiscaleUSA : requestBodyFatca.residenzaFiscaleUSA;
            sendBodyFatca.cittadinanzaUSA = cittadinanzaUSA != null ? cittadinanzaUSA : requestBodyFatca.cittadinanzaUSA;
            sendBodyFatca.poteriFirmaUSA = poteriFirmaUSA != null ? poteriFirmaUSA : requestBodyFatca.poteriFirmaUSA;
            sendBodyFatca.tin = tinUSA != null ? tinUSA : requestBodyFatca.tin;
            sendBodyFatca.cittadinanze = actualCittadinanze;
            sendBodyFatca.poteriFirma = poteriFirmaEstero != null ? poteriFirmaEstero : requestBodyFatca.poteriFirma;
            sendBodyFatca.residenzaFiscale = residenzaFiscaleEstero != null ? residenzaFiscaleEstero : requestBodyFatca.residenzaFiscale;
            sendBodyFatca.giin = requestBodyFatca.giin;
            sendBodyFatca.indirizzo = null;
            sendBodyFatca.residenzeFiscaliEstere = residenzeFiscaliEstere;
            sendBodyFatca.potCittadinanzaUsa = requestBodyFatca.potCittadinanzaUsa;
            sendBodyFatca.tipologiaSocietaFatca = requestBodyFatca.tipologiaSocietaFatca;
            sendBodyFatca.userId = userId;
            sendBodyFatca.username = username;

            //System.debug('sendBodyFatca: ' + JSON.serialize(sendBodyFatca));
            //System.debug('sendBodyFatca.cittadinanzaUSA: ' + sendBodyFatca.cittadinanzaUSA);
            Boolean checkError = false;
            String message = '';
            Integer errorType = 0;
            
            /*
            //System.debug('actualCittadinanze: ' + actualCittadinanze);
            if((!cittadinanzaUSA && accdetail[0] != null && ( 'USA'.equalsIgnoreCase(accdetail[0].Country__c) ||  actualCittadinanze.contains(Z404))) || (!cittadinanzaUSA && actualCittadinanze.contains('536')) ){
                //System.debug('Errore 1 : Cittadinanza negli U.S.A. / possesso Green Card : dati non congruenti con informazioni anagrafiche');
                requestBodyFatca.CittadinanzaUsaError = true;
                message = 'Cittadinanza negli U.S.A. / possesso Green Card : dati non congruenti con informazioni anagrafiche';
                errorType = 1;
                checkError = true;
                
                
            }    
            
            else if(!residenzaFiscaleUSA  && cittadinanzaUSA ){
                requestBodyFatca.ResidenzaFiscaleUsaError = true;
                //System.debug('Errore 2 : Residenza Fiscale U.S.A. : dati non congruenti con informazioni anagrafiche');
                message = 'Residenza Fiscale U.S.A. : dati non congruenti con informazioni anagrafiche';
                errorType = 2;
                checkError = true;
                
            }*/
            if((cittadinanzaUSA || residenzaFiscaleUSA) && (!String.isNotBlank(tinUSA) || (tinUSA.length() < 9))){
                requestBodyFatca.TinUSAError = true;
                //System.debug('Errore 3 : Richiesto codice TIN');
                message = 'Richiesto codice TIN';
                errorType = 3;
                checkError = true;
               
            }
            else if(actualCittadinanze.size()<1 || actualCittadinanze.size()>3){
                requestBodyFatca.StatoCittadinanzeError = true;
                //System.debug('Errore 4 : Inserire da 1 a 3 cittadinanze');
                message = 'Inserire da 1 a 3 cittadinanze';    
                errorType = 4;           
                checkError = true;
                
            }

            if(poteriFirmaUSA){
                //System.debug('check poteriFirmaUSAFlagInput true');
                sendBodyFatca.cittadinanzaUSA = true;
            }  
            
            if(poteriFirmaEstero){   
                sendBodyFatca.residenzaFiscale = true; 
            }
            
            //System.debug('check errori : ' + checkError);
            if(checkError){
                outMap.put('success', false);
                outMap.put('errorType', errorType);
                outMap.put('message', message);
                outMap.put('response', JSON.serialize(requestBodyFatca));    
                //System.debug('outMap :' + outMap);
            }else{
                outMap.put('response', JSON.serialize(sendBodyFatca)); 
                outMap.put('success', true);    
            }
            
          
            //outMap.put('response', JSON.serialize(sendBodyFatca));
            ////System.debug('response: ' + JSON.serialize(sendBodyFatca));


        }catch(Exception e){
            //System.debug(methodName+ ' Error: ' + e.getMessage() + 'line: ' + e.getLineNumber());
            outMap.put('success', false);
            outMap.put('errorType', 0);
            outMap.put('message', 'Errore generico');
        }
    }

    private void modifyFatcaPg(String methodName, Map<String,Object> inputMap, Map<String,Object> outMap, Map<String,Object> options){
        System.debug('methodName: ' + methodName);
        System.debug('inputMap: ' + JSON.serializePretty(inputMap));
        //System.debug('Request: ' + inputMap.get('Request'));

        try{

            Boolean checkError = true;

            String username = (String)inputMap.get('Username') != null ? (String)inputMap.get('Username') : '';
            String userId = (String)inputMap.get('UserId') != null ? (String)inputMap.get('UserId') : '';
            System.debug('username: ' + username + ' - userId: ' + userId);

            RequestBodyFatcaPg requestBodyFatcaPg = new RequestBodyFatcaPg();
            requestBodyFatcaPg = (RequestBodyFatcaPg) JSON.deserialize(JSON.serialize(inputMap.get('Request')), RequestBodyFatcaPg.class);
            System.debug('requestBodyFatcaPg: ' + requestBodyFatcaPg);

            IndirizzoNormWrapper indirizzoNorm = new IndirizzoNormWrapper();
            indirizzoNorm = (IndirizzoNormWrapper) JSON.deserialize(JSON.serialize(inputMap.get('IndirizzoNorm')), IndirizzoNormWrapper.class);
            System.debug('indirizzoNorm: ' + indirizzoNorm);

            Boolean residenzaFiscaleUsa = (Boolean)inputMap.get('ResidenzaFiscaleUsa');
            System.debug('residenzaFiscaleUsa: ' + residenzaFiscaleUsa);
            Boolean residenzaFiscale = (Boolean)inputMap.get('ResidenzaFiscaleStatoEstero');
            System.debug('residenzaFiscale: ' + residenzaFiscale);
            String tin = String.valueOf(inputMap.get('Tin')) != null ? String.valueOf(inputMap.get('Tin')) : '';
            System.debug('tin: ' + tin);
            String giin = String.valueOf(inputMap.get('Giin') ) != null ? String.valueOf(inputMap.get('Giin')) : '';
            System.debug('giin: ' + giin);
            String stato = String.valueOf(inputMap.get('Stato')) != null ? String.valueOf(inputMap.get('Stato')) : '';
            System.debug('stato: ' + stato);
            String tipologiaSocietaFatca = String.valueOf(inputMap.get('TipologiaSocietaFatca')) != null ? String.valueOf(inputMap.get('TipologiaSocietaFatca')) : '';
            System.debug('tipologiaSocietaFatca: ' + tipologiaSocietaFatca);
            
            
            if(String.isBlank(tin) || '-'.equalsIgnoreCase(tin)){
                tin = null;
            }else if(String.isNotBlank(tin) && tin.length() != 9){
                checkError = false;
                outMap.put('success', false);
                outMap.put('errorType', 2);
                outMap.put('message', 'Il TIN deve essere di 9 caratteri');
            }

            // Fix 1344137
            if(residenzaFiscaleUsa && (String.isBlank(tin) || '-'.equalsIgnoreCase(tin))){
                outMap.put('success', false);
                outMap.put('errorType', 2);
                outMap.put('message', 'Il TIN è obbligatorio');
                System.debug('Error: Il TIN è obbligatorio');
                checkError = false;
            }else if(residenzaFiscaleUsa && String.isNotBlank(tin) && tin.length() != 9){
                outMap.put('success', false);
                outMap.put('errorType', 2);
                outMap.put('message', 'Il TIN deve essere composto da 9 caratteri');
                System.debug('Error: Il TIN deve essere composto da 9 caratteri');
                checkError = false;
            }
            
            if(String.isBlank(giin) || '-'.equalsIgnoreCase(giin)){
                giin = null;
            }else if(String.isNotBlank(giin) && (giin.length() != 0 && giin.length() != 19)){
                checkError = false;
                outMap.put('success', false);
                outMap.put('errorType', 1);
                outMap.put('message', 'Il GIIN deve essere di 19 caratteri');
            }

            

            if(checkError){
                List<Cittadinanza> cittadinanzeList = requestBodyFatcaPg.cittadinanze;
                List<String> cittadinze = new List<String>();
                for(Cittadinanza c : cittadinanzeList){
                    cittadinze.add(c.codice);
                }

                IndirizzoSend indToSend = new IndirizzoSend();
                if(indirizzoNorm != null){
                    indToSend.abbreviazioneProvincia = indirizzoNorm.siglaProvincia;
                    indToSend.codiceBelfioreComune = indirizzoNorm.codiceBelfioreComune;
                    indToSend.codiceBelfioreStato = stato != null ? stato : 'Z000'; 
                    indToSend.codicePostale = indirizzoNorm.codiceTerritoriale;
                    indToSend.indirizzoCompleto = indirizzoNorm.tipoOdonimo + ' ' + indirizzoNorm.nomeOdonimo;
                    indToSend.localita = indirizzoNorm.descrizioneLocalita;
                    indToSend.numeroCivico = indirizzoNorm.civico.trim();
                    indToSend.tipoIndirizzo = 'AMMI';
                    indToSend.tipoNormalizzato = 'S';
                    if(requestBodyFatcaPg.indirizzo != null && requestBodyFatcaPg.indirizzo.id != null){
                        indToSend.id = requestBodyFatcaPg.indirizzo.id;
                    }
                    
                }

                List<ResidenzaFiscaleEstera> residenzeFiscaliEstere = new List<ResidenzaFiscaleEstera>();
                residenzeFiscaliEstere = requestBodyFatcaPg.residenzeFiscaliEstere;
                for(ResidenzaFiscaleEstera rfe : residenzeFiscaliEstere){
                    if(String.isBlank(rfe.numeroIdentificazioneFiscale)){
                        rfe.numeroIdentificazioneFiscale = '';
                    }
                } 

                SendBodyFatca sendBodyFatca = new SendBodyFatca();
                sendBodyFatca.id = requestBodyFatcaPg.id;
                sendBodyFatca.compagnia = requestBodyFatcaPg.compagnia;
                sendBodyFatca.ciu = requestBodyFatcaPg.ciu;
                sendBodyFatca.tipologiaSocietaFatca = tipologiaSocietaFatca!= null ? tipologiaSocietaFatca : requestBodyFatcaPg.tipologiaSocietaFatca;
                sendBodyFatca.residenzaFiscaleUSA = residenzaFiscaleUsa != null ? residenzaFiscaleUsa : requestBodyFatcaPg.residenzaFiscaleUSA;
                sendBodyFatca.cittadinanzaUSA = requestBodyFatcaPg.cittadinanzaUSA;
                sendBodyFatca.poteriFirmaUSA = requestBodyFatcaPg.poteriFirmaUSA;
                sendBodyFatca.tin = tin != null ? tin : requestBodyFatcaPg.tin;
                sendBodyFatca.cittadinanze = cittadinze;
                sendBodyFatca.poteriFirma = requestBodyFatcaPg.poteriFirma;
                sendBodyFatca.residenzaFiscale = residenzaFiscale != null ? residenzaFiscale : requestBodyFatcaPg.residenzaFiscale;
                sendBodyFatca.giin = giin;
                sendBodyFatca.indirizzo = indToSend;
                sendBodyFatca.residenzeFiscaliEstere = residenzeFiscaliEstere;
                sendBodyFatca.userId =  userId;
                sendBodyFatca.username = username;

                System.debug('sendBodyFatca: ' + JSON.serializePretty(sendBodyFatca));
                outMap.put('response', JSON.serialize(sendBodyFatca));
                outMap.put('success', true);  
            }
              


        }catch(Exception e){
            System.debug(methodName+ ' Error: ' + e.getMessage() + 'line: ' + e.getLineNumber());
            outMap.put('success', false);
            outMap.put('errorType', 0);
            outMap.put('message', 'Errore generico');
        }
    }

    private void insertFatcaPF(String methodName, Map<String,Object> inputMap, Map<String,Object> outMap, Map<String,Object> options){
        System.debug('methodName: ' + methodName);
        //System.debug('inputMap: ' + JSON.serializePretty(inputMap));
        //System.debug('Request: ' + inputMap.get('Request'));
        try{

            // Recupera i valori dalla mappa 'Request'
            Map<String, Object> requestMap = (Map<String, Object>)inputMap.get('Request');
            String username = (String)inputMap.get('Username') != null ? (String)inputMap.get('Username') : '';
            String userId = (String)inputMap.get('UserId') != null ? (String)inputMap.get('UserId') : '';
            //System.debug('username: ' + username + 'userId: ' + userId);
            // Assegna i valori alle variabili
            String actualCittadinanzeString = (String)requestMap.get('ActualCittadinanzeString');
            Boolean cittadinanzaUSA = (Boolean)requestMap.get('CittadinanzaUSA');
            Boolean residenzaFiscaleUSA = (Boolean)requestMap.get('ResidenzaFiscaleUSA');
            Boolean poteriFirmaUSA = (Boolean)requestMap.get('PoteriFirmaUSA');
            Boolean poteriFirmaEstero = (Boolean)requestMap.get('PoteriFirmaEstero');
            Boolean residenzaFiscaleEstero = (Boolean)requestMap.get('ResidenzaFiscaleEstero');
            String tinUSA = (String)requestMap.get('TinUSA');
            Integer ciu = (Integer)requestMap.get('Ciu');
            String compagnia = (String)requestMap.get('Compagnia');
           
            /*
            Boolean cittadinanzaUSA = (Boolean)inputMap.get('CittadinanzaUSA');
            Boolean residenzaFiscaleUSA = (Boolean)inputMap.get('ResidenzaFiscaleUSA');
            Boolean poteriFirmaUSA = (Boolean)inputMap.get('PoteriFirmaUSA');
            String tinUSA = String.valueOf(inputMap.get('TinUSA')) != null ? String.valueOf(inputMap.get('TinUSA')) : '';
            String actualCittadinanzeString = String.valueOf(inputMap.get('ActualCittadinanzeString')) != null ? String.valueOf(inputMap.get('ActualCittadinanzeString')) : '';
            Boolean poteriFirmaEstero = (Boolean)inputMap.get('PoteriFirmaEstero');
            Boolean residenzaFiscaleEstero = (Boolean)inputMap.get('ResidenzaFiscaleEstero'); 
            String compagnia = String.valueOf(inputMap.get('Compagnia')) != null ? String.valueOf(inputMap.get('Compagnia')) : '';
            String ciu = String.valueOf(inputMap.get('Ciu')) != null ? String.valueOf(inputMap.get('Ciu')) : '';
            Integer trasformCiu  = ciu != '' ? Integer.valueOf(ciu) : null; */
            
            //System.debug('CittadinanzaUSA: ' + cittadinanzaUSA);
            //System.debug('ResidenzaFiscaleUSA: ' + residenzaFiscaleUSA);
            //System.debug('PoteriFirmaUSA: ' + poteriFirmaUSA);
            //System.debug('TinUSA: ' + tinUSA);
            //System.debug('ActualCittadinanzeString: ' + actualCittadinanzeString);
            //System.debug('PoteriFirmaEstero: ' + poteriFirmaEstero);
            //System.debug('ResidenzaFiscaleEstero: ' + residenzaFiscaleEstero);  

            List<String> actualCittadinanze = (actualCittadinanzeString != null ) ? actualCittadinanzeString.split(';') : new List<String>();

            SendBodyFatca sendBodyFatca = new SendBodyFatca();
            
            sendBodyFatca.compagnia = compagnia;
            sendBodyFatca.ciu = ciu;
            sendBodyFatca.tipologiaSocietaFatca = '0';
            sendBodyFatca.residenzaFiscaleUSA =  residenzaFiscaleUSA != null ? residenzaFiscaleUSA : false;
            sendBodyFatca.cittadinanzaUSA =  cittadinanzaUSA != null ? cittadinanzaUSA : false;
            sendBodyFatca.poteriFirmaUSA = poteriFirmaUSA != null ? poteriFirmaUSA : false;
            sendBodyFatca.tin = (tinUSA != null || tinUSA != '-') ? tinUSA : '';
            sendBodyFatca.cittadinanze = actualCittadinanze;
            sendBodyFatca.poteriFirma =  poteriFirmaEstero != null ? poteriFirmaEstero : false;
            sendBodyFatca.residenzaFiscale =  residenzaFiscaleEstero != null ? residenzaFiscaleEstero : false;
            sendBodyFatca.giin = null;
            sendBodyFatca.indirizzo = null;
            sendBodyFatca.residenzeFiscaliEstere = new List<ResidenzaFiscaleEstera>();
            sendBodyFatca.potCittadinanzaUsa = false;
            sendBodyFatca.userId = userId;
            sendBodyFatca.username = username;

            //System.debug('sendBodyFatca: ' + JSON.serialize(sendBodyFatca));

            Boolean checkError = false;
            String message = '';
            Integer errorType = 0;

            if(cittadinanzaUSA && residenzaFiscaleUSA && (!String.isNotBlank(tinUSA) || (tinUSA.length() < 9))){
                //System.debug('Errore 3 : Richiesto codice TIN');
                message = 'Richiesto codice TIN';
                errorType = 3;
                checkError = true;
               
            }
            else if(actualCittadinanze.size()<1 || actualCittadinanze.size()>3){
                //System.debug('Errore 4 : Inserire da 1 a 3 cittadinanze');
                message = 'Inserire da 1 a 3 cittadinanze';    
                errorType = 4;           
                checkError = true;
                
            }

            if(poteriFirmaUSA){
                //System.debug('check poteriFirmaUSAFlagInput true');
                sendBodyFatca.cittadinanzaUSA = true;
            }  
            
            if(poteriFirmaEstero){   
                sendBodyFatca.residenzaFiscale = true; 
            }
            
            //System.debug('check errori : ' + checkError);
            if(checkError){
                outMap.put('success', false);
                outMap.put('errorType', errorType);
                outMap.put('message', message);
                //System.debug('outMap :' + outMap);
            }else{
                outMap.put('response', JSON.serialize(sendBodyFatca)); 
                outMap.put('success', true);    
            }
            

        }catch(Exception e){
            //System.debug(methodName+ ' Error: ' + e.getMessage() + 'line: ' + e.getLineNumber());
            outMap.put('success', false);
            outMap.put('errorType', 0);
            outMap.put('message', 'Errore generico');
        }
    }

    private void insertFatcaPG(String methodName, Map<String,Object> inputMap, Map<String,Object> outMap, Map<String,Object> options){
        System.debug('methodName: ' + methodName);
        System.debug('inputMap: ' + JSON.serializePretty(inputMap));

        try{

            String userId = inputMap.containsKey('UserId') ? String.valueOf(inputMap.get('UserId')) : '';
            String username = inputMap.containsKey('Username') ? String.valueOf(inputMap.get('Username')) : '';

            
            IndirizzoNormWrapper indirizzoNorm = new IndirizzoNormWrapper();
            System.debug('IndirizzoNorm: ' + inputMap.get('IndirizzoNorm'));
            indirizzoNorm = (IndirizzoNormWrapper) JSON.deserialize(JSON.serialize(inputMap.get('IndirizzoNorm')), IndirizzoNormWrapper.class);
            System.debug('indirizzoNorm: ' + indirizzoNorm);

            Boolean flegSedeLegale = false; // Valore predefinito
            if (inputMap.containsKey('FlagSedeLegale')) {
                Object flegSedeLegaleValue = inputMap.get('FlagSedeLegale');
                if (flegSedeLegaleValue instanceof Boolean) {
                    flegSedeLegale = (Boolean)flegSedeLegaleValue;
                } else if (flegSedeLegaleValue instanceof String) {
                    flegSedeLegale = 'true'.equalsIgnoreCase((String)flegSedeLegaleValue);
                }
            }
            System.debug('flegSedeLegale: ' + flegSedeLegale);

            List<Indirizzo> listIndirizzi = new List<Indirizzo>();
            //System.debug('AllIndirizzi: ' + inputMap.get('AllIndirizzi'));
            //System.debug('Test 1: ' + (inputMap.get('AllIndirizzi') != null) + ' - Test 2: ' + !String.isBlank(String.valueOf(inputMap.get('AllIndirizzi'))));
            if(inputMap.get('AllIndirizzi') != null && !String.isBlank(String.valueOf(inputMap.get('AllIndirizzi'))) ){
                listIndirizzi = (List<Indirizzo>) JSON.deserialize(JSON.serialize(inputMap.get('AllIndirizzi')), List<Indirizzo>.class);
                System.debug('listIndirizzi: ' + listIndirizzi);
            }

            

            Boolean residenzaFiscaleUsa = false; // Valore predefinito
            if (inputMap.containsKey('ResidenzaFiscaleUsa')) {
                Object residenzaFiscaleUsaValue = inputMap.get('ResidenzaFiscaleUsa');
                if (residenzaFiscaleUsaValue instanceof Boolean) {
                    residenzaFiscaleUsa = (Boolean)residenzaFiscaleUsaValue;
                } else if (residenzaFiscaleUsaValue instanceof String) {
                    residenzaFiscaleUsa = 'true'.equalsIgnoreCase((String)residenzaFiscaleUsaValue);
                }
            }
            //System.debug('residenzaFiscaleUsa: ' + residenzaFiscaleUsa);
           
            Boolean residenzaFiscale = false; // Valore predefinito
            if (inputMap.containsKey('ResidenzaFiscaleStatoEstero')) {
                Object residenzaFiscaleValue = inputMap.get('ResidenzaFiscaleStatoEstero');
                if (residenzaFiscaleValue instanceof Boolean) {
                    residenzaFiscale = (Boolean)residenzaFiscaleValue;
                } else if (residenzaFiscaleValue instanceof String) {
                    residenzaFiscale = 'true'.equalsIgnoreCase((String)residenzaFiscaleValue);
                }
            }


            String tin = String.valueOf(inputMap.get('Tin'));
            System.debug('tin: ' + tin);
            String giin = String.valueOf(inputMap.get('Giin'));
            //System.debug('giin: ' + giin);
            if('-'.equalsIgnoreCase(giin)){
                giin = null;
            }
            String tipologiaSocietaFatca = String.valueOf(inputMap.get('TipologiaSocietaFatca')) != null ? String.valueOf(inputMap.get('TipologiaSocietaFatca')) : '';
            //System.debug('tipologiaSocietaFatca: ' + tipologiaSocietaFatca);
            String stato = String.valueOf(inputMap.get('Stato')) != null ? String.valueOf(inputMap.get('Stato')) : '';
            //System.debug('stato: ' + stato);
            Integer ciu = Integer.valueOf(inputMap.get('Ciu'));
            //System.debug('ciu: ' + ciu);
            String compagnia = String.valueOf(inputMap.get('Compagnia'));
            //System.debug('compagnia: ' + compagnia);

            Boolean check = true;
            if(giin != null && (giin.length() != 0 && giin.length() != 19)){
                outMap.put('success', false);
                outMap.put('errorType', 1);
                outMap.put('message', 'Il Giin deve essere composto da 19 caratteri');
                check = false;
            }
            
            // Fix 1344137
            if(residenzaFiscaleUsa && (String.isBlank(tin) || '-'.equalsIgnoreCase(tin))){
                outMap.put('success', false);
                outMap.put('errorType', 2);
                //outMap.put('message', 'Il TIN è obbligatorio');
                System.debug('Error: Il TIN è obbligatorio');
                check = false;
            }else if(residenzaFiscaleUsa && String.isNotBlank(tin) && tin.length() != 9){
                outMap.put('success', false);
                outMap.put('errorType', 2);
                //outMap.put('message', 'Il TIN deve essere composto da 9 caratteri');
                System.debug('Error: Il TIN deve essere composto da 9 caratteri');
                check = false;
            }



            if(check){
                IndirizzoSend indToSend = new IndirizzoSend();
                Indirizzo indirizzoSele = new Indirizzo();
                if(flegSedeLegale){
                    for(Indirizzo ind : listIndirizzi){
                        //System.debug('indirizzo: ' + ind.tipoIndirizzo);
                        if('SELE'.equalsIgnoreCase(ind.tipoIndirizzo)){
                            indirizzoSele = ind;
                            //System.debug('indirizzoSele: ' + indirizzoSele);
                            indToSend.abbreviazioneProvincia = indirizzoSele.abbreviazioneProvincia;
                            indToSend.codiceBelfioreComune = indirizzoSele.codiceBelfioreComune;
                            indToSend.codiceBelfioreStato = indirizzoSele.codiceBelfioreStato != null ? indirizzoSele.codiceBelfioreStato : 'Z000';
                            indToSend.codicePostale = indirizzoSele.codicePostale;
                            indToSend.indirizzoCompleto = indirizzoSele.indirizzoCompleto;
                            indToSend.localita = indirizzoSele.localita;
                            indToSend.numeroCivico = indirizzoSele.numeroCivico != null ? indirizzoSele.numeroCivico.trim() : '';
                            indToSend.tipoIndirizzo = indirizzoSele.tipoIndirizzo;
                            indToSend.tipoNormalizzato = indirizzoSele.tipoNormalizzato;
                            indToSend.id = indirizzoSele.id;
                            break;
                        }
                    }   

                }
                else if(indirizzoNorm != null){
                    indToSend.abbreviazioneProvincia = indirizzoNorm.siglaProvincia;
                    indToSend.codiceBelfioreComune = indirizzoNorm.codiceBelfioreComune;
                    indToSend.codiceBelfioreStato = stato != null ? stato : 'Z000';
                    indToSend.codicePostale = indirizzoNorm.codiceTerritoriale;
                    indToSend.indirizzoCompleto = indirizzoNorm.tipoOdonimo + ' ' + indirizzoNorm.nomeOdonimo;
                    indToSend.localita = indirizzoNorm.descrizioneLocalita;
                    indToSend.numeroCivico = indirizzoNorm.civico;
                    indToSend.tipoIndirizzo =  flegSedeLegale? 'SELE' : 'AMMI';
                    indToSend.tipoNormalizzato = 'S';
                }else{
                    indToSend = null;
                }

                SendBodyFatca sendBodyFatca = new SendBodyFatca();
                sendBodyFatca.compagnia = compagnia;
                sendBodyFatca.ciu = ciu;
                sendBodyFatca.tipologiaSocietaFatca = tipologiaSocietaFatca;
                sendBodyFatca.residenzaFiscaleUSA = residenzaFiscaleUsa != null ? residenzaFiscaleUsa : null;
                sendBodyFatca.cittadinanzaUSA = false;
                sendBodyFatca.poteriFirmaUSA = false;
                sendBodyFatca.tin = tin != null ? tin : null;
                sendBodyFatca.cittadinanze = new List<String>();
                sendBodyFatca.poteriFirma = false;
                sendBodyFatca.residenzaFiscale = residenzaFiscale != null ? residenzaFiscale : null;
                sendBodyFatca.giin = giin;
                sendBodyFatca.indirizzo =  flegSedeLegale ? null : indToSend;
                sendBodyFatca.residenzeFiscaliEstere =  new List<ResidenzaFiscaleEstera>();
                sendBodyFatca.potCittadinanzaUsa = false;
                sendBodyFatca.userId = userId;
                sendBodyFatca.username = username;

                System.debug('sendBodyFatca: ' + JSON.serializePretty(sendBodyFatca));
                outMap.put('response', JSON.serialize(sendBodyFatca));
                outMap.put('success', true); 
            }

        }catch(Exception e){
            //System.debug(methodName+ ' Error: ' + e.getMessage() + 'line: ' + e.getLineNumber());
            outMap.put('success', false);
            outMap.put('errorType', 0);
            outMap.put('message', 'Errore generico');
        }
    }
    
    public class Estero {
        public Integer Iterazioni { get; set; }
        public String CodiceABI { get; set; }
        public String CodiceBelfiore { get; set; }
        public String CodiceBelfioreOld { get; set; }
        public Boolean Attivo { get; set; }
        public String NumeroIdentificazioneFiscale { get; set; }
        public Boolean FlagIdentificazione { get; set; }
        public String Nazione { get; set; }
        public Boolean UnioneEuropea { get; set; }
        public String Ciu { get; set; }
        public String Compagnia { get; set; }
        public List<Option> OptionsStati { get; set; }
        public String RecordId { get; set; }
    }
    
    public class DatiFatcaPF {
        public List<String> ActualCittadinanze { get; set; }
        public String ActualCittadinanzeString { get; set; }
        public String Ciu { get; set; }
        public String CodiceFiscale { get; set; }
        public List<Cittadinanza> Cittadinanze { get; set; }
        public Boolean CittadinanzaUSA { get; set; }
        public Boolean CittadinanzaUsaError { get; set; }
        public String Compagnia { get; set; }
        public Boolean DisableCittadinanzaUSA { get; set; }
        public Boolean DisableResidenzaEstero { get; set; }
        public Boolean DisableResidenzaUSA { get; set; }
        public List<Estero> Estero { get; set; }
        public Integer Id { get; set; }
        public Boolean PaeseDiNascitaUSA { get; set; }
        public String PaeseDiNascita { get; set; }
        public String PaeseDiResidenza { get; set; }
        public Boolean PotCittadinanzaUsa { get; set; }
        public Boolean PoteriFirmaEstero { get; set; }
        public Boolean PoteriFirmaUSA { get; set; }
        public Boolean ResidenzaFiscaleEstero { get; set; }
        public Boolean ResidenzaFiscaleUsaError { get; set; }
        public Boolean ResidenzaFiscaleUSA { get; set; }
        public List<Option> OptionsCittadinanze { get; set; }
        public List<Option> OptionsStati { get; set; }
        public Boolean StatoCittadinanzeError { get; set; }
        public String StringaCittadinanze { get; set; }
        public String TinUSA { get; set; }
        public Boolean TinUSAError { get; set; }
        public String TipologiaSocietaFatca { get; set; }
        public Boolean NifTin { get; set; }
        public Boolean DisableNifTin { get; set; }
        public String RecordId { get; set; }

    }

    public class RequestBodyFatca {
        public Integer id { get; set; }
        public String compagnia { get; set; }
        public Integer ciu { get; set; }
        public String tipologiaSocietaFatca { get; set; }
        public Boolean residenzaFiscaleUSA { get; set; }
        public Boolean residenzaFiscale { get; set; }
        public Boolean  cittadinanzaUSA { get; set; }
        public List<Cittadinanza> cittadinanze { get; set; }
        //public List<String> cittadinanze { get; set; }
        public Boolean poteriFirmaUSA { get; set; }
        public Boolean poteriFirma { get; set; }
        public Boolean potCittadinanzaUsa { get; set; }
        public String tin { get; set; }
        public String giin { get; set; }
        public Indirizzo indirizzo { get; set; }
        List<ResidenzaFiscaleEstera> residenzeFiscaliEstere { get; set; }
        public Boolean CittadinanzaUsaError { get; set; }
        public Boolean ResidenzaFiscaleUsaError { get; set; }
        public Boolean TinUSAError { get; set; }
        public Boolean StatoCittadinanzeError { get; set; }
    }

    public class SendBodyFatca {
        public Integer id { get; set; }
        public String compagnia { get; set; }
        public Integer ciu { get; set; }
        public String tipologiaSocietaFatca { get; set; }
        public Boolean residenzaFiscaleUSA { get; set; }
        public Boolean residenzaFiscale { get; set; }
        public Boolean  cittadinanzaUSA { get; set; }
        //public List<Cittadinanza> cittadinanze { get; set; }
        public List<String> cittadinanze { get; set; }
        public Boolean poteriFirmaUSA { get; set; }
        public Boolean poteriFirma { get; set; }
        public Boolean potCittadinanzaUsa { get; set; }
        public String tin { get; set; }
        public String giin { get; set; }
        public IndirizzoSend indirizzo { get; set; }
        //public String indirizzo { get; set; }
        List<ResidenzaFiscaleEstera> residenzeFiscaliEstere { get; set; }
        public String userId { get; set; }
        public String username { get; set; }
        
    }

    public class ResidenzaFiscaleEstera{
        public String residenzaFiscaleEstera { get; set; }
        public String numeroIdentificazioneFiscale { get; set; }
    }

    public class Cittadinanza {
        public String codice { get; set; }
        public String descrizione { get; set; }
       
    }
   
    // Nuova classe innestata DatiFatcaPG
    public class DatiFatcaPG {
        public String CellaCensuaria { get; set; }
        public String Provincia { get; set; }
        public String Stato { get; set; }
        public String IndirizzoBreve { get; set; }
        public String Tin { get; set; }
        public List<Estero> Estero { get; set; }
        public Boolean ResidenzaFiscaleStatoEstero { get; set; }
        public Double Longitudine { get; set; }
        public String CAP { get; set; }
        public String TipologiaSocietaFatca { get; set; }
        public Boolean ResidenzaFiscaleUsa { get; set; }
        public String Comune { get; set; }
        public String Localita { get; set; }
        public Double Latitudine { get; set; }
        public String Ciu { get; set; }
        public String IndirizzoCompleto { get; set; }
        public String Giin { get; set; }
        public String Giin1 { get; set; }
        public String Giin2 { get; set; }
        public String Giin3 { get; set; }
        public String Giin4 { get; set; }
        public String TipoIndirizzo { get; set; }
        public Boolean FlagSedeLegale { get; set; }
        public String CodiceBelfioreComune { get; set; }
        public List<Option> Options { get; set; }
        public List<Option> OptionProvince { get; set; }
        public List<Option> OptionsComuni { get; set; }
        public List<Option> OptionsLocalita { get; set; }
        public List<Option> OptionsStati { get; set; }
        public String SiglaProvincia { get; set; }
        public String NumeroCivico { get; set; }
        public Boolean FlagGiin { get; set; }
        public Integer MinLenghtGiin { get; set; }
        public Integer MinLenghtTin { get; set; }
        public Integer IndirizzoId { get; set; }
        public String Compagnia { get; set; }
        public Boolean NifTin { get; set; }
        public Boolean DisableNifTin { get; set; }
        public String RecordId { get; set; }
    }

    public class Option{
        public String value { get; set; }
        public String label { get; set; }
    }

    public class RequestBodyFatcaPG {
        public List<ResidenzaFiscaleEstera> residenzeFiscaliEstere { get; set; }
        public Indirizzo indirizzo { get; set; }
        public String giin { get; set; }
        public String tin { get; set; }
        public Boolean potCittadinanzaUsa { get; set; }
        public Boolean poteriFirma { get; set; }
        public Boolean poteriFirmaUSA { get; set; }
        public List<Cittadinanza> cittadinanze { get; set; }
        public Boolean cittadinanzaUSA { get; set; }
        public Boolean residenzaFiscale { get; set; }
        public Boolean residenzaFiscaleUSA { get; set; }
        public String tipologiaSocietaFatca { get; set; }
        public Integer ciu { get; set; }
        public String compagnia { get; set; }
        public Integer id { get; set; }
    }

    public class Indirizzo {
        public Integer ciu { get; set; }
        public String compagnia { get; set; }
        public Integer id { get; set; }
        public DatiTracciatura datiTracciatura { get; set; }
        public String distrettoCensuario { get; set; }
        public String cabStatoCod { get; set; }
        public Double latitudine { get; set; }
        public Double longitudine { get; set; }
        public String presso { get; set; }
        public String indirizzoBreve { get; set; }
        public String tipoNormalizzato { get; set; }
        public String indirizzoCompleto { get; set; }
        public String dus { get; set; }
        public String numeroCivico { get; set; }
        public String dug { get; set; }
        public String localita { get; set; }
        public String localitaBreve { get; set; }
        public String codiceBelfioreComune { get; set; }
        public String codicePostale { get; set; }
        public String codiceIstatComune { get; set; }
        public String abbreviazioneProvincia { get; set; }
        public String codiceIstatAnag1 { get; set; }
        public String tipoIndirizzo { get; set; }
        public String codiceBelfioreStato { get; set; }
    }

    public class DatiTracciatura {
        public String proceduraChiamante { get; set; }
        public String applicazioneChiamante { get; set; }
        public String sottosistemaAggiornamento { get; set; }
        public String sistemaAggiornamento { get; set; }
        public String compagniaAggiornamento { get; set; }
        public String canaleAggiornamento { get; set; }
        public String sottosistemaCreazione { get; set; }
        public String sistemaCreazione { get; set; }
        public String compagniaCreazione { get; set; }
        public String canaleCreazione { get; set; }
        public String usernameUltimoAggiornamento { get; set; }
        public String userIdUltimoAggiornamento { get; set; }
        public String dataUltimoAggiornamento { get; set; }
        public String usernameCreazione { get; set; }
        public String userIdCreazione { get; set; }
        public String dataCreazione { get; set; }
    }

    public class IndirizzoNormWrapper {
        public String codiceISTATRegione { get; set; }
        public String codiceUnipolRegione { get; set; }
        public String descrizioneRegione { get; set; }
        public String siglaProvincia { get; set; }
        public String codiceISTATProvincia { get; set; }
        public String descrizioneProvincia { get; set; }
        public String codiceBelfioreComune { get; set; }
        public String codiceISTATComune { get; set; }
        public String codiceCABComune { get; set; }
        public String descrizioneComune { get; set; }
        public String descrizioneLocalita { get; set; }
        public String descrizioneLocalitaAbbreviata { get; set; }
        public String tipoOdonimo { get; set; }
        public String tipoOdonimoAbbreviato { get; set; }
        public String nomeOdonimo { get; set; }
        public String nomeOdonimoAbbreviato { get; set; }
        public String civico { get; set; }
        public String indirizzoBreveLegacy { get; set; }
        public String codiceTerritoriale { get; set; }
        public Double latitudine { get; set; }
        public Double longitudine { get; set; }
        public Integer accuratezza { get; set; }
        public String cellaCensuaria { get; set; }
        public String StatusCode { get; set; }
    }

    public class IndirizzoSend {
        public String abbreviazioneProvincia { get; set; }
        public String codiceBelfioreComune { get; set; }
        public String codiceBelfioreStato { get; set; }
        public String codicePostale { get; set; }
        public String indirizzoCompleto { get; set; }
        public String localita { get; set; }
        public String numeroCivico { get; set; }
        public String tipoIndirizzo { get; set; }
        public String tipoNormalizzato { get; set; }
        public Integer id { get; set; }
    }

    public class StatoEstero {
        public String CodiceABI { get; set; }
        public String Descrizione { get; set; }
        public String CodiceBelfiore { get; set; }
        public Boolean Attivo { get; set; }
        public Boolean Ue { get; set; }
    }
    
}