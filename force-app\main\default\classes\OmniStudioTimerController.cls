/***************************************************************************************************************************************************
* <AUTHOR>
* @description    OmniStudioTimerController class that implements System.Callable and omnistudio.VlocityOpenInterface. This class provides methods to 
*                 manage and retrieve SLA information for Opportunities. It includes functionality to calculate remaining SLA time and format the 
*                 results for use in OmniStudio and Lightning Components.
* @date           2024-01-26
* @group          OmniStudio Timer Controller
****************************************************************************************************************************************************/
global with sharing class OmniStudioTimerController  implements System.Callable, omnistudio.VlocityOpenInterface  { //NOPMD

    public class SLAOmniStudio {
        @AuraEnabled
        public String expiryDate {get; set;}

        @AuraEnabled
        public Integer remainingDays { get; set; }

        @AuraEnabled
        public Integer remainingHours { get; set; }

        @AuraEnabled
        public Integer totalRemainingHours { get; set; }

        public SLAOmniStudio(String expiryDate, Integer remainingDays, Integer remainingHours, Integer totalRemainingHours) { //NOPMD
            this.expiryDate = expiryDate;
            this.remainingDays = remainingDays;
            this.remainingHours = remainingHours;
            this.totalRemainingHours = totalRemainingHours;
        }
    }
    
    public class TimeSLA {
        @AuraEnabled
        public Integer remainingDays { get; set; }

        @AuraEnabled
        public Integer remainingHours { get; set; }

        @AuraEnabled
        public Integer totalRemainingHours { get; set; }

        public TimeSLA(Integer remainingDays, Integer remainingHours, Integer totalRemainingHours) {
            this.remainingDays = remainingDays;
            this.remainingHours = remainingHours;
            this.totalRemainingHours = totalRemainingHours;
        }
    }

    /******************************************************************************************
    * @description  Retrieves the SLA information for the given Opportunity, determining whether it's for "Taken In Charge" or "Working" SLA.
    * @param        opp - Opportunity record
    * @param        isTakenInCharge - Boolean flag to determine SLA type
    * @return       SLAOmniStudio - SLA information formatted for OmniStudio
    *******************************************************************************************/
    @TestVisible
    private static SLAOmniStudio getSLAOmniStudio(Opportunity opp, boolean isTakenInCharge){
        try{
            if(isTakenInCharge) {
                String expiryDate = Datetime.valueOf(opp.TakenInChargeSLAExpiryDate__c).format('dd/MM/yyyy, HH:mm');
                TimeSLA remainingTimeCalendar = calculateCalendarTime(opp, true);
                return new SLAOmniStudio(expiryDate, remainingTimeCalendar.remainingDays, remainingTimeCalendar.remainingHours, remainingTimeCalendar.totalRemainingHours);
            } else {
                String expiryDate = Datetime.valueOf(opp.WorkingSLAExpiryDate__c).format('dd/MM/yyyy, HH:mm');
                TimeSLA remainingTimeCalendar = calculateCalendarTime(opp, false);      
                return new SLAOmniStudio(expiryDate, remainingTimeCalendar.remainingDays, remainingTimeCalendar.remainingHours, remainingTimeCalendar.totalRemainingHours);
            }

        }
        catch(Exception e){
            System.debug('An error has occurred: ' + e.getMessage());
            return null;
        }
    }
    
    /******************************************************************************************
    * @description  Retrieves the Opportunity record based on the given record ID.
    * @param        recordId - ID of the Opportunity record
    * @return       Opportunity - Retrieved Opportunity record
    *******************************************************************************************/
    @TestVisible
    public static Opportunity getOpportunity(String recordId){
        try{
            Id currentId = Id.valueOf(recordId);
            List<Opportunity> oppList = new List<Opportunity>();
            oppList = [ SELECT Id, TakenInChargeSLAExpiryDate__c, TakenInChargeSLAStartDate__c, WorkingSLAStartDate__c, WorkingSLAExpiryDate__c, TakenInChargeDate__c, TakenInChargeSLARemainingHours__c, TakenInChargeSLARemainingDays__c, WorkingSLARemainingDays__c, WorkingSLARemainingHours__c, Agency__r.BusinessHours__c //NOPMD
                        FROM Opportunity
                        WHERE Id =: currentId
                        LIMIT 1 ];

            return (!oppList.isEmpty()) ? oppList.get(0) : null;
        }
        catch(Exception e){
            System.debug('An error has occurred: ' + e.getMessage());
            return null;
        }
    }
    
    /******************************************************************************************
    * @description  Calculates the remaining calendar time for the SLA based on the given Opportunity.
    * @param        opp - Opportunity record
    * @param        isTakenInCharge - Boolean flag to determine SLA type
    * @return       TimeSLA - Remaining time information
    *******************************************************************************************/
    @TestVisible
    private static TimeSLA calculateCalendarTime(Opportunity opp, Boolean isTakenInCharge) {
        Decimal remainingDays = (isTakenInCharge) ?  opp.TakenInChargeSLARemainingDays__c : opp.WorkingSLARemainingDays__c;
        Decimal totalRemainingHours = (isTakenInCharge) ?  opp.TakenInChargeSLARemainingHours__c : opp.WorkingSLARemainingHours__c;
        Decimal remainingHours = getRemainingHours((Integer)totalRemainingHours, false);
        return new TimeSLA((Integer)remainingDays, (Integer)remainingHours, (Integer)totalRemainingHours);
    }
    
    /******************************************************************************************
    * @description  Calculates the remaining hours from the total hours, considering if they are business hours.
    * @param        totalHours - Total remaining hours
    * @param        isBusinessHours - Boolean flag to determine if business hours are considered
    * @return       Decimal - Remaining hours
    *******************************************************************************************/
    @TestVisible
    private static Decimal getRemainingHours(Integer totalHours, Boolean isBusinessHours) {
        Decimal remainingHours = Math.mod(totalHours, (isBusinessHours ? 9 : 24));
        return remainingHours;
    }

    /******************************************************************************************
    * @description  Retrieves the SLA information for "Taken In Charge" SLA of a given Opportunity record.
    * @param        recordId - ID of the Opportunity record
    * @return       SLAOmniStudio - SLA information formatted for OmniStudio
    *******************************************************************************************/
    public SLAOmniStudio getCruscottoTakeInChargeSLA(String recordId) {
        Opportunity opp = getOpportunity(recordId);
        SLAOmniStudio tic = getSLAOmniStudio(opp, true);
        
        return tic;
    }

    /******************************************************************************************
    * @description  Retrieves the SLA information for "Working" SLA of a given Opportunity record.
    * @param        recordId - ID of the Opportunity record
    * @return       SLAOmniStudio - SLA information formatted for OmniStudio
    *******************************************************************************************/
    public SLAOmniStudio getCruscottoWorkingSLA(String recordId) {
        Opportunity opp = getOpportunity(recordId);
        SLAOmniStudio tic = getSLAOmniStudio(opp, false);
        
        return tic;
    }

    /******************************************************************************************
    * @description  Invokes the specified method based on the given action and arguments.
    * @param        action - Name of the method to be invoked
    * @param        args - Arguments for the method
    * @return       Object - Result of the invoked method
    *******************************************************************************************/
    public Object call(String action, Map<String, Object> args) {
        Map<String, Object> input = (Map<String, Object>)args.get('input');
        Map<String, Object> output = (Map<String, Object>)args.get('output');
        Map<String, Object> options = (Map<String, Object>)args.get('options');

        return invokeMethod(action, input, output, options);
    }

    /******************************************************************************************
    * @description  Invokes the method specified by the method name with given inputs, output, and options.
    * @param        methodName - Name of the method to be invoked
    * @param        inputs - Input parameters for the method
    * @param        output - Output parameters to store the results
    * @param        options - Additional options for the method invocation
    * @return       Boolean - True if the method was invoked successfully, false otherwise
    *******************************************************************************************/
    public Boolean invokeMethod(String methodName, Map<String,Object> inputs, Map<String,Object> output, Map<String,Object> options) { //NOPMD
        Boolean result = true;
        try{
            if(methodName.equals('getCruscottoTakeInChargeSLA')){
                SLAOmniStudio tic = this.getCruscottoTakeInChargeSLA((String)inputs.get('recordId'));
                this.setOutputFields(output, tic);
            } else if(methodName.equals('getCruscottoWorkingSLA')){
                SLAOmniStudio w = this.getCruscottoWorkingSLA((String)inputs.get('recordId'));
                this.setOutputFields(output, w);
            } else {
            	throw new System.TypeException('Invalid method name');
            }
        } catch(Exception e){       
            result = false;         
        }

        return result;          
    }

    /******************************************************************************************
    * @description  Sets the output fields with the SLA information.
    * @param        output - Output parameters to store the results
    * @param        os - SLA information
    *******************************************************************************************/
    private void setOutputFields(Map<String,Object> output, SLAOmniStudio os) {
        Boolean displayHours = os.totalRemainingHours < 24;

        output.put('expiryDate', os.expiryDate);
        output.put('remainingDays', os.remainingDays);
        output.put('remainingHours', os.remainingHours);
        output.put('totalRemainingHours', os.totalRemainingHours);

        output.put('calendarRemainingTimeFormated', (os.remainingDays + 'g ' + os.remainingHours + 'h (calendar)'));

        if(displayHours) {
            output.put('displayTime', os.remainingHours);
            output.put('displayTimeText', 'H');                    
        } else {
            output.put('displayTime', os.remainingDays);
            output.put('displayTimeText', 'G');       
        }
    }
}
