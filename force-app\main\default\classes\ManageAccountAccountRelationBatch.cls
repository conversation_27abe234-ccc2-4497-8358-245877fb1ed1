/**
 * @description This Batch create a record share of FinServ__AccountAccountRelation__c
 * <AUTHOR>
 * @version     1.0
 * @since       30.04.2025
 * @testClass   ManageAccountAccountRelationBatchTest
 */
public with sharing class ManageAccountAccountRelationBatch implements Database.Batchable<sObject>, Database.Stateful {
    Date dt;
    Id recordTypeId;
    List<String> agencyList;
    DateTime startDT;
    DateTime endDT;
    
    Id idAccountSociety = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AccountSociety').getRecordTypeId();
    Id idAccountAgency = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AccountAgency').getRecordTypeId();
    Id IdAgencySociety = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AgencySociety').getRecordTypeId();

    public ManageAccountAccountRelationBatch() {
        this.dt = null;
        this.recordTypeId = null;
    }

    public ManageAccountAccountRelationBatch(Object condition) {
        String stringDate = condition?.toString();
        try {
            this.dt = Date.valueOf(stringDate);
            return;
        } catch (Exception ex) {
        }
        try {
            this.dt = Date.parse(stringDate);
            return;
        } catch (Exception ex) {
        }
    }

    public ManageAccountAccountRelationBatch(Object condition, String recordtypeDeveloperName, List<String> agencyList) {
        this.recordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get(recordtypeDeveloperName).getRecordTypeId();
        this.agencyList = agencyList;
        String stringDate = condition?.toString();
        try {
            this.dt = Date.valueOf(stringDate);
            return;
        } catch (Exception ex) {
        }
        try {
            this.dt = Date.parse(stringDate);
            return;
        } catch (Exception ex) {
        }
    }
    
    public ManageAccountAccountRelationBatch(String recordtypeDeveloperName, DateTime startDT, DateTime endDT, List<String> agencyList) {
        this.recordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get(recordtypeDeveloperName).getRecordTypeId();
        this.agencyList = agencyList;
        this.startDT = startDT;
        this.endDT = endDT;
    }

    public Database.QueryLocator start(Database.BatchableContext bc) {
        List<String> conditions = new List<String>();
        String baseQuery = 'SELECT Id, FinServ__Account__c, FinServ__RelatedAccount__r.ExternalId__c, FinServ__Account__r.ExternalId__c, RecordTypeId ' + 'FROM FinServ__AccountAccountRelation__c WHERE ';

        if (dt != null) {
            conditions.add('LastModifiedDate >= :dt');
        } else {
            //conditions.add('LastModifiedDate >= YESTERDAY');
        }

        if (recordTypeId != null) {
            conditions.add('RecordTypeId = :recordTypeId');
        }

        if (agencyList != null && !agencyList.isEmpty()) {
            conditions.add('FinServ__RelatedAccount__r.ExternalId__c IN :agencyList');
        }
        
        if(this.startDT != null){
            conditions.add('LastModifiedDate >= :startDT');
        }
        
        if(this.endDT != null){
            conditions.add('LastModifiedDate <= :endDT');
        }

        String fullQuery = conditions.isEmpty() ? baseQuery.removeEnd(' WHERE ') : baseQuery + String.join(conditions, ' AND ');
        return Database.getQueryLocator(fullQuery);
    }

    public void execute(Database.BatchableContext bc, List<FinServ__AccountAccountRelation__c> scope) {
        ManageAccountAccountRelationUtility.WrapperShare wrpShare = new ManageAccountAccountRelationUtility.WrapperShare();
        wrpShare.listAccountShare = new List<AccountShare>();
        wrpShare.listShare = new List<FinServ__AccountAccountRelation__Share>();
        wrpShare.mapGroupAgency = new Map<String, Id>();
        wrpShare.mapGroupSociety = new Map<String, Id>();
        wrpShare.mapGroupAgencySociety = new Map<String, Id>();
        List<FinServ__AccountAccountRelation__c> listAgencyAAR = new List<FinServ__AccountAccountRelation__c>();
        List<FinServ__AccountAccountRelation__c> listSocietyAAR = new List<FinServ__AccountAccountRelation__c>();
        List<FinServ__AccountAccountRelation__c> listAgencySocietyAAR = new List<FinServ__AccountAccountRelation__c>();
        Set<String> groupValue = new Set<String>();
        for (FinServ__AccountAccountRelation__c aar : scope) {
            if (aar.RecordTypeId == idAccountAgency) {
                listAgencyAAR.add(aar);
                groupValue.add(Label.AgencyExtIdAAR + aar.FinServ__RelatedAccount__r.ExternalId__c);
            } else if (aar.RecordTypeId == idAccountSociety) {
                listSocietyAAR.add(aar);
                groupValue.add(aar.FinServ__RelatedAccount__r.ExternalId__c);
            } else if (aar.RecordTypeId == IdAgencySociety) {
                listAgencySocietyAAR.add(aar);
                groupValue.add(aar.FinServ__Account__r.ExternalId__c);
            }
        }
        ManageAccountAccountRelationUtility.manageGroup(groupValue, wrpShare);
        ManageAccountAccountRelationUtility.manageAgencyAAR(listAgencyAAR, wrpShare);
        ManageAccountAccountRelationUtility.manageSocietyAAR(listSocietyAAR, wrpShare);
        ManageAccountAccountRelationUtility.manageAgencySocietyAAR(listAgencySocietyAAR, wrpShare);
        try {
            Database.insert(wrpShare.listAccountShare, false);
            Database.insert(wrpShare.listShare, false);
        } catch (Exception ex) {
            System.debug('***** ERROR *****');
            System.debug('* getMessage: ' + ex.getMessage() + ' *');
            System.debug('* getStackTraceString: ' + ex.getStackTraceString() + ' *');
            System.debug('***** ERROR *****');
        }
    }

    public void finish(Database.BatchableContext bc) {
    }
}