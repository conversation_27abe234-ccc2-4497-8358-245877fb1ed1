public with sharing class TimelineController {
    @AuraEnabled(cacheable=true)
    public static List<ActivityWrapper> getRecentActivities() {
        List<ActivityWrapper> activities = new List<ActivityWrapper>();

        activities.add(new ActivityWrapper(
            '1', 'Appuntamento', 'UniSalute', 'Appuntamento', 
            'Via 9 Gennaio 1950 13 - Nonantola', '10 giugno 2024'
        ));
        activities.add(new ActivityWrapper(
            '2', 'Polizza/Prodotto', 'Unipol', 'Nuova emissione', 
            null, '23 novembre 2023'
        ));
        activities.add(new ActivityWrapper(
            '3', 'Privacy', 'Unipol', 'Raccolta consensi', 
            null, '09 febbraio 2022'
        ));
        activities.add(new ActivityWrapper(
            '4', 'Privacy', 'UniSalute', 'Raccolta consensi', 
            null, '25 gennaio 2022'
        ));

        return activities;
    }

    public class ActivityWrapper {
        @AuraEnabled public String id;
        @AuraEnabled public String type;
        @AuraEnabled public String company;
        @AuraEnabled public String detail;
        @AuraEnabled public String description;
        @AuraEnabled public String eventDate;

        public ActivityWrapper(String id, String type, String company, String detail, String description, String eventDate) {
            this.id = id;
            this.type = type;
            this.company = company;
            this.detail = detail;
            this.description = description;
            this.eventDate = eventDate;
        }
    }
}