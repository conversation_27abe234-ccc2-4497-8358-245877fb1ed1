/**
 * @File Name         : InsurancePolicyBatch.cls
 * @Description       :
 * <AUTHOR> <EMAIL>
 * @Group             :
 * @Last Modified On  : 27-01-2025
 * @Last Modified By  : <EMAIL>
 **/
global class InsurancePolicyBatch implements Database.Batchable<SObject>, Database.Stateful {
    Date dt;

    String mode; // New parameter to determine the logic to execute
    Integer toleranceDays; // New parameter to determine the logic to execute
    private Set<Id> idsSet;
    private Id idValueFrom;
    private Id idValueTo;
    private String dateType;
    private DateTime dateFrom;
    private DateTime dateTo;

    public InsurancePolicyBatch() {
        this.dt = null;
        this.mode = 'default'; // Default mode
        this.toleranceDays = 0; // Default tollerance zero
    }

    public InsurancePolicyBatch(Object condition) {
        String stringDate = condition?.toString();
        this.mode = 'default'; // Default mode
        this.toleranceDays = 0; // Default tollerance zero
        try {
            this.dt = Date.valueOf(stringDate);
            return;
        } catch (Exception ex) {
        }
        try {
            this.dt = Date.parse(stringDate);
            return;
        } catch (Exception ex) {
        }
        
    }

     public InsurancePolicyBatch(Set<Id> idsSet, Id idValueFrom, Id idValueTo, DateTime dateFrom, DateTime dateTo){
        this.mode = 'default'; // Default mode
        this.idsSet = idsSet;
        this.idValueFrom = idValueFrom;
        this.idValueTo = idValueTo;
        this.dateFrom = dateFrom;
        this.dateTo = dateTo;
    }

    // New constructor with only the mode parameter
    public InsurancePolicyBatch(String mode, Integer toleranceDays) {
        this.toleranceDays = toleranceDays != null ? toleranceDays : 0; // Set tolerance days or default to 0
         // Initialize dt to null for unpairing logic
         // If you want to use a specific date, you can set it here
         // For example: this.dt = Date.today();
         // But for unpairing, we don't need a specific date
        this.dt = null; // No date is needed for the new logic of unpairing
        this.mode = mode != null ? mode : 'default'; // Set mode or default to 'default'
    }

 
    global Database.QueryLocator start(Database.BatchableContext BC) {
        if (this.mode == 'unMatching') {
            System.debug('### DEV ## InsurancePolicyBatch started in unpair mode');
            Date today = Date.today();
            String toleranceDaysIntegerLiteral = this.toleranceDays.toString();
            // Retrieves all InsurancePolicy records that need to be unpaired
            
            //TO DO: Optimize the query with input of "NameInsuredId" 
            //TO DO Optimize the query with input of Is of InsurancePolicy to process    


            return Database.getQueryLocator(
                [
                    SELECT Id, Name, ActiveDate__c,Agency__c, Agency__r.ExternalId__c, Society__c, Society__r.ExternalId__c, CIP__c, NameInsuredId
                    FROM InsurancePolicy
                    WHERE(ActiveDate__c < :Date.today().addDays(-this.toleranceDays)  OR  ActiveDate__c != null)// Use binding for dynamic filtering
                ]
            );
            
        }else{
            // System.debug('### DEV ## InsurancePolicyBatch started in default mode');
            // // Retrieves all InsurancePolicy records created today
            // // String query = 'SELECT Id, Name, Agency__c, Agency__r.ExternalId__c, Society__c, Society__r.ExternalId__c, CIP__c, NameInsuredId FROM InsurancePolicy WHERE CreatedDate = TODAY';
            // // return Database.getQueryLocator(query);
            // if (dt == null) {
            //     return Database.getQueryLocator(
            //         [
            //             SELECT Id, Name, Agency__c, Agency__r.ExternalId__c, Society__c, Society__r.ExternalId__c, CIP__c, NameInsuredId
            //             FROM InsurancePolicy
            //             WHERE LastModifiedDate >= YESTERDAY
            //         ]
            //     );
            // } else {
            //     return Database.getQueryLocator(
            //         [
            //             SELECT Id, Name, Agency__c, Agency__r.ExternalId__c, Society__c, Society__r.ExternalId__c, CIP__c, NameInsuredId
            //             FROM InsurancePolicy
            //             WHERE LastModifiedDate >= :this.dt
            //         ]
            //     );
            // }

            List<String> conditions = new List<String>();
            String baseQuery = 'SELECT Id, Name, Agency__c, Agency__r.ExternalId__c, Society__c, Society__r.ExternalId__c, CIP__c, NameInsuredId FROM InsurancePolicy WHERE ';

            if (dt != null) {
                conditions.add('LastModifiedDate >= :dt');
            } else {
                if(this.dateFrom != null){
                    conditions.add('LastModifiedDate >= :startDT');
                }
                
                if(this.dateTo != null){
                    conditions.add('LastModifiedDate <= :endDT');
                }
            }
            
            if(idsSet != null) {
                conditions.add('Id IN :idsSet');
            }
            
            if(idValueFrom != null){
                conditions.add('Id >= :idValueFrom');
            }
            
            if(idValueTo != null){
                conditions.add('Id <= :idValueTo');
            }

            String fullQuery = conditions.isEmpty() ? baseQuery.removeEnd(' WHERE ') : baseQuery + String.join(conditions, ' AND ');
            return Database.getQueryLocator(fullQuery);
        }
    }

    global void execute(Database.BatchableContext BC, List<InsurancePolicy> scope) {

            // Builds a Set of NameInsuredId for subsequent queries
            Set<Id> nameInsuredIds = new Set<Id>();
            Set<Id> agencyIds = new Set<Id>();
            Set<Id> societyIds = new Set<Id>();
            Set<String> policyExternalIds = new Set<String>();
            system.debug('### DEV ## InsurancePolicyBatch started in unpair mode with tolerance days: ' + scope.size() + ' records');
            system.debug('### DEV ## InsurancePolicyBatch started in unpair mode with tolerance days: ' + this.toleranceDays);
            //TEST ActivaDate retireved
            // for (InsurancePolicy policyAD : scope) {
            //     System.debug('### Processed Policy in BATCH(NOT CLASS TEST): ' + policyAD.Name + ', ActiveDate__c: ' + policyAD.ActiveDate__c);
            // }

            for (InsurancePolicy policy : scope) {
                nameInsuredIds.add(policy.NameInsuredId);
                agencyIds.add(policy.Agency__c);
                societyIds.add(policy.Society__c);
                policyExternalIds.add(policy.CIP__c);
            }
            System.debug('### DEV -> nameInsuredIds = ' + nameInsuredIds);
            System.debug('### DEV -> agencyIds = ' + agencyIds);
            System.debug('### DEV -> societyIds = ' + societyIds);
            System.debug('### DEV -> policyExternalIds = ' + policyExternalIds);
            /*
            // Retrieves FinServ__AccountAccountRelation__c records for Agency and Society
            Map<Id, FinServ__AccountAccountRelation__c> agencyRelations = new Map<Id, FinServ__AccountAccountRelation__c>([SELECT Id, FinServ__Account__c, FinServ__RelatedAccount__c FROM FinServ__AccountAccountRelation__c WHERE FinServ__Account__c IN :nameInsuredIds AND FinServ__RelatedAccount__c IN :agencyIds]);

            Map<Id, FinServ__AccountAccountRelation__c> societyRelations = new Map<Id, FinServ__AccountAccountRelation__c>([SELECT Id, FinServ__Account__c, FinServ__RelatedAccount__c FROM FinServ__AccountAccountRelation__c WHERE FinServ__Account__c IN :nameInsuredIds AND FinServ__RelatedAccount__c IN :societyIds]);
            */

            Map<String, Id> accountRelationIdMap = new Map<String, Id>();
            Map<String, Id> accountRelationSocietyIdMap = new Map<String, Id>();

            // Retrieves FinServ__AccountAccountRelation__c records for Agency
            Map<Id, Id> agencyRelations = new Map<Id, Id>();
            for (FinServ__AccountAccountRelation__c aar : [
                SELECT FinServ__Account__c, FinServ__RelatedAccount__c
                FROM FinServ__AccountAccountRelation__c
                WHERE FinServ__Account__c IN :nameInsuredIds AND FinServ__RelatedAccount__c IN :agencyIds
            ]) {
                agencyRelations.put(aar.FinServ__Account__c, aar.FinServ__RelatedAccount__c);
                accountRelationIdMap.put(aar.FinServ__Account__c + '_' + aar.FinServ__RelatedAccount__c, aar.Id);
            }
            System.debug('### DEVCAP -> agencyRelations = ' + agencyRelations.size());

            // Retrieves FinServ__AccountAccountRelation__c records for Society
            Map<Id, Id> societyRelations = new Map<Id, Id>();
            for (FinServ__AccountAccountRelation__c aar : [
                SELECT FinServ__Account__c, FinServ__RelatedAccount__c
                FROM FinServ__AccountAccountRelation__c
                WHERE FinServ__Account__c IN :nameInsuredIds AND FinServ__RelatedAccount__c IN :societyIds
            ]) {
                societyRelations.put(aar.FinServ__Account__c, aar.FinServ__RelatedAccount__c);
                accountRelationIdMap.put(aar.FinServ__Account__c + '_' + aar.FinServ__RelatedAccount__c, aar.Id);
                accountRelationSocietyIdMap.put(aar.FinServ__Account__c + '_' + aar.FinServ__RelatedAccount__c, aar.Id);
            }

            Map<Id, Id> societyRelationsWithAccDetNPI = new Map<Id, Id>();
            for (AccountDetails__c accDet : [SELECT AccountDetailsNPI__c, Relation__c FROM AccountDetails__c WHERE Relation__c IN :accountRelationSocietyIdMap.values() AND AccountDetailsNPI__c != NULL]) {
                societyRelationsWithAccDetNPI.put(accDet.Relation__c, accDet.AccountDetailsNPI__c);
            }
            System.debug('### DEVCAP -> societyRelations = ' + societyRelations.size());

            System.debug('### DEV -> agencyRelations = ' + agencyRelations);
            System.debug('### DEV -> societyRelations = ' + societyRelations);

            // Builds the required maps
            Map<Id, Id> agencyRelationMap = new Map<Id, Id>();
            Map<Id, Id> societyRelationMap = new Map<Id, Id>();
            Map<Id, String> agencyExtIdMap = new Map<Id, String>();
            Map<Id, String> societyExtIdMap = new Map<Id, String>();
            Map<Id, String> cipExtIdMap = new Map<Id, String>();

            for (InsurancePolicy policy : scope) {
                // Populates the maps for Agency and Society
                if (agencyRelations.containsKey(policy.NameInsuredId)) {
                    agencyRelationMap.put(policy.NameInsuredId, agencyRelations.get(policy.NameInsuredId));
                }
                if (societyRelations.containsKey(policy.NameInsuredId)) {
                    societyRelationMap.put(policy.NameInsuredId, societyRelations.get(policy.NameInsuredId));
                }

                // Populates the maps for ExternalId
                agencyExtIdMap.put(policy.Id, policy.Agency__r.ExternalId__c.replace(Label.AgencyExtIdStr, ''));
                societyExtIdMap.put(policy.Id, policy.Society__r.ExternalId__c.replace(Label.SocietyExtIdStr, ''));

                // Populates the map for CIP ExternalId
                String insPolSocietyCode = societyExtIdMap.get(policy.Id);
                String insPolAgencyCode = agencyExtIdMap.get(policy.Id);
                String paddedCip = String.valueOf(policy.CIP__c).leftPad(5, '0');
                //**BELOW COMMENTED THE INitial Version with a DOUBLE "_"  IN THE RESULT CONCATENATION**
                //cipExtIdMap.put(policy.Id, Label.CIPExtIdStr + '_' + insPolSocietyCode + '_' + insPolAgencyCode + '_' + paddedCip);
                cipExtIdMap.put(policy.Id, Label.CIPExtIdStr +  insPolSocietyCode + '_' + insPolAgencyCode + '_' + paddedCip);
                // system.debug( '### DEV ### Label.CIPExtIdStr: ' + Label.CIPExtIdStr);
                // system.debug( '### DEV ### Label.insPolSocietyCode: ' + insPolSocietyCode);
                // system.debug( '### DEV ### Label.insPolAgencyCode: ' + insPolAgencyCode);
                // system.debug( '### DEV ### Label.paddedCip: ' + paddedCip);
                // system.debug( '### DEV ### cipExtIdMap element = ' + cipExtIdMap.get(policy.Id));
                system.debug( '### DEV ### cipExtIdMap MAP = ' + cipExtIdMap);
                if(cipExtIdMap!= null ) {
                    system.debug( '### DEV ### cipExtIdMap = ' + cipExtIdMap.size());
                }
            }

            System.debug('### DEV cipExtIdMap.values() = ' + cipExtIdMap.values());
           
            // Retrieves groups from the Group object
            List<Group> groups = [SELECT Id, DeveloperName FROM Group WHERE DeveloperName IN :cipExtIdMap.values()];

            // Builds the map for groups
            Map<Id, Id> insurancePolicyGroupMap = new Map<Id, Id>();
            for (Group grp : groups) {
                for (Id policyId : cipExtIdMap.keySet()) {
                    // system.debug('### DEV ### grp.DeveloperName = ' + grp.DeveloperName);
                    // system.debug('### DEV ### cipExtIdMap.get(policyId) = ' + cipExtIdMap.get(policyId));
                    if (cipExtIdMap.get(policyId) == grp.DeveloperName) {
                        insurancePolicyGroupMap.put(policyId, grp.Id);
                    }
                }
            }
            
            System.debug('### DEV ### insurancePolicyGroupMap SIZE = ' + insurancePolicyGroupMap.size());
            System.debug('### DEV ### insurancePolicyGroupMap = ' + insurancePolicyGroupMap);

        if (this.mode == 'default') {

            // Builds the lists for sharing
            List<AccountShare> lstAccShares = new List<AccountShare>();
            List<FinServ__AccountAccountRelation__Share> lstAccAccRelAgencyShares = new List<FinServ__AccountAccountRelation__Share>();
            List<FinServ__AccountAccountRelation__Share> lstAccAccRelSocietyShares = new List<FinServ__AccountAccountRelation__Share>();
            List<AccountDetailsNPI__Share> lstAccDetNPIShares = new List<AccountDetailsNPI__Share>();

            // Maps to track unique elements
            Map<Id, AccountShare> accShareMap = new Map<Id, AccountShare>();
            Map<String, FinServ__AccountAccountRelation__Share> accAccRelAgencyShareMap = new Map<String, FinServ__AccountAccountRelation__Share>();
            Map<String, FinServ__AccountAccountRelation__Share> accAccRelSocietyShareMap = new Map<String, FinServ__AccountAccountRelation__Share>();
            Map<String, AccountDetailsNPI__Share> accDetNPIShareMap = new Map<String, AccountDetailsNPI__Share>();

            for (InsurancePolicy policy : scope) {
                // Adds sharing records for Account
                if (!accShareMap.containsKey(policy.NameInsuredId)) {
                    AccountShare accShare = new AccountShare(AccountAccessLevel = 'Read', CaseAccessLevel = 'Read', OpportunityAccessLevel = 'Read', AccountId = policy.NameInsuredId, UserOrGroupId = insurancePolicyGroupMap.get(policy.Id));
                    accShareMap.put(policy.NameInsuredId, accShare);
                    lstAccShares.add(accShare);
                }

                // Adds sharing records for AccountAccountRelation (Agency)
                if (agencyRelationMap.containsKey(policy.NameInsuredId) && insurancePolicyGroupMap.containsKey(policy.Id)) {
                    String accAccRelAgencyShareKey = agencyRelationMap.get(policy.NameInsuredId) + '_' + insurancePolicyGroupMap.get(policy.Id);
                    if (!accAccRelAgencyShareMap.containsKey(accAccRelAgencyShareKey)) {
                        FinServ__AccountAccountRelation__Share accAccRelAgencyShare = new FinServ__AccountAccountRelation__Share(AccessLevel = 'Read', ParentId = accountRelationIdMap.get(policy.NameInsuredId + '_' + agencyRelationMap.get(policy.NameInsuredId)), UserOrGroupId = insurancePolicyGroupMap.get(policy.Id));
                        accAccRelAgencyShareMap.put(accAccRelAgencyShareKey, accAccRelAgencyShare);
                        lstAccAccRelAgencyShares.add(accAccRelAgencyShare);
                    }
                }

                // Adds sharing records for AccountAccountRelation (Society)
                if (societyRelationMap.containsKey(policy.NameInsuredId) && insurancePolicyGroupMap.containsKey(policy.Id)) {
                    String accAccRelSocietyShareKey = societyRelationMap.get(policy.NameInsuredId) + '_' + insurancePolicyGroupMap.get(policy.Id);
                    if (!accAccRelSocietyShareMap.containsKey(accAccRelSocietyShareKey)) {
                        FinServ__AccountAccountRelation__Share accAccRelSocietyShare = new FinServ__AccountAccountRelation__Share(AccessLevel = 'Read', ParentId = accountRelationIdMap.get(policy.NameInsuredId + '_' + societyRelationMap.get(policy.NameInsuredId)), UserOrGroupId = insurancePolicyGroupMap.get(policy.Id));
                        accAccRelSocietyShareMap.put(accAccRelSocietyShareKey, accAccRelSocietyShare);
                        lstAccAccRelSocietyShares.add(accAccRelSocietyShare);
                    }
                    if (!accDetNPIShareMap.containsKey(accAccRelSocietyShareKey)) {
                        AccountDetailsNPI__Share accDetNPIShare = new AccountDetailsNPI__Share(AccessLevel = 'Read', ParentId = societyRelationsWithAccDetNPI.get(accountRelationIdMap.get(policy.NameInsuredId + '_' + societyRelationMap.get(policy.NameInsuredId))), UserOrGroupId = insurancePolicyGroupMap.get(policy.Id));
                        accDetNPIShareMap.put(accAccRelSocietyShareKey, accDetNPIShare);
                        lstAccDetNPIShares.add(accDetNPIShare);
                    }
                }
            }

            // Inserts the lists into the database
            if (!lstAccShares.isEmpty()) {
                System.debug('### DEV -> lstAccShares = ' + lstAccShares);
                System.debug('### DEV -> lstAccShares SIZE = ' + lstAccShares.size());
                Database.insert(lstAccShares, false);
            }
            if (!lstAccAccRelAgencyShares.isEmpty()) {
                System.debug('### DEV -> lstAccAccRelAgencyShares = ' + lstAccAccRelAgencyShares);
                System.debug('### DEV -> lstAccAccRelAgencyShares SIZE = ' + lstAccAccRelAgencyShares.size());
                Database.insert(lstAccAccRelAgencyShares, false);
            }
            if (!lstAccAccRelSocietyShares.isEmpty()) {
                System.debug('### DEV -> lstAccAccRelSocietyShares = ' + lstAccAccRelSocietyShares);
                System.debug('### DEV -> lstAccAccRelSocietyShares SIZE = ' + lstAccAccRelSocietyShares.size());
                Database.insert(lstAccAccRelSocietyShares, false);
            }
            if (!lstAccDetNPIShares.isEmpty()) {
                System.debug('### DEV -> lstAccDetNPIShares = ' + lstAccDetNPIShares);
                System.debug('### DEV -> lstAccDetNPIShares SIZE = ' + lstAccDetNPIShares.size());
                Database.insert(lstAccDetNPIShares, false);
            }
        } else if (this.mode == 'unMatching') {
            try{
            // Logic for unpairing InsurancePolicy records           
            // Call the private method to process unpairing logic
            //and to retrieve the maps called "insurancePolicyGroupMap" and "insurancePolicyToNOTUnpairGroupMap"
            //that contains respective map of Policy and UserOrGroupIds for unpairing and not unpairing
                Map<String, Map<Id, Id>> resultMaps = processUnpairingLogic(
                    policyExternalIds,
                    nameInsuredIds,
                    agencyIds,
                    societyIds,
                    insurancePolicyGroupMap
                );

                // Extract the maps from the result
                Map<Id, Id> insurancePolicyGroupToUnpairMap = resultMaps.get('insurancePolicyGroupToUnpairMap');
                Map<Id, Id> insurancePolicyGroupToNOTUnpairpMap = resultMaps.get('insurancePolicyGroupToNOTUnpairpMap');

                System.debug('### DEV ### insurancePolicyGroupToUnpairMap.size = ' + insurancePolicyGroupToUnpairMap.size());
                System.debug('### DEV ### insurancePolicyGroupToUnpairMap = ' + insurancePolicyGroupToUnpairMap);
                System.debug('### DEV ### insurancePolicyGroupToNOTUnpairpMap.size = ' + insurancePolicyGroupToNOTUnpairpMap.size());
                System.debug('### DEV ### insurancePolicyGroupToNOTUnpairpMap = ' + insurancePolicyGroupToNOTUnpairpMap);



                // Unpairing logic
                // Step 1: Identify the UserOrGroupIds to delete
                Set<Id> groupIdsToDelete = new Set<Id>();

                // Add all UserOrGroupIds from insurancePolicyGroupMap
                groupIdsToDelete.addAll(insurancePolicyGroupToUnpairMap.values());
                System.debug('### DEV ### groupIdsToDelete.size (before removing NOT UNPAIR groups) = ' + groupIdsToDelete.size());
                System.debug('### DEV ### groupIdsToDelete (before removing NOT UNPAIR groups) = ' + groupIdsToDelete);

                // Debug to verify the group IDs to delete
                System.debug('### DEV ### insurancePolicyGroupToUnpairMap = ' + insurancePolicyGroupToUnpairMap);

                // Remove UserOrGroupIds that are present in insurancePolicyToNOTUnpairGroupMap
                groupIdsToDelete.removeAll(insurancePolicyGroupToNOTUnpairpMap.values());
                System.debug('### DEV ### groupIdsToDelete.size (after removing NOT UNPAIR groups) = ' + groupIdsToDelete.size());
                System.debug('### DEV ### groupIdsToDelete (after removing NOT UNPAIR groups) = ' + groupIdsToDelete);
                // Debug to verify the group IDs to delete
                System.debug('### DEV ### groupIdsToDelete = ' + groupIdsToDelete);

                 // Step 2: Delete CaseShare records
                deleteCaseShareRecord(groupIdsToDelete);

                // Step 3: Delete OpportunityShare records
                deleteOpportunityShareRecord(groupIdsToDelete);

                // Step 4: Delete FinServ__AccountAccountRelation__Share records
                deleteAccountAccountRelationShareRecord(groupIdsToDelete, agencyIds, nameInsuredIds);

                // Step 5: Delete the groups from the deleteAccountDetailsNPIShareRecord records
                deleteAccountDetailsNPIShareRecord(groupIdsToDelete, societyIds);


                System.debug('### DEV ### Deleted CaseShare, OpportunityShare, and FinServ__AccountAccountRelation__Share records for groupIdsToDelete: ' + groupIdsToDelete);



            } catch (DmlException e) {
                System.debug('### DEV ### DmlException occurred in execute: ' + e.getMessage());
                throw e; // Rethrow the exception to the batch framework
            } catch (Exception e) {
                System.debug('### DEV ### Error during unpairing logic: ' + e.getMessage());
                throw e; // Rethrow the exception to handle it in the batch framework
            }

            


        }
    }

    /**
     * Private method to process unpairing logic and return the required maps.  
     *   This method retrieves InsurancePolicy records 
     *   based on the provided external IDs and performs the necessary logic to build the maps for unpairing.  
     * 
     * */

        private Map<String, Map<Id, Id>> processUnpairingLogic(
        Set<String> policyExternalIds,
        Set<Id> nameInsuredIds,
        Set<Id> agencyIds,
        Set<Id> societyIds,
        Map<Id, Id> insurancePolicyGroupMap
        ) {
            // Step 1: Retrieve policies to check
            List<InsurancePolicy> policiesToCheck = [
                SELECT Id, Name, Agency__c, Agency__r.ExternalId__c, Society__c, Society__r.ExternalId__c, CIP__c, NameInsuredId
                FROM InsurancePolicy
                WHERE CIP__c IN :policyExternalIds
                AND NameInsuredId IN :nameInsuredIds
                AND Agency__c IN :agencyIds
                AND Society__c IN :societyIds
                AND (ActiveDate__c >= :Date.today().addDays(-this.toleranceDays) )
            ];

            // FOR TESTING PURPOSES ---TO COMMENT AFTER TEST---               
                // List<InsurancePolicy> policiesToCheckNOTExpired = [
                //     SELECT Id, Name, Agency__c, Agency__r.ExternalId__c, Society__c, Society__r.ExternalId__c, CIP__c, NameInsuredId,NameInsured.Name
                //     FROM InsurancePolicy
                //     WHERE CIP__c IN :policyExternalIds
                //     AND NameInsuredId IN :nameInsuredIds
                //     AND Agency__c IN :agencyIds
                //     AND Society__c IN :societyIds
                //     AND (ActiveDate__c >= :Date.today().addDays(-this.toleranceDays) )
                    
                // ];
                //
            // System.debug('### DEV ### Retrieved policiesToCheckNOTExpired.size = ' + policiesToCheckNOTExpired.size());
            // System.debug('### DEV ### Retrieved policiesToCheckNOTExpired = ' + policiesToCheckNOTExpired);
            //  ---TO COMMENT AFTER TEST---   

            // Step 2: Populate the CIP group map
            Map<Id, String> policyIdActiveCipGroupMap = new Map<Id, String>();
            Map<Id, String> agencyExtIdMap = new Map<Id, String>();
            Map<Id, String> societyExtIdMap = new Map<Id, String>();

            for (InsurancePolicy policyElement : policiesToCheck) {
                // Populate the maps for ExternalId
                agencyExtIdMap.put(policyElement.Id, policyElement.Agency__r.ExternalId__c.replace(Label.AgencyExtIdStr, ''));
                societyExtIdMap.put(policyElement.Id, policyElement.Society__r.ExternalId__c.replace(Label.SocietyExtIdStr, ''));

                // Populate the map for CIP ExternalId
                String insPolSocietyCodeElement = societyExtIdMap.get(policyElement.Id);
                String insPolAgencyCodeElement = agencyExtIdMap.get(policyElement.Id);
                String paddedCipElement = String.valueOf(policyElement.CIP__c).leftPad(5, '0');
                policyIdActiveCipGroupMap.put(
                    policyElement.Id,
                    Label.CIPExtIdStr + insPolSocietyCodeElement + '_' + insPolAgencyCodeElement + '_' + paddedCipElement
                );
            }

            System.debug('### DEV ### policyIdActiveCipGroupMap = ' + policyIdActiveCipGroupMap);

            // Step 3: Retrieve groups to NOT UNPAIR from the Group object
            List<Group> groupsToNOTUnpair = [
                SELECT Id, DeveloperName
                FROM Group
                WHERE DeveloperName IN :policyIdActiveCipGroupMap.values()
            ];
            System.debug('### DEV ### Retrieved groupsToNOTUnpair = ' + groupsToNOTUnpair);

            // Step 4: Build the map  insurancePolicyGrouToNOTUnpairpMap for groups to NOT UNPAIR
            Map<Id, Id> insurancePolicyGroupToNOTUnpairpMap = new Map<Id, Id>();
            for (Group grpElement : groupsToNOTUnpair) {
                for (Id policyId : policyIdActiveCipGroupMap.keySet()) {
                    if (policyIdActiveCipGroupMap.get(policyId) == grpElement.DeveloperName) {
                        insurancePolicyGroupToNOTUnpairpMap.put(policyId, grpElement.Id);
                    }
                }
            }

            System.debug('### DEV ### insurancePolicyGroupToNOTUnpairpMap = ' + insurancePolicyGroupToNOTUnpairpMap);

            // Step 5: Build the insurancePolicyGroupToUnpairMap for groups to NOT UNPAIRfor groups to  UNPAIR
            Map<Id, Id> insurancePolicyGroupToUnpairMap = new Map<Id, Id>();
            insurancePolicyGroupToUnpairMap=insurancePolicyGroupMap.clone();

            System.debug('### DEV ### insurancePolicyGroupMap = ' + insurancePolicyGroupMap);
            System.debug('### DEV ### insurancePolicyGroupMap.size = ' + insurancePolicyGroupMap.size());
            System.debug('### DEV ### insurancePolicyGroupToUnpairMap = ' + insurancePolicyGroupToUnpairMap);
            System.debug('### DEV ### insurancePolicyGroupToUnpairMap.size = ' + insurancePolicyGroupToUnpairMap.size());

            // Step 6: Return both maps as a single result
            return new Map<String, Map<Id, Id>>{
                'insurancePolicyGroupToUnpairMap' => insurancePolicyGroupToUnpairMap,
                'insurancePolicyGroupToNOTUnpairpMap' => insurancePolicyGroupToNOTUnpairpMap
            };
        }

        private void deleteOpportunityShareRecord(Set<Id> groupIdsToDelete) {
            System.debug('### DEV ### Starting deleteOpportunityShareRecord...');
            System.debug('### DEV ### groupIdsToDelete = ' + groupIdsToDelete);
        
            try {
                // Step 1: Query OpportunityShare records to delete
                List<OpportunityShare> opportunitySharesToDelete = [
                    SELECT Id
                    FROM OpportunityShare
                    WHERE UserOrGroupId IN :groupIdsToDelete
                ];
        
                // Debug to verify the OpportunityShare records to delete
                System.debug('### DEV ### opportunitySharesToDelete.size = ' + opportunitySharesToDelete.size());
                System.debug('### DEV ### opportunitySharesToDelete = ' + opportunitySharesToDelete);
        
                // Step 2: Perform the delete operation
                if (!opportunitySharesToDelete.isEmpty()) {
                    delete opportunitySharesToDelete;
                    System.debug('### DEV ### Deleted OpportunityShare records: ' + opportunitySharesToDelete.size());
                } else {
                    System.debug('### DEV ### No OpportunityShare records to delete.');
                }
            } catch (DmlException e) {
                System.debug('### DEV ### DmlException occurred in deleteOpportunityShareRecord: ' + e.getMessage());
                throw e; // Rethrow the exception to the caller
            }
        }

        private void deleteCaseShareRecord(Set<Id> groupIdsToDelete) {
            System.debug('### DEV ### Starting deleteCaseShareRecord...');
            System.debug('### DEV ### groupIdsToDelete = ' + groupIdsToDelete);
        
            try {
                // Step 1: Query CaseShare records to delete
                List<CaseShare> caseSharesToDelete = [
                    SELECT Id
                    FROM CaseShare
                    WHERE UserOrGroupId IN :groupIdsToDelete
                ];
        
                // Debug to verify the CaseShare records to delete
                System.debug('### DEV ### caseSharesToDelete.size = ' + caseSharesToDelete.size());
                System.debug('### DEV ### caseSharesToDelete = ' + caseSharesToDelete);
        
                // Step 2: Perform the delete operation
                if (!caseSharesToDelete.isEmpty()) {
                    delete caseSharesToDelete;
                    System.debug('### DEV ### Deleted CaseShare records: ' + caseSharesToDelete.size());
                } else {
                    System.debug('### DEV ### No CaseShare records to delete.');
                }
            } catch (DmlException e) {
                System.debug('### DEV ### DmlException occurred in deleteCaseShareRecord: ' + e.getMessage());
                throw e; // Rethrow the exception to the caller
            }
        }

        private void deleteAccountAccountRelationShareRecord(Set<Id> groupIdsToDelete, Set<Id> agencyIds, Set<Id> nameInsuredIds) {
            System.debug('### DEV ### Starting deleteAccountAccountRelationShareRecord...');
            System.debug('### DEV ### groupIdsToDelete = ' + groupIdsToDelete);
            System.debug('### DEV ### agencyIds = ' + agencyIds);
            System.debug('### DEV ### nameInsuredIds = ' + nameInsuredIds);
        
            try {
                // Step 1: Query FinServ__AccountAccountRelation__Share records to delete
                List<FinServ__AccountAccountRelation__Share> accountAccountRelationSharesToDelete = [
                    SELECT Id
                    FROM FinServ__AccountAccountRelation__Share
                    WHERE UserOrGroupId IN :groupIdsToDelete
                    AND ParentId IN (
                        SELECT Id
                        FROM FinServ__AccountAccountRelation__c
                        WHERE FinServ__RelatedAccount__c IN :agencyIds
                        AND Finserv__Account__c IN :nameInsuredIds
                    )
                    
                ];
        
                // Debug to verify the records to delete
                System.debug('### DEV ### accountAccountRelationSharesToDelete.size = ' + accountAccountRelationSharesToDelete.size());
                System.debug('### DEV ### accountAccountRelationSharesToDelete = ' + accountAccountRelationSharesToDelete);
        
                // Step 2: Perform the delete operation
                if (!accountAccountRelationSharesToDelete.isEmpty()) {
                    delete accountAccountRelationSharesToDelete;
                    System.debug('### DEV ### Deleted FinServ__AccountAccountRelation__Share records: ' + accountAccountRelationSharesToDelete.size());
                } else {
                    System.debug('### DEV ### No FinServ__AccountAccountRelation__Share records to delete.');
                }
            } catch (DmlException e) {
                System.debug('### DEV ### DmlException occurred in deleteAccountAccountRelationShareRecord: ' + e.getMessage());
                throw e; // Rethrow the exception to the caller
            }
        }

        private void deleteAccountDetailsNPIShareRecord(Set<Id> groupIdsToDelete, Set<Id> societyIds) {
            System.debug('### DEV ### Starting deleteAccountDetailsNPIShareRecord...');
            System.debug('### DEV ### groupIdsToDelete = ' + groupIdsToDelete);
            System.debug('### DEV ### societyIds = ' + societyIds);
        
            try {
                // Step 1: Retrieve FinServ__AccountAccountRelation__c records for the given societyIds
                List<FinServ__AccountAccountRelation__c> accountRelations = [
                    SELECT Id
                    FROM FinServ__AccountAccountRelation__c
                    WHERE FinServ__RelatedAccount__c IN :societyIds
                ];
                Set<Id> accountRelationIds = new Set<Id>();
                for (FinServ__AccountAccountRelation__c relation : accountRelations) {
                    accountRelationIds.add(relation.Id);
                }
                System.debug('### DEV ### accountRelationIds = ' + accountRelationIds);
        
                // Step 2: Retrieve AccountDetailsNPI__c records linked to the retrieved accountRelationIds
                List<AccountDetailsNPI__c> accountDetailsNPIRecords = [
                    SELECT Id
                    FROM AccountDetailsNPI__c
                    WHERE Relation__c IN :accountRelationIds
                ];
                Set<Id> accountDetailsNPIIds = new Set<Id>();
                for (AccountDetailsNPI__c accountDetails : accountDetailsNPIRecords) {
                    accountDetailsNPIIds.add(accountDetails.Id);
                }
                System.debug('### DEV ### accountDetailsNPIIds = ' + accountDetailsNPIIds);
        
                // Step 3: Retrieve AccountDetailsNPI__Share records to delete
                List<AccountDetailsNPI__Share> accountDetailsNPIShareToDelete = [
                    SELECT Id
                    FROM AccountDetailsNPI__Share
                    WHERE ParentId IN :accountDetailsNPIIds
                    AND UserOrGroupId IN :groupIdsToDelete
                ];
        
                // Debug to verify the records to delete
                System.debug('### DEV ### accountDetailsNPIShareToDelete.size = ' + accountDetailsNPIShareToDelete.size());
                System.debug('### DEV ### accountDetailsNPIShareToDelete = ' + accountDetailsNPIShareToDelete);
        
                // Step 4: Perform the delete operation
                if (!accountDetailsNPIShareToDelete.isEmpty()) {
                    delete accountDetailsNPIShareToDelete;
                    System.debug('### DEV ### Deleted AccountDetailsNPI__Share records: ' + accountDetailsNPIShareToDelete.size());
                } else {
                    System.debug('### DEV ### No AccountDetailsNPI__Share records to delete.');
                }
            } catch (DmlException e) {
                System.debug('### DEV ### DmlException occurred in deleteAccountDetailsNPIShareRecord: ' + e.getMessage());
                throw e; // Rethrow the exception to the caller
            }
        }

    global void finish(Database.BatchableContext BC) {
        System.debug('InsurancePolicyBatch completed successfully.');
    }
}