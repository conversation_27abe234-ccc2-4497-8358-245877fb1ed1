@isTest
private class EngineWrapperTest {
    
    @isTest
    static void testGetMacroAreas() {
        // Caso standard con più valori validi
        String input = 'Casa;Infortuni;Veicoli';
        List<String> result = EngineWrapper.getMacroAreas(input);

        // Caso con valore non valido
        input = 'Casa;Altro';
        result = EngineWrapper.getMacroAreas(input);

        // Caso stringa vuota
        input = '';
        result = EngineWrapper.getMacroAreas(input);

        // Caso null
        input = null;
        result = EngineWrapper.getMacroAreas(input);

    }

    @isTest
    static void testEngineWrapperInputConstructors() {
        EngineWrapper.EngineWrapperInput input = new EngineWrapper.EngineWrapperInput(
            'Private', 'CRM', new List<String>{'001xx000003DG0FAAW'}, 454642, 91900, 
            new List<String>{'Motor'}, 'Veicoli'
        );
    }

    @isTest
    static void testEngineWrapperInvocableOutputConstructors() {
        Account a = new Account(Name='Test Agenzia');
        insert a;

        EngineWrapper.EngineWrapperInvocableOutput output = new EngineWrapper.EngineWrapperInvocableOutput(true, null);

        EngineWrapper.EngineWrapperInvocableOutput outputFull = new EngineWrapper.EngineWrapperInvocableOutput(true, '', new List<Account>{a}, a, a);

    }

    @isTest
    static void testEngineWrapperOutputConstructors() {
        EngineWrapper.EngineWrapperInput input = new EngineWrapper.EngineWrapperInput();
        EngineWrapper.EngineError error = new EngineWrapper.EngineError('ERR01');
        EngineWrapper.EngineSettings settings = new EngineWrapper.EngineSettings(true, 100, '001xx000003DG0FAAW', 'DefaultAssign', 3, 50, 08);

        EngineWrapper.EngineWrapperOutput out1 = new EngineWrapper.EngineWrapperOutput(input, false, error, settings);

        EngineWrapper.EngineWrapperOutput out2 = new EngineWrapper.EngineWrapperOutput(input, true, new List<EngineWrapper.EngineAgency>(), null, null, new List<EngineWrapper.EngineExclusionAgency>(), settings);
    }

    @isTest
    static void testEngineAgencyCompareTo() {
        // Mock KPI
        KPI__c kpi = new KPI__c(Name='Test KPI');
        kpi.AverageLeadProcessingTime__c = (Math.random() * 10) + 1;
        kpi.DigitalPenetration__c = 50;
        kpi.ContactRate__c = 60;
        kpi.ExternalId__c = 'Default KPI Id';
        kpi.ScoreTotal__c = 1.8;
        kpi.PrivateAreaRegistration__c = 60;
        kpi.OmnichannelQuotes__c = 55;
        kpi.BenchmarkPrivateAreaRegistration__c = 69.2;
        kpi.BenchmarkOmnichannelQuotes__c = 64.2;
        kpi.ScorePrivateAreaRegistration__c = 0.1;
        kpi.ScoreOmnichannelQuotes__c = 0.1;
        kpi.WeightPrivateAreaRegistration__c = 10;
        kpi.WeightOmnichannelQuotes__c = 10;
        kpi.GapPrivateAreaRegistration__c = -9.2;
        kpi.GapOmnichannelQuotes__c = -9.2;
        kpi.NumberPoliciesByContact__c = 65;
        kpi.ContactActivitiesProcessedClosed__c = 90;
        kpi.ContactActivitiesAssignedPeriod__c = 200;
        kpi.ScoreProcessingAssignedActivities__c = 0.2;
        kpi.ScoreConversionAssignedActivities__c = 0.4;
        kpi.ScoreAverageLeadProcessingTime__c = 0.8;
        kpi.ScoreDigitalPenetration__c = 0.2;
        kpi.NumberClientsPortfolio__c = 221;
        kpi.NumberContactableClients__c = 491;
        kpi.BenchmarkContactActivitiesAssigned__c = 56.6;
        kpi.BenchmarkConversionAssignedActivities__c = 76.8;
        kpi.BenchmarkAverageLeadProcessingTime__c = 7.2;
        kpi.BenchmarkDigitalPenetration__c = 59.2;
        kpi.WeightContactActivitiesAssigned__c = 20;
        kpi.WeightConversionAssignedActivities__c = 20;
        kpi.WeightAverageLeadProcessingTime__c = 20;
        kpi.WeightDigitalPenetration__c = 20;
        kpi.AverageLeadProcessingTime__c = 5;
        kpi.GapContactActivitiesAssigned__c = -11.6;
        kpi.GapConversionAssignedActivities__c = -4.8;
        kpi.GapAverageLeadProcessingTime__c = -2.2;
        kpi.GapDigitalPenetration__c = -9.2;
        
        FlowTriggersActivation__c  fta = new FlowTriggersActivation__c ();
        fta.SetupOwnerId = UserInfo.getUserId();
        fta.SkipTriggers__c = true;
        insert fta;
        
        insert kpi;
        EngineWrapper.KPISet kpiSet = new EngineWrapper.KPISet(kpi);

        Account agency = new Account(Name='Agenzia A', AccountNumber='123', ExternalId__c='EXT123', IsLocatorEnabled__c=true);
        insert agency;

        EngineWrapper.EngineAgency a1 = new EngineWrapper.EngineAgency(agency, 100, 20, true, kpiSet, true, true);
        EngineWrapper.EngineAgency a2 = new EngineWrapper.EngineAgency(agency, 50, 30, false, kpiSet, true, true);

        // La discretionality è attiva su a1
        Integer result = a1.compareTo(a2);

        // Uguali per discretionality, test sullo score
        //a2.discretionality = true;
        result = a1.compareTo(a2);

        // Stesso score, test sulla distanza
        a2.score = 10.0;
        a2.distance = 3.0;
        result = a1.compareTo(a2);

        // Uguali in tutto
        a2.distance = 2.0;
        result = a1.compareTo(a2);
    }

    @isTest
    static void testEngineExclusionAgencyConstructor() {
        Account agency = new Account(Name='Agenzia Esclusa');
        insert agency;

        EngineWrapper.EngineAgency engineAgency = new EngineWrapper.EngineAgency(agency, 50, 30, false, null, false, true);
        EngineWrapper.EngineExclusionAgency exclusion = new EngineWrapper.EngineExclusionAgency(engineAgency, 'Blacklist');

    }
}