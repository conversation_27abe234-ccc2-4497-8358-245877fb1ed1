@isTest
public class getRecordIdForAura_Test {

    @isTest
    static void test_method(){
        
        Test.startTest();
        
        try{
            getRecordIdForAura.getAccounts();
        }catch(Exception ex){}
        
        try{
            getRecordIdForAura.getAnyAccount();
        }catch(Exception ex){}
        
        try{
            getRecordIdForAura.getInteractionSummary();
        }catch(Exception ex){}
        
        try{
            getRecordIdForAura.getResidentialLoanApplication();
        }catch(Exception ex){}
        
        try{
            getRecordIdForAura.getActionPlan();
        }catch(Exception ex){}
        
        try{
            getRecordIdForAura.getHouseholdAccounts();
        }catch(Exception ex){}
        
        try{
            getRecordIdForAura.getAnyHouseholdAccount();
        }catch(Exception ex){}
        
        try{
            getRecordIdForAura.getFinancialDeal();
        }catch(Exception ex){}
        
        Test.stopTest();
        
    }
    
}