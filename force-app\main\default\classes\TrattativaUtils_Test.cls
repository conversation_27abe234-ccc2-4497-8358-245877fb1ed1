@isTest
private class TrattativaUtils_Test {
    
    @isTest
    static void testCountOmniParent_MultipleOmni() {
        // Arrange
        Map<String, Object> args = new Map<String, Object>();
        Map<String, Object> input = new Map<String, Object>();
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        // Lista di opportunità con due Omni
        List<Map<String, Object>> oppList = new List<Map<String, Object>>{
            new Map<String, Object>{ 'isOmni' => true },
            new Map<String, Object>{ 'isOmni' => true },
            new Map<String, Object>{ 'isOmni' => false }
        };

        input.put('oppList', oppList);
        args.put('input', input);
        args.put('output', output);
        args.put('options', options);

        // Act
        TrattativaUtils utils = new TrattativaUtils();
        Object result = utils.call('countOmniParent', args);

        // Assert
        System.assertEquals(true, result);
        System.assertEquals(true, output.get('success'));
        System.assertEquals(true, output.get('isMultiParent'));
    }

    @isTest
    static void testCountOmniParent_SingleOmni() {
        Map<String, Object> args = new Map<String, Object>();
        Map<String, Object> input = new Map<String, Object>();
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        // Solo una Omni = false atteso per isMultiParent
        List<Map<String, Object>> oppList = new List<Map<String, Object>>{
            new Map<String, Object>{ 'isOmni' => true },
            new Map<String, Object>{ 'isOmni' => false }
        };

        input.put('oppList', oppList);
        args.put('input', input);
        args.put('output', output);
        args.put('options', options);

        TrattativaUtils utils = new TrattativaUtils();
        Object result = utils.call('countOmniParent', args);

        System.assertEquals(true, result);
        System.assertEquals(true, output.get('success'));
        System.assertEquals(false, output.get('isMultiParent'));
    }

    @isTest
    static void testCall_ExceptionHandling() {
        // Simula un input malformato che causa un errore
        Map<String, Object> args = new Map<String, Object>();
        args.put('input', null); // Questo causerà un'eccezione nel try
        args.put('output', new Map<String, Object>());
        args.put('options', new Map<String, Object>());

        TrattativaUtils utils = new TrattativaUtils();
        Object result = utils.call('countOmniParent', args);

        Map<String, Object> output = (Map<String, Object>)args.get('output');
        System.assertEquals(false, result);
        System.assertEquals(false, output.get('success'));
        System.assertEquals('Si è verificato un errore', output.get('errorMessage'));
    }
}