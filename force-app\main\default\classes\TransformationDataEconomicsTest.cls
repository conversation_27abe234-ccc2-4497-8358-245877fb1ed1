@isTest
public class TransformationDataEconomicsTest {

    public static Map<String, Object> inputMap;
    public static Map<String, Object> outMap;
    public static Map<String, Object> options;

    @isTest
    static void testCheckDataEconomics() {
        List<TransformationDataEconomics.wrapClassDataEconomics> resultList = new List<TransformationDataEconomics.wrapClassDataEconomics>();
        TransformationDataEconomics.wrapClassDataEconomics item1 = new TransformationDataEconomics.wrapClassDataEconomics();
        item1.Name = 'UniSalute';
        item1.AccountAccountSocietyId = null;
        resultList.add(item1);
        TransformationDataEconomics.wrapClassDataEconomics item2 = new TransformationDataEconomics.wrapClassDataEconomics();
        item2.Name = 'UnipolSai';
        item2.AccountAccountSocietyId = null;
        resultList.add(item2);
        inputMap = new Map<String, Object>();
        inputMap.put('Result', (Object)resultList);
        outMap = new Map<String, Object>();
        options = new Map<String, Object>();
        Test.startTest();
        TransformationDataEconomics instance = new TransformationDataEconomics();
        instance.call('checkDataEconomics', new Map<String, Object>{'input' => inputMap, 'output' => outMap, 'options' => options});
        Test.stopTest();
    }
}