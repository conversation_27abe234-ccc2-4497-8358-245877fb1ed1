public without sharing class OpportunityRelatedListControllerNS {

    @AuraEnabled
    public static String createOpportunity(String accountId, Date closeDate, /*Integer giorniLavorazione, */String idAgenzia){
        try {

            DateTime workingSlaDT = DateTime.newInstanceGMT(closeDate.year(), closeDate.month(), closeDate.day());
            workingSlaDT = workingSlaDT.addDays(1).addSeconds(-1);

            TimeZone tz = UserInfo.getTimeZone();
            Integer offSet = tz.getOffSet(DateTime.now());

            workingSlaDT = DateTime.newInstance(workingSlaDT.getTime() - offSet);

            Opportunity opp = new Opportunity(
                Name = 'AGE',
                AccountId = accountId,
                CloseDate = closeDate,
                StageName = 'In Gestione',
                Channel__c = 'Agenzia',
                ContactChannel__c = 'Agenzia',
                Rating__c = 'Calda',
                EngagementPoint__c = 'Agenzia',
                TakenInChargeDate__c = System.now(),
                TakenInChargeSLAExpiryDate__c = System.now().addDays(7),
                TakenInChargeSLAStartDate__c = System.now(),
                WorkingSLAStartDate__c = System.now(),
                WorkingSLAExpiryDate__c = workingSlaDT,//System.now().addDays(giorniLavorazione),
                AssignedTo__c = UserInfo.getUserId(),
                RecordTypeId = Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Agenziale').getRecordTypeId()
            );

            if (String.isNotBlank(idAgenzia)) {
                opp.Agency__c = idAgenzia;
            }

            Database.Insert(opp);
            return opp.Id;
            
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static void updateOpportunity(String opportunityId, String newName){
        try {
            
            Opportunity opp = new Opportunity(
                Id = opportunityId,
                Name = newName
            );

            Database.Update(opp);

        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Integer getTempiLavorazione(String accountId, String idAzienda){

        try {

            String tipoSoggetto = '';
            List<Asset> assetList = new List<Asset>();

            if (String.isNotBlank(idAzienda)) {
                
                assetList = [SELECT Key__c, Value__c from Asset WHERE MasterRecordId__r.FinServ__Account__c = :accountId AND MasterRecordId__r.FinServ__RelatedAccount__c = :idAzienda AND Key__c = 'PP_AGENZIA_SOCIETA_STATO'];
            }

            if (!assetList.isEmpty()) {
                tipoSoggetto = assetList[0].Value__c;
            } else {
                tipoSoggetto = 'Prospect Puro';
            }

            List<CalculationMatrixRow> calculationMatrixRows = [SELECT Id, InputData, OutputData FROM CalculationMatrixRow WHERE CalculationMatrixVersion.CalculationMatrix.UniqueName = 'Tempi_di_Lavorazione_Altro'];
            Map<String, Object> outputMatrix = new Map<String, Object>();
            Integer giorniLavorazione = 0;
            for (CalculationMatrixRow row : calculationMatrixRows) {
                if(row.InputData.contains(tipoSoggetto) && row.InputData.contains('Canale Fisico')){
                    outputMatrix = (Map<String, Object>) JSON.deserializeUntyped(row.OutputData);
                    giorniLavorazione = (Integer) outputMatrix.get('GiorniSLATrattativa');
                }
            }

            return giorniLavorazione;
            
            } catch (Exception e) {
                throw new AuraHandledException(e.getMessage());
            }
        }
}