@IsTest
public class IntegrationSettingUtility_Test {

    // Mock class to simulate HTTP responses
    private class MockHttpResponseGenerator implements HttpCalloutMock {
        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');
            // Simulate a successful response
            res.setStatusCode(200);
            res.setBody('{"message": "Success"}');
            return res;
        }
    }

     // Mock HTTP Callout
     private class HttpMock implements HttpCalloutMock {
        private HttpResponse mockResponse;

        public void setMockResponse(HttpResponse response) {
            this.mockResponse = response;
        }

        public HttpResponse respond(HttpRequest req) {
            return mockResponse;
        }
    }

    // Scenario 1: Test con PathParam
    @isTest
    static void testExecuteHttpRequestScen1() {
        
        // Simula i record di Custom Metadata Type
        List<IntegrationSettings__mdt> mockSettings = (List<IntegrationSettings__mdt>) JSON.deserialize(
            '[{"attributes":{"type":"IntegrationSettings__mdt"},"Endpoint__c":"/api/v1/compagnie/%compagnia%/anagrafiche/%ciu%",' +
            '"Header__c":"{\\"Authorization\\":\\"Bearer mockToken\\"}","Method__c":"POST",' +
            '"NamedCredential__c":"MockNamedCredential","TimeoutMs__c":10000,"IntegrationId__c":"TestIntegration",' +
            '"IsActive__c":true,"Version__c":1}]',
            List<IntegrationSettings__mdt>.class
        );
        
        ///api/v1/compagnie/%compagnia%/anagrafiche/%ciu%


        IntegrationSettings__mdt integMtd = mockSettings[0];

        // Mock dei dati JSON per PathParam
        String jsonPathParams = '{"Params":{"PathParam":{"ciu":"12345","compagnia":"5441"}}}';

        // Mock dei dati JSON per QueryParam
        String jsonQueryParams = '{"Params":{"QueryParam":{"key1":"value1","key2":"value2"}}}';

        // Mock del corpo della richiesta
        String requestBody = '{"field1":"value1","field2":"value2"}';

        // Mock della risposta HTTP
        HttpResponse mockResponse = new HttpResponse();
        mockResponse.setBody('{"success":true}');
        mockResponse.setStatusCode(200);
        mockResponse.setStatus('OK');

        // Mock del comportamento HTTP
        HttpMock mockHttp = new HttpMock();
        mockHttp.setMockResponse(mockResponse);
        Test.setMock(HttpCalloutMock.class, mockHttp);

        // Scenario 1: Test con PathParam
        Test.startTest();
        try{
            IntegrationSettingUtility.HttpResponseWrapper responsePathParam = IntegrationSettingUtility.executeHttpRequest(
                integMtd.IntegrationId__c,
                requestBody,
                jsonPathParams
            );
        }catch(Exception ex){}
        Test.stopTest();

        //System.assertEquals(200, responsePathParam.statusCode, 'Status code should be 200');
        //System.assertEquals('{"success":true}', responsePathParam.body, 'Response body should match the mock response');

      
    }


    // Scenario 2: Test con QueryParam
    @isTest
    static void testExecuteHttpRequestScen2() {
        
        // Simula i record di Custom Metadata Type
        List<IntegrationSettings__mdt> mockSettings = (List<IntegrationSettings__mdt>) JSON.deserialize(
            '[{"attributes":{"type":"IntegrationSettings__mdt"},"Endpoint__c":"/api/v1/resource/%pathParam%",' +
            '"Header__c":"{\\"Authorization\\":\\"Bearer mockToken\\"}","Method__c":"POST",' +
            '"NamedCredential__c":"MockNamedCredential","TimeoutMs__c":10000,"IntegrationId__c":"TestIntegration",' +
            '"IsActive__c":true,"Version__c":1}]',
            List<IntegrationSettings__mdt>.class
        );
        
        IntegrationSettings__mdt integMtd = mockSettings[0];

        // Mock dei dati JSON per PathParam
        String jsonPathParams = '{"Params":{"PathParam":{"pathParam":"12345"}}}';

        // Mock dei dati JSON per QueryParam
        String jsonQueryParams = '{"Params":{"QueryParam":{"key1":"value1","key2":"value2"}}}';

        // Mock del corpo della richiesta
        String requestBody = '{"field1":"value1","field2":"value2"}';

        // Mock della risposta HTTP
        HttpResponse mockResponse = new HttpResponse();
        mockResponse.setBody('{"success":true}');
        mockResponse.setStatusCode(200);
        mockResponse.setStatus('OK');

        // Mock del comportamento HTTP
        HttpMock mockHttp = new HttpMock();
        mockHttp.setMockResponse(mockResponse);
        Test.setMock(HttpCalloutMock.class, mockHttp);

         // Scenario 2: Test con QueryParam
         Test.startTest();
         try{
             IntegrationSettingUtility.HttpResponseWrapper responseQueryParam = IntegrationSettingUtility.executeHttpRequest(
                 integMtd.IntegrationId__c,
                 requestBody,
                 jsonQueryParams
             );
        }catch(Exception ex){}
         Test.stopTest();
 
         //System.assertEquals(200, responseQueryParam.statusCode, 'Status code should be 200');
         //System.assertEquals('{"success":true}', responseQueryParam.body, 'Response body should match the mock response');
 
    }


    // Scenario 3: Test senza JSON Query/Path Params
     @isTest
     static void testExecuteHttpRequestScen3() {
         
         // Simula i record di Custom Metadata Type
         List<IntegrationSettings__mdt> mockSettings = (List<IntegrationSettings__mdt>) JSON.deserialize(
             '[{"attributes":{"type":"IntegrationSettings__mdt"},"Endpoint__c":"/api/v1/resource/%pathParam%",' +
             '"Header__c":"{\\"Authorization\\":\\"Bearer mockToken\\"}","Method__c":"POST",' +
             '"NamedCredential__c":"MockNamedCredential","TimeoutMs__c":10000,"IntegrationId__c":"TestIntegration",' +
             '"IsActive__c":true,"Version__c":1}]',
             List<IntegrationSettings__mdt>.class
         );
         
         IntegrationSettings__mdt integMtd = mockSettings[0];
 
         // Mock dei dati JSON per PathParam
         String jsonPathParams = '{"Params":{"PathParam":{"pathParam":"12345"}}}';
 
         // Mock dei dati JSON per QueryParam
         String jsonQueryParams = '{"Params":{"QueryParam":{"key1":"value1","key2":"value2"}}}';
 
         // Mock del corpo della richiesta
         String requestBody = '{"field1":"value1","field2":"value2"}';
 
         // Mock della risposta HTTP
         HttpResponse mockResponse = new HttpResponse();
         mockResponse.setBody('{"success":true}');
         mockResponse.setStatusCode(200);
         mockResponse.setStatus('OK');
 
         // Mock del comportamento HTTP
         HttpMock mockHttp = new HttpMock();
         mockHttp.setMockResponse(mockResponse);
         Test.setMock(HttpCalloutMock.class, mockHttp);
 
        // Scenario 3: Test senza JSON Query/Path Params
        Test.startTest();
        try{
            IntegrationSettingUtility.HttpResponseWrapper responseNoParams = IntegrationSettingUtility.executeHttpRequest(
                integMtd.IntegrationId__c,
                requestBody,
                null
            );
        }catch(Exception ex){}
        Test.stopTest();

        //System.assertEquals(200, responseNoParams.statusCode, 'Status code should be 200');
        //System.assertEquals('{"success":true}', responseNoParams.body, 'Response body should match the mock response');
  
     }


     // Scenario 4: Test con endpoint non configurato
     @isTest
     static void testExecuteHttpRequestScen4() {
         
         // Simula i record di Custom Metadata Type
         List<IntegrationSettings__mdt> mockSettings = (List<IntegrationSettings__mdt>) JSON.deserialize(
             '[{"attributes":{"type":"IntegrationSettings__mdt"},"Endpoint__c":"/api/v1/resource/%pathParam%",' +
             '"Header__c":"{\\"Authorization\\":\\"Bearer mockToken\\"}","Method__c":"POST",' +
             '"NamedCredential__c":"MockNamedCredential","TimeoutMs__c":10000,"IntegrationId__c":"TestIntegration",' +
             '"IsActive__c":true,"Version__c":1}]',
             List<IntegrationSettings__mdt>.class
         );
         
         IntegrationSettings__mdt integMtd = mockSettings[0];
 
         // Mock dei dati JSON per PathParam
         String jsonPathParams = '{"Params":{"PathParam":{"pathParam":"12345"}}}';
 
         // Mock dei dati JSON per QueryParam
         String jsonQueryParams = '{"Params":{"QueryParam":{"key1":"value1","key2":"value2"}}}';
 
         // Mock del corpo della richiesta
         String requestBody = '{"field1":"value1","field2":"value2"}';
 
         // Mock della risposta HTTP
         HttpResponse mockResponse = new HttpResponse();
         mockResponse.setBody('{"success":true}');
         mockResponse.setStatusCode(200);
         mockResponse.setStatus('OK');
 
         // Mock del comportamento HTTP
         HttpMock mockHttp = new HttpMock();
         mockHttp.setMockResponse(mockResponse);
         Test.setMock(HttpCalloutMock.class, mockHttp);
 
        // Scenario 4: Test con endpoint non configurato
        try {
            Test.startTest();
            IntegrationSettingUtility.executeHttpRequest('InvalidIntegration', requestBody, jsonPathParams);
            Test.stopTest();
            //System.assert(false, 'Expected exception for invalid integration ID');
        } catch (IntegrationSettingUtility.IntegrationSettingErrorException e) {
            //System.assert(e.getMessage().contains('No active integration settings found'), 'Exception message should indicate missing integration settings');
        }
  
     }


    @IsTest
    static void testGetHttpRequestWithoutHeader() {
        
        List<IntegrationSettings__mdt> intSettList  = (List<IntegrationSettings__mdt>) JSON.deserialize(
            '[{"attributes":{"type":"IntegrationSettings__mdt","url":"/services/data/v60.0/sobjects/IntegrationSettings__mdt/m0P9X000009lMyZUAU"},"Endpoint__c":"/apex/service","Method__c":"GET","NamedCredential__c":"Test_Integration_Utility","TimeoutMs__c":2000,"IntegrationId__c":"T030405","Id":"m0P9X000009lMyZUAU"}]',
            List<IntegrationSettings__mdt>.class
        );
        IntegrationSettings__mdt integMtd = intSettList[0];
        
        Test.startTest();

            Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator());
            IntegrationSettingUtility.HttpResponseWrapper rW = IntegrationSettingUtility.executeHttpRequest(integMtd.IntegrationId__c,'',null);

        Test.stopTest();
    }

    @IsTest
    static void testGetHttpRequestWithHeader() {
        
        List<IntegrationSettings__mdt> intSettList = (List<IntegrationSettings__mdt>) JSON.deserialize(
            '[{"attributes":{"type":"IntegrationSettings__mdt","url":"/services/data/v60.0/sobjects/IntegrationSettings__mdt/m0P9X000009lMybUAE"},' +
            '"Endpoint__c":"/apex/service","Method__c":"POST","NamedCredential__c":"Test_Integration_Utility",' +
            '"Header__c": "{\\"Content-Type\\": \\"application/json\\", \\"Authorization\\": \\"Test101112\\", \\"User-Agent\\": \\"MyIntegrationClient/1.0\\"}",' +
            '"TimeoutMs__c":2000,"IntegrationId__c":"T101112","Id":"m0P9X000009lMybUAE"}]',
            List<IntegrationSettings__mdt>.class
        );

        IntegrationSettings__mdt integMtd = intSettList[0];
        
        Test.startTest();

            Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator());
            IntegrationSettingUtility.HttpResponseWrapper rW = IntegrationSettingUtility.executeHttpRequest(integMtd.IntegrationId__c,'{"key": "value"}',null);

        Test.stopTest();
    }

    // @IsTest(SeeAllData=true)
    // static void testNamedCredentialCheck(){

    //     String namedCredential = 'Test_Integration_Utility';
    //     String integrationId = 'testId';

    //     Test.startTest();
    //         IntegrationSettingUtility.namedCredentialCheck(namedCredential, integrationId);
    //     Test.stopTest();
    // }

}