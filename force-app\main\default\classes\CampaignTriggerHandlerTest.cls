@isTest
private class CampaignTriggerHandlerTest {

    @testSetup
    static void setupData() {
        // Creazione gerarchia di Campaign (fino a 3 livelli)
        Campaign root = new Campaign(Name = 'Root Campaign');
        insert root;

        Campaign child1 = new Campaign(Name = 'Child 1', ParentId = root.Id);
        insert child1;

        Campaign child2 = new Campaign(Name = 'Child 2', ParentId = child1.Id);
        insert child2;

        Campaign child3 = new Campaign(Name = 'Child 3', ParentId = child2.Id);
        insert child3;
    }

    @isTest
    static void testBeforeUpdate_WithRecursiveEvaluation() {
        // Recupera la campagna più "figlia"
        Campaign c = [SELECT Id, Name, ParentId FROM Campaign WHERE Name = 'Child 3' LIMIT 1];
        c.Name = 'Updated Name';

        Test.startTest();
        CampaignTriggerHandler.beforeUpdate(new List<Campaign>{ c });
        Test.stopTest();

    }

    @isTest
    static void testEvaluateCampaign_Campaign1() {
        Campaign c = new Campaign(Name = 'Test C1');
        insert c;

        Map<String, Campaign> mapC = new Map<String, Campaign>{ 'campaign0' => c };
        Map<String, Object> args = new Map<String, Object>{
            'acc' => 1,
            'campaign' => mapC
        };

        Object result = CampaignTriggerHandler.evaluateCampaign(args);
    }

    @isTest
    static void testEvaluateCampaign_Campaign2() {
        Campaign c = new Campaign(Name = 'Test C2',ParentId=null);
        insert c;

        Map<String, Campaign> mapC = new Map<String, Campaign>{ 'campaign0' => c };
        Map<String, Object> args = new Map<String, Object>{
            'acc' => 2,
            'campaign' => mapC
        };

        try{
            Object result = CampaignTriggerHandler.evaluateCampaign(args);
        }catch(Exception ex){}
    }

    @isTest
    static void testEvaluateCampaign_Campaign3() {
        Campaign root = [SELECT Id FROM Campaign WHERE Name = 'Root Campaign'];
        Campaign c1 = [SELECT Id FROM Campaign WHERE Name = 'Child 1'];
        Campaign c2 = [SELECT Id FROM Campaign WHERE Name = 'Child 2'];
        Campaign c3 = [SELECT Id FROM Campaign WHERE Name = 'Child 3'];

        Map<String, Campaign> mapC = new Map<String, Campaign>{
            'campaign0' => c3,
            'campaign1' => c2,
            'campaign2' => c1,
            'campaign3' => root
        };

        Map<String, Object> args = new Map<String, Object>{
            'acc' => 3,
            'campaign' => mapC
        };

        Object result = CampaignTriggerHandler.evaluateCampaign(args);
    }

    @isTest
    static void testCheckRecursive_PermissionDenied() {
        Test.startTest();
        try {
            // Simula mancanza accesso (richiede impostazioni di permessi profilo o mocks avanzati)
            Schema.DescribeSObjectResult describe = Campaign.SObjectType.getDescribe();
            System.assertEquals(true, describe.isAccessible()); // In realtà true in test
        } catch (Exception e) {
        }
        Test.stopTest();
    }
}