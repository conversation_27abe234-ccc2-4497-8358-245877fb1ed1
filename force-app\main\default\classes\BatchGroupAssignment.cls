public class BatchGroupAssignment implements Database.Batchable<SObject>, Database.Stateful {
    // Impostiamo il tipo di record da elaborare (GroupAssignment__c)
    public String query;
    private Boolean concatenate = false;

    public BatchGroupAssignment() {
        this(false);
    }

    public BatchGroupAssignment(Boolean concatenate) {
        this.concatenate = concatenate;
        // Query per ottenere i record da VDM_CIP_ASSIGNMENT
        query = 'SELECT Id, CIP__c, CIP__r.Name, NetworkUser__c, NetworkUser__r.FiscalCode__c, IsActive__c,TechStatus__c,TechStatusDetail__c FROM GroupAssignment__c WHERE TechStatus__c = \'TO_BE_PROCESSED\'';
    }

    public BatchGroupAssignment(String status) {
        // Query per ottenere i record da VDM_CIP_ASSIGNMENT
        String tempStatus = (String.isNotBlank(status)) ? status : 'TO_BE_PROCESSED';
        query = 'SELECT Id, CIP__c, CIP__r.Name, NetworkUser__c, NetworkUser__r.FiscalCode__c, IsActive__c,TechStatus__c,TechStatusDetail__c FROM GroupAssignment__c WHERE TechStatus__c = \'' + tempStatus + '\'';
    }

    // Query per ottenere i record da elaborare
    public Database.QueryLocator start(Database.BatchableContext BC) {
        return Database.getQueryLocator(query);
    }

    // Logica di elaborazione dei record (per ogni record di GroupAssignment__c)
    public void execute(Database.BatchableContext BC, List<sObject> scope) {
        if (scope != null && !scope.isEmpty()) {
            Map<String, Id> mapGroupByName = new Map<String, Id>();
            Map<String, User> mapUserByCF = new Map<String, User>();
            Map<String, GroupMember> mapExistingMembers = new Map<String, GroupMember>();
            Set<String> groupNames = new Set<String>();
            Set<String> cfList = new Set<String>();
            Set<String> groupIds = new Set<String>();

            List<Group__c> groupToUpdate = new List<Group__c>();
            List<GroupMember> memberToInsert = new List<GroupMember>();
            List<GroupMember> memberToDelete = new List<GroupMember>();

            // Compongo Set di "Nomi gruppi" e "Fiscal Code"
            for (GroupAssignment__c ga : (List<GroupAssignment__c>) scope) {
                if (String.isNotBlank(ga.CIP__c))
                    groupNames.add(ga.CIP__r.Name);
                if (String.isNotBlank(ga.NetworkUser__c))
                    cfList.add(ga.NetworkUser__r.FiscalCode__c);
            }

            // Recupero gli Utenti con quei Fiscal Code
            for (User u : [SELECT Id, FiscalCode__c FROM User WHERE FiscalCode__c IN :cfList]) {
                mapUserByCF.put(u.FiscalCode__c, u);
            }

            // Recupero i Gruppi con quei Nomi
            for (Group g : [SELECT Id, Name FROM Group WHERE Name IN :groupNames]) {
                mapGroupByName.put(g.Name, g.Id);
            }

            for (GroupMember gm : [SELECT Id, UserOrGroupId, GroupId FROM GroupMember WHERE GroupId IN :mapGroupByName.values()]) {
                String key = gm.GroupId + '_' + gm.UserOrGroupId;
                mapExistingMembers.put(key, gm);
            }

            for (GroupAssignment__c ga : (List<GroupAssignment__c>) scope) {
                // Update GROUP__c for PublicGroupId__c
                if (String.isNotBlank(ga.CIP__c) && mapGroupByName.containsKey(ga.CIP__r.Name) && !groupIds.contains(ga.CIP__c)) {
                    Group__c g = new Group__c();
                    g.Id = ga.CIP__c;
                    g.PublicGroupId__c = mapGroupByName.get(ga.CIP__r.Name);

                    groupToUpdate.add(g);

                    groupIds.add(ga.CIP__c);
                }

                if (String.isNotBlank(ga.CIP__c) && !mapGroupByName.containsKey(ga.CIP__r.Name)) {
                    // Se non ho trovato il gruppo nella query

                    ga.TechStatus__c = 'PROCESSED_KO';
                    ga.TechStatusDetail__c = 'Group not found: ' + ga.CIP__r.Name;
                } else if (String.isNotBlank(ga.NetworkUser__c) && String.isBlank(ga.NetworkUser__r.FiscalCode__c)) {
                    // Se il NetworkUser ha il campo FiscalCode__c vuoto

                    ga.TechStatus__c = 'PROCESSED_KO';
                    ga.TechStatusDetail__c = 'Fiscal Code empty or null on NetworkUser, ID: ' + ga.NetworkUser__c;
                } else if (String.isNotBlank(ga.NetworkUser__c) && !mapGroupByName.containsKey(ga.NetworkUser__r.FiscalCode__c)) {
                    // Se non ho trovato lo User nella query

                    ga.TechStatus__c = 'PROCESSED_KO';
                    ga.TechStatusDetail__c = 'User not found: ' + ga.NetworkUser__r.FiscalCode__c;
                } else if (String.isNotBlank(ga.CIP__c) && String.isNotBlank(ga.NetworkUser__c) && mapGroupByName.containsKey(ga.CIP__r.Name) && mapUserByCF.containsKey(ga.NetworkUser__r.FiscalCode__c) && ga.IsActive__c) {
                    // GroupAssignment TRUE --> da inserire

                    GroupMember gm = new GroupMember();
                    gm.GroupId = mapGroupByName.get(ga.CIP__r.Name);
                    gm.UserOrGroupId = mapUserByCF.get(ga.NetworkUser__r.FiscalCode__c).Id;

                    memberToInsert.add(gm);

                    ga.TechStatus__c = 'PROCESSED_OK';
                    ga.TechStatusDetail__c = '';
                } else if (String.isNotBlank(ga.CIP__c) && String.isNotBlank(ga.NetworkUser__c) && mapGroupByName.containsKey(ga.CIP__r.Name) && mapUserByCF.containsKey(ga.NetworkUser__r.FiscalCode__c) && !ga.IsActive__c) {
                    // GroupAssignment FALSE --> da eliminare

                    String key = mapGroupByName.get(ga.CIP__r.Name) + '_' + mapUserByCF.get(ga.NetworkUser__r.FiscalCode__c);
                    if (mapExistingMembers.containsKey(key)) {
                        memberToDelete.add(mapExistingMembers.get(key));
                    }

                    ga.TechStatus__c = 'PROCESSED_OK';
                    ga.TechStatusDetail__c = '';
                }
            }

            if (!memberToDelete.isEmpty())
                Database.delete(memberToDelete);
            if (!memberToInsert.isEmpty())
                Database.insert(memberToInsert);
            if (!groupToUpdate.isEmpty())
                Database.update(groupToUpdate);
            if (!scope.isEmpty())
                Database.update((List<GroupAssignment__c>) scope);
        }
    }

    // Metodo di gestione per concludere il batch
    public void finish(Database.BatchableContext BC) {
        // Puoi aggiungere logiche finali o inviare notifiche, se necessario.
        System.debug('Batch Process Finished.');

        if (concatenate) {
            Database.executeBatch(new BatchProcessServiceResourcesV2(concatenate), 200);
        }
    }
}
