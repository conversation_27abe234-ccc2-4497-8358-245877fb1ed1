public with sharing class PolicyWSWrapper {
    @AuraEnabled
    public String id;
    @AuraEnabled
    public String agencyCode;
    @AuraEnabled
    public String appendixNumber;
    @AuraEnabled
    public String archiveNumber;
    @AuraEnabled
    public String areaOfNeed;
    @AuraEnabled
    public String cip;
    @AuraEnabled
    public String company;
    @AuraEnabled
    public String companyCode;
    @AuraEnabled
    public Datetime effectiveDate;
    @AuraEnabled
    public Datetime expirationDate;
    @AuraEnabled
    public String externalId;
    @AuraEnabled
    public String folderId;
    @AuraEnabled
    public String lob1;
    @AuraEnabled
    public String lob2;
    @AuraEnabled
    public String lob3;
    @AuraEnabled
    public String managementBranchCode;
    @AuraEnabled
    public String name;
    @AuraEnabled
    public String policyBranchCode;
    @AuraEnabled
    public String policyName;
    @AuraEnabled
    public String policyType;
    @AuraEnabled
    public String position;
    @AuraEnabled
    public Decimal premiumAmount;
    @AuraEnabled
    public String premiumFrequency;
    @AuraEnabled
    public String referencePolicyNumber;
    @AuraEnabled
    public String sourceSystem;
    @AuraEnabled
    public String sourceSystemId;
    @AuraEnabled
    public String status;
    @AuraEnabled
    public String substatus;
    @TestVisible
    private static Boolean testCoverage { get; set; }
}
