public without sharing class ServiceResourceSharingStrategy implements SharingStrategy {
    private List<ServiceResource> serviceResources = new List<ServiceResource>();
    private List<ServiceResourceShare> shares = new List<ServiceResourceShare>();

    public void collectTargetRecords(List<SObject> records) {
        for (SObject s : records) {
            if (s instanceof ServiceResource) serviceResources.add((ServiceResource)s);
        }
    }

    public void applySharing(){
        Map<Id, User> userMap = new Map<Id, User>();

        for (ServiceResource sr : serviceResources) {
            if (sr.RelatedRecordId != null)
                userMap.put(sr.RelatedRecordId, null);
        }

        userMap.putAll([SELECT Id, FiscalCode__c FROM User WHERE Id IN :userMap.keySet()]);

        Set<String> fiscalCodes = new Set<String>();
        for (User u : userMap.values()) {
            if (u.FiscalCode__c != null) {
                fiscalCodes.add(u.FiscalCode__c);
            }
        }

        Map<String, NetworkUser__c> fiscalCodeToNetworkUser = new Map<String, NetworkUser__c>();
        List<NetworkUser__c> networkUsersList = [SELECT Id, FiscalCode__c, Agency__c, Agency__r.ExternalId__c FROM NetworkUser__c WHERE FiscalCode__c IN :fiscalCodes];
        for(NetworkUser__c nu : networkUsersList){
            if(nu.FiscalCode__c != null){
                fiscalCodeToNetworkUser.put(nu.FiscalCode__c, nu);
            }
        }

        Set<String> externalIds = new Set<String>();
        for (NetworkUser__c nu : fiscalCodeToNetworkUser.values()) {
            if (nu.Agency__r.ExternalId__c != null)
                externalIds.add(nu.Agency__r.ExternalId__c);
        }

        Map<String, Group> groupMap = GroupHelper.getGroupsByExternalIds(externalIds);

        for (ServiceResource sr : serviceResources) {
            User u = userMap.get(sr.RelatedRecordId);
            if (u == null) continue;

            NetworkUser__c nu = fiscalCodeToNetworkUser.get(u.FiscalCode__c);
            if (nu == null) continue;

            Group publicGroup = groupMap.get(nu.Agency__r.ExternalId__c);
            if (publicGroup == null) continue;

            shares.add(new ServiceResourceShare(
                ParentId = sr.Id,
                UserOrGroupId = publicGroup.Id,
                AccessLevel = 'Edit',
                RowCause = Schema.ServiceResourceShare.RowCause.Manual
            ));
        }

        if (!shares.isEmpty()) insert shares;
    }
}