public with sharing class NetworkUserHelper {

    public class NetworkUserWrapper {
        @AuraEnabled
        public String companyId;
        @AuraEnabled
        public String companyName;
        @AuraEnabled
        public List<NetworkUserOption> options;
        @AuraEnabled
        public String selectedUserId;
    }

    public class NetworkUserOption {
        @AuraEnabled
        public String label;
        @AuraEnabled
        public String value;
    }

    @AuraEnabled(cacheable=true)
    public static List<NetworkUserWrapper> getUserNetworkData() {
        System.debug('START getUserNetworkData');
        List<NetworkUserWrapper> result = new List<NetworkUserWrapper>();
        try {
            User u = [SELECT FederationIdentifier FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];
            System.debug('Current user FederationIdentifier: ' + u.FederationIdentifier);

            if(String.isBlank(u.FederationIdentifier)) {
                System.debug('FederationIdentifier is blank, returning empty result');
                return result;
            }

            Id recordTypeSociety = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId();
            System.debug('Record Type Id for Society: ' + recordTypeSociety);

            // Get all societies (Account) and build a map ExternalId__c -> Name
            List<Account> societies = [SELECT ExternalId__c, Name FROM Account WHERE RecordTypeId = :recordTypeSociety];
            Map<String, String> societyMap = new Map<String, String>();
            for (Account acc : societies) {
                societyMap.put(acc.ExternalId__c, acc.Name);
            }
            System.debug('Society map size: ' + societyMap.size());
            System.debug('Society map: ' + societyMap);
            if (societyMap.isEmpty()) {
                System.debug('No societies found, returning empty result');
                //return result;
            }

            // Get all active NetworkUser__c for this user
            List<NetworkUser__c> nus = [
                SELECT Id, NetworkUser__c, Preferred__c, Society__c
                FROM NetworkUser__c
                WHERE FiscalCode__c = :u.FederationIdentifier AND IsActive__c = true
                AND Society__c IN :societyMap.keySet()
                ORDER BY Society__c, NetworkUser__c
            ];
            System.debug('Found ' + nus.size() + ' active NetworkUser__c records for user: ' + u.FederationIdentifier);

            // Group by Society__c (which is ExternalId__c)
            Map<String, NetworkUserWrapper> companyMap = new Map<String, NetworkUserWrapper>();
            for (NetworkUser__c nu : nus) {
                if (!companyMap.containsKey(nu.Society__c)) {
                    NetworkUserWrapper wrap = new NetworkUserWrapper();
                    wrap.companyId = nu.Society__c;
                    wrap.companyName = societyMap.get(nu.Society__c);
                    wrap.options = new List<NetworkUserOption>();
                    wrap.selectedUserId = null;
                    companyMap.put(nu.Society__c, wrap);
                }
                NetworkUserOption opt = new NetworkUserOption();
                opt.label = nu.NetworkUser__c;
                opt.value = nu.Id;
                companyMap.get(nu.Society__c).options.add(opt);
                if (nu.Preferred__c) {
                    companyMap.get(nu.Society__c).selectedUserId = nu.Id;
                }
            }
            result.addAll(companyMap.values());
            System.debug('END getUserNetworkData: ' + result);
        } catch(Exception ex) {
            System.debug('Exception in getUserNetworkData: ' + ex.getMessage());
            // Optionally, you can throw a custom exception or handle as needed
        }
        return result;
    }

    @AuraEnabled
    public static void setPreferredNetworkUser(String networkUserId, String companyId) {
        System.debug('START setPreferredNetworkUser: ' + networkUserId + ', ' + companyId);
        try {
            if (String.isBlank(networkUserId) || String.isBlank(companyId)) return;

            User u = [SELECT FederationIdentifier FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];
            List<NetworkUser__c> nus = [
                SELECT Id, Preferred__c
                FROM NetworkUser__c
                WHERE FiscalCode__c = :u.FederationIdentifier
                AND Society__c = :companyId
                AND IsActive__c = true
            ];
            for (NetworkUser__c nu : nus) {
                nu.Preferred__c = (nu.Id == networkUserId);
            }
            update nus;
            System.debug('END setPreferredNetworkUser');
        } catch(Exception ex) {
            System.debug('Exception in setPreferredNetworkUser: ' + ex.getMessage());
            // Optionally, you can throw a custom exception or handle as needed
        }
    }
}