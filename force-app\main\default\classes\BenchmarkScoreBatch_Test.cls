@isTest
public with sharing class BenchmarkScoreBatch_Test {
    @testSetup
    static void makeData(){
        List<String> agencySizes = new List<String>{'Piccola','Media','Grande'};
        List<String> agencyClusters = new List<String>{'Cluster 1','Cluster 2','Cluster 3'};

        List<RecordType> agencyRT = new List<RecordType>();
        List<BusinessHours> standardBusinessHours = new List<BusinessHours>();
        List<Account> testAgencies = new List<Account>();
        List<KPI__c> testKPIs = new List<KPI__c>();
        agencyRT = [SELECT Id, Name FROM RecordType WHERE DeveloperName = 'Agency' LIMIT 1];
        standardBusinessHours = [SELECT Id, Name from BusinessHours WHERE Name = 'Standard Office BH' LIMIT 1];

        FlowTriggersActivation__c triggerSettings = new FlowTriggersActivation__c();
        triggerSettings.SkipTriggers__c = true;
        insert triggerSettings;
        
        if(!agencyRT.isEmpty() && !standardBusinessHours.isEmpty()){
            for(Integer i = 0; i < 50; i++){
                Account testAgency = new Account();
                testAgency.RecordTypeId = agencyRT.get(0).Id; 
                testAgency.AccountNumber = String.valueOf(i);
                testAgency.Name = 'Test Agenzia ' + i;
                Integer randomSize = (Integer)(Math.random() * 3);
                Integer randomCluster = (Integer)(Math.random() * 3);
                testAgency.Size__c = agencySizes.get(randomSize);
                testAgency.Cluster__c = agencyClusters.get(randomCluster);
                testAgency.BusinessHours__c = standardBusinessHours.get(0).Id;
                testAgencies.add(testAgency);
            }
            insert testAgencies;

            List<Account> insertedTestAgencies = new List<Account>();
            insertedTestAgencies = [SELECT Id,Name,Cluster__c,Size__c,AccountNumber FROM Account WHERE RecordType.DeveloperName = 'Agency'];
            for(Account acc : insertedTestAgencies){
                KPI__c testKPI = new KPI__c();
                testKPI.Name = 'Test KPI ' + acc.AccountNumber;
                testKPI.Agency__c = acc.Id;
                testKPI.AverageLeadProcessingTime__c = (Math.random() * 10) + 1;
                testKPI.DigitalPenetration__c = 50;
                testKPI.ContactRate__c = 60;
                testKPI.ExternalId__c = 'Default KPI Id' + acc.AccountNumber;
                testKPI.ScoreTotal__c = 1.8;
                testKPI.PrivateAreaRegistration__c = 60;
                testKPI.OmnichannelQuotes__c = 55;
                testKPI.BenchmarkPrivateAreaRegistration__c = 69.2;
                testKPI.BenchmarkOmnichannelQuotes__c = 64.2;
                testKPI.ScorePrivateAreaRegistration__c = 0.1;
                testKPI.ScoreOmnichannelQuotes__c = 0.1;
                testKPI.WeightPrivateAreaRegistration__c = 10;
                testKPI.WeightOmnichannelQuotes__c = 10;
                testKPI.GapPrivateAreaRegistration__c = -9.2;
                testKPI.GapOmnichannelQuotes__c = -9.2;
                testKPI.NumberPoliciesByContact__c = 65;
                testKPI.ContactActivitiesProcessedClosed__c = 90;
                testKPI.ContactActivitiesAssignedPeriod__c = 200;
                testKPI.ScoreProcessingAssignedActivities__c = 0.2;
                testKPI.ScoreConversionAssignedActivities__c = 0.4;
                testKPI.ScoreAverageLeadProcessingTime__c = 0.8;
                testKPI.ScoreDigitalPenetration__c = 0.2;
                testKPI.NumberClientsPortfolio__c = 221;
                testKPI.NumberContactableClients__c = 491;
                testKPI.BenchmarkContactActivitiesAssigned__c = 56.6;
                testKPI.BenchmarkConversionAssignedActivities__c = 76.8;
                testKPI.BenchmarkAverageLeadProcessingTime__c = 7.2;
                testKPI.BenchmarkDigitalPenetration__c = 59.2;
                testKPI.WeightContactActivitiesAssigned__c = 20;
                testKPI.WeightConversionAssignedActivities__c = 20;
                testKPI.WeightAverageLeadProcessingTime__c = 20;
                testKPI.WeightDigitalPenetration__c = 20;
                testKPI.AverageLeadProcessingTime__c = 5;
                testKPI.GapContactActivitiesAssigned__c = -11.6;
                testKPI.GapConversionAssignedActivities__c = -4.8;
                testKPI.GapAverageLeadProcessingTime__c = -2.2;
                testKPI.GapDigitalPenetration__c = -9.2;
                testKPIs.add(testKPI);
            }
            insert testKPIs;

            List<KPI__c> insertedTestKPIs = new List<KPI__c>();
            insertedTestKPIs = [SELECT Id,Name,ScoreTotal__c,AverageLeadProcessingTime__c FROM KPI__c];
            for(Integer i = 0; i < insertedTestKPIs.size(); i++){
                insertedTestAgencies.get(i).KPI__c = insertedTestKPIs.get(0).Id;
            }
            update insertedTestAgencies;
        }
    }

    @isTest
    static void testExecute(){
        Test.startTest();
        Id batchJobId = Database.executeBatch(new BenchmarkScoreBatch());
        Test.stopTest();   
        List<KPI__c> insertedTestKPIs = new List<KPI__c>();
        insertedTestKPIs = [SELECT Id,Name,BenchmarkScore__c FROM KPI__c LIMIT 50];
      	Integer benchmarkScoreCounter = 0;
        for(KPI__c kpi : insertedTestKPIs){
            if(kpi.BenchmarkScore__c != null){
            	benchmarkScoreCounter = benchmarkScoreCounter + 1;
            }
        }
        System.assertEquals(50, benchmarkScoreCounter);
    }

    @isTest
    static void testExecuteScheduled(){
        Test.startTest();
        BenchmarkScoreBatch job = new BenchmarkScoreBatch();
        String chron = '0 0 23 * * ?';        
        String jobId = System.schedule('BenchmarkScoreBatch', chron, job);
        Test.stopTest();

        //Verify that the job is scheduled
        CronTrigger ct = [SELECT Id, CronExpression, TimesTriggered, NextFireTime FROM CronTrigger WHERE Id = :jobId LIMIT 1];
        System.assertEquals(chron, ct.CronExpression);
        System.assertEquals(0, ct.TimesTriggered);
    }
}
