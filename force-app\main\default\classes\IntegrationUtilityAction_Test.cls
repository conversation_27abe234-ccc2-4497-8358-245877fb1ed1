@isTest
public class IntegrationUtilityAction_Test {
    
    @isTest
    static void testCall() {
        HttpResponse mockResponse = new HttpResponse();
        mockResponse.setBody('{"key": "value"}');
        mockResponse.setStatusCode(200);
        mockResponse.setStatus('OK');
        
        HttpRequest mockRequest = new HttpRequest();
        mockRequest.setEndpoint('https://example.com');
        mockRequest.setMethod('POST');
        
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator(mockResponse));
        
        Map<String, Object> input = new Map<String, Object>{
            'integrationId' => 'T030405',
            'body' => '{"test": "data"}'
        };
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>{
            'input' => input,
            'output' => output
        };
        
        IntegrationUtilityAction action = new IntegrationUtilityAction();
        
        Test.startTest();
        Object result = action.call('execute', args);
        Test.stopTest();
        
        System.assertNotEquals(null, result, 'Result should not be null');
        System.assertEquals('{"key": "value"}', output.get('result'), 'Output should contain the result body');
        System.assertEquals(200, output.get('statusCode'), 'Output should contain the status code');
        System.assertEquals('OK', output.get('status'), 'Output should contain the status');
    }
    
    private class MockHttpResponseGenerator implements HttpCalloutMock {
        private HttpResponse response;
        
        public MockHttpResponseGenerator(HttpResponse response) {
            this.response = response;
        }
        
        public HttpResponse respond(HttpRequest req) {
            return response;
        }
    }
}