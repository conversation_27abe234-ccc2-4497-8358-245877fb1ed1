<aura:component implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction,lightning:availableForFlowActions" access="global" >
	<aura:attribute name="listViewId" type="String" />    
    <lightning:navigation aura:id="navLink"/>
    <aura:handler name="init" value="{!this}" action="{!c.invoke}" />
</aura:component>
