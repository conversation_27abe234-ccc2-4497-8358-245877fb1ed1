@isTest
public class GetRecordId_Test {

    @isTest
    static void test_method(){
        
        Test.startTest();
        
        try{
            GetRecordId.getAccount();
        }catch(Exception ex){}
        
        try{
            GetRecordId.getPersonAccount();
        }catch(Exception ex){}
        
        try{
            GetRecordId.getAnyAccount();
        }catch(Exception ex){}
        
        try{
            GetRecordId.getInteractionSummary();
        }catch(Exception ex){}
        
        try{
            GetRecordId.getResidentialLoanApplication();
        }catch(Exception ex){}
        
        try{
            GetRecordId.getActionPlan();
        }catch(Exception ex){}
        
        try{
            GetRecordId.getActionPlanTemplate();
        }catch(Exception ex){}
        
        try{
            GetRecordId.getHouseholdAccounts();
        }catch(Exception ex){}
        
        try{
            GetRecordId.getAnyHouseholdAccount();
        }catch(Exception ex){}
        
        try{
            GetRecordId.getFinancialDeal();
        }catch(Exception ex){}
        
        Test.stopTest();
        
    }
    
}