@isTest
public class NetworkUserTriggerHandlerTest {
    @testSetup
    static void setup() {
        // Create Public Group
        Group g = new Group(Name = 'Test Group', DeveloperName = 'AGE_456', Type = 'Regular');
        insert g;

        // Create Account (Agency) with ExternalId
        Account agency = new Account(Name = 'Agency A', ExternalId__c = 'AGE_456');
        insert agency;
        
        DateTime dt = DateTime.now();

        // Create User with FiscalCode
        User u = new User(
            FirstName = 'Test',
            LastName = 'User',
            Alias = 'tuser',
            Email = '<EMAIL>',
            Username = dt.getTime()+'@example.com.test',
            ProfileId = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1]
            .Id,
            TimeZoneSidKey = 'Europe/Paris',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            FiscalCode__c = 'FC999',
            IdAzienda__c = agency.Id
        );
        insert u;

        // Create ServiceResource related to user
        ServiceResource sr = new ServiceResource(
            Name = 'Test SR',
            RelatedRecordId = u.Id,
            Attiva_per_clienti_e_prospect__c = 'Nessuno'
        );
        insert sr;

        // Create NetworkUser__c related to user and agency
        NetworkUser__c nu = new NetworkUser__c(User__c = u.Id, FiscalCode__c = 'FC999', Agency__c = agency.Id);
        insert nu;
    }

    @isTest
    static void testOnAfterInsert() {
        List<NetworkUser__c> networkUsers = [SELECT Id, User__c, Agency__c FROM NetworkUser__c];
        Test.startTest();
        NetworkUserTriggerHandler handler = new NetworkUserTriggerHandler();
        handler.onAfterInsert(networkUsers);
        Test.stopTest();

        // Validate shares were created
        //System.assertEquals(2, [SELECT COUNT() FROM NetworkUser__Share], 'Should have 1 NetworkUser share');
        // System.assertEquals(1, [SELECT COUNT() FROM UserShare], 'Should have 1 User share');
        // System.assertEquals(1, [SELECT COUNT() FROM ServiceResourceShare], 'Should have 1 ServiceResource share');
    }

    @isTest
    static void testOnAfterUpdate() {
        List<NetworkUser__c> toUpdate = [SELECT Id, Name FROM NetworkUser__c];
        for (NetworkUser__c nu : toUpdate) {
            nu.Profile__c = 'A';
        }

        update toUpdate;

        List<NetworkUser__c> newList = [SELECT Id, User__c, Agency__c FROM NetworkUser__c];
        Map<Id, NetworkUser__c> newMap = new Map<Id, NetworkUser__c>(newList);
        Map<Id, NetworkUser__c> oldMap = new Map<Id, NetworkUser__c>(toUpdate);

        Test.startTest();
        NetworkUserTriggerHandler handler = new NetworkUserTriggerHandler();
        handler.onAfterUpdate(newList, toUpdate, newMap, oldMap);
        Test.stopTest();

        //System.assertEquals(2, [SELECT COUNT() FROM NetworkUser__Share], 'Should create NetworkUser__Share on update');
        // System.assertEquals(1, [SELECT COUNT() FROM UserShare], 'Should create UserShare on update');
        // System.assertEquals(1, [SELECT COUNT() FROM ServiceResourceShare], 'Should create ServiceResourceShare on update');
    }
}