/*****************************************************************************************************
* <AUTHOR>
* @description    TimerController class handles the retrieval and calculation of SLA (Service Level Agreement) 
*                 information for Opportunities. It calculates remaining time in both calendar and business 
*                 hours, and provides SLA details such as completion percentage and overdue status.
* @date           2023-11-23
******************************************************************************************************/
public with sharing class TimerController {

    // Constants for time conversions
    public static final Integer IN_DAYS = 86400000; //milliseconds
    public static final Integer IN_HOURS = 3600000; //milliseconds

    // Represents SLA (Service Level Agreement) information for an Opportunity.
    public class SLAInfo {
        @AuraEnabled
        public String expiryDate {get; set;}

        @AuraEnabled
        public TimeSLA remainingTimeCalendar {get; set;}

        @AuraEnabled
        public TimeSLA remainingTimeBusiness {get; set;}

        @AuraEnabled
        public Integer completionPercent {get; set;}

        @AuraEnabled
        public Boolean isOverdue {get; set;}

        @AuraEnabled
        public Boolean isCompleted {get; set;}

        public SLAInfo(String expiryDate, TimeSLA remainingTimeCalendar, TimeSLA remainingTimeBusiness, Integer completionPercent, Boolean isOverdue, Boolean isCompleted) { //NOPMD
            this.expiryDate = expiryDate;
            this.remainingTimeCalendar = remainingTimeCalendar;
            this.remainingTimeBusiness = remainingTimeBusiness;
            this.completionPercent = completionPercent;
            this.isOverdue = isOverdue;
            this.isCompleted = isCompleted;
        }
    }

    // Represents remaining time details in days and hours for SLA.   
    public class TimeSLA {
        @AuraEnabled
        public Integer remainingDays { get; set; }

        @AuraEnabled
        public Integer remainingHours { get; set; }

        @AuraEnabled
        public Integer totalRemainingHours { get; set; }

        public TimeSLA(Integer remainingDays, Integer remainingHours, Integer totalRemainingHours) {
            this.remainingDays = remainingDays;
            this.remainingHours = remainingHours;
            this.totalRemainingHours = totalRemainingHours;
        }
    }

    // Represents the combined SLA information for an Opportunity.
    public class TimerSLA {
        @AuraEnabled
        public SLAInfo takeInChargeSLA {get; set;}

        @AuraEnabled
        public SLAInfo workingSLA {get; set;}

        @AuraEnabled
        public Boolean hasCallMeBack {get; set;}

        @AuraEnabled
        public Boolean channelIsContactCenter {get; set;}

        public timerSLA(SLAInfo takeInChargeSLA, SLAInfo workingSLA, Boolean hasCallMeBack, Boolean channelIsContactCenter){
            this.takeInChargeSLA = takeInChargeSLA;
            this.workingSLA = workingSLA;
            this.hasCallMeBack = hasCallMeBack;
            this.channelIsContactCenter = channelIsContactCenter;
        }
    }

    /******************************************************************************************
    * @description  Retrieves SLA information for a given Opportunity by its record ID.
    * @param        recordId - The ID of the Opportunity record.
    * @return       TimerSLA - An object containing SLA details for the Opportunity.
    * @throws       AuraHandledException - If an error occurs during data retrieval.
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static timerSLA getOpportunitySLAInfo(String recordId){
        try{
            Opportunity opp = getOpportunity(recordId);
            SLAInfo takenInChargeSLAInfo = getSLAInfo(opp, true);
            SLAInfo workingSLAInfo = getSLAInfo(opp, false);
            Boolean hasCallMeBack = checkIfHasCallMeBackTask(opp);
            
            System.debug('Channel: ' + opp.ContactChannel__c);
            Boolean channelIsContactCenter = opp.ContactChannel__c == 'Contact Center';
            return new timerSLA(takenInChargeSLAInfo, workingSLAInfo, hasCallMeBack, channelIsContactCenter);
        }
        catch(Exception e){
            String  errorMsg ='An error has occurred: ' + e.getMessage(); 
            throw new AuraHandledException(errorMsg);
        }
    }

    /******************************************************************************************
    * @description  Retrieves an Opportunity record by its ID.
    * @param        recordId - The ID of the Opportunity record.
    * @return       Opportunity - The retrieved Opportunity record.
    * @throws       Exception - If an error occurs during the query.
    *******************************************************************************************/
    @TestVisible
    public static Opportunity getOpportunity(String recordId){
        try{
            Id currentId = Id.valueOf(recordId);
            List<Opportunity> oppList = new List<Opportunity>();
            oppList = [ SELECT Id, ContactChannel__c, TakenInChargeSLAExpiryDate__c, TakenInChargeSLAStartDate__c, WorkingSLAStartDate__c, WorkingSLAExpiryDate__c, TakenInChargeDate__c, TakenInChargeSLARemainingHours__c, TakenInChargeSLARemainingDays__c, WorkingSLARemainingDays__c, WorkingSLARemainingHours__c, Agency__r.BusinessHours__c //NOPMD
                        FROM Opportunity
                        WHERE Id =: currentId
                        LIMIT 1 ];

            return (!oppList.isEmpty()) ? oppList.get(0) : null;
        }
        catch(Exception e){
            System.debug('An error has occurred: ' + e.getMessage());
            return null;
        }
    }
    
    /******************************************************************************************
    * @description  Retrieves SLA information for an Opportunity.
    * @param        opp - The Opportunity record.
    * @param        isTakenInCharge - Boolean flag indicating if it's taken in charge SLA.
    * @return       SLAInfo - An object containing SLA details for the Opportunity.
    *******************************************************************************************/
    @TestVisible
    private static SLAInfo getSLAInfo(Opportunity opp, boolean isTakenInCharge){
        try{
            if(isTakenInCharge) {
                String expiryDate = Datetime.valueOf(opp.TakenInChargeSLAExpiryDate__c).format('dd/MM/yyyy, HH:mm');
                TimeSLA remainingTimeCalendar = calculateCalendarTime(opp, true);
                TimeSLA remainingTimeBusiness = calculateBusinessTime(opp, true);
                Integer completionPercentage = (Integer) calculateSLAPercent(opp.TakenInChargeSLAExpiryDate__c, opp.TakenInChargeSLAStartDate__c);
                Boolean isOverdue = getFirstTimerExpired(opp.TakenInChargeSLAExpiryDate__c, opp.TakenInChargeDate__c, opp.TakenInChargeSLARemainingHours__c);
                Boolean isCompleted = getFirstTimerCompleted(opp.TakenInChargeDate__c, opp.TakenInChargeSLAExpiryDate__c, isOverdue);
                Integer completedPercentage = (isCompleted || isOverdue) ? 100 : completionPercentage;
                return new SLAInfo(expiryDate, remainingTimeCalendar, remainingTimeBusiness, completedPercentage, isOverdue, isCompleted);
            } else {
                String expiryDate = Datetime.valueOf(opp.WorkingSLAExpiryDate__c).format('dd/MM/yyyy, HH:mm');
                TimeSLA remainingTimeCalendar = calculateCalendarTime(opp, false);
                TimeSLA remainingTimeBusiness = calculateBusinessTime(opp, false);
                Integer completionPercentage = (Integer) calculateSLAPercent(opp.WorkingSLAExpiryDate__c, opp.WorkingSLAStartDate__c);
                Boolean isOverdue = getSecondTimerExpired(opp);
                Boolean isCompleted = getSecondTimerCompleted(opp.WorkingSLARemainingHours__c, isOverdue);
                Integer completedPercentage = (isCompleted || isOverdue) ? 100 : completionPercentage;          
                return new SLAInfo(expiryDate, remainingTimeCalendar, remainingTimeBusiness, completedPercentage, isOverdue, isCompleted);
            }

        }
        catch(Exception e){
            System.debug('An error has occurred: ' + e.getMessage());
            TimeSLA t = new TimeSLA(0, 0, 0);
            return new SLAInfo(null, t, t, 0, false, false);
        }
    }
    
    /******************************************************************************************
    * @description  Calculates remaining calendar time for an SLA.
    * @param        opp - The Opportunity record.
    * @param        isTakenInCharge - Boolean flag indicating if it's taken in charge SLA.
    * @return       TimeSLA - An object containing remaining time details.
    *******************************************************************************************/
    @TestVisible
    private static TimeSLA calculateCalendarTime(Opportunity opp, Boolean isTakenInCharge) {
        Decimal remainingDays = (isTakenInCharge) ?  opp.TakenInChargeSLARemainingDays__c : opp.WorkingSLARemainingDays__c;
        Decimal totalRemainingHours = (isTakenInCharge) ?  opp.TakenInChargeSLARemainingHours__c : opp.WorkingSLARemainingHours__c;
        Decimal remainingHours = getRemainingHours((Integer)totalRemainingHours, false);
        return new TimeSLA((Integer)remainingDays, (Integer)remainingHours, (Integer)totalRemainingHours);
    }

    /******************************************************************************************
    * @description  Calculates remaining business time for an SLA.
    * @param        opp - The Opportunity record.
    * @param        isTakenInCharge - Boolean flag indicating if it's taken in charge SLA.
    * @return       TimeSLA - An object containing remaining business time details.
    *******************************************************************************************/
    @TestVisible
    public static TimeSLA calculateBusinessTime(Opportunity opp, Boolean isTakenInCharge) {
        Datetime ticStartingPoint = System.Now();
        Datetime wStartingPoint = System.Now();
        if(opp.TakenInChargeSLAStartDate__c > System.Now()) ticStartingPoint = opp.TakenInChargeSLAStartDate__c;
        if(opp.WorkingSLAStartDate__c > System.Now()) wStartingPoint = opp.WorkingSLAStartDate__c;
        Decimal ticHours = (calculateRemainingTimeWithBH(opp, ticStartingPoint, opp.TakenInChargeSLAExpiryDate__c)) / IN_HOURS;
        Decimal wHours = (calculateRemainingTimeWithBH(opp, wStartingPoint, opp.WorkingSLAExpiryDate__c)) / IN_HOURS;

        Decimal remainingDays = (isTakenInCharge) ?  ticHours / 9 : wHours / 9;
        Decimal totalRemainingHours = (isTakenInCharge) ? (ticHours + 0.5) : wHours; // SC 08-04-35 OCT 1289110
        Decimal remainingHours = getRemainingHours((Integer)totalRemainingHours, true);
        return new TimeSLA((Integer)remainingDays, (Integer)remainingHours, (Integer)totalRemainingHours);
    }

    /******************************************************************************************
    * @description  Calculates the remaining time in business hours.
    * @param        opp - The Opportunity record.
    * @param        startDate - The start date for the calculation.
    * @param        endDate - The end date for the calculation.
    * @return       Decimal - The remaining time in business hours.
    *******************************************************************************************/
    @TestVisible
    private static Decimal calculateRemainingTimeWithBH(Opportunity opp, Datetime startDate, Datetime endDate){
        Id businessHoursId = opp.Agency__r?.BusinessHours__c;
        if(businessHoursId == null)
        {
            businessHoursId = [SELECT Id FROM BusinessHours WHERE IsDefault = true LIMIT 1].Id;
        }
        Decimal remainingTime = (Decimal) ((BusinessHours.diff(businessHoursId, startDate, endDate)) * 1.0);
        return (remainingTime > 0) ? remainingTime : 0;        
    }

    /******************************************************************************************
    * @description  Calculates the percentage of SLA completion.
    * @param        expiryDate - The expiry date of the SLA.
    * @param        createdDate - The creation date of the SLA.
    * @return       Decimal - The SLA completion percentage.
    *******************************************************************************************/
    @TestVisible
    private static Decimal calculateSLAPercent(Datetime expiryDate, Datetime createdDate){
        if(createdDate > System.Now()) return 0;

        Long timeAvailable = calculateTimeInHours(createdDate, expiryDate);
        Long timeLeft = calculateTimeInHours(System.Now(), expiryDate);
        Decimal percent = 100 - Math.floor(((timeLeft * 1.0) / (timeAvailable * 1.0)) * 100);
        return (percent > 0) ? percent : 100;
    }

    /******************************************************************************************
    * @description  Checks if the first timer (taken in charge SLA) is expired.
    * @param        takenInChargeSLAExpiryDate - The expiry date of the taken in charge SLA.
    * @param        takenInChargeDate - The date the opportunity was taken in charge.
    * @param        takenInChargeRemainingHours - The remaining hours for the taken in charge SLA.
    * @return       Boolean - True if the first timer is expired, false otherwise.
    *******************************************************************************************/
    @TestVisible
    private static Boolean getFirstTimerExpired(Datetime takenInChargeSLAExpiryDate, Datetime takenInChargeDate, Decimal takenInChargeRemainingHours) {
        if(((takenInChargeDate != null) && ((calculateTimeInHours(takenInChargeDate, takenInChargeSLAExpiryDate) <= 0))) ||
            ((takenInChargeDate == null) && ((Integer)takenInChargeRemainingHours <= 0))){
            return true;
        }
        return false;
    }
    
    /******************************************************************************************
    * @description  Checks if the first timer (taken in charge SLA) is completed.
    * @param        takenInChargeDate - The date the opportunity was taken in charge.
    * @param        takenInChargeSLAExpiryDate - The expiry date of the taken in charge SLA.
    * @param        isExpired - Boolean flag indicating if the SLA is expired.
    * @return       Boolean - True if the first timer is completed, false otherwise.
    *******************************************************************************************/
    @TestVisible
    private static Boolean getFirstTimerCompleted(Datetime takenInChargeDate, Datetime takenInChargeSLAExpiryDate, Boolean isExpired) {
        return ((!isExpired) && (takenInChargeDate != null) && (calculateTimeInHours(takenInChargeDate, takenInChargeSLAExpiryDate) > 0)) ? true : false;
    }

    /******************************************************************************************
    * @description  Checks if the second timer (working SLA) is expired.
    * @param        opp - The Opportunity record.
    * @return       Boolean - True if the second timer is expired, false otherwise.
    *******************************************************************************************/
    @TestVisible
    private static Boolean getSecondTimerExpired(Opportunity opp) {
        return ((Integer)calculateTimeInHours(System.Now(), opp.WorkingSLAExpiryDate__c) <= 0) ? true : false;
    }

    /******************************************************************************************
    * @description  Checks if the second timer (working SLA) is completed.
    * @param        workingSLARemainingHours - The remaining hours for the working SLA.
    * @param        isExpired - Boolean flag indicating if the SLA is expired.
    * @return       Boolean - True if the second timer is completed, false otherwise.
    *******************************************************************************************/
    @TestVisible
    private static Boolean getSecondTimerCompleted(Decimal workingSLARemainingHours, Boolean isExpired) {
        return (!isExpired) ? false : false; //TODO
    }
    
    /******************************************************************************************
    * @description  Gets the remaining hours, considering if it's business hours or not.
    * @param        totalHours - The total remaining hours.
    * @param        isBusinessHours - Boolean flag indicating if the hours are business hours.
    * @return       Decimal - The remaining hours.
    *******************************************************************************************/
    @TestVisible
    private static Decimal getRemainingHours(Integer totalHours, Boolean isBusinessHours) {
        Decimal remainingHours = Math.mod(totalHours, (isBusinessHours ? 9 : 24));
        return remainingHours;
    }

    /******************************************************************************************
    * @description  Calculates the time in hours between two datetime values.
    * @param        createdTime - The starting datetime.
    * @param        expireTime - The ending datetime.
    * @return       Integer - The time in hours between the two datetime values.
    *******************************************************************************************/
    @TestVisible
    private static Integer calculateTimeInHours(DateTime createdTime, DateTime expireTime) {
        return (Integer)((expireTime.getTime() - createdTime.getTime()) / IN_HOURS);
    }
	
    /******************************************************************************************
    * @description  Checks if there are any "Call Me Back" tasks related to the Opportunity.
    * @param        opp - The Opportunity record.
    * @return       Boolean - True if there are "Call Me Back" tasks, false otherwise.
    *******************************************************************************************/
    @TestVisible
    private static Boolean checkIfHasCallMeBackTask(Opportunity opp){
        List<Task> cmbTasks = new List<Task>();
        cmbTasks = [SELECT Id  //NOPMD
                    FROM Task
                    WHERE WhatId =: opp.Id
                    AND RecordType.DeveloperName = 'CallMeBack'
                   ];
        return !cmbTasks.isEmpty() ? true : false;
    }
}