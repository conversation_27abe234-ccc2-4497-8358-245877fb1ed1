@isTest
public with sharing class ApexSharingUtility_Test {
    @testSetup static void setup() {
        //create User
        Test.startTest();
        User u1 = UTL_DataFactory.createUser('Admintst', '<EMAIL>', 'User', 'System Administrator', '', '<EMAIL>', true);
        User u2 = UTL_DataFactory.createUser('Admintst', '<EMAIL>', 'User', 'Unipol Standard User', '', '<EMAIL>', true);
        Test.stopTest();

        System.runas(u1){
            //create a record for the Standard Object
            List<Opportunity> testOpp = new List<Opportunity>();
            for(Integer i=0;i<2;i++) {
                testOpp.add(new Opportunity(StageName = 'Nuovo', Name = 'OK'+'i',CloseDate = Date.today().addDays(30)));
            }
            insert testOpp;
            //create a record for the custom Object 
            List<UniViewUserPreference__c> UserPref = new List<UniViewUserPreference__c>();
            for(Integer i=0;i<2;i++) {
                UserPref.add(new UniViewUserPreference__c());
            }
            insert UserPref;
        }   
    }
    @isTest
    static void shareToStandardObjTest() {
        User u1 = [SELECT id FROM User WHERE username = '<EMAIL>' LIMIT 1 ] ;
        User userToGivePermissions = [SELECT id FROM User WHERE username = '<EMAIL>' LIMIT 1];

        List<Opportunity> oppList = [SELECT id FROM Opportunity LIMIT 2];
        List<Id> oppListIds = new List<Id>();
        for(Opportunity opp : oppList) {
            oppListIds.add(opp.Id);
        }
        List<Database.SaveResult> results = new List<Database.SaveResult>();

        results = ApexSharingUtility.shareTo('Opportunity',oppListIds,userToGivePermissions.id,'Read','Manual');

        System.assertEquals(2, results.size(), 'Results should contain 2 elements');
        for (Database.SaveResult result : results) {
            System.assertEquals(true,result.isSuccess(), 'Sharing should be successful for all records');
        }

        List<OpportunityShare> OppoListShr = [SELECT Id, UserOrGroupId, OpportunityAccessLevel,RowCause FROM OpportunityShare WHERE OpportunityId = :oppListIds[0] AND UserOrGroupId= :userToGivePermissions.id];
        
        System.assertEquals(OppoListShr.size(), 1, 'Set the object\'s sharing model to Private.');
        System.assertEquals(OppoListShr[0].OpportunityAccessLevel, 'Read','AccessLevel');
        System.assertEquals(OppoListShr[0].RowCause, 'Manual','Reason');
        System.assertEquals(OppoListShr[0].UserOrGroupId, userToGivePermissions.Id, 'User');

        delete OppoListShr[0];
        List<Id> oppDeletedIds = new List<Id>();
        oppDeletedIds.add(OppoListShr[0].id);

        List<Database.SaveResult> results1 = new List<Database.SaveResult>();
        results1 = ApexSharingUtility.shareTo('Opportunity',oppDeletedIds,userToGivePermissions.id,'Read','Manual');
        System.assertEquals(1, results1.size(), 'Results should contain 1 elements');
        for (Database.SaveResult result : results1) {
            System.assertEquals(false,result.isSuccess(), 'Sharing shouldnt be successful for all records');
        }
    }
    @isTest
    static void shareToCustomObjTest() {
        User u1 = [SELECT id FROM User WHERE username = '<EMAIL>' LIMIT 1];
        User userToGivePermissions = [SELECT id FROM User WHERE username = '<EMAIL>' LIMIT 1];

        List<UniViewUserPreference__c> customList = [SELECT id FROM UniViewUserPreference__c LIMIT 2];
        List<Id> customListIds = new List<Id>();
        for(UniViewUserPreference__c opp : customList) {
            customListIds.add(opp.Id);
        }
        List<Database.SaveResult> results = new List<Database.SaveResult>();

        results = ApexSharingUtility.shareTo('UniViewUserPreference__c',customListIds,userToGivePermissions.id,'Read','Manual');
        EntityDefinition eds = [SELECT DeveloperName, ExternalSharingModel, InternalSharingModel FROM EntityDefinition WHERE DeveloperName = 'UniViewUserPreference'];

        System.assertEquals(2, results.size(), 'Results should contain 2 elements');
        if(eds.InternalSharingModel != 'Private'){
            for (Database.SaveResult result : results) {
                System.assertEquals(false,result.isSuccess(), 'Sharing shouldnt be successful for all records');
            }
        }else{
            for (Database.SaveResult result : results) {
                System.assertEquals(true,result.isSuccess(), 'Sharing should be successful for all records');
            }
        }     
    }

    @isTest
    static void testThrowIfBlank() {
        User u1 = [SELECT id FROM User WHERE username = '<EMAIL>' LIMIT 1];
        User userToGivePermissions = [SELECT id FROM User WHERE username = '<EMAIL>' LIMIT 1];

        Boolean isExceptionCaught = false;
        List<UniViewUserPreference__c> oppList = [SELECT id FROM UniViewUserPreference__c LIMIT 2];
        List<Id> oppListIds = new List<Id>();
        for(UniViewUserPreference__c opp : oppList) {
            oppListIds.add(opp.Id);
        }
        List<Database.SaveResult> results = new List<Database.SaveResult>();

        Test.startTest();
          try {
            results = ApexSharingUtility.shareTo('',oppListIds,userToGivePermissions.id,'Read','Manual');
          } catch (Exception ex) {
            isExceptionCaught=true;
          }
        Test.stopTest();

        System.assertEquals(true,isExceptionCaught,'Exception was caught');
    }

    @isTest
    static void testThrowIfEmpty() {
        User u1 = [SELECT id FROM User WHERE username = '<EMAIL>' LIMIT 1];
        User userToGivePermissions = [SELECT id FROM User WHERE username = '<EMAIL>' LIMIT 1];
        
        Boolean isExceptionCaught = false;
        List<UniViewUserPreference__c> oppList = [SELECT id FROM UniViewUserPreference__c LIMIT 2];
        List<Id> oppListIds = new List<Id>();
        List<Database.SaveResult> results = new List<Database.SaveResult>();

        Test.startTest();
          try {
            results = ApexSharingUtility.shareTo('UniViewUserPreference__c',oppListIds,userToGivePermissions.id,'Read','Manual');
          } catch (Exception ex) {
            isExceptionCaught=true;
          }
        Test.stopTest();

        System.assertEquals(true,isExceptionCaught,'Exception was caught');
    }
}