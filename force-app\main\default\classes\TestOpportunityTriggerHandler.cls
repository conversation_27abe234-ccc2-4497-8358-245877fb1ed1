/**
 * @File Name         : TestOpportunityTriggerHandler.cls
 * @description       :
 * <AUTHOR> <EMAIL>
 * @group             :
 * @last modified on  : 29-10-2024
 * @last modified by  : <EMAIL>
 **/
@isTest
private class TestOpportunityTriggerHandler {
    @testSetup
    static void setup() {
    }

    /*@isTest
    public static void testperformShares() {
        // INSERT SF PUBLIC GROUP (Group) records
        // List<Group> lstTestGrp = new List<Group>();
		// Group testGrp1 = new Group(Name = 'TestGroup1', Type = 'Regular');
		// lstTestGrp.add(testGrp1);
		// Group testGrp2 = new Group(Name = 'TestGroup2', Type = 'Regular');
		// lstTestGrp.add(testGrp2);
		// Group testGrp3 = new Group(Name = 'TestGroup3', Type = 'Regular');
		// lstTestGrp.add(testGrp3);
		// insert lstTestGrp;
        List<Group> lstTestGrp = createGroupList();

        // INSERT ACCOUNT records	
        // List<Account> lstAccs = new List<Account>();
		// Id perAccRecTypId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(System.Label.PersonAccRecType).getRecordTypeId();
		// Account newAcc1 = new Account(FirstName = 'TestConi', LastName = 'Account 1', RecordTypeId = perAccRecTypId);
		// lstAccs.add(newAcc1);
		// Account newAcc2 = new Account(FirstName = 'TestConi', LastName = 'Account 2', RecordTypeId = perAccRecTypId);
		// lstAccs.add(newAcc2);
		// insert lstAccs;
        List<Account> lstAccs = createAccountList();

        // INSERT ACCOUNT AGENCY records
        // List<Account> lstAgencyAccs = new List<Account>();
		// Id agAccRecTypId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(System.Label.AgencyAccRecType).getRecordTypeId();
		// Account newAgAcc1 = new Account(Name = 'TestConi Agenzia 1', RecordTypeId = agAccRecTypId);
		// lstAgencyAccs.add(newAgAcc1);
		// Account newAgAcc2 = new Account(Name = 'TestConi Agenzia 2', RecordTypeId = agAccRecTypId);
		// lstAgencyAccs.add(newAgAcc2);
		// insert lstAgencyAccs;
        List<Account> lstAgencyAccs = createAgencyList();

        // INSERT Group__c records
        // List<Group__c> lstGroups = new List<Group__c>();
		// Id cObjGroupRecTypeId = Schema.SObjectType.Group__c.getRecordTypeInfosByDeveloperName().get(System.Label.CustomObjGroupRecType).getRecordTypeId();
		// Group__c newGroup1 = new Group__c(Name = 'TestGroup1', RecordTypeId = cObjGroupRecTypeId, PublicGroupId__c = lstTestGrp[0].Id);
		// lstGroups.add(newGroup1);
		// Group__c newGroup2 = new Group__c(Name = 'TestGroup2', RecordTypeId = cObjGroupRecTypeId, PublicGroupId__c = lstTestGrp[1].Id);
		// lstGroups.add(newGroup2);
		// Group__c newGroup3 = new Group__c(Name = 'TestGroup3', RecordTypeId = cObjGroupRecTypeId, PublicGroupId__c = lstTestGrp[2].Id);
		// lstGroups.add(newGroup3);
		// insert lstGroups;
        List<Group__c> lstGroups = createGroupCustomList(lstTestGrp);

        // INSERT InsurancePolicy records
        List<InsurancePolicy> lstInsPol = new List<InsurancePolicy>();
        InsurancePolicy insPol = new InsurancePolicy(
            Name = 'Test Coni Policy 1',
            NameInsuredId = lstAccs[0].Id,
            Agency__c = lstAgencyAccs[0].Id,
            CIP__c = '100',
            CompanyCode__c = '1',
            Society__c = lstAgencyAccs.get(0).Id
        );
        lstInsPol.add(insPol);
        insert lstInsPol;
        InsurancePolicyShare insPolShare = new InsurancePolicyShare();
        insPolShare.AccessLevel = 'Edit';
        insPolShare.ParentId = lstInsPol[0].Id;
        insPolShare.UserOrGroupId = lstTestGrp[0].Id;
        insert insPolShare;

        // INSERT Case records
        List<Case> lstCase = new List<Case>();
        Case case1 = new Case(
            AccountId = lstAccs[1].Id,
            Status = 'Nuovo',
            Origin = 'Phone',
            dueDate__c = Date.today().addDays(10)
        );
        lstCase.add(case1);
        insert lstCase;
        CaseShare cShare = new CaseShare();
        cShare.CaseAccessLevel = 'Edit';
        cShare.CaseId = lstCase[0].Id;
        cShare.UserOrGroupId = lstTestGrp[0].Id;
        insert cShare;

        
		// OpportunityShare opptyShar2 = new OpportunityShare();
		// opptyShar2.OpportunityAccessLevel = 'Edit'; 
		// opptyShar2.OpportunityId = Id.valueOf('0069V00000Hqx2DQAR'); 
		// opptyShar2.UserOrGroupId = Id.valueOf('00G9V000006fq8sUAA');
		// insert opptyShar2;
		

        // INSERT Opportunity records
        List<Opportunity> lstOpps = new List<Opportunity>();
        Opportunity newOpp = new Opportunity();
        newOpp.Name = 'OppTestConiGB Mario Rossi - 29/10';
        newOpp.AccountId = lstAccs[0].Id;
        newOpp.CloseDate = System.today() + 90;
        newOpp.StageName = 'Nuovo';
        newOpp.Agency__c = lstAgencyAccs[0].Id;
        newOpp.AssignedGroup__c = lstGroups[0].Id;
        lstOpps.add(newOpp);

        Opportunity newOpp2 = new Opportunity();
        newOpp2.Name = 'OppTestConiGB Alvaro Perez - 29/10';
        newOpp2.AccountId = lstAccs[1].Id;
        newOpp2.CloseDate = System.today() + 90;
        newOpp2.StageName = 'Nuovo';
        newOpp2.Agency__c = lstAgencyAccs[0].Id;
        newOpp2.AssignedGroup__c = lstGroups[1].Id;
        lstOpps.add(newOpp2);

        Opportunity newOpp3 = new Opportunity();
        newOpp3.Name = 'OppTestConiGB Alba Rivas - USER - 29/10';
        newOpp3.AccountId = lstAccs[0].Id;
        newOpp3.CloseDate = System.today() + 90;
        newOpp3.StageName = 'Nuovo';
        newOpp3.Agency__c = lstAgencyAccs[0].Id;
        newOpp3.AssignedTo__c = UserInfo.getUserId();
        lstOpps.add(newOpp3);

        Opportunity newOpp4 = new Opportunity();
        newOpp4.Name = 'OppTestConiGB Alba Rivas - 29/10';
        newOpp4.AccountId = lstAccs[1].Id;
        newOpp4.CloseDate = System.today() + 90;
        newOpp4.StageName = 'Nuovo';
        newOpp4.Agency__c = lstAgencyAccs[0].Id;
        newOpp4.AssignedGroup__c = lstGroups[1].Id;
        lstOpps.add(newOpp4);

        insert lstOpps;
    }*/

    /*@isTest
    public static void testConiService() {
        List<User> lstUser = createUserList();
        List<Account> lstAccs = createAccountList();
        List<Group> lstTestGrp = createGroupList();
        //agencyId = accountId
        List<Group__c> lstGroups = createGroupCustomList(lstTestGrp);
        List<Account> lstAgency = createAgencyList();
        Account agency = lstAgency[0];
        agency.Name = 'Test agency - Referenti';
        update agency;
        OperatingHours op = new OperatingHours();
        op.Name = 'Test';
        op.TimeZone = 'Europe/Rome';
        insert op;
        ServiceTerritory st = new ServiceTerritory();
        st.Agency__c = agency.Id;
        st.IsActive = true;
        st.Name = 'Nome';
        st.Code__c = 'codice';
        st.OperatingHoursId = op.Id;
        insert st;
        Group__c groupRef = lstGroups[0];
        groupRef.Agenzia__c = agency.Id;
        groupRef.Name = 'Gruppo - Referenti';
        update groupRef;
        //SalesPoint__c = Account.Id
        //RecordType per opportunity
        RecordType opportunityRecordType = [
            SELECT Id
            FROM RecordType
            WHERE DeveloperName = 'InterestShow' AND SObjectType = 'Opportunity'
            LIMIT 1
        ];

        // Opportunity op4 = ConiAssignmentServiceTest.caso1(lstAccs[0].Id, lstUser[0].Id, opportunityRecordType.id, lstAccs[1].Id, agency.Id);
		// System.debug('++++ op4 - 1: ' + op1);
		// op4.isSetRef__c = true;
		// op4.AssignedTo__c = lstUser[0].Id;
		// update op4;
		// System.debug('++++ op4 - 2: ' + op4);
		// op4.AssignedTo__c = lstUser[1].Id;
		// update op4;

        //Caso 1
        Opportunity op0 = ConiAssignmentServiceTest.caso1(true);
        Opportunity op1 = ConiAssignmentServiceTest.caso1(
            lstAccs[0].Id,
            lstUser[0].Id,
            opportunityRecordType.id,
            st.Id,
            agency.Id
        );
        //Caso 2
        ConiAssignmentServiceTest.caso2(op1.Id, lstUser[1].Id);
        //Caso 3
        Opportunity op2 = ConiAssignmentServiceTest.caso3_1(
            lstAccs[1].Id,
            lstUser[0].Id,
            opportunityRecordType.id,
            st.Id,
            agency.Id
        );
        ConiAssignmentServiceTest.caso3_2(op2.Id, lstUser[2].Id);
        //Caso 4
        ConiAssignmentServiceTest.caso4(op2.Id, lstUser[3].Id);
        //Caso 5
        ConiAssignmentServiceTest.caso5(op2.Id, lstUser[2].Id);

        Opportunity op3 = ConiAssignmentServiceTest.caso1(
            lstAccs[0].Id,
            lstUser[1].Id,
            opportunityRecordType.id,
            st.Id,
            agency.Id
        );
        op0.AssignedTo__c = lstUser[2].Id;
        update op0;
        op1.AssignedTo__c = lstUser[2].Id;
        update op1;
        op2.AssignedTo__c = lstUser[2].Id;
        update op2;
        op3.AssignedTo__c = lstUser[1].Id;
        update op3;
        op3.AssignedTo__c = lstUser[2].Id;
        update op3;
        op2.AssignedTo__c = lstUser[1].Id;
        update op2;

        Opportunity op4 = ConiAssignmentServiceTest.caso1(
            lstAccs[0].Id,
            lstUser[0].Id,
            opportunityRecordType.id,
            st.Id,
            agency.Id
        );
        System.debug('++++ op4 - 1: ' + op4);
        op4.IsSetRef__c = true;
        op4.AssignedTo__c = lstUser[0].Id;
        update op4;
        System.debug('++++ op4 - 2: ' + op4);

        op0.AssignedTo__c = lstUser[0].Id;
        op1.AssignedTo__c = lstUser[0].Id;
        op2.AssignedTo__c = lstUser[0].Id;
        op3.AssignedTo__c = lstUser[0].Id;
        update op0;
        update op1;
        update op2;
        update op3;

        Set<Id> accId = new Set<Id>();
        accId.add(lstAccs[0].Id);
        accId.add(lstAccs[1].Id);
        ConiAssignmentService.getAccountIdToMatchedUserOrGroupIds(accId);

        System.debug('++++ lstTestGrp[0].Id: ' + lstTestGrp[0].Id);
        op1.AssignedTo__c = null;
        op1.AssignedGroup__c = lstGroups[0].Id;
        update op1;
        op1.AssignedGroup__c = lstGroups[1].Id;
        op1.AssignedGroup__c = null;
        op1.AssignedTo__c = lstUser[3].Id;

        Set<Id> oppId = new Set<Id>();
        oppId.add(op1.Id);
        oppId.add(op2.Id);
        //test queuable
        Map<String, Object> data = new Map<String, Object>{ 'refUpdateId' => oppId, 'objName' => 'Opportunity' };
        ConiQueRefUpdate job = new ConiQueRefUpdate(data);

        Case case1 = new Case(AccountId = lstAccs[1].Id, Status = 'Nuovo', Origin = 'Phone');
        insert case1;

        Test.startTest();
        job.execute(null);
        Test.stopTest();
        List<Opportunity> oppLst = [SELECT Id, IsSetRef__c FROM Opportunity WHERE Id IN :oppId];
        for (Opportunity o : oppLst) {
            System.debug('++++ o.IsSetRef__c: ' + o.IsSetRef__c);
        }
    }*/

    @IsTest
    static void testNoAccessException() {
        try {
            // Simula una situazione in cui l'eccezione viene lanciata
            throw new NoAccessException('Accesso negato');
        } catch (NoAccessException e) {
            // Verifica che il messaggio dell'eccezione sia quello previsto
            System.debug('++++ NoAccessException: ' + e.getMessage());
        }
    }

    @isTest
    static void testCustomSettingSkipTrigger() {
        SkipTrigger__c skipTrigger = SkipTrigger__c.getInstance();
        skipTrigger.SkipOpportunity__c = true;
        skipTrigger.SetupOwnerId = UserInfo.getUserId();
        insert skipTrigger;
        Test.startTest();
        Opportunity opp = new Opportunity(Name = 'Test', StageName = '1 - Test', CloseDate = System.today().addDays(30));
        insert opp;
        Test.stoptest();
    }

    @isTest
    static void testCustomSettingSkipTrigger2() {
        SkipTrigger__c skipTrigger = SkipTrigger__c.getInstance();
        skipTrigger.SkipOpportunity__c = false;
        skipTrigger.SetupOwnerId = UserInfo.getUserId();
        insert skipTrigger;
        Opportunity opp = new Opportunity(Name = 'Test', StageName = 'New', CloseDate = System.today().addDays(30));
        insert opp;
        Test.startTest();
        opp.StageName = 'Chiuso';
        update opp;
        Test.stoptest();
    }

    @isTest
    static void testCustomSettingSkipTrigger3() {
        SkipTrigger__c skipTrigger = SkipTrigger__c.getInstance();
        skipTrigger.SkipOpportunity__c = false;
        skipTrigger.SetupOwnerId = UserInfo.getUserId();
        insert skipTrigger;
        Opportunity opp = new Opportunity(Name = 'Test', StageName = 'New', CloseDate = System.today().addDays(30));
        insert opp;
        Test.startTest();
        opp.AssignedTo__c = UserInfo.getUserId();
        update opp;
        Test.stoptest();
    }

    @isTest
    static void testCustomSettingSkipTrigger4() {
        SkipTrigger__c skipTrigger = SkipTrigger__c.getInstance();
        skipTrigger.SkipOpportunity__c = false;
        skipTrigger.SetupOwnerId = UserInfo.getUserId();
        insert skipTrigger;
        Opportunity opp = new Opportunity(Name = 'Test', StageName = 'New', CloseDate = System.today().addDays(30));
        insert opp;
        Account agency = createAgencyList().get(0);
        Test.startTest();
        opp.Agency__c = agency.Id;
        update opp;
        Test.stoptest();
    }

    public static User createUser(string key) {
        return new User(
            ProfileId = userinfo.getProfileId(),
            LastName = 'TEST' + key,
            Email = 'TEST_VIDLA_User' + key + '@user1.atlas.com',
            Username = 'TEST_VIDLA_User' + key + '@user1.atlas.com' + System.currentTimeMillis(),
            CompanyName = 'TEST' + key,
            //VID_EmailSMS__c = true,
            //VID_Status__c = true,
            Alias = 'alias' + key,
            TimeZoneSidKey = 'America/Los_Angeles',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US'
            //CC_Codigo_ERP__c = 'TEST'
        );
    }

    public static List<User> createUserList() {
        List<User> lstUser = new List<User>();
        lstUser.add(createUser('1'));
        lstUser.add(createUser('2'));
        lstUser.add(createUser('3'));
        lstUser.add(createUser('4'));
        insert lstUser;
        return lstUser;
    }

    public static List<Account> createAccountList() {
        List<Account> lstAccs = new List<Account>();
        Id perAccRecTypId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(System.Label.PersonAccRecType).getRecordTypeId();
        Account newAcc1 = new Account(FirstName = 'TestConi', LastName = 'Account 1', RecordTypeId = perAccRecTypId);
        lstAccs.add(newAcc1);
        Account newAcc2 = new Account(FirstName = 'TestConi', LastName = 'Account 2', RecordTypeId = perAccRecTypId);
        lstAccs.add(newAcc2);
        Account newAcc3 = new Account(FirstName = 'TestConi', LastName = 'Account 3', RecordTypeId = perAccRecTypId);
        lstAccs.add(newAcc3);
        insert lstAccs;
        return lstAccs;
    }

    public static List<Account> createAgencyList() {
        List<Account> lstAgencyAccs = new List<Account>();
        Id agAccRecTypId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(System.Label.AgencyAccRecType).getRecordTypeId();
        Account newAgAcc1 = new Account(Name = 'TestConi Agenzia 1', RecordTypeId = agAccRecTypId);
        lstAgencyAccs.add(newAgAcc1);
        Account newAgAcc2 = new Account(Name = 'TestConi Agenzia 2', RecordTypeId = agAccRecTypId);
        lstAgencyAccs.add(newAgAcc2);
        insert lstAgencyAccs;
        return lstAgencyAccs;
    }

    public static List<Group> createGroupList() {
        List<Group> lstTestGrp = new List<Group>();
        Group testGrp1 = new Group(Name = 'TestGroup1', Type = 'Regular');
        lstTestGrp.add(testGrp1);
        Group testGrp2 = new Group(Name = 'TestGroup2', Type = 'Regular');
        lstTestGrp.add(testGrp2);
        Group testGrp3 = new Group(Name = 'TestGroup3', Type = 'Regular');
        lstTestGrp.add(testGrp3);
        insert lstTestGrp;
        return lstTestGrp;
    }

    public static List<Group__c> createGroupCustomList(List<Group> lstTestGrp) {
        List<Group__c> lstGroups = new List<Group__c>();
        Id cObjGroupRecTypeId = Schema.SObjectType.Group__c.getRecordTypeInfosByDeveloperName().get(System.Label.CustomObjGroupRecType).getRecordTypeId();
        Group__c newGroup1 = new Group__c(Name = 'TestGroup1', RecordTypeId = cObjGroupRecTypeId, PublicGroupId__c = lstTestGrp[0].Id);
        lstGroups.add(newGroup1);
        Group__c newGroup2 = new Group__c(Name = 'TestGroup2', RecordTypeId = cObjGroupRecTypeId, PublicGroupId__c = lstTestGrp[1].Id);
        lstGroups.add(newGroup2);
        Group__c newGroup3 = new Group__c(Name = 'TestGroup3', RecordTypeId = cObjGroupRecTypeId, PublicGroupId__c = lstTestGrp[2].Id);
        lstGroups.add(newGroup3);
        insert lstGroups;
        return lstGroups;
    }
}
