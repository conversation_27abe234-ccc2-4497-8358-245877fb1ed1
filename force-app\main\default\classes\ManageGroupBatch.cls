/**
 * @description This Batch create a record share of Group__c
 * <AUTHOR>
 * @since       05.05.2025
 * @cicd_tests ManageGroupBatchTest
 */
public with sharing class ManageGroupBatch implements Database.Batchable<SObject>, Database.Stateful {
    Date dt;
    static final Id RECORD_TYPE_CIP = Schema.SObjectType.Group__c.getRecordTypeInfosByDeveloperName()
        .get('CIP')
        .getRecordTypeId();

    private Boolean concatenate = false;

    public ManageGroupBatch() {
        this(false);
    }

    public ManageGroupBatch(Boolean concatenate) {
        this.concatenate = concatenate;
        this.dt = null;
    }

    public ManageGroupBatch(Object condition) {
        String stringDate = condition?.toString();
        try {
            this.dt = Date.valueOf(stringDate);
            return;
        } catch (Exception ex) {
        }
        try {
            this.dt = Date.parse(stringDate);
            return;
        } catch (Exception ex) {
        }
    }

    public Database.QueryLocator start(Database.BatchableContext bc) {
        if (dt == null) {
            return Database.getQueryLocator(
                [
                    SELECT Id, Agenzia__c, Agenzia__r.ExternalId__c
                    FROM Group__c
                    WHERE RecordTypeId = :RECORD_TYPE_CIP AND LastModifiedDate >= YESTERDAY
                ]
            );
        } else {
            return Database.getQueryLocator(
                [
                    SELECT Id, Agenzia__c, Agenzia__r.ExternalId__c
                    FROM Group__c
                    WHERE RecordTypeId = :RECORD_TYPE_CIP AND LastModifiedDate >= :this.dt
                ]
            );
        }
    }

    public void execute(Database.BatchableContext bc, List<Group__c> scope) {
        try {
            List<Group__Share> listShare = new List<Group__Share>();
            Map<String, Id> mapKeyWithIdGroup = new Map<String, Id>();
            for (Group__c gr : scope) {
                mapKeyWithIdGroup.put(gr.Agenzia__r.ExternalId__c, null);
            }
            List<Group> listGroup = [
                SELECT Id, DeveloperName
                FROM Group
                WHERE DeveloperName IN :mapKeyWithIdGroup.keySet()
            ];
            if (!listGroup.isEmpty()) {
                for (Group gr : listGroup) {
                    mapKeyWithIdGroup.put(gr.DeveloperName, gr.Id);
                }
                for (Group__c gr : scope) {
                    Group__Share share = new Group__Share();
                    share.ParentId = gr.Id;
                    share.UserOrGroupId = mapKeyWithIdGroup.get(gr.Agenzia__r.ExternalId__c);
                    share.AccessLevel = 'Read';
                    listShare.add(share);
                }
                Database.insert(listShare, false);
            }
        } catch (Exception ex) {
            System.debug('***** ERROR *****');
            System.debug('* getMessage: ' + ex.getMessage() + ' *');
            System.debug('* getStackTraceString: ' + ex.getStackTraceString() + ' *');
            System.debug('***** ERROR *****');
        }
    }

    public void finish(Database.BatchableContext bc) {
        //if(concatenate){}
    }
}