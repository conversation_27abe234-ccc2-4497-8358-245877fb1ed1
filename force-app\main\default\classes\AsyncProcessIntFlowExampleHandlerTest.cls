@isTest
private class AsyncProcessIntFlowExampleHandlerTest {

    @isTest
    static void testExecuteBatch() {
        // Inserisce dati fittizi per il batch
        List<Account> accounts = new List<Account>();
        for (Integer i = 0; i < 10; i++) {
            accounts.add(new Account(Name = 'Test Account ' + i));
        }
        insert accounts;

        Test.startTest();
        // Passa il nome della classe batch come stringa
        AsyncProcessIntFlowExampleHandler.executeBatch(
            new List<String>{ 'CaseCalculatePriorityBatch' }
        );
        Test.stopTest();

        // Nessuna asserzione necessaria: se non ci sono eccezioni, il test è passato
        System.assert(true, 'Batch eseguito senza errori');
    }
}