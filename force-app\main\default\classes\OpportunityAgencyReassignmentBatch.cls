global class OpportunityAgencyReassignmentBatch implements Database.Batchable<SObject>, Database.Stateful {

    global Database.QueryLocator start(Database.BatchableContext bc) {
        return Database.getQueryLocator([
            SELECT Id, StageName, RecordType.DeveloperName, TakenInChargeSLAExpiryDate__c, Agency__c, Agency__r.ExternalId__c, Channel__c, Account.BillingLatitude, Account.BillingLongitude, AreaOfNeed__c, Account.AgencyCode__c, Account.BillingPostalCode,
            Agency__r.BillingLatitude,
            Agency__r.BillingLongitude,
            Agency__r.BillingPostalCode, AssignmentCounter__c,
            Agency__r.IsRolledOut__c 
            FROM Opportunity
            WHERE StageName IN ('Assegnato', 'Nuovo')
              AND RecordType.DeveloperName = 'Omnicanale'
              AND TakenInChargeSLAExpiryDate__c < :Date.today()
              AND Agency__r.IsRolledOut__c = true
        ]);
    }

    global void execute(Database.BatchableContext bc, List<SObject> scope) {

        List<CalculationMatrixRow> calculationMatrixRows = [SELECT Id, InputData, OutputData FROM CalculationMatrixRow WHERE CalculationMatrixVersion.CalculationMatrix.UniqueName = 'Parametri_Motore'];
        Map<String, Object> outputMatrix = new Map<String, Object>();
        Integer numeroMaxRiassegnazioni = 0;
        for (CalculationMatrixRow row : calculationMatrixRows) {
            if(row.InputData.contains('NumeroMaxRiassegnazioni') && row.InputData.contains('Preventivatore digitale Unica')){
                outputMatrix = (Map<String, Object>) JSON.deserializeUntyped(row.OutputData);
                numeroMaxRiassegnazioni = (Integer) outputMatrix.get('NumeroMaxRiassegnazioni');
            }
        }

        List<Opportunity> opportunitiesToUpdate = new List<Opportunity>();
        System.debug('SCOPE: ' + scope);
        for (Opportunity opp : (List<Opportunity>) scope) {
            if(opp.AssignmentCounter__c > numeroMaxRiassegnazioni || opp.Channel__c != 'Preventivatore digitale Unica'){
                continue;
            }

            // Build input
            List<String> agencyToExclude = new List<String>{opp.Agency__r.ExternalId__c};
            EngineWrapper_V2.EngineWrapperInput input = new EngineWrapper_V2.EngineWrapperInput(
                opp.Channel__c,
                agencyToExclude,
                (Double) opp.Agency__r.BillingLatitude,
                (Double) opp.Agency__r.BillingLongitude,
                new List<String>(),
                opp.AreaOfNeed__c,
                '',
                '',
                opp.Account.AgencyCode__c,
                opp.Agency__r.BillingPostalCode,
                '',
                '',
                false,
                'Unipol'
            );
    
            EngineWrapper_V2.EngineWrapperOutput output;
            try {
                output = EngineAgencyRanking_V2.rankAgencies(input);
            } catch (Exception e) {
                System.debug('Error calling rankAgencies: ' + e.getMessage());
                continue; // Skip to next opp
            }

            if(!output.isSuccess){
                opp.ErrorMessage__c = output.engineError + ' SubjectType: ' + output.subjectType;
            }else{
                if (!output.matchingSalespoints.isEmpty()) {
                    opp.ErrorMessage__c = output.matchingSalespoints[0].agency + ' SubjectType: ' + output.subjectType;
                    opp.Agency__c = output.matchingSalespoints[0].agency;
                }
                output = null;

                Map<String, Object> inputs = new Map<String, Object>{
                    'OpportunityId' => opp.Id,
                    'CategoryName' => 'TakeInCharge'
                };
                Flow.Interview.Engine_SLAExpiryDateCalculation SLAflow = 
                    new Flow.Interview.Engine_SLAExpiryDateCalculation(inputs);
            
                SLAflow.start();
                Date slaDate = (Date) SLAflow.getVariableValue('quoteTICExpiryDate');
                if(slaDate != null){
                    opp.TakenInChargeSLAExpiryDate__c = slaDate;
                }
                SLAflow = null;

                Map<String, Object> paramsEngineAssReassProdOptFlow = new Map<String, Object>{'recordId' => opp.Id};
                Flow.Interview.Engine_Assignment_And_Reassignment_Product_Opt engineAssReassProdOptFlow = new Flow.Interview.Engine_Assignment_And_Reassignment_Product_Opt(paramsEngineAssReassProdOptFlow);
                engineAssReassProdOptFlow = null;

                Map<String, Object> paramsEngineAssReassCMBFlow = new Map<String, Object>{'AgencyId' => output.matchingSalespoints[0].agency};
                Flow.Interview.Engine_Assignment_And_Reassignment_CMB engineAssReassCMBFlow = new Flow.Interview.Engine_Assignment_And_Reassignment_CMB(paramsEngineAssReassCMBFlow);
                engineAssReassCMBFlow = null;

                engineAssReassProdOptFlow.start();
                engineAssReassCMBFlow.start();

                opp.assignmentcounter__c = opp.assignmentcounter__c + 1;
            }
            opportunitiesToUpdate.add(opp);
        }
    
        if (!opportunitiesToUpdate.isEmpty()) {
            update opportunitiesToUpdate;
        }
    }

    global void finish(Database.BatchableContext bc) {
    }
}