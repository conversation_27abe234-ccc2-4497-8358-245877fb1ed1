/**
 * @File Name         : Sogg_RetriveProdotti.cls
 * @Description       : 
 * <AUTHOR> <EMAIL>
 * @Group             : 
 * @Last Modified On  : 26-11-2024
 * @Last Modified By  : <EMAIL>
@cicd_tests Sogg_RetriveProdottiTest
**/
global with sharing class Sogg_RetriveProdotti implements omnistudio.VlocityOpenInterface  {
    
    global Boolean invokeMethod(String methodName, Map<String,Object> inputMap, Map<String,Object> outMap, Map<String,Object> options){
        
        if(methodName == 'parentCardCountProdotti'){
            parentCardCountProdotti ('parentCardCountProdotti', inputMap, outMap, options);
        }
        return true;
    }

    private void parentCardCountProdotti(String methodName, Map<String,Object> inputMap, Map<String,Object> outMap, Map<String,Object> options){
        
        try{
            //System.debug('inputMap : '+inputMap);
            Map<String,Object> result = new Map<String,Object>();
            String accountId = (String)inputMap.get('AccountId');
            String userId = (String)inputMap.get('UserId');

            User userInfo = [SELECT Id, Name, IdAzienda__c FROM User WHERE id =: userId];
            String idAgenzia = userInfo.IdAzienda__c != null ? userInfo.IdAzienda__c : '';
            //System.debug('idAgenzia : '+idAgenzia);
            List<InsurancePolicy> policyAgenzia = [SELECT Id, Agency__c , RecordTypeId FROM InsurancePolicy WHERE NameInsuredId =: accountId AND AreaOfNeed__c != ''];

            Integer countAgency = 0;
            Integer countNotAgency = 0;
            Integer unicaAgency = 0;
            Integer unicaNotAgency = 0;

            String unicaRt =  SObjectType.InsurancePolicy.getRecordTypeInfosByDeveloperName().get('UNICA').getRecordTypeId();

            for(InsurancePolicy pol : policyAgenzia){
                if(idAgenzia.equalsIgnoreCase(pol.Agency__c)){
                    countAgency ++ ;
                    if(unicaRt.equalsIgnoreCase(pol.RecordTypeId)) unicaAgency ++;
                }else{
                    countNotAgency ++;
                    if(unicaRt.equalsIgnoreCase(pol.RecordTypeId)) unicaNotAgency ++;
                }
            }

            result.put('unicaAgency', String.valueOf(unicaAgency));
            result.put('unicaNotAgency', String.valueOf(unicaNotAgency));
            result.put('childResult' , (Map<String,Object>)retriveChildValues(idAgenzia,accountId, true ));
            result.put('childResultOtherAg' , (Map<String,Object>)retriveChildValues(idAgenzia,accountId, false));
            result.put('accountId', accountId);
            result.put('prodottiAgenzia' ,String.valueOf(countAgency));
            result.put('prodottiNonAgenzia' ,String.valueOf(countNotAgency));
            result.put('selezioneProdottiTipo', 'prodottiAgenzia');
            outMap.put('response', result);
            
        }
        catch(Exception e)
        {
            System.debug('Error : ' + e.getMessage() + ' - AT LINE : '+ e.getLineNumber());
        }
        

    }
    
    private Map<String,Map<String,String>> retriveChildValues (String idAgenzia, String accountId, Boolean agency ){
        Map<String,Map<String,String>> childResult = new Map<String,Map<String,String>>();
        List<AggregateResult> insuranceList = new List<AggregateResult>(); 
        if(agency){
             insuranceList = [SELECT  AreaOfNeed__c ,  SUM(PremiumAmount) totalAmount , COUNT(Id) countRecord  FROM InsurancePolicy WHERE Agency__c =:idAgenzia and NameInsuredId =: accountId GROUP BY AreaOfNeed__c  ];
        }else{
            insuranceList = [SELECT  AreaOfNeed__c ,  SUM(PremiumAmount) totalAmount , COUNT(Id) countRecord  FROM InsurancePolicy WHERE Agency__c != :idAgenzia and NameInsuredId =: accountId GROUP BY AreaOfNeed__c  ];
        }
           
        Integer totalCount = 0; // Inizializza il conteggio totale
        Decimal totalPremium = 0.00;
        Decimal arr = 0.00;
        Map<String,String> prodottiTotali = new Map<String,String>();
        for(AggregateResult ar : insuranceList){
            if(ar.get('AreaOfNeed__c') != null && ar.get('AreaOfNeed__c') != ''){
                String areaOfNeed = String.valueOf(ar.get('AreaOfNeed__c')).deleteWhitespace().replaceAll('à','');
                Map<String,String> values = new Map<String,String>();

                // Cod arrotondamento
                Decimal decValue = (Decimal) ar.get('totalAmount') + arr;
                Decimal importo = decValue.setScale(2);
                String totalAm = String.valueOf(importo).replace(',', '.').replace('.', ',');

                // Popolamento mappa valori
                values.put('totalAmount' , totalAm);
                values.put('count' , String.valueOf(ar.get('countRecord')));
                values.put('visible','true');

                // Incrementa il conteggio totale
                totalCount += (Integer)ar.get('countRecord');
                childResult.put(areaOfNeed, values);
                
                totalPremium += importo;
                
            }
        }
        
        prodottiTotali.put('prodottiTotali', String.valueOf(totalCount));
        prodottiTotali.put('premioTotale', String.valueOf(totalPremium).replace(',', '.').replace('.', ','));
        childResult.put('valoriTotali', prodottiTotali);
        Set<String> picklistValueKey = picklistValues('InsurancePolicy', 'AreaOfNeed__c').keySet();
        for(String val : picklistValueKey){
            String valNoSpace = val.deleteWhitespace().replaceAll('à','');
            if(childResult.get(valNoSpace) == null){
                Map<String,String> values = new Map<String,String>();
                values.put('visible','false');
                values.put('count','0');
                values.put('totalAmount','0');
                childResult.put(valNoSpace, values);
            }
        }

        return childResult;
    }
    
    // Metodo di Utilità per retrivare valori picklist
    public static Map<String, String> picklistValues(String objectName, String fieldName) {

        Map<String, String> values = new Map<String, String>{};
        List<Schema.DescribeSobjectResult> results = Schema.describeSObjects(new List<String>{objectName});
        for(Schema.DescribeSobjectResult res : results) {
            for (Schema.PicklistEntry entry : res.fields.getMap().get(fieldName).getDescribe().getPicklistValues()) {
                if (entry.isActive()) {
                    values.put(entry.getValue(), entry.getLabel());
                }
            }
        }
        return values;
    }

}