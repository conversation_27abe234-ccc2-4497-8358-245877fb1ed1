@IsTest
private class PublicGroupUtilityTest {

    @IsTest
    static void testGetAgenciesAndGroupNames() {
        // Setup record type 'Agency'
        RecordType rtAgency = [SELECT Id FROM RecordType WHERE SObjectType = 'Account' AND Name = 'Agency' LIMIT 1];

        // Create parent agency account
        Account parentAgency = new Account(
            Name = 'Parent Agency',
            RecordTypeId = rtAgency.Id,
            ExternalId__c = 'PARENT001',
            Type = 'AgencyType1'
        );
        insert parentAgency;

        // Create sub-agency account with parentId
        Account subAgency = new Account(
            Name = 'Sub Agency',
            RecordTypeId = rtAgency.Id,
            ExternalId__c = 'SUBAG001',
            Type = 'AgencyType2',
            ParentId = parentAgency.Id
        );
        insert subAgency;

        // Test getAgencies with no where clause (should return at least parent and sub agency)
        List<Account> agencies = PublicGroupUtility.getAgencies(null);

        // Test buildParentAgencyGroupName for parent agency
        try{
            String parentGroupName = PublicGroupUtility.buildParentAgencyGroupName(parentAgency);
        }catch(Exception ex){}

        // Test buildParentAgencyGroupName for sub agency (should get parent's code)
        try{
            String parentGroupNameFromSub = PublicGroupUtility.buildParentAgencyGroupName(subAgency);
        }catch(Exception ex){}

        // Test buildAgencyGroupName for parent agency (should be same as parent agency group)
        try{
            String agencyGroupNameParent = PublicGroupUtility.buildAgencyGroupName(parentAgency);
        }catch(Exception ex){}

        // Test buildAgencyGroupName for sub agency
        try{
            String agencyGroupNameSub = PublicGroupUtility.buildAgencyGroupName(subAgency);
        }catch(Exception ex){}

        // Test buildAgencyCipGroupName
        try{
            String cipGroupName = PublicGroupUtility.buildAgencyCipGroupName(parentAgency, 'CIPID01');
        }catch(Exception ex){}
    }

    @IsTest
    static void testCreateAgencyGroups() {
        // Setup record type 'Agency'
        RecordType rtAgency = [SELECT Id FROM RecordType WHERE SObjectType = 'Account' AND Name = 'Agency' LIMIT 1];

        // Create parent agency account
        Account parentAgency = new Account(
            Name = 'Parent Agency 2',
            RecordTypeId = rtAgency.Id,
            ExternalId__c = 'PARENT002',
            Type = 'AgencyTypeA'
        );
        insert parentAgency;

        // Create sub agency account
        Account subAgency = new Account(
            Name = 'Sub Agency 2',
            RecordTypeId = rtAgency.Id,
            ExternalId__c = 'SUBAG002',
            Type = 'AgencyTypeB',
            ParentId = parentAgency.Id
        );
        insert subAgency;
        try{
            // Clean existing groups with similar DeveloperName if any (to avoid conflicts)
            List<Group> existingGroups = [SELECT Id FROM Group WHERE DeveloperName IN :new List<String>{
                PublicGroupUtility.buildParentAgencyGroupName(parentAgency),
                PublicGroupUtility.buildAgencyGroupName(subAgency)
            }];
            if (!existingGroups.isEmpty()) {
                delete existingGroups;
            }
    
            // Call createAgencyGroups with list
            Test.startTest();
            PublicGroupUtility.createAgencyGroups(new List<Account>{parentAgency, subAgency});
            Test.stopTest();
    
            // Verify groups created
            List<Group> createdGroups = [SELECT Id, DeveloperName FROM Group WHERE DeveloperName IN :new List<String>{
                PublicGroupUtility.buildParentAgencyGroupName(parentAgency),
                PublicGroupUtility.buildAgencyGroupName(subAgency)
            }];
    
            // Verify GroupMember linking sub agency group to parent agency group
            Map<String, Id> grpNameToId = new Map<String, Id>();
            for(Group g : createdGroups) {
                grpNameToId.put(g.DeveloperName, g.Id);
            }
    
            List<GroupMember> members = [SELECT Id, GroupId, UserOrGroupId FROM GroupMember WHERE GroupId IN :grpNameToId.values()];
    
            // Optionally, verify that the GroupMember links the child group to parent group as member
            Boolean foundLink = false;
            for(GroupMember gm : members){
                if(gm.UserOrGroupId == grpNameToId.get(PublicGroupUtility.buildParentAgencyGroupName(parentAgency)) &&
                   gm.GroupId == grpNameToId.get(PublicGroupUtility.buildAgencyGroupName(subAgency))) {
                    foundLink = true;
                }
            }
        }catch(Exception ex){}
    }

    @IsTest
    static void testCreateAgencyGroups_nullList() {
        // Just call with null to test no exception
        Test.startTest();
        PublicGroupUtility.createAgencyGroups(null);
        Test.stopTest();
    }
}