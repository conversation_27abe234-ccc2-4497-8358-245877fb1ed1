@isTest
private class AppointmentTopicTimeSlotDataLoaderTest {
	
    @testSetup
    static void setupTestData() {
    	OperatingHours op = new OperatingHours(Name = 'TestOp');
        insert op;    
    }
    
    @isTest
    static void testRetrieveAndInsertRecords() {
        // Crea un WorkTypeGroup dummy
        WorkTypeGroup workTypeGroup = new WorkTypeGroup(Name = 'Test Group');
        insert workTypeGroup;
        
        OperatingHours oh = [SELECT Id FROM OperatingHours LIMIT 1];

        // Crea TimeSlot con WorkTypeGroup associato
        TimeSlot ts1 = new TimeSlot(WorkTypeGroupId = workTypeGroup.Id,StartTime=Time.newInstance(9, 0, 0, 0),EndTime=Time.newInstance(18, 0, 0, 0),OperatingHoursId=oh.Id);
        insert new List<TimeSlot>{ ts1 };

        Test.startTest();
        AppointmentTopicTimeSlotDataLoader.retrieveAndInsertRecords();
        Test.stopTest();

        // Verifica che siano stati creati 2 AppointmentTopicTimeSlot
        List<AppointmentTopicTimeSlot> results = [
            SELECT Id, TimeSlotId, WorkTypeGroupId
            FROM AppointmentTopicTimeSlot
            WHERE TimeSlotId IN :new List<Id>{ ts1.Id }
        ];
    }

    @isTest
    static void testNoEligibleTimeSlot() {
        // Nessun TimeSlot con WorkTypeGroupId → nessun inserimento
		OperatingHours oh = [SELECT Id FROM OperatingHours LIMIT 1];
        TimeSlot ts = new TimeSlot(StartTime=Time.newInstance(9, 0, 0, 0),EndTime=Time.newInstance(18, 0, 0, 0),OperatingHoursId=oh.Id); // WorkTypeGroupId = null
        insert ts;

        Test.startTest();
        AppointmentTopicTimeSlotDataLoader.retrieveAndInsertRecords();
        Test.stopTest();

        List<AppointmentTopicTimeSlot> results = [
            SELECT Id FROM AppointmentTopicTimeSlot
        ];
    }
}