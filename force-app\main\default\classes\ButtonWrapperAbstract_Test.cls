@isTest
public class ButtonWrapperAbstract_Test {

    @isTest
    static void getValidButtonsBiggerThanOperatorsTest() {
        ButtonWrapperController.buttonMetadata = (List<ButtonWrapper__mdt>) JSON.deserialize(
            '[{"attributes":{"type":"ButtonWrapper__mdt","url":"/services/data/v60.0/sobjects/ButtonWrapper__mdt/m0T1x0000009VBqEAM"},"DeveloperName":"AssigntoContactCenterReject","ButtonLabel__c":"Rifiuta","ButtonName__c":"assigntoContactCenterBtn","ButtonStyling__c":"btn green-background","Checks__c":"{\\"clientType\\": {\\"value\\": \\"Prospect;Cliente\\",\\"operator\\": \\"contains\\",\\"failureVisibility\\": \\"hidden\\"},\\"slaRemainingHours\\": {\\"value\\": 0,\\"operator\\": \\">\\",\\"failureVisibility\\": \\"hidden\\"},\\"stage\\": {\\"value\\": \\"Assegnato\\",\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"hidden\\"}}","IsActive__c":true,"Object__c":"Opportunity","Order__c":2,"Position__c":"Reassign","Id":"m0T1x0000009VBqEAM"},{"attributes":{"type":"ButtonWrapper__mdt","url":"/services/data/v60.0/sobjects/ButtonWrapper__mdt/m0T1x0000009VBWEA2"},"DeveloperName":"ReassigntoAgencyInGestione","ButtonLabel__c":"Riassegna in agenzia","ButtonName__c":"reassigntoAgencyBtn","ButtonStyling__c":"btn green-background","Checks__c":"{\\"clientType\\": {\\"value\\": \\"Prospect;Cliente\\",\\"operator\\": \\"contains\\",\\"failureVisibility\\": \\"hidden\\"},\\"stage\\": {\\"value\\": \\"In gestione\\",\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"hidden\\"}}","IsActive__c":true,"Object__c":"Opportunity","Order__c":3,"Position__c":"Reassign","Id":"m0T1x0000009VBWEA2"},{"attributes":{"type":"ButtonWrapper__mdt","url":"/services/data/v60.0/sobjects/ButtonWrapper__mdt/m0T1x0000009VBbEAM"},"DeveloperName":"AssigntoContactCenterAssegnato","ButtonLabel__c":"Assegna a Contact Center","ButtonName__c":"assigntoContactCenterBtn","ButtonStyling__c":"btn green-background","Checks__c":"{\\"clientType\\": {\\"value\\": \\"Prospect;Cliente\\",\\"operator\\": \\"contains\\",\\"failureVisibility\\": \\"hidden\\"},\\"slaRemainingHours\\": {\\"value\\": 0,\\"operator\\": \\">=\\",\\"failureVisibility\\": \\"hidden\\"},\\"stage\\": {\\"value\\": \\"Assegnato\\",\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"hidden\\"},\\"omnichannelAgreement\\": {\\"value\\": true,\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"disabled\\"}}","IsActive__c":true,"Object__c":"Opportunity","Order__c":4,"Position__c":"Reassign","Id":"m0T1x0000009VBbEAM"},{"attributes":{"type":"ButtonWrapper__mdt","url":"/services/data/v60.0/sobjects/ButtonWrapper__mdt/m0T1x0000009VBvEAM"},"DeveloperName":"AssigntoContactCenterInGestione","ButtonLabel__c":"Assegna a Contact Center","ButtonName__c":"assigntoContactCenterBtn","ButtonStyling__c":"btn green-background","Checks__c":"{\\"clientType\\": {\\"value\\": \\"Prospect;Cliente\\",\\"operator\\": \\"contains\\",\\"failureVisibility\\": \\"hidden\\"},\\"stage\\": {\\"value\\": \\"In gestione\\",\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"hidden\\"},\\"omnichannelAgreement\\": {\\"value\\": true,\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"disabled\\"}}","IsActive__c":true,"Object__c":"Opportunity","Order__c":4,"Position__c":"Reassign","Id":"m0T1x0000009VBvEAM"}]',
            List<ButtonWrapper__mdt>.class
        );
        System.debug('# buttonMetadata: ' + ButtonWrapperController.buttonMetadata.size());
        String recordDataJSON = '{"clientType":"Cliente","slaRemainingHours":0,"stage":"In gestione","substatus":"In lavorazione","omnichannelAgreement":true}';
        String classTypeName = 'OpportunityButtonWrapper';
        Type classType = Type.forName(classTypeName);
        ButtonWrapperAbstract buttonHandler = (ButtonWrapperAbstract) classType.newInstance();

        Test.startTest();
        ButtonWrapperController.ButtonWrapper response = buttonHandler.getValidButtons(recordDataJSON, ButtonWrapperController.buttonMetadata, classTypeName);
        Test.stopTest();

        Assert.areEqual(2, response.buttons.size(), 'There should have been two buttons available');
    }

    @isTest
    static void getValidButtonsSmallerThanOperatorsTest() {
        ButtonWrapperController.buttonMetadata = (List<ButtonWrapper__mdt>) JSON.deserialize(
            '[{"attributes":{"type":"ButtonWrapper__mdt","url":"/services/data/v60.0/sobjects/ButtonWrapper__mdt/m0T1x0000009VBqEAM"},"DeveloperName":"AssigntoContactCenterReject","ButtonLabel__c":"Rifiuta","ButtonName__c":"assigntoContactCenterBtn","ButtonStyling__c":"btn green-background","Checks__c":"{\\"clientType\\": {\\"value\\": \\"Prospect;Cliente\\",\\"operator\\": \\"contains\\",\\"failureVisibility\\": \\"hidden\\"},\\"slaRemainingHours\\": {\\"value\\": 0,\\"operator\\": \\"<\\",\\"failureVisibility\\": \\"hidden\\"},\\"stage\\": {\\"value\\": \\"Assegnato\\",\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"hidden\\"}}","IsActive__c":true,"Object__c":"Opportunity","Order__c":2,"Position__c":"Reassign","Id":"m0T1x0000009VBqEAM"},{"attributes":{"type":"ButtonWrapper__mdt","url":"/services/data/v60.0/sobjects/ButtonWrapper__mdt/m0T1x0000009VBWEA2"},"DeveloperName":"ReassigntoAgencyInGestione","ButtonLabel__c":"Riassegna in agenzia","ButtonName__c":"reassigntoAgencyBtn","ButtonStyling__c":"btn green-background","Checks__c":"{\\"clientType\\": {\\"value\\": \\"Prospect;Cliente\\",\\"operator\\": \\"contains\\",\\"failureVisibility\\": \\"hidden\\"},\\"stage\\": {\\"value\\": \\"In gestione\\",\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"hidden\\"}}","IsActive__c":true,"Object__c":"Opportunity","Order__c":3,"Position__c":"Reassign","Id":"m0T1x0000009VBWEA2"},{"attributes":{"type":"ButtonWrapper__mdt","url":"/services/data/v60.0/sobjects/ButtonWrapper__mdt/m0T1x0000009VBbEAM"},"DeveloperName":"AssigntoContactCenterAssegnato","ButtonLabel__c":"Assegna a Contact Center","ButtonName__c":"assigntoContactCenterBtn","ButtonStyling__c":"btn green-background","Checks__c":"{\\"clientType\\": {\\"value\\": \\"Prospect;Cliente\\",\\"operator\\": \\"contains\\",\\"failureVisibility\\": \\"hidden\\"},\\"slaRemainingHours\\": {\\"value\\": 0,\\"operator\\": \\"<=\\",\\"failureVisibility\\": \\"hidden\\"},\\"stage\\": {\\"value\\": \\"Assegnato\\",\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"hidden\\"},\\"omnichannelAgreement\\": {\\"value\\": true,\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"disabled\\"}}","IsActive__c":true,"Object__c":"Opportunity","Order__c":4,"Position__c":"Reassign","Id":"m0T1x0000009VBbEAM"},{"attributes":{"type":"ButtonWrapper__mdt","url":"/services/data/v60.0/sobjects/ButtonWrapper__mdt/m0T1x0000009VBvEAM"},"DeveloperName":"AssigntoContactCenterInGestione","ButtonLabel__c":"Assegna a Contact Center","ButtonName__c":"assigntoContactCenterBtn","ButtonStyling__c":"btn green-background","Checks__c":"{\\"clientType\\": {\\"value\\": \\"Prospect;Cliente\\",\\"operator\\": \\"contains\\",\\"failureVisibility\\": \\"hidden\\"},\\"stage\\": {\\"value\\": \\"In gestione\\",\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"hidden\\"},\\"omnichannelAgreement\\": {\\"value\\": true,\\"operator\\": \\"=\\",\\"failureVisibility\\": \\"disabled\\"}}","IsActive__c":true,"Object__c":"Opportunity","Order__c":4,"Position__c":"Reassign","Id":"m0T1x0000009VBvEAM"}]',
            List<ButtonWrapper__mdt>.class
        );
        System.debug('# buttonMetadata: ' + ButtonWrapperController.buttonMetadata.size());
        String recordDataJSON = '{"clientType":"Cliente","slaRemainingHours":0,"stage":"In gestione","substatus":"In lavorazione","omnichannelAgreement":true}';
        String classTypeName = 'OpportunityButtonWrapper';
        Type classType = Type.forName(classTypeName);
        ButtonWrapperAbstract buttonHandler = (ButtonWrapperAbstract) classType.newInstance();

        Test.startTest();
        ButtonWrapperController.ButtonWrapper response = buttonHandler.getValidButtons(recordDataJSON, ButtonWrapperController.buttonMetadata, classTypeName);
        Test.stopTest();

        Assert.areEqual(2, response.buttons.size(), 'There should have been two buttons available');
    }    
}
