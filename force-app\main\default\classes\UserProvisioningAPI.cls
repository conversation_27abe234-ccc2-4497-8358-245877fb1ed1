/*
* @description Class for handling UserProvisioningAPI
* @cicd_tests UserProvisioningHandler_Test
*/
public without sharing class UserProvisioningAPI {
    
    public class UCARequest{
        public List<String> utenti = new List<String>();
    }
    
    public class UCAResponse{
        public List<UCARequestItem> result = new List<UCARequestItem>();
    }
    
    public class UCARequestItem{
        
        public String codiceFiscale		{get;set;}
        public String utenza 			{get;set;}
        public List<String> gruppi = new List<String>();
        public EmbeddedItem embedded = new EmbeddedItem();
        
    }
    
    public class EmbeddedItem{
        
        public AbilitazioniOperativeItem abilitazioniOperative = new AbilitazioniOperativeItem();
        
    }
    
    public class AbilitazioniOperativeItem{
        
        public List<AbilitazioneItem> abilitazioni = new List<AbilitazioneItem>();
        
    }
    
    public class AbilitazioneItem{
        
        public String codice 			{get;set;}
        
    }

    /*
    * @description Method to call UCA API and get information on UNIPOL Users related to a specific Fiscal Code
    * @param userCF The fiscal code of the user
    * @return UCAResponse the API Response
    */
    public static UCAResponse getUCAData(String userCF){
        
        UCARequest requestBody = new UCARequest();
        requestBody.utenti = UserProvisioningUtils.getRelatedNetworkUser(userCF);
        
        Http h = new Http();
        HttpRequest req = new HttpRequest();
        
        req.setEndpoint('callout:UserProvisioning');
        req.setMethod('POST');
        req.setTimeout(120000);
        req.setHeader('Content-Type','application/json');
        req.setHeader('Accept','application/json');
        
        //System.debug('requestBody --> '+requestBody);
        
        req.setBody(JSON.serialize(requestBody));

        HttpResponse res = (!Test.isRunningTest()) ? h.send(req) : new HttpResponse();
        
        //System.debug('response body --> '+res.getBody());
        
        List<UCARequestItem> apiResponse = (Test.isRunningTest()) ? getTestReponse() : (List<UCARequestItem>)JSON.deserialize(res.getBody(),List<UCARequestItem>.class);
        
        UCAResponse response = new UCAResponse();
        response.result = apiResponse;
        
        //System.debug('response --> '+response);
        
        return response;
        
    }

    /*public static UCAResponse getTestReponse(){

        UCAResponse response = new UCAResponse();

        response.result = new List<UCARequestItem>();

        UCARequestItem item = new UCARequestItem();
        item.codiceFiscale = 'TEST';
        item.utenza = 'TEST';
        item.embedded = new EmbeddedItem();
        item.embedded.abilitazioniOperative = new AbilitazioniOperativeItem();
        item.embedded.abilitazioniOperative.abilitazioni = new List<AbilitazioneItem>();
        item.gruppi = new List<String>();

        AbilitazioneItem ai = new AbilitazioneItem();
        ai.codice = '0000';

        item.embedded.abilitazioniOperative.abilitazioni.add(ai);

        response.result.add(item);

        return response;

    }*/
    
    public static List<UCARequestItem> getTestReponse(){

        UCAResponse response = new UCAResponse();

        response.result = new List<UCARequestItem>();

        UCARequestItem item = new UCARequestItem();
        item.codiceFiscale = '****************';
        item.utenza = '1TEST99999XX';
        item.embedded = new EmbeddedItem();
        item.embedded.abilitazioniOperative = new AbilitazioniOperativeItem();
        item.embedded.abilitazioniOperative.abilitazioni = new List<AbilitazioneItem>();
        item.gruppi = new List<String>();
		item.gruppi.add('AAAGQAAS');
        item.gruppi.add('AAAGQAASDI');
        item.gruppi.add('AAAQAGQA');
        item.gruppi.add('AAAQSUPPIT');
        item.gruppi.add('AACRAGRC');
        item.gruppi.add('AACRAGRP');
        item.gruppi.add('AACRAGRT');
        item.gruppi.add('AACRAGVC');
        item.gruppi.add('AACRAGVE');
        item.gruppi.add('AACRDCNV');
        item.gruppi.add('AACRDIAO');
        item.gruppi.add('AACRDIHO');
        item.gruppi.add('AACRMKCO');
        item.gruppi.add('AACRMUNIB');
        item.gruppi.add('AACRRAIS');
        item.gruppi.add('AACRSUIT');
        item.gruppi.add('AAORGXDRTA');
        item.gruppi.add('AAQARAPA');
        item.gruppi.add('AAQARAPD');
        item.gruppi.add('AAQTAGRP');
        item.gruppi.add('AAQTDIVL');
        item.gruppi.add('AARADIRP');
        item.gruppi.add('AASMA2DIRE');
        item.gruppi.add('APAGQAAS');
        item.gruppi.add('APAGQAASDI');
        item.gruppi.add('APAQAGQA');
        item.gruppi.add('APAQSUPPIT');
        item.gruppi.add('APCRAGRC');
        item.gruppi.add('APCRAGRP');
        item.gruppi.add('APCRAGRT');
        item.gruppi.add('APCRAGVC');
        item.gruppi.add('APCRAGVE');
        item.gruppi.add('APCRDCNV');
        item.gruppi.add('APCRDIAO');
        item.gruppi.add('APCRDIHO');
        item.gruppi.add('APCRMKCO');
        item.gruppi.add('APCRMUNIB');
        item.gruppi.add('APCRRAIS');
        item.gruppi.add('APCRSUIT');
        item.gruppi.add('APORGXDRTA');
        item.gruppi.add('APQARAPA');
        item.gruppi.add('APQARAPD');
        item.gruppi.add('APQTAGRP');
        item.gruppi.add('APQTDIVL');
        item.gruppi.add('APRAAGRP');
        item.gruppi.add('APRADIRP');
        item.gruppi.add('APSMA2DIRE');
        item.gruppi.add('AXAGEXTBRO');
        item.gruppi.add('AXAGEXTRES');
        item.gruppi.add('AXORGXDV');
        item.gruppi.add('AXORGXDVCA');
        item.gruppi.add('AXORGXDVCD');
        item.gruppi.add('AXORGXDVIS');
        item.gruppi.add('AXORGXDVSU');
        item.gruppi.add('AXORGXDVTB');
        item.gruppi.add('AXORGXDVTV');
        item.gruppi.add('GROUP_AACRAGRC');
        item.gruppi.add('GROUP_AACRAGRT');
        item.gruppi.add('GROUP_AACRSUIT');
        item.gruppi.add('GROUP_AAORGXDLT');
        item.gruppi.add('GROUP_AAQARAPA');
        item.gruppi.add('GROUP_AAQARAPD');
        item.gruppi.add('GROUP_AAQTAGRP');
        item.gruppi.add('GROUP_AAQTDIVL');
        item.gruppi.add('GROUP_AARAAGRP');
        item.gruppi.add('GROUP_AARADIRP');
        item.gruppi.add('GROUP_AARADMON');
        item.gruppi.add('GROUP_APCRAGRC');
        item.gruppi.add('GROUP_APCRAGRT');
        item.gruppi.add('GROUP_APCRSUIT');
        item.gruppi.add('GROUP_APORGXDLT');
        item.gruppi.add('GROUP_APQARAPA');
        item.gruppi.add('GROUP_APQARAPD');
        item.gruppi.add('GROUP_APQTAGRP');
        item.gruppi.add('GROUP_APQTDIVL');
        item.gruppi.add('GROUP_APRAAGRP');
        item.gruppi.add('GROUP_APRADIRP');
        item.gruppi.add('GROUP_APRADMON');
        item.gruppi.add('GROUP_AXAGEXTRES');
        item.gruppi.add('GROUP_AXORGXDVCA');
        item.gruppi.add('GROUP_AXORGXDVCD');
        item.gruppi.add('GROUP_AXORGXDVIS');
        item.gruppi.add('GROUP_AXORGXDVSU');
        item.gruppi.add('GROUP_AXORGXDVTF');
        item.gruppi.add('GROUP_AXORGXDVTP');
        item.gruppi.add('GROUP_AXORGXDVTV');
        item.gruppi.add('201000000');
        item.gruppi.add('201010000');
        item.gruppi.add('201020000');
        item.gruppi.add('201030100');
        item.gruppi.add('201030200');
        item.gruppi.add('201030300');
        item.gruppi.add('201030400');
        item.gruppi.add('201030500');
        item.gruppi.add('201030600');
        item.gruppi.add('201030700');
        item.gruppi.add('201030800');
        item.gruppi.add('201030900');
        item.gruppi.add('201031000');
        item.gruppi.add('202000000');
        item.gruppi.add('202010000');
        item.gruppi.add('202020000');
        item.gruppi.add('202020100');
        item.gruppi.add('202020101');
        item.gruppi.add('202020102');
        item.gruppi.add('202020103');
        item.gruppi.add('202020104');
        item.gruppi.add('202020105');
        item.gruppi.add('202020500');
        item.gruppi.add('202020501');
        item.gruppi.add('202020502');
        item.gruppi.add('202020503');
        item.gruppi.add('202020504');
        item.gruppi.add('202020600');
        item.gruppi.add('202030000');
        item.gruppi.add('202120000');
        item.gruppi.add('202140000');
        item.gruppi.add('202150000');
        item.gruppi.add('202150100');
        item.gruppi.add('202150101');
        item.gruppi.add('202150102');
        item.gruppi.add('202150103');
        item.gruppi.add('202150104');
        item.gruppi.add('202150105');
        item.gruppi.add('202150106');
        item.gruppi.add('202150107');
        item.gruppi.add('202150200');
        item.gruppi.add('202150300');
        item.gruppi.add('202150301');
        item.gruppi.add('202150302');
        item.gruppi.add('202150303');
        item.gruppi.add('202150304');
        item.gruppi.add('202150305');
        item.gruppi.add('202150400');
        item.gruppi.add('202150500');
        item.gruppi.add('202150501');
        item.gruppi.add('202150502');
        item.gruppi.add('202150503');
        item.gruppi.add('202150504');
        item.gruppi.add('202150600');
        item.gruppi.add('202200100');
        item.gruppi.add('202200200');
        item.gruppi.add('202210100');
        item.gruppi.add('202210200');
        item.gruppi.add('206110100');
        item.gruppi.add('206110200');
        item.gruppi.add('401010000');
        item.gruppi.add('401010100');
        item.gruppi.add('401010200');
        item.gruppi.add('401040000');
        item.gruppi.add('401040200');
        item.gruppi.add('401040300');
        item.gruppi.add('502000000');
        item.gruppi.add('505000000');
        item.gruppi.add('506000000');
        item.gruppi.add('601050000');

        AbilitazioneItem ai = new AbilitazioneItem();
        ai.codice = '0000';

        item.embedded.abilitazioniOperative.abilitazioni.add(ai);

        response.result.add(item);

        return response.result;

    }

}