/**
 * @File Name         : AnagraficaTest.cls
 * @Description       :
 * <AUTHOR> <EMAIL>
 * @Group             :
 * @Last Modified On  : 03-02-2025
 * @Last Modified By  : <EMAIL>
**/
@isTest
public with sharing class ProfessioniTest {
    @isTest
    static void getProfessioniTest() {
        Test.startTest();
        Object result = ProfessioniController.getProfessioni();
        Test.stopTest();
    }

    @isTest
    static void getRicProfTest() {
        Test.startTest();
        Object result = ProfessioniController.getRicProf(new Map<String, Object>());
        Test.stopTest();
    }

    @isTest
    static void getPersonaGiuridicaTest() {
        Test.startTest();
        Object result = ProfessioniController.getPersonaGiuridica();
        Test.stopTest();
    }

    @isTest
    static void getRicPgTest() {
        Test.startTest();
        Object result = ProfessioniController.getRicPg(new Map<String, Object>());
        Test.stopTest();
    }
}