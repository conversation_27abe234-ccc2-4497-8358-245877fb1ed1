public without sharing class UserSharingStrategy implements SharingStrategy {
    private List<User> users = new List<User>();
    private List<UserShare> shares = new List<UserShare>();

    public void collectTargetRecords(List<SObject> records) {
        for (SObject s : records) {
            if (s instanceof User) users.add((User)s);
        }
    }

    public void applySharing() {
        Set<String> fiscalCodes = new Set<String>();
        for (User u : users) {
            if (u.FiscalCode__c != null) {
                fiscalCodes.add(u.FiscalCode__c);
            }
        }

        Map<String, NetworkUser__c> fiscalCodeToNetworkUser = new Map<String, NetworkUser__c>();
        List<NetworkUser__c> networkUsersList = [SELECT Id, FiscalCode__c, Agency__c, Agency__r.ExternalId__c FROM NetworkUser__c WHERE FiscalCode__c IN :fiscalCodes];
        for(NetworkUser__c nu : networkUsersList){
            if(nu.FiscalCode__c != null){
                fiscalCodeToNetworkUser.put(nu.FiscalCode__c, nu);
            }
        }

        Set<String> externalIds = new Set<String>();
        for (NetworkUser__c nu : fiscalCodeToNetworkUser.values()) {
            if (nu.Agency__r.ExternalId__c != null)
                externalIds.add(nu.Agency__r.ExternalId__c);
        }

        Map<String, Group> groupMap = GroupHelper.getGroupsByExternalIds(externalIds);

        for (User u : users) {
            NetworkUser__c nu = fiscalCodeToNetworkUser.get(u.FiscalCode__c);
            if (nu == null) continue;

            Group publicGroup = groupMap.get(nu.Agency__r.ExternalId__c);
            if (publicGroup == null) continue;

            shares.add(new UserShare(
                UserId = u.Id,
                UserOrGroupId = publicGroup.Id,
                UserAccessLevel = 'Read',
                RowCause = Schema.UserShare.RowCause.Manual
            ));
        }

        if (!shares.isEmpty()){
            insert shares;
        }
    }
}