@isTest
public with sharing class InsurancePolicyControllerTest {
    
    @TestSetup
    static void makeData(){
        
        Opportunity opp1 = new Opportunity();
        Account subject = new Account();
        Account agency = new Account();
        
        InsurancePolicy ip1 = new InsurancePolicy();
        
        RecordType prospectRecordType = [SELECT Id FROM RecordType WHERE SObjectType = 'Account' LIMIT 1]; // LP rimozione BusinessProspect -> Business
        RecordType agencyRecordType = [SELECT Id FROM RecordType WHERE SObjectType = 'Account' AND DeveloperName = 'Agency' LIMIT 1];
        
        subject.Name = 'Mario BWTest';
        subject.RecordTypeId = prospectRecordType.Id;
        insert subject;
        
        agency.Name = 'BWTest Agency';
        agency.RecordTypeId = agencyRecordType.Id;
        agency.OmnichannelAgreement__c = true;
        insert agency;
        
        opp1.Name = 'BWTest Opportunity 1';
        opp1.Company__c = '1';
        opp1.CIP__c = 'BWTest Opportunity 1 CIP';
        opp1.AccountId = subject.Id;
        opp1.TakenInChargeSLAExpiryDate__c = Datetime.newInstance(2024, 3, 19, 12, 0, 0);
        opp1.StageName = 'In gestione';
        opp1.Substatus__c = 'In lavorazione';
        opp1.Agency__c = agency.Id;
        opp1.CloseDate = Date.today().addDays(60);
        insert opp1;
        
        String recordTypeId = Schema.SObjectType.InsurancePolicy.getRecordTypeInfosByName().get('Polizza Prodotto Unico').getRecordTypeId();
        
        ip1.Name = 'IP Test';
        ip1.Company__c = '1';
        ip1.CIP__c = 'IP Test CIP';
        ip1.NameInsuredId = subject.Id;
        ip1.CompanyCode__c = 'TEST';
        ip1.Society__c = subject.Id;
        ip1.RecordTypeId = recordTypeId;
        insert ip1;
        
        Asset ast = new Asset(Name='ass1',AccountId=subject.Id,Price=100);
        insert ast;
        
        System.debug('subject' +subject.Id);
        System.debug('agency' +agency.Id);
        System.debug('opp1' +opp1.Id);
        System.debug('ip1' +ip1.Id);
        System.debug('ast' +ast.Id);
        
    }
    
    @isTest
    static void testgetInsurancePolicies() {
        Test.startTest();
        Id accId = [SELECT Id FROM Account WHERE Name='Mario BWTest' LIMIT 1].Id;
        List<InsurancePolicy> listInsurance = InsurancePolicyController.getInsurancePolicies(accId);
        List<Id> listInsuranceId = InsurancePolicyController.getFilteredPolicyIds(accId);
        System.assertEquals(1, listInsurance.size());
        System.assertEquals(1, listInsuranceId.size());
        Test.stopTest();
    }
    
    @isTest
    static void testgetTrattativa() {
        Test.startTest();
        Id accId = getAccountCreated().Id;
        List<Opportunity> listOppo = InsurancePolicyController.getTrattativa(accId);
        System.assertEquals(1, listOppo.size());
        Test.stopTest();
    }
    
    @isTest
    static void testgetProdottoNon() {
        Test.startTest();
        Id accId = getAccountCreated().Id;
        List<Asset> listAsset = InsurancePolicyController.getProdottoNon(accId);
        System.assertEquals(1, listAsset.size());
        Test.stopTest();
    }
    
    public static Account getAccountCreated(){
        return [SELECT Id FROM Account WHERE Name='Mario BWTest' LIMIT 1];
    }

    @isTest
    static void testCheckAbbinato() {
        Test.startTest();
        // Recupera un account creato dal setup
        Id accId = getAccountCreated().Id;
        // Esegui il metodo
        Map<String, Object> result = InsurancePolicyController.checkAbbinato(accId);
        System.assert(result.containsKey('isAbbinato'), 'La chiave isAbbinato deve essere presente');
        Test.stopTest();
    }

    @isTest
    static void testGetUserFromAgency() {
        Test.startTest();
        // Esegui il metodo
        List<Id> userIds = InsurancePolicyController.getUserFromAgency();
        System.assertNotEquals(null, userIds, 'La lista non deve essere null');
        // Non possiamo garantire che ci sia almeno un user, ma la chiamata non deve fallire
        Test.stopTest();
    }
    
}