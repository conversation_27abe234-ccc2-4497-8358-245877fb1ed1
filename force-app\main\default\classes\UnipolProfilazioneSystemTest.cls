@isTest
public class UnipolProfilazioneSystemTest {
    
    @isTest
    static void test_SOC1_and_agency(){

        Test.startTest();

        /*
        * FiscalCode 
        * Name = SystemTest1
        * LastName = Responsabile1
        */
        String resp1FiscalCode = '****************';

        /*
        * Agency Code = TEST99999
        */
        String agencyCode = 'TEST99999';

        /*
        * Society
        */
        String socName = 'SOC_1';

        List<Account> accountToInsert = new List<Account>();

        /*
        * Insert Agency Account 01853
        */
        Account agency = new Account();
        agency.Name = 'SystemTest '+agencyCode;
        agency.ExternalId__c = 'AGE_'+agencyCode;
        agency.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId();
        accountToInsert.add(agency);

        /*
        * Insert Society Account SOC_1 (Unipol)
        */
        Account soc1 = new Account();
        soc1.Name = 'Unipol';
        soc1.ExternalId__c = socName;
        soc1.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId();
        accountToInsert.add(soc1);

        insert accountToInsert;

        /*
        * Insert Responsabile User for agency 01853
        */
        Profile userProfile = [SELECT Id FROM Profile WHERE Name = 'Unipol Responsabile User' LIMIT 1];
        User testUser = new User(
            FirstName = 'Responsabile1',
            LastName = 'SystemTest1',
            Email = '<EMAIL>',
            Username = '<EMAIL>',
            Alias = 'resyt',
            ProfileId = userProfile.Id,
            TimeZoneSidKey = 'GMT',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            IdAzienda__c = agency.Id,
            FiscalCode__c = resp1FiscalCode,
            FederationIdentifier = resp1FiscalCode,
            ExternalId__c = resp1FiscalCode
        );
        insert testUser;

        /*
        * Create NetworkUser record related to the User Fiscal Code
        * It represents the Unipol RACF User
        */
        NetworkUser__c nu = new NetworkUser__c();
        nu.IsActive__c = true;
        nu.Agency__c = agency.Id;
        nu.FiscalCode__c = resp1FiscalCode;
        nu.ExternalId__c = '1'+agencyCode+'XX';
        nu.NetworkUser__c = '1'+agencyCode+'XX';
        nu.Profile__c = 'A';
        nu.Society__c = socName;
        insert nu;

        /*
        * Insert Groups
        */
        List<Group> groupToInsert = new List<Group>();

        Group groupAgents = new Group();
        groupAgents.Name = 'AGE_'+agencyCode;
        groupAgents.DeveloperName = 'AGE_'+agencyCode;
        groupToInsert.add(groupAgents);

        Group groupRes = new Group();
        groupRes.Name = 'R_AGE_'+agencyCode;
        groupRes.DeveloperName = 'R_AGE_'+agencyCode;
        groupToInsert.add(groupRes);

        insert groupToInsert;

        List<PermissionSet> existingPS = [SELECT Id, Name FROM PermissionSet];
        Set<String> existingPSSet = new Set<String>();
        for(PermissionSet ps : existingPS){
            existingPSSet.add(ps.Name);
        }

        List<PermissionSetAssignment> psaBefore = [SELECT Id, PermissionSet.Name FROM PermissionSetAssignment WHERE AssigneeId = :testUser.Id];
        Set<String> permissionSetName = new Set<String>();
        for(PermissionSetAssignment ps : psaBefore) {
            permissionSetName.add(ps.PermissionSet.Name);
        }

        List<GroupMember> gmBefore = [SELECT Id, Group.DeveloperName FROM GroupMember WHERE UserOrGroupId = :testUser.Id];
        Set<String> gmName = new Set<String>();
        for(GroupMember gm : gmBefore){
            gmName.add(gm.Group.DeveloperName);
        }

        // Permission Set not assigned to the user
        System.assert(!permissionSetName.contains('MandatoUnipolSai'));
        System.assert(!gmName.contains('AGE_'+agencyCode));
        System.assert(!gmName.contains('R_AGE_'+agencyCode));

        Database.executeBatch(new BatchProcessServiceResourcesV2());

        Test.stopTest();

        List<PermissionSetAssignment> psaAfter = [SELECT Id, PermissionSet.Name FROM PermissionSetAssignment WHERE AssigneeId = :testUser.Id];
        permissionSetName = new Set<String>();
        for(PermissionSetAssignment ps : psaAfter) {
            permissionSetName.add(ps.PermissionSet.Name);
        }

        List<GroupMember> gmAfter = [SELECT Id, Group.DeveloperName FROM GroupMember WHERE UserOrGroupId = :testUser.Id];
        gmName = new Set<String>();
        for(GroupMember gm : gmAfter){
            gmName.add(gm.Group.DeveloperName);
        }
        
        // Permission Set assigned to the user
        if(existingPSSet.contains('MandatoUnipolSai')) System.assert(permissionSetName.contains('MandatoUnipolSai'));
        System.assert(gmName.contains('AGE_'+agencyCode));
        System.assert(gmName.contains('R_AGE_'+agencyCode));

    }

    @isTest
    static void test_SOC4_and_agency(){

        Test.startTest();

        /*
        * FiscalCode 
        * Name = SystemTest1
        * LastName = Responsabile1
        */
        String resp1FiscalCode = '****************';

        /*
        * Agency Code = TEST99999
        */
        String agencyCode = 'TEST99999';

        /*
        * Society
        */
        String socName = 'SOC_4';

        List<Account> accountToInsert = new List<Account>();

        /*
        * Insert Agency Account 01853
        */
        Account agency = new Account();
        agency.Name = 'SystemTest '+agencyCode;
        agency.ExternalId__c = 'AGE_'+agencyCode;
        agency.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId();
        accountToInsert.add(agency);

        /*
        * Insert Society Account SOC_1 (Unipol)
        */
        Account soc = new Account();
        soc.Name = 'Unipol';
        soc.ExternalId__c = socName;
        soc.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId();
        accountToInsert.add(soc);

        insert accountToInsert;

        /*
        * Insert Responsabile User for agency 01853
        */
        Profile userProfile = [SELECT Id FROM Profile WHERE Name = 'Unipol Responsabile User' LIMIT 1];
        User testUser = new User(
            FirstName = 'Responsabile1',
            LastName = 'SystemTest1',
            Email = '<EMAIL>',
            Username = '<EMAIL>',
            Alias = 'resyt',
            ProfileId = userProfile.Id,
            TimeZoneSidKey = 'GMT',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            IdAzienda__c = agency.Id,
            FiscalCode__c = resp1FiscalCode,
            FederationIdentifier = resp1FiscalCode,
            ExternalId__c = resp1FiscalCode
        );
        insert testUser;

        /*
        * Create NetworkUser record related to the User Fiscal Code
        * It represents the Unipol RACF User
        */
        NetworkUser__c nu = new NetworkUser__c();
        nu.IsActive__c = true;
        nu.Agency__c = agency.Id;
        nu.FiscalCode__c = resp1FiscalCode;
        nu.ExternalId__c = '1'+agencyCode+'XX';
        nu.NetworkUser__c = '1'+agencyCode+'XX';
        nu.Profile__c = 'A';
        nu.Society__c = socName;
        insert nu;

        /*
        * Insert Groups
        */
        List<Group> groupToInsert = new List<Group>();

        Group groupAgents = new Group();
        groupAgents.Name = 'AGE_'+agencyCode;
        groupAgents.DeveloperName = 'AGE_'+agencyCode;
        groupToInsert.add(groupAgents);

        Group groupRes = new Group();
        groupRes.Name = 'R_AGE_'+agencyCode;
        groupRes.DeveloperName = 'R_AGE_'+agencyCode;
        groupToInsert.add(groupRes);

        insert groupToInsert;

        List<PermissionSet> existingPS = [SELECT Id, Name FROM PermissionSet];
        Set<String> existingPSSet = new Set<String>();
        for(PermissionSet ps : existingPS){
            existingPSSet.add(ps.Name);
        }

        List<PermissionSetAssignment> psaBefore = [SELECT Id, PermissionSet.Name FROM PermissionSetAssignment WHERE AssigneeId = :testUser.Id];
        Set<String> permissionSetName = new Set<String>();
        for(PermissionSetAssignment ps : psaBefore) {
            permissionSetName.add(ps.PermissionSet.Name);
        }

        List<GroupMember> gmBefore = [SELECT Id, Group.DeveloperName FROM GroupMember WHERE UserOrGroupId = :testUser.Id];
        Set<String> gmName = new Set<String>();
        for(GroupMember gm : gmBefore){
            gmName.add(gm.Group.DeveloperName);
        }

        // Permission Set not assigned to the user
        System.assert(!permissionSetName.contains('MandatoUniSalute'));
        System.assert(!gmName.contains('AGE_'+agencyCode));
        System.assert(!gmName.contains('R_AGE_'+agencyCode));

        Database.executeBatch(new BatchProcessServiceResourcesV2());

        Test.stopTest();

        List<PermissionSetAssignment> psaAfter = [SELECT Id, PermissionSet.Name FROM PermissionSetAssignment WHERE AssigneeId = :testUser.Id];
        permissionSetName = new Set<String>();
        for(PermissionSetAssignment ps : psaAfter) {
            permissionSetName.add(ps.PermissionSet.Name);
        }

        List<GroupMember> gmAfter = [SELECT Id, Group.DeveloperName FROM GroupMember WHERE UserOrGroupId = :testUser.Id];
        gmName = new Set<String>();
        for(GroupMember gm : gmAfter){
            gmName.add(gm.Group.DeveloperName);
        }

        // Permission Set assigned to the user
        if(existingPSSet.contains('MandatoUniSalute')) System.assert(permissionSetName.contains('MandatoUniSalute'));
        System.assert(gmName.contains('AGE_'+agencyCode));
        System.assert(gmName.contains('R_AGE_'+agencyCode));

    }

    @isTest
    static void test_SOC1_SOC4_and_agency(){

        Test.startTest();

        /*
        * FiscalCode 
        * Name = SystemTest1
        * LastName = Responsabile1
        */
        String resp1FiscalCode = '****************';

        /*
        * Agency Code = TEST99999
        */
        String agencyCode = 'TEST99999';

        List<Account> accountToInsert = new List<Account>();

        /*
        * Insert Agency Account 01853
        */
        Account agency = new Account();
        agency.Name = 'SystemTest '+agencyCode;
        agency.ExternalId__c = 'AGE_'+agencyCode;
        agency.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId();
        accountToInsert.add(agency);

        /*
        * Insert Society Account SOC_1 (Unipol)
        */
        Account soc1 = new Account();
        soc1.Name = 'Unipol';
        soc1.ExternalId__c = 'SOC_1';
        soc1.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId();
        accountToInsert.add(soc1);

        /*
        * Insert Society Account SOC_4 (Unisalute)
        */
        Account soc4 = new Account();
        soc4.Name = 'Unipol';
        soc4.ExternalId__c = 'SOC_4';
        soc4.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId();
        accountToInsert.add(soc4);

        insert accountToInsert;

        /*
        * Insert Responsabile User for agency 01853
        */
        Profile userProfile = [SELECT Id FROM Profile WHERE Name = 'Unipol Responsabile User' LIMIT 1];
        User testUser = new User(
            FirstName = 'Responsabile1',
            LastName = 'SystemTest1',
            Email = '<EMAIL>',
            Username = '<EMAIL>',
            Alias = 'resyt',
            ProfileId = userProfile.Id,
            TimeZoneSidKey = 'GMT',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            IdAzienda__c = agency.Id,
            FiscalCode__c = resp1FiscalCode,
            FederationIdentifier = resp1FiscalCode,
            ExternalId__c = resp1FiscalCode
        );
        insert testUser;

        List<NetworkUser__c> nuToInsert = new List<NetworkUser__c>();
        /*
        * Create NetworkUser record related to the User Fiscal Code
        * It represents the Unipol RACF User
        */
        NetworkUser__c nu = new NetworkUser__c();
        nu.IsActive__c = true;
        nu.Agency__c = agency.Id;
        nu.FiscalCode__c = resp1FiscalCode;
        nu.ExternalId__c = '1'+agencyCode+'XX';
        nu.NetworkUser__c = '1'+agencyCode+'XX';
        nu.Profile__c = 'A';
        nu.Society__c = 'SOC_1';
        nuToInsert.add(nu);

        NetworkUser__c nu2 = new NetworkUser__c();
        nu2.IsActive__c = true;
        nu2.Agency__c = agency.Id;
        nu2.FiscalCode__c = resp1FiscalCode;
        nu2.ExternalId__c = '4'+agencyCode+'XX';
        nu2.NetworkUser__c = '4'+agencyCode+'XX';
        nu2.Profile__c = 'A';
        nu2.Society__c = 'SOC_4';
        nuToInsert.add(nu2);

        insert nuToInsert;

        /*
        * Insert Groups
        */
        List<Group> groupToInsert = new List<Group>();

        Group groupAgents = new Group();
        groupAgents.Name = 'AGE_'+agencyCode;
        groupAgents.DeveloperName = 'AGE_'+agencyCode;
        groupToInsert.add(groupAgents);

        Group groupRes = new Group();
        groupRes.Name = 'R_AGE_'+agencyCode;
        groupRes.DeveloperName = 'R_AGE_'+agencyCode;
        groupToInsert.add(groupRes);

        insert groupToInsert;

        List<PermissionSet> existingPS = [SELECT Id, Name FROM PermissionSet];
        Set<String> existingPSSet = new Set<String>();
        for(PermissionSet ps : existingPS){
            existingPSSet.add(ps.Name);
        }

        List<PermissionSetAssignment> psaBefore = [SELECT Id, PermissionSet.Name FROM PermissionSetAssignment WHERE AssigneeId = :testUser.Id];
        Set<String> permissionSetName = new Set<String>();
        for(PermissionSetAssignment ps : psaBefore) {
            permissionSetName.add(ps.PermissionSet.Name);
        }

        List<GroupMember> gmBefore = [SELECT Id, Group.DeveloperName FROM GroupMember WHERE UserOrGroupId = :testUser.Id];
        Set<String> gmName = new Set<String>();
        for(GroupMember gm : gmBefore){
            gmName.add(gm.Group.DeveloperName);
        }

        // Permission Set not assigned to the user
        System.assert(!permissionSetName.contains('MandatoUniSalute'));
        System.assert(!permissionSetName.contains('MandatoUnipolSai'));
        System.assert(!gmName.contains('AGE_'+agencyCode));
        System.assert(!gmName.contains('R_AGE_'+agencyCode));
        

        Database.executeBatch(new BatchProcessServiceResourcesV2());

        Test.stopTest();

        List<PermissionSetAssignment> psaAfter = [SELECT Id, PermissionSet.Name FROM PermissionSetAssignment WHERE AssigneeId = :testUser.Id];
        permissionSetName = new Set<String>();
        for(PermissionSetAssignment ps : psaAfter) {
            permissionSetName.add(ps.PermissionSet.Name);
        }

        List<GroupMember> gmAfter = [SELECT Id, Group.DeveloperName FROM GroupMember WHERE UserOrGroupId = :testUser.Id];
        gmName = new Set<String>();
        for(GroupMember gm : gmAfter){
            gmName.add(gm.Group.DeveloperName);
        }

        // Permission Set assigned to the user
        if(existingPSSet.contains('MandatoUniSalute')) System.assert(permissionSetName.contains('MandatoUniSalute'));
        if(existingPSSet.contains('MandatoUnipolSai')) System.assert(permissionSetName.contains('MandatoUnipolSai'));
        System.assert(gmName.contains('AGE_'+agencyCode));
        System.assert(gmName.contains('R_AGE_'+agencyCode));

    }
    
    @isTest
    static void test_JITHandler_default(){

        /*
        * FiscalCode 
        * Name = SystemTest1
        * LastName = Responsabile1
        */
        String resp1FiscalCode = '****************';

        /*
        * Agency Code = TEST99999
        */
        String agencyCode = 'TEST99999';

        /*
        * Society
        */
        String socName = 'SOC_1';

        List<Account> accountToInsert = new List<Account>();

        /*
        * Insert Agency Account 01853
        */
        Account agency = new Account();
        agency.Name = 'SystemTest '+agencyCode;
        agency.ExternalId__c = 'AGE_'+agencyCode;
        agency.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId();
        accountToInsert.add(agency);

        /*
        * Insert Society Account SOC_1 (Unipol)
        */
        Account soc1 = new Account();
        soc1.Name = 'Unipol';
        soc1.ExternalId__c = socName;
        soc1.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId();
        accountToInsert.add(soc1);

        insert accountToInsert;

        /*
        * Insert Responsabile User for agency 01853
        */
        Profile userProfile = [SELECT Id FROM Profile WHERE Name = 'Unipol Responsabile User' LIMIT 1];
        User testUser = new User(
            FirstName = 'Responsabile1',
            LastName = 'SystemTest1',
            Email = '<EMAIL>',
            Username = '<EMAIL>',
            Alias = 'resyt',
            ProfileId = userProfile.Id,
            TimeZoneSidKey = 'GMT',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            IdAzienda__c = agency.Id,
            FiscalCode__c = resp1FiscalCode,
            FederationIdentifier = resp1FiscalCode,
            ExternalId__c = resp1FiscalCode
        );
        insert testUser;

        /*
        * Create NetworkUser record related to the User Fiscal Code
        * It represents the Unipol RACF User
        */
        NetworkUser__c nu = new NetworkUser__c();
        nu.IsActive__c = true;
        nu.Agency__c = agency.Id;
        nu.FiscalCode__c = resp1FiscalCode;
        nu.ExternalId__c = '1'+agencyCode+'XX';
        nu.NetworkUser__c = '1'+agencyCode+'XX';
        nu.Profile__c = 'A';
        nu.Society__c = socName;
        insert nu;

        /*
        * Insert Groups
        */
        List<Group> groupToInsert = new List<Group>();

        Group groupAgents = new Group();
        groupAgents.Name = 'AGE_'+agencyCode;
        groupAgents.DeveloperName = 'AGE_'+agencyCode;
        groupToInsert.add(groupAgents);

        Group groupRes = new Group();
        groupRes.Name = 'R_AGE_'+agencyCode;
        groupRes.DeveloperName = 'R_AGE_'+agencyCode;
        groupToInsert.add(groupRes);

        insert groupToInsert;
        
        List<PermissionSet> existingPS = [SELECT Id, Name FROM PermissionSet];
        Set<String> existingPSSet = new Set<String>();
        for(PermissionSet ps : existingPS){
            existingPSSet.add(ps.Name);
        }
        
		List<UCA_Mapping__mdt> getAllUCAMapping = UCA_Mapping__mdt.getAll().values();
        Set<String> defaultUCAMapping = new Set<String>();
        for(UCA_Mapping__mdt uca : getAllUCAMapping){
            if(uca.Default_Assignment__c) defaultUCAMapping.addAll(uca.Permission_Set_Name__c.split(','));
        }

        List<PermissionSetAssignment> psaBefore = [SELECT Id, PermissionSet.Name FROM PermissionSetAssignment WHERE AssigneeId = :testUser.Id];
        Set<String> permissionSetName = new Set<String>();
        for(PermissionSetAssignment ps : psaBefore) {
            permissionSetName.add(ps.PermissionSet.Name);
        }
	
        for(String str : defaultUCAMapping){
            System.assert(!permissionSetName.contains(str));
        }
        
        Test.startTest();
        
        User currentUser = [SELECT Id, FederationIdentifier FROM User WHERE Id = :testUser.Id LIMIT 1];
        System.runAs(currentUser){
         	Map<String, String> attributes = new Map<String, String>();
            attributes.put('CF',resp1FiscalCode);
            attributes.put('sub','1'+agencyCode+'XX');
            UserProvisioningUpdate.handleUser(currentUser, attributes);   
        }
        
        Test.stopTest();

        List<PermissionSetAssignment> psaAfter = [SELECT Id, PermissionSet.Name FROM PermissionSetAssignment WHERE AssigneeId = :currentUser.Id];
        permissionSetName = new Set<String>();
        for(PermissionSetAssignment ps : psaAfter) {
            permissionSetName.add(ps.PermissionSet.Name);
        }

        for(String str : defaultUCAMapping){
            if(existingPSSet.contains(str)){
				System.assert(permissionSetName.contains(str));   
            }
        }
        
    }
    
    @isTest
    static void test_JITHandler_UCA(){

        /*
        * FiscalCode 
        * Name = SystemTest1
        * LastName = Responsabile1
        */
        String resp1FiscalCode = '****************';

        /*
        * Agency Code = TEST99999
        */
        String agencyCode = 'TEST99999';

        /*
        * Society
        */
        String socName = 'SOC_1';

        List<Account> accountToInsert = new List<Account>();

        /*
        * Insert Agency Account 01853
        */
        Account agency = new Account();
        agency.Name = 'SystemTest '+agencyCode;
        agency.ExternalId__c = 'AGE_'+agencyCode;
        agency.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId();
        accountToInsert.add(agency);

        /*
        * Insert Society Account SOC_1 (Unipol)
        */
        Account soc1 = new Account();
        soc1.Name = 'Unipol';
        soc1.ExternalId__c = socName;
        soc1.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId();
        accountToInsert.add(soc1);

        insert accountToInsert;

        /*
        * Insert Responsabile User for agency 01853
        */
        Profile userProfile = [SELECT Id FROM Profile WHERE Name = 'Unipol Responsabile User' LIMIT 1];
        User testUser = new User(
            FirstName = 'Responsabile1',
            LastName = 'SystemTest1',
            Email = '<EMAIL>',
            Username = '<EMAIL>',
            Alias = 'resyt',
            ProfileId = userProfile.Id,
            TimeZoneSidKey = 'GMT',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            IdAzienda__c = agency.Id,
            FiscalCode__c = resp1FiscalCode,
            FederationIdentifier = resp1FiscalCode,
            ExternalId__c = resp1FiscalCode
        );
        insert testUser;

        /*
        * Create NetworkUser record related to the User Fiscal Code
        * It represents the Unipol RACF User
        */
        NetworkUser__c nu = new NetworkUser__c();
        nu.IsActive__c = true;
        nu.Agency__c = agency.Id;
        nu.FiscalCode__c = resp1FiscalCode;
        nu.ExternalId__c = '1'+agencyCode+'XX';
        nu.NetworkUser__c = '1'+agencyCode+'XX';
        nu.Profile__c = 'A';
        nu.Society__c = socName;
        insert nu;

        /*
        * Insert Groups
        */
        List<Group> groupToInsert = new List<Group>();

        Group groupAgents = new Group();
        groupAgents.Name = 'AGE_'+agencyCode;
        groupAgents.DeveloperName = 'AGE_'+agencyCode;
        groupToInsert.add(groupAgents);

        Group groupRes = new Group();
        groupRes.Name = 'R_AGE_'+agencyCode;
        groupRes.DeveloperName = 'R_AGE_'+agencyCode;
        groupToInsert.add(groupRes);

        insert groupToInsert;
        
        List<PermissionSet> existingPS = [SELECT Id, Name FROM PermissionSet];
        Set<String> existingPSSet = new Set<String>();
        for(PermissionSet ps : existingPS){
            existingPSSet.add(ps.Name);
        }
        
		List<UCA_Mapping__mdt> getAllUCAMapping = UCA_Mapping__mdt.getAll().values();
        Set<String> allUCAMapping = new Set<String>();
        for(UCA_Mapping__mdt uca : getAllUCAMapping){
            allUCAMapping.addAll(uca.Permission_Set_Name__c.split(','));
        }

        List<PermissionSetAssignment> psaBefore = [SELECT Id, PermissionSet.Name FROM PermissionSetAssignment WHERE AssigneeId = :testUser.Id];
        Set<String> permissionSetName = new Set<String>();
        for(PermissionSetAssignment ps : psaBefore) {
            permissionSetName.add(ps.PermissionSet.Name);
        }
	
        for(String str : allUCAMapping){
            System.assert(!permissionSetName.contains(str));
        }
        
        Test.startTest();
        
        User currentUser = [SELECT Id, FederationIdentifier FROM User WHERE Id = :testUser.Id LIMIT 1];
        System.runAs(currentUser){
         	Map<String, String> attributes = new Map<String, String>();
            attributes.put('CF',resp1FiscalCode);
            attributes.put('sub','1'+agencyCode+'XX');
            UserProvisioningUpdate.handleUser(currentUser, attributes);   
        }
        
        Test.stopTest();

        List<PermissionSetAssignment> psaAfter = [SELECT Id, PermissionSet.Name FROM PermissionSetAssignment WHERE AssigneeId = :testUser.Id];
        permissionSetName = new Set<String>();
        for(PermissionSetAssignment ps : psaAfter) {
            permissionSetName.add(ps.PermissionSet.Name);
        }

        for(String str : allUCAMapping){
            if(existingPSSet.contains(str)){
				System.assert(permissionSetName.contains(str));   
            }
        }
        
    }

}