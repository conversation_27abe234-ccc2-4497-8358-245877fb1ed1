@isTest
private class ServiceTerritoryTriggerHandlerTest {
    @testSetup
    static void setup() {
        Account acc = new Account(Name = 'Agency ST', ExternalId__c = 'ST123');
        insert acc;

        Group g = new Group(Name = 'ST Group', DeveloperName = 'ST123', Type = 'Regular');
        insert g;
    }

    @isTest
    static void testOnAfterInsert() {
        Account acc = [SELECT Id FROM Account WHERE ExternalId__c = 'ST123' LIMIT 1];

        OperatingHours oh = new OperatingHours(Name = 'Test');
        insert oh;

        ServiceTerritory st = new ServiceTerritory(Name = 'ST Insert', Agency__c = acc.Id, OperatingHoursId = oh.Id);
        insert st;

        // Simulate trigger handler call
        Test.startTest();
        ServiceTerritoryTriggerHandler handler = new ServiceTerritoryTriggerHandler();
        handler.onAfterInsert(new List<ServiceTerritory>{st});
        Test.stopTest();

        ServiceTerritoryShare share = [
            SELECT Id FROM ServiceTerritoryShare 
            WHERE ParentId = :st.Id 
            LIMIT 1
        ];
        System.assertNotEquals(null, share);
    }

    @isTest
    static void testOnAfterUpdate() {
        Account acc = [SELECT Id FROM Account WHERE ExternalId__c = 'ST123' LIMIT 1];

        OperatingHours oh = new OperatingHours(Name = 'Test');
        insert oh;

        ServiceTerritory st = new ServiceTerritory(Name = 'ST Update', Agency__c = acc.Id, OperatingHoursId = oh.Id);
        insert st;

        st.Name = 'ST Updated';
        update st;

        // Simulate trigger handler update
        Test.startTest();
        ServiceTerritoryTriggerHandler handler = new ServiceTerritoryTriggerHandler();
        handler.onAfterUpdate(
            new List<ServiceTerritory>{ st },
            new List<ServiceTerritory>{ st.clone(false) },
            new Map<Id, ServiceTerritory>{ st.Id => st },
            new Map<Id, ServiceTerritory>{ st.Id => st.clone(false) }
        );
        Test.stopTest();

        ServiceTerritoryShare share = [
            SELECT Id FROM ServiceTerritoryShare 
            WHERE ParentId = :st.Id 
            LIMIT 1
        ];
        System.assertNotEquals(null, share);
    }
}