@isTest
public class CaseHandlerBatchTest {
    @testSetup
    static void setup() {
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;
        String recordTypeId = Schema.SObjectType.Case.getRecordTypeInfosByName().get('AttivitaContatto').getRecordTypeId();

        List<Case> cases = new List<Case>();
        for (Integer i = 0; i < 10; i++) {
            Case c = new Case(Status = (i < 5) ? 'Open' : 'Expired', DueDate__c = Date.today().addDays(-i * 10), AccountId = testAccount.Id, Area__c = 'Anagrafica e Documenti', Detail__c = 'Certificazione numero cellulare Unibox', Activity__c = 'Gestione Anagrafiche', RecordTypeId = recordTypeId);
            cases.add(c);
        }

        Case cEx = new Case(Status = 'Expired', DueDate__c = Date.today().addDays(-10), ClosedDate__c = Date.today(), AccountId = testAccount.Id, Area__c = 'Anagrafica e Documenti', Detail__c = 'Certificazione numero cellulare Unibox', Activity__c = 'Gestione Anagrafiche', RecordTypeId = recordTypeId);
        cases.add(cEx);
        insert cases;
    }

    @isTest
    static void testCaseHandlerBatch() {
        Test.startTest();
        CaseHandlerBatch batch = new CaseHandlerBatch();
        Database.executeBatch(batch);
        Test.stopTest();

        List<Case> updatedCases = [SELECT Id, Status, DueDate__c, ClosedDate__c FROM Case WHERE AccountId != NULL];
        for (Case c : updatedCases) {
            system.debug('%%c: ' + c);
            if (c.DueDate__c == Date.today()) {
                System.assertEquals('Expired', c.Status);
            } else if (c.ClosedDate__c == Date.today()) {
                System.assertEquals('Closed', c.Status);
            }
        }
    }

    @isTest
    static void testCaseHandlerScheduler() {
        Test.startTest();

        String cronExpression = '0 0 12 * * ?';
        CaseHandlerScheduler scheduler = new CaseHandlerScheduler();
        String jobId = System.schedule('Test CaseHandlerScheduler Job', cronExpression, scheduler);

        Test.stopTest();

        CronTrigger ct = [SELECT Id, CronExpression, TimesTriggered, NextFireTime FROM CronTrigger WHERE Id = :jobId];
        System.assertEquals(cronExpression, ct.CronExpression);
        System.assertEquals(0, ct.TimesTriggered);
    }

    @isTest
    static void testCaseHandlerBatch2() {
        List<Case> listCase = [SELECT Id FROM Case];
        Test.startTest();
        ConiAssignmentHelper.checkPermissionCRUD();
        Case c = listCase.get(0);
        c.Status = 'Closed';
        update c;
        Test.stopTest();
    }
}
