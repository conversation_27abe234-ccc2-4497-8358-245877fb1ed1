@isTest
public with sharing class STProductTabsetControllerTest
{
    
    @TestSetup
    static void makeData()
    {
        RecordType omniRT = [SELECT Id FROM RecordType WHERE SobjectType = 'Opportunity' AND DeveloperName = 'Omnicanale' LIMIT 1];
        RecordType productRT = [SELECT Id FROM RecordType WHERE SobjectType = 'Opportunity' AND DeveloperName = 'Prodotto' LIMIT 1];

        Opportunity cont = new Opportunity
        (
            Name = 'Test',
            StageName = 'Assegnato',
            CloseDate = Date.today(),
            RecordTypeId = omniRT.Id
        );

        insert cont;

        Opportunity prod = new Opportunity
        (
            Name = 'Test',
            StageName = 'Assegnato',
            CloseDate = Date.today(),
            RecordTypeId = productRT.Id,
            DomainType__c = 'PU',
            AssignmentCounter__c = 1,
            EngagementPoint__c = 'Canale digitale',
            Parent__c = cont.Id
        );

        insert prod;
    }

    @isTest
    private static void testPositive()
    {
        RecordType omniRT = [SELECT Id FROM RecordType WHERE SobjectType = 'Opportunity' AND DeveloperName = 'Omnicanale' LIMIT 1];
        Opportunity opp = [SELECT Id FROM Opportunity WHERE RecordTypeId =: omniRT.Id LIMIT 1];

        Test.startTest();
        List<STProductTabsetController.ProductContainer> result = STProductTabsetController.retrieveProducts(opp.Id);
        Test.stopTest();

        System.assertEquals(1, result.size());
    }
}