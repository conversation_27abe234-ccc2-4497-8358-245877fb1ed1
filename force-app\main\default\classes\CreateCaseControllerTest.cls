@isTest
public with sharing class CreateCaseControllerTest {

    @testSetup
    static void setup() {
        List<CaseActivityInit__c> testRecords = new List<CaseActivityInit__c>();

        testRecords.add(new CaseActivityInit__c(
            Area__c = 'Anagrafica e Documenti',
            Activity__c = 'Recupero Documenti',
            Detail__c = 'Attestato di rischio',
            OpenManualAutomatic__c = 'Apertura MANUAL'
        ));

        testRecords.add(new CaseActivityInit__c(
            Area__c = 'Anagrafica e Documenti',
            Activity__c = 'Recupero Documenti',
            Detail__c = 'Altri documenti',
            OpenManualAutomatic__c = 'Apertura MIX'
        ));

        testRecords.add(new CaseActivityInit__c(
            Area__c = 'Portafoglio Danni',
            Activity__c = 'Gestione Rilievi',
            Detail__c = 'Cilindrata motocicli',
            OpenManualAutomatic__c = 'Apertura MANUAL'
        ));

        insert testRecords;
    }

    @isTest
    static void testGetDependentPicklistValues() {
        Test.startTest();
        CreateCaseController.DependentPicklistData result = CreateCaseController.getDependentPicklistValues();
        Test.stopTest();

        System.assertNotEquals(null, result);
        System.assert(result.areas.contains('Anagrafica e Documenti'), 'Anagrafica e Documenti should be present');
        System.assert(result.areas.contains('Portafoglio Danni'), 'Portafoglio Danni should be present');

        System.assert(result.areaToActivities.containsKey('Anagrafica e Documenti'), 'Anagrafica e Documenti should have activities');
        System.assert(result.areaToActivities.get('Anagrafica e Documenti').contains('Recupero Documenti'), 'Recupero Documenti should be under Anagrafica e Documenti');

        System.assert(result.activityToDetails.containsKey('Recupero Documenti'), 'Recupero Documenti should have details');
        System.assert(result.activityToDetails.get('Recupero Documenti').contains('Attestato di rischio'), 'Attestato di rischio should be under Recupero Documenti');
        System.assert(result.activityToDetails.get('Recupero Documenti').contains('Altri documenti'), 'Altri documenti should be under Recupero Documenti');
    }
}