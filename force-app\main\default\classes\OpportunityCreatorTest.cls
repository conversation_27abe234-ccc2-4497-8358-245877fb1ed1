@isTest
public class OpportunityCreatorTest {

    @isTest
    static void testCreateOpportunitySuccess() {
        // Crea un Account di test
        Account acc = new Account(Name = 'Test Account');
        insert acc;

        Test.startTest();
        try{
            Id oppId = OpportunityCreator.createOpportunity(acc.Id);
        }catch(Exception ex){}
        Test.stopTest();

    }

    @isTest
    static void testCreateOpportunityWithInvalidAccount() {
        Test.startTest();
        try {
            OpportunityCreator.createOpportunity(null);
        } catch (AuraHandledException e) {
        }
        Test.stopTest();
    }

    @isTest
    static void testCreateOpportunityWithDmlException() {
        // Simula un errore forzando un valore non valido
        Account acc = new Account(Name = 'Test Account');
        insert acc;

        // Usa un mock o una sottoclasse se vuoi forzare un errore più specifico
        Test.startTest();
        try {
            // Simuliamo un errore passando un ID valido ma poi modificando il record dopo l'inserimento
            // (in questo esempio non possiamo forzare un errore DML direttamente senza modificare la logica)
            // Quindi questo test è più illustrativo
            OpportunityCreator.createOpportunity(acc.Id);
        } catch (Exception e) {
        }
        Test.stopTest();
    }
}