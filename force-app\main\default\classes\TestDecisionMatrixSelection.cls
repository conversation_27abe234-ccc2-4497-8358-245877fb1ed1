public with sharing class TestDecisionMatrixSelection {
    
    public static void run(String codiceClasse, String codiceUsoSpeciale, Integer sommaQuintali) {
        List<CalculationMatrixRow> matrixRows = [
            SELECT Id, InputData, OutputData 
            FROM CalculationMatrixRow 
            WHERE CalculationMatrixVersion.CalculationMatrix.UniqueName = 'VR_VehicleType'
        ];
        System.debug('# matrixRows: ' + JSON.serialize(matrixRows));

        for(CalculationMatrixRow row : matrixRows) {
            Map<String, Object> meritocraticData;
            meritocraticData = (Map<String, Object>) JSON.deserializeUntyped(row.InputData);
            System.debug('# meritocraticData: ' + meritocraticData);

            String rowCodiceClasse = (String) meritocraticData.get('CodiceClasse');
            if(rowCodiceClasse != 'NA' && (rowCodiceClasse != codiceClasse)) {
                continue;
            }
            
            String rowCodiceUsoSpeciale = (String) meritocraticData.get('CodiceUsoSpeciale');
            if(rowCodiceUsoSpeciale != 'NA' && (rowCodiceUsoSpeciale != codiceUsoSpeciale)) {
                continue;
            }

            Integer rowSommaQuintali = (Integer) meritocraticData.get('SommaQuintali');
            if(rowSommaQuintali != null && (rowSommaQuintali != sommaQuintali)) {
                continue;
            }
            System.debug('# Selected: ' + meritocraticData);
            break;
        }
    }
}

// CAN WE LEAVE NUMBERS EMPTY?