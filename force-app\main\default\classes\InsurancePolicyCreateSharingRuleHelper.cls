/**
 * @description Classe helper per la gestione delle logiche di recupero, creazione e inserimento delle regole di sharing
 *              su Case, Opportunity, FinServ__AccountAccountRelation__c, AccountShare e AccountDetailsNPI__Share.
 *              Contiene metodi di supporto per la classe principale InsurancePolicyCreateSharingRule.
 * <AUTHOR>
 * @since       18.06.2025
 */
public without sharing class InsurancePolicyCreateSharingRuleHelper {
    private static Set<String> setIdAARSociety = new Set<String>();
    /**
     * @description     Recupera tutti i Case associati agli Account e Agency specificati.
     * @param idAccount Set di Id degli Account.
     * @param idAgency  Set di Id delle Agency.
     * @return          `Map<Id, List<Case>>` -> Key: Id dell'Account, Value: Lista di Case associati.
     */
    public static Map<Id, List<Case>> getCases(Set<String> idAccount, Set<String> idAgency) {
        if (Schema.sObjectType.Case.isAccessible()) {
            Map<Id, List<Case>> mapCases = new Map<Id, List<Case>>();
            for (Case c : [SELECT Id, AccountId FROM Case WHERE AccountId IN :idAccount AND Agency__c IN :idAgency]) {
                if (mapCases.containsKey(c.AccountId)) {
                    mapCases.get(c.AccountId).add(c);
                } else {
                    mapCases.put(c.AccountId, new List<Case>{ c });
                }
            }
            return mapCases;
        }
        return new Map<Id, List<Case>>();
    }

    /**
     * @description     Recupera tutte le Opportunity associate agli Account e Agency specificati.
     * @param idAccount Set di Id degli Account.
     * @param idAgency  Set di Id delle Agency.
     * @return          `Map<Id, List<Opportunity>>` -> Key: Id dell'Account, Value: Lista di Opportunity associate.
     */
    public static Map<Id, List<Opportunity>> getOpportunities(Set<String> idAccount, Set<String> idAgency) {
        if (Schema.sObjectType.Opportunity.isAccessible()) {
            Map<Id, List<Opportunity>> mapOpportunities = new Map<Id, List<Opportunity>>();
            for (Opportunity opp : [SELECT Id, AccountId FROM Opportunity WHERE AccountId IN :idAccount AND Agency__c IN :idAgency]) {
                if (mapOpportunities.containsKey(opp.AccountId)) {
                    mapOpportunities.get(opp.AccountId).add(opp);
                } else {
                    mapOpportunities.put(opp.AccountId, new List<Opportunity>{ opp });
                }
            }
            return mapOpportunities;
        }
        return new Map<Id, List<Opportunity>>();
    }

    /**
     * @description     Recupera tutte le relazioni FinServ__AccountAccountRelation__c associate agli Account, Agency e Society specificati.
     * @param idAccount Set di Id degli Account.
     * @param idAgency  Set di Id delle Agency.
     * @param idSociety Set di Id delle Society.
     * @return          `Map<Id, List<FinServ__AccountAccountRelation__c>>` -> Key: Id dell'Account, Value: Lista di relazioni associate.
     */
    public static Map<Id, List<FinServ__AccountAccountRelation__c>> getAARs(Set<String> idAccount, Set<String> idAgency, Set<String> idSociety) {
        if (Schema.sObjectType.FinServ__AccountAccountRelation__c.isAccessible()) {
            Map<Id, List<FinServ__AccountAccountRelation__c>> mapAARs = new Map<Id, List<FinServ__AccountAccountRelation__c>>();
            for (FinServ__AccountAccountRelation__c aar : [
                SELECT Id, RecordTypeId, FinServ__Account__c, FinServ__RelatedAccount__r.RecordType.DeveloperName
                FROM FinServ__AccountAccountRelation__c
                WHERE FinServ__Account__c IN :idAccount AND (FinServ__RelatedAccount__c IN :idAgency OR FinServ__RelatedAccount__c IN :idSociety)
            ]) {
                if (mapAARs.containsKey(aar.FinServ__Account__c)) {
                    mapAARs.get(aar.FinServ__Account__c).add(aar);
                } else {
                    mapAARs.put(aar.FinServ__Account__c, new List<FinServ__AccountAccountRelation__c>{ aar });
                }
            }
            return mapAARs;
        }
        return new Map<Id, List<FinServ__AccountAccountRelation__c>>();
    }

    /**
     * @description Costruisce la lista di tutti i record di sharing da inserire, in base agli input e ai dati recuperati.
     * @param wrp   Wrapper contenente input e liste di record.
     * @return      `List<SObject>` -> Lista di SObject da inserire.
     */
    public static List<SObject> buildAllShares(InsurancePolicyCreateSharingRule.WrapperShare wrp) {
        List<SObject> listRecordShareToInsert = new List<SObject>();
        for (InsurancePolicyCreateSharingRule.FlowInputs input : wrp.flowInput) {
            listRecordShareToInsert.addAll(createShareForCase(wrp.listCase.get(input.accountId), input.groupId));
            listRecordShareToInsert.addAll(createShareForOpportunity(wrp.listOpportunity.get(input.accountId), input.groupId));
            listRecordShareToInsert.addAll(createShareForAccountAccountRelation(wrp.listAccAccRel.get(input.accountId), input.groupId));
            listRecordShareToInsert.addAll(createShareForAccountDetailsNonPublicInformation(setIdAARSociety, input.groupId));
        }
        return listRecordShareToInsert;
    }

    /**
     * @description    Crea i record di sharing per i Case passati in input.
     * @param listCase Lista di Case.
     * @param groupId  Id del gruppo destinatario della condivisione.
     * @return         `List<SObject>` -> Lista di CaseShare come SObject.
     */
    private static List<SObject> createShareForCase(List<Case> listCase, Id groupId) {
        List<CaseShare> listCaseShare = new List<CaseShare>();
        if (listCase != null) {
            for (Case c : listCase) {
                CaseShare caseShare = new CaseShare();
                caseShare.CaseId = c.Id;
                caseShare.UserOrGroupId = groupId;
                caseShare.CaseAccessLevel = 'Read';
                listCaseShare.add(caseShare);
            }
        }
        return (List<SObject>) listCaseShare;
    }

    /**
     * @description           Crea i record di sharing per le Opportunity passate in input.
     * @param listOpportunity Lista di Opportunity.
     * @param groupId         Id del gruppo destinatario della condivisione.
     * @return                `List<SObject>` -> Lista di OpportunityShare come SObject.
     */
    private static List<SObject> createShareForOpportunity(List<Opportunity> listOpportunity, Id groupId) {
        List<OpportunityShare> listOpportunityShare = new List<OpportunityShare>();
        if (listOpportunity != null) {
            for (Opportunity opp : listOpportunity) {
                OpportunityShare opportunityShare = new OpportunityShare();
                opportunityShare.OpportunityId = opp.Id;
                opportunityShare.UserOrGroupId = groupId;
                opportunityShare.OpportunityAccessLevel = 'Read';
                listOpportunityShare.add(opportunityShare);
            }
        }
        return (List<SObject>) listOpportunityShare;
    }

    /**
     * @description         Crea i record di sharing per le relazioni FinServ__AccountAccountRelation__c passate in input.
     *                      Per ogni relazione:
     *                      - Crea sempre una condivisione su FinServ__AccountAccountRelation__Share.
     *                      - Se la relazione è di tipo 'AccountSociety', aggiunge anche la condivisione su AccountShare per l'Account collegato.
     *                      - Se la relazione è di tipo 'AgencySociety', aggiunge l'Id della relazione al set usato per la condivisione su AccountDetailsNPI__Share.
     * @param listAccAccRel Lista di FinServ__AccountAccountRelation__c.
     * @param groupId       Id del gruppo destinatario della condivisione.
     * @return              `List<SObject>` -> Lista di SObject contenente:
     *                          - FinServ__AccountAccountRelation__Share per tutte le relazioni in input
     *                          - AccountShare per gli Account collegati a relazioni di tipo 'AccountSociety'.
     *                          La condivisione su AccountDetailsNPI__Share viene gestita separatamente.
     */
    private static List<SObject> createShareForAccountAccountRelation(List<FinServ__AccountAccountRelation__c> listAccAccRel, Id groupId) {
        Id idAccountSociety = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AccountSociety').getRecordTypeId();
        Id idAgencySociety = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AgencySociety').getRecordTypeId();
        List<SObject> lstSObjects = new List<SObject>();
        Set<Id> setIdFinServAccount = new Set<Id>();
        if (listAccAccRel != null) {
            for (FinServ__AccountAccountRelation__c aar : listAccAccRel) {
                FinServ__AccountAccountRelation__Share accAccRelShare = new FinServ__AccountAccountRelation__Share();
                accAccRelShare.ParentId = aar.Id;
                accAccRelShare.UserOrGroupId = groupId;
                accAccRelShare.AccessLevel = 'Read';
                lstSObjects.add(accAccRelShare);
                if (String.valueOf(idAccountSociety).equalsIgnoreCase(aar.RecordTypeId)) {
                    setIdFinServAccount.add(aar.FinServ__Account__c);
                }
                if (String.valueOf(idAgencySociety).equalsIgnoreCase(aar.RecordTypeId)) {
                    setIdAARSociety.add(aar.Id);
                }
            }
        }
        lstSObjects.addAll(createShareForAccountShare(setIdFinServAccount, groupId));
        return lstSObjects;
    }

    /**
     * @description               Crea i record di sharing per AccountShare a partire da una lista di AccountId.
     * @param setIdFinServAccount Set di Id Account.
     * @param groupId             Id del gruppo destinatario della condivisione.
     * @return                    `List<SObject>` -> Lista di AccountShare come SObject.
     */
    private static List<SObject> createShareForAccountShare(Set<Id> setIdFinServAccount, Id groupId) {
        List<SObject> lstSObjects = new List<SObject>();
        for (Id idAcc : setIdFinServAccount) {
            AccountShare accShare = new AccountShare();
            accShare.AccountId = idAcc;
            accShare.UserOrGroupId = groupId;
            accShare.AccountAccessLevel = 'Read';
            accShare.OpportunityAccessLevel = 'None';
            lstSObjects.add(accShare);
        }
        return lstSObjects;
    }

    /**
     * @description           Crea i record di sharing per AccountDetailsNPI__c collegati alle relazioni di tipo Society.
     * @param setIdAARSociety Set di Id delle relazioni di tipo Society.
     * @param groupId         Id del gruppo destinatario della condivisione.
     * @return                `List<SObject>` -> Lista di AccountDetailsNPI__Share come SObject.
     */
    private static List<SObject> createShareForAccountDetailsNonPublicInformation(Set<String> setIdAARSociety, Id groupId) {
        List<AccountDetailsNPI__Share> lstAccDetNPIShares = new List<AccountDetailsNPI__Share>();
        if (!setIdAARSociety.isEmpty() && Schema.sObjectType.AccountDetails__c.isAccessible()) {
            List<AccountDetails__c> listAccDetails = [SELECT AccountDetailsNPI__c FROM AccountDetails__c WHERE Relation__c IN :setIdAARSociety AND AccountDetailsNPI__c != NULL];
            for (AccountDetails__c accDet : listAccDetails) {
                AccountDetailsNPI__Share accDetNPIShare = new AccountDetailsNPI__Share();
                accDetNPIShare.ParentId = accDet.AccountDetailsNPI__c;
                accDetNPIShare.UserOrGroupId = groupId;
                accDetNPIShare.AccessLevel = 'Read';
                lstAccDetNPIShares.add(accDetNPIShare);
            }
        }
        return (List<SObject>) lstAccDetNPIShares;
    }

    /**
     * @description                   Inserisce i record di sharing nei rispettivi oggetti Share (CaseShare, OpportunityShare, FinServ__AccountAccountRelation__Share, AccountShare, AccountDetailsNPI__Share).
     * @param listRecordShareToInsert Lista di SObject contenente i record di sharing da inserire.
     */
    public static void insertRecordShare(List<SObject> listRecordShareToInsert) {
        Map<Schema.SObjectType, List<SObject>> shareMap = new Map<Schema.SObjectType, List<SObject>>();

        for (SObject sObj : listRecordShareToInsert) {
            Schema.SObjectType sObjType = sObj.getSObjectType();
            if (!shareMap.containsKey(sObjType)) {
                shareMap.put(sObjType, new List<SObject>());
            }
            shareMap.get(sObjType).add(sObj);
        }

        insertShareIfCreateable(shareMap, Schema.SObjectType.CaseShare.getSObjectType());
        insertShareIfCreateable(shareMap, Schema.SObjectType.OpportunityShare.getSObjectType());
        insertShareIfCreateable(shareMap, Schema.SObjectType.FinServ__AccountAccountRelation__Share.getSObjectType());
        insertShareIfCreateable(shareMap, Schema.SObjectType.AccountShare.getSObjectType());
        insertShareIfCreateable(shareMap, Schema.SObjectType.AccountDetailsNPI__Share.getSObjectType());
    }

    /**
     * @description    Inserisce i record di sharing per uno specifico tipo di oggetto se presenti e se l'utente ha i permessi di creazione.
     * @param shareMap Mappa tra SObjectType e lista di SObject da inserire.
     * @param sObjType Tipo di oggetto da inserire.
     */
    private static void insertShareIfCreateable(Map<Schema.SObjectType, List<SObject>> shareMap, Schema.SObjectType sObjType) {
        if (shareMap.containsKey(sObjType) && !shareMap.get(sObjType).isEmpty() /* && sObjType.getDescribe().isCreateable()*/) {
            insert shareMap.get(sObjType);
        }
    }

    /**
     * @description     Costruisce la lista dei risultati di successo per ogni input processato.
     * @param flowInput Lista di input processati.
     * @return          `List<InsurancePolicyCreateSharingRule.Results>` -> Lista di risultati con esito positivo.
     */
    public static List<InsurancePolicyCreateSharingRule.Results> buildSuccessResults(List<InsurancePolicyCreateSharingRule.FlowInputs> flowInput) {
        List<InsurancePolicyCreateSharingRule.Results> listResult = new List<InsurancePolicyCreateSharingRule.Results>();
        for (InsurancePolicyCreateSharingRule.FlowInputs input : flowInput) {
            InsurancePolicyCreateSharingRule.Results result = new InsurancePolicyCreateSharingRule.Results();
            result.esito = 'OK';
            result.message = 'Share create con successo';
            listResult.add(result);
        }
        return listResult;
    }

    /**
     * @description      Costruisce la lista dei risultati di errore per ogni input processato.
     * @param message    Messaggio di errore.
     * @param flowInput  Lista di input processati.
     * @param stackTrace Stack trace dell'eccezione (se presente).
     * @return           `List<InsurancePolicyCreateSharingRule.Results>` -> Lista di risultati con esito negativo.
     */
    public static List<InsurancePolicyCreateSharingRule.Results> buildErrorResults(String message, List<InsurancePolicyCreateSharingRule.FlowInputs> flowInput, String stackTrace) {
        List<InsurancePolicyCreateSharingRule.Results> listResult = new List<InsurancePolicyCreateSharingRule.Results>();
        for (InsurancePolicyCreateSharingRule.FlowInputs input : flowInput) {
            InsurancePolicyCreateSharingRule.Results result = new InsurancePolicyCreateSharingRule.Results();
            result.esito = 'KO';
            result.message = message;
            result.stackTraceString = stackTrace;
            listResult.add(result);
        }
        return listResult;
    }
}