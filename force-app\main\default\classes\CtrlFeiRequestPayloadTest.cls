@isTest
public class CtrlFeiRequestPayloadTest {

    @isTest
    static void test_coverage(){

        Test.startTest();

        FEI_Environment__c fei = new FEI_Environment__c();
        fei.SetupOwnerId = UserInfo.getUserId();
        fei.Environment__c = 'EURO';
        
        insert fei;

        try{
            CtrlFeiRequestPayload.getPayload(UserInfo.getUserId());
        }catch(Exception ex){}

        Test.stopTest();

    }

} 