@isTest
public class defaultManagerGroupMatchingBatchTest {
    @isTest
    static void testBatchExecution() {
        // Create test data
        System.debug('Creating test Agency account...');
        Account agency = new Account(
            Name = 'Test Agency',
            RecordTypeId = [
                SELECT Id
                FROM RecordType
                WHERE DeveloperName = 'Agency' AND SObjectType = 'Account'
                LIMIT 1
            ].Id,
            ExternalId__c = 'AGE_12345'
        );
        insert agency;
        System.debug('Created Agency account: ' + JSON.serialize(agency));

        System.debug('Creating test Society account...');
        Account society = new Account(
            Name = 'Test Society',
            RecordTypeId = [
                SELECT Id
                FROM RecordType
                WHERE DeveloperName = 'Society' AND SObjectType = 'Account'
                LIMIT 1
            ].Id,
            ExternalId__c = 'SOC_12345'
        );
        insert society;
        System.debug('Created Society account: ' + JSON.serialize(society));

        System.debug('Creating Reciprocal Role...');
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(
            Name = 'Agenzia',
            FinServ__InverseRole__c = 'Compagnia'
        );
        insert role;
        System.debug('Created Reciprocal Role: ' + JSON.serialize(role));

        System.debug('Creating mandate between Agency and Society...');
        FinServ__AccountAccountRelation__c mandate = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = agency.Id,
            FinServ__RelatedAccount__c = society.Id,
            FinServ__Role__c = role.Id,
            RecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName()
                .get('AgencySociety')
                .getRecordTypeId()
        );
        insert mandate;
        System.debug('Created mandate: ' + JSON.serialize(mandate));

        System.debug('Creating test Client account...');
        Account client = new Account(
            Name = 'Test Client',
            RecordTypeId = [
                SELECT Id
                FROM RecordType
                WHERE DeveloperName = 'IndustriesBusiness' AND SObjectType = 'Account'
                LIMIT 1
            ].Id,
            ExternalId__c = 'CLI_12345'
        );
        insert client;
        System.debug('Created Client account: ' + JSON.serialize(client));

        System.debug('Creating AccountAgency relation between Client and Agency...');
        FinServ__AccountAccountRelation__c aarClientAgency = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = client.Id,
            FinServ__RelatedAccount__c = agency.Id,
            FinServ__Role__c = role.Id,
            RecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName()
                .get('AccountAgency')
                .getRecordTypeId()
        );
        insert aarClientAgency;
        System.debug('Created AccountAgency relation: ' + JSON.serialize(aarClientAgency));

        System.debug('Creating AccountSociety relation between Client and Society...');
        FinServ__AccountAccountRelation__c aarClientSociety = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = client.Id,
            FinServ__RelatedAccount__c = society.Id,
            FinServ__Role__c = role.Id,
            RecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName()
                .get('AccountSociety')
                .getRecordTypeId()
        );
        insert aarClientSociety;
        System.debug('Created AccountSociety relation: ' + JSON.serialize(aarClientSociety));

        System.debug('Creating AccountDetailsNPI record...');
        AccountDetailsNPI__c accountDetailsNPI = new AccountDetailsNPI__c(
            Name = 'Test Account Details NPI',
            ExternalId__c = 'NPI_12345',
            Society__c = society.Id
        );
        insert accountDetailsNPI;
        System.debug('Created AccountDetailsNPI record: ' + JSON.serialize(accountDetailsNPI));

        System.debug('Creating Group...');
        Group groupElement = new Group(
            Name = 'Test Group',
            DeveloperName = 'R_AGE_12345',
            Type = 'Regular'
        );
        insert groupElement;
        System.debug('Created Group: ' + JSON.serialize(groupElement));

        // Execute the batch
        Test.startTest();
        System.debug('Starting batch execution...');
        defaultManagerGroupMatchingBatch batch = new defaultManagerGroupMatchingBatch();
        Database.executeBatch(batch, 200);
        Test.stopTest();
        System.debug('Batch execution completed.');

        // Verify results
        System.debug('Verifying Apex Sharing records...');
        List<AccountDetailsNPI__Share> shares = [
            SELECT Id, ParentId, UserOrGroupId, AccessLevel
            FROM AccountDetailsNPI__Share 
            WHERE ParentId = :accountDetailsNPI.Id
            AND AccessLevel='Read'
        ];
        System.debug('Queried Apex Sharing records: ' + JSON.serialize(shares));
        System.assertEquals(1, shares.size(), 'Expected one sharing record to be created.');
        System.assertEquals('Read', shares[0].AccessLevel, 'Expected sharing access level to be Read.');
        System.debug('Apex Sharing records verified successfully.');
    }

    // @isTest
    // static void multiRecordTestBatchExecution() {
    //     // Reduce the number of test records
    //     Integer numAgencies = 5;
    //     Integer numSocieties = 5;
    //     Integer numClients = 50;

    //     // Pre-query RecordType IDs
    //     Id agencyRecordTypeId = [
    //         SELECT Id
    //         FROM RecordType
    //         WHERE DeveloperName = 'Agency' AND SObjectType = 'Account'
    //         LIMIT 1
    //     ].Id;

    //     Id societyRecordTypeId = [
    //         SELECT Id
    //         FROM RecordType
    //         WHERE DeveloperName = 'Society' AND SObjectType = 'Account'
    //         LIMIT 1
    //     ].Id;

    //     Id clientRecordTypeId = [
    //         SELECT Id
    //         FROM RecordType
    //         WHERE DeveloperName = 'IndustriesBusiness' AND SObjectType = 'Account'
    //         LIMIT 1
    //     ].Id;

    //     // Create test agencies
    //     System.debug('Creating test agencies...');
    //     List<Account> agencies = new List<Account>();
    //     for (Integer i = 1; i <= numAgencies; i++) {
    //         agencies.add(new Account(
    //             Name = 'Test Agency ' + i,
    //             RecordTypeId = agencyRecordTypeId,
    //             ExternalId__c = 'AGE_' + i
    //         ));
    //     }
    //     insert agencies;

    //     // Create test societies
    //     System.debug('Creating test societies...');
    //     List<Account> societies = new List<Account>();
    //     for (Integer i = 1; i <= numSocieties; i++) {
    //         societies.add(new Account(
    //             Name = 'Test Society ' + i,
    //             RecordTypeId = societyRecordTypeId,
    //             ExternalId__c = 'SOC_' + i
    //         ));
    //     }
    //     insert societies;

    //     // Create test clients
    //     System.debug('Creating test clients...');
    //     List<Account> clients = new List<Account>();
    //     for (Integer i = 1; i <= numClients; i++) {
    //         clients.add(new Account(
    //             Name = 'Test Client ' + i,
    //             RecordTypeId = clientRecordTypeId,
    //             ExternalId__c = 'CLI_' + i
    //         ));
    //     }
    //     insert clients;

    //     // Create Reciprocal Role
    //     System.debug('Creating Reciprocal Role...');
    //     FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(
    //         Name = 'Agenzia',
    //         FinServ__InverseRole__c = 'Compagnia'
    //     );
    //     insert role;

    //     // Create mandates between agencies and societies
    //     System.debug('Creating mandates between agencies and societies...');
    //     List<FinServ__AccountAccountRelation__c> mandates = new List<FinServ__AccountAccountRelation__c>();
    //     for (Integer i = 0; i < numAgencies; i++) {
    //         Integer maxMandates = (i == 0) ? numSocieties : 2; // First agency gets all societies, others get 2
    //         for (Integer j = 0; j < maxMandates; j++) {
    //             mandates.add(new FinServ__AccountAccountRelation__c(
    //                 FinServ__Account__c = agencies[i].Id,
    //                 FinServ__RelatedAccount__c = societies[j].Id,
    //                 FinServ__Role__c = role.Id,
    //                 RecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName()
    //                     .get('AgencySociety')
    //                     .getRecordTypeId()
    //             ));
    //         }
    //     }
    //     insert mandates;

    //     // Create AccountAgency relations between clients and agencies
    //     System.debug('Creating AccountAgency relations between clients and agencies...');
    //     List<FinServ__AccountAccountRelation__c> clientAgencyRelations = new List<FinServ__AccountAccountRelation__c>();
    //     for (Integer i = 0; i < numClients; i++) {
    //         clientAgencyRelations.add(new FinServ__AccountAccountRelation__c(
    //             FinServ__Account__c = clients[i].Id,
    //             FinServ__RelatedAccount__c =agencies[Math.Mod(i, numAgencies)].Id, //agencies[Math.Mod(i, numAgencies)].Id
    //             FinServ__Role__c = role.Id,
    //             RecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName()
    //                 .get('AccountAgency')
    //                 .getRecordTypeId()
    //         ));
    //     }
    //     insert clientAgencyRelations;

    //     // Create AccountSociety relations between clients and societies
    //     System.debug('Creating AccountSociety relations between clients and societies...');
    //     List<FinServ__AccountAccountRelation__c> clientSocietyRelations = new List<FinServ__AccountAccountRelation__c>();
    //     for (Integer i = 0; i < numClients; i++) {
    //         for (Integer j = 0; j < numSocieties; j++) {
    //             clientSocietyRelations.add(new FinServ__AccountAccountRelation__c(
    //                 FinServ__Account__c = clients[i].Id,
    //                 FinServ__RelatedAccount__c = societies[j].Id,
    //                 FinServ__Role__c = role.Id,
    //                 RecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName()
    //                     .get('AccountSociety')
    //                     .getRecordTypeId()
    //             ));
    //         }
    //     }
    //     insert clientSocietyRelations;

    //     // // Execute the batch
    //     // Test.startTest();
    //     // System.debug('Starting batch execution...');
    //     // defaultManagerGroupMatchingBatch batch = new defaultManagerGroupMatchingBatch();
    //     // Database.executeBatch(batch, 50); // Reduce batch size
    //     // Test.stopTest();

    //     // // Verify results
    //     // System.debug('Verifying Apex Sharing records...');
    //     // List<AccountDetailsNPI__Share> shares = [
    //     //     SELECT Id, ParentId, UserOrGroupId, AccessLevel
    //     //     FROM AccountDetailsNPI__Share
    //     //     WHERE AccessLevel = 'Read'
    //     // ];
    //     // System.debug('Queried Apex Sharing records: ' + JSON.serialize(shares));
    //     // System.assert(shares.size() > 0, 'Expected sharing records to be created.');
    //     // System.debug('Apex Sharing records verified successfully.');

    //     Test.startTest();
    //     System.debug('Starting batch execution...');
    //     defaultManagerGroupMatchingBatch batch = new defaultManagerGroupMatchingBatch();
    //     Id batchJobId = Database.executeBatch(batch, 50); // Riduci la dimensione del batch
    //     Test.stopTest();

    //     // Verifica lo stato del batch
    //     AsyncApexJob batchJob = [
    //         SELECT Id, Status, NumberOfErrors, JobItemsProcessed, TotalJobItems
    //         FROM AsyncApexJob
    //         WHERE Id = :batchJobId
    //     ];
    //     System.debug('Batch Status: ' + batchJob.Status);

    //     // Esegui la query di assert solo se il batch è completato
    //     if (batchJob.Status == 'Completed') {
    //         System.debug('Verifying Apex Sharing records...');
    //         List<AccountDetailsNPI__Share> shares = [
    //             SELECT Id, ParentId, UserOrGroupId, AccessLevel
    //             FROM AccountDetailsNPI__Share
    //             WHERE AccessLevel = 'Read'
    //         ];
    //         System.debug('Queried Apex Sharing records: ' + JSON.serialize(shares));
    //         System.assert(shares.size() > 0, 'Expected sharing records to be created.');
    //         System.debug('Apex Sharing records verified successfully.');
    //     } else {
    //         System.debug('Batch did not complete successfully. Status: ' + batchJob.Status);
    //     }


    // }

    
}