@IsTest
public with sharing class EngineAgencyRanking_Test {

    @TestSetup
    static void makeData() {
        FlowTriggersActivation__c triggerSettings = new FlowTriggersActivation__c();
        triggerSettings.SkipTriggers__c = true;
        insert triggerSettings;
        
        Account defaultAgency = new Account(
            Name = 'Default Agency',
            AccountNumber = 'AG-SOGEINT',
            ExternalId__c = '0000',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId(),
            BillingLatitude = 50.464664,
            BillingLongitude = 15.788540,
            Size__c = 'Media',
            Cluster__c = 'Cluster 2',
            OmnichannelAgreement__c = true,
            Type = 'Privata'
        );
        insert defaultAgency;

        Account defaultSalespoint = new Account(
            Name = 'Default SP',
            AccountNumber = 'SP-SOGEINT',
            ExternalId__c = '0001',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId(),
            BillingLatitude = 50.464664,
            BillingLongitude = 15.788540,
            Size__c = 'Media',
            Cluster__c = 'Cluster 2',
            Agency__c = defaultAgency.Id,
            IsLocatorEnabled__c = true
        );
        insert defaultSalespoint;

        System.debug('Account new: ' + defaultAgency);
        KPI__c defaultAgencyKPI = new KPI__c(
            Agency__c = defaultAgency.Id,
            Name = 'Default KPI',
            ScoreTotal__c = 1.6,
            // NumberClientsPortfolio__c = 221,
            // NumberContactableClients__c = 491
            BenchmarkConversionAssignedActivities__c = 79,
            BenchmarkContactActivitiesAssigned__c = 55.6,
            BenchmarkDigitalPenetration__c = 69,
            BenchmarkOmnichannelQuotes__c = 67,
            BenchmarkPrivateAreaRegistration__c = 72,
            BenchmarkAverageLeadProcessingTime__c = 7.6,
            // ConversionAssignedContactActivities__c = 72,
            // ContactActivitiesProcessingAssigned__c = 45,
            DigitalPenetration__c = 50,
            OmnichannelQuotes__c = 55,
            PrivateAreaRegistration__c = 60,
            AverageLeadProcessingTime__c = 5
        );
        insert defaultAgencyKPI;

        defaultAgency.KPI__c = defaultAgencyKPI.Id;
        update defaultAgency;
    }
    
    @IsTest
    static void rankAgenciesMissingInputErrorTest() {
        EngineWrapper.EngineWrapperInput input = new EngineWrapper.EngineWrapperInput(
            null,
            'Preventivatore digitale Unica',
            new List<String>(), 
            (Double) 50.464664,
            (Double) 15.788540,
            new List<String> {'Motor'},
            null
        );

        Test.startTest();
        EngineWrapper.EngineWrapperOutput output = EngineAgencyRanking.rankAgencies(input);
        Test.stopTest();

        Assert.isFalse(output.isSuccess, 'The output.isSuccess property should have been false');
        Assert.areEqual('MISSING_INPUT_PARAMETER', output?.engineError?.errorKeyword, 'The output.engineError.errorKeyword property should have been MISSING_INPUT_PARAMETER');
    }

    @IsTest
    static void rankAgenciesClientTypeErrorTest() {
        EngineWrapper.EngineWrapperInput input = new EngineWrapper.EngineWrapperInput(
            'Client',
            'Preventivatore digitale Unica',
            new List<String>(), 
            (Double) 50.464664,
            (Double) 15.788540,
            new List<String> {'Motor'},
            null
        );

        Test.startTest();
        EngineWrapper.EngineWrapperOutput output = EngineAgencyRanking.rankAgencies(input);
        Test.stopTest();

        Assert.isFalse(output.isSuccess, 'The output.isSuccess property should have been false');
        Assert.areEqual('CLIENT_TYPE_NOT_COMPATIBLE', output?.engineError?.errorKeyword, 'The output.engineError.errorKeyword property should have been CLIENT_TYPE_NOT_COMPATIBLE');
    }

    @IsTest
    static void rankAgenciesTest() {
        EngineAgencyRanking.testingMatrixRows = new List<CalculationMatrixRow> {
            new CalculationMatrixRow(
                InputData = '{"Canale": "Preventivatore digitale Unica","Motore":"Meritocratico"}',
                OutputData = '{"%TassoContattabilità":40,"RaggioCentroideKM":1.5,"NumeroMaxRiassegnazioni":0,"AssegnazioneDefault":"Contact Center","AgenziaDefault":"AG-SOGEINT","RaggioEstensioneKM":3,"Discrezionalità":false}'
            )
        };
        System.debug('EngineAgencyRanking.testingMatrixRows: ' + EngineAgencyRanking.testingMatrixRows);
        EngineWrapper.EngineWrapperInput input = new EngineWrapper.EngineWrapperInput(
            'Prospect',
            'Preventivatore digitale Unica',
            new List<String>(), 
            (Double) 50.464664,
            (Double) 15.788540,
            new List<String> {'Motor'},
            null
        );

        Test.startTest();
        EngineWrapper.EngineWrapperOutput output = EngineAgencyRanking.rankAgencies(input);
        Test.stopTest();

        //Assert.areEqual(1, output.matchingSalespoints?.size(), 'Salespoints should have been returned!');
    }

    @IsTest
    static void rankAgenciesOmnichannelAgreementErrorTest() {
        List<Account> accounts = [SELECT Id FROM Account WHERE ExternalId__c = '0000'];
        accounts[0].OmnichannelAgreement__c = false;
        update accounts;
        
        EngineAgencyRanking.testingMatrixRows = new List<CalculationMatrixRow> {
            new CalculationMatrixRow(
                InputData = '{"Canale": "Preventivatore digitale Unica","Motore":"Meritocratico"}',
                OutputData = '{"%TassoContattabilità":40,"RaggioCentroideKM":1.5,"NumeroMaxRiassegnazioni":0,"AssegnazioneDefault":"Contact Center","AgenziaDefault":"AG-SOGEINT","RaggioEstensioneKM":3,"Discrezionalità":false}'
            )
        };
        System.debug('EngineAgencyRanking.testingMatrixRows: ' + EngineAgencyRanking.testingMatrixRows);
        EngineWrapper.EngineWrapperInput input = new EngineWrapper.EngineWrapperInput(
            'Prospect',
            'Preventivatore digitale Unica',
            new List<String>(), 
            (Double) 50.464664,
            (Double) 15.788540,
            new List<String> {'Motor'},
            null
        );

        Test.startTest();
        EngineWrapper.EngineWrapperOutput output = EngineAgencyRanking.rankAgencies(input);
        Test.stopTest();

        Assert.areEqual(null, output.matchingSalespoints, 'Salespoints should have not been returned!');
    }

    @IsTest
    static void rankAgenciesTypeErrorTest() {
        List<Account> accounts = [SELECT Id FROM Account WHERE ExternalId__c = '0000'];
        accounts[0].Type = null;
        update accounts;

        EngineAgencyRanking.testingMatrixRows = new List<CalculationMatrixRow> {
            new CalculationMatrixRow(
                InputData = '{"Canale": "Preventivatore digitale Unica","Motore":"Meritocratico"}',
                OutputData = '{"%TassoContattabilità":40,"RaggioCentroideKM":1.5,"NumeroMaxRiassegnazioni":0,"AssegnazioneDefault":"Contact Center","AgenziaDefault":"AG-SOGEINT","RaggioEstensioneKM":3,"Discrezionalità":false}'
            )
        };
        System.debug('EngineAgencyRanking.testingMatrixRows: ' + EngineAgencyRanking.testingMatrixRows);
        EngineWrapper.EngineWrapperInput input = new EngineWrapper.EngineWrapperInput(
            'Prospect',
            'Preventivatore digitale Unica',
            new List<String>(), 
            (Double) 50.464664,
            (Double) 15.788540,
            new List<String> {'Motor'},
            null
        );

        Test.startTest();
        EngineWrapper.EngineWrapperOutput output = EngineAgencyRanking.rankAgencies(input);
        Test.stopTest();

        Assert.areEqual(null, output.matchingSalespoints, 'Salespoints should have not been returned!');
    }

    @IsTest
    static void rankAgenciesBlacklistErrorTest() {
        List<Account> accounts = [SELECT Id FROM Account WHERE ExternalId__c = '0000'];
        accounts[0].Blacklist__c = true;
        update accounts;

        EngineAgencyRanking.testingMatrixRows = new List<CalculationMatrixRow> {
            new CalculationMatrixRow(
                InputData = '{"Canale": "Preventivatore digitale Unica","Motore":"Meritocratico"}',
                OutputData = '{"%TassoContattabilità":40,"RaggioCentroideKM":1.5,"NumeroMaxRiassegnazioni":0,"AssegnazioneDefault":"Contact Center","AgenziaDefault":"AG-SOGEINT","RaggioEstensioneKM":3,"Discrezionalità":false}'
            )
        };
        System.debug('EngineAgencyRanking.testingMatrixRows: ' + EngineAgencyRanking.testingMatrixRows);
        EngineWrapper.EngineWrapperInput input = new EngineWrapper.EngineWrapperInput(
            'Prospect',
            'Preventivatore digitale Unica',
            new List<String>(), 
            (Double) 50.464664,
            (Double) 15.788540,
            new List<String> {'Motor'},
            null
        );

        Test.startTest();
        EngineWrapper.EngineWrapperOutput output = EngineAgencyRanking.rankAgencies(input);
        Test.stopTest();

        Assert.areEqual(null, output.matchingSalespoints, 'Salespoints should have not been returned!');
    }

    @IsTest
    static void rankAgenciesLocatorErrorTest() {
        List<Account> accounts = [SELECT Id FROM Account WHERE ExternalId__c = '0001'];
        accounts[0].IsLocatorEnabled__c = false;
        update accounts;

        EngineAgencyRanking.testingMatrixRows = new List<CalculationMatrixRow> {
            new CalculationMatrixRow(
                InputData = '{"Canale": "Preventivatore digitale Unica","Motore":"Meritocratico"}',
                OutputData = '{"%TassoContattabilità":40,"RaggioCentroideKM":1.5,"NumeroMaxRiassegnazioni":0,"AssegnazioneDefault":"Contact Center","AgenziaDefault":"AG-SOGEINT","RaggioEstensioneKM":3,"Discrezionalità":false}'
            )
        };
        System.debug('EngineAgencyRanking.testingMatrixRows: ' + EngineAgencyRanking.testingMatrixRows);
        EngineWrapper.EngineWrapperInput input = new EngineWrapper.EngineWrapperInput(
            'Prospect',
            'Preventivatore digitale Unica',
            new List<String>(), 
            (Double) 50.464664,
            (Double) 15.788540,
            new List<String> {'Motor'},
            null
        );

        Test.startTest();
        EngineWrapper.EngineWrapperOutput output = EngineAgencyRanking.rankAgencies(input);
        Test.stopTest();

        Assert.areEqual(null, output.matchingSalespoints, 'Salespoints should have not been returned!');
    }

    @IsTest
    static void rankAgenciesExternalIdErrorTest() {
        EngineAgencyRanking.testingMatrixRows = new List<CalculationMatrixRow> {
            new CalculationMatrixRow(
                InputData = '{"Canale": "Preventivatore digitale Unica","Motore":"Meritocratico"}',
                OutputData = '{"%TassoContattabilità":40,"RaggioCentroideKM":1.5,"NumeroMaxRiassegnazioni":0,"AssegnazioneDefault":"Contact Center","AgenziaDefault":"AG-SOGEINT","RaggioEstensioneKM":3,"Discrezionalità":false}'
            )
        };
        System.debug('EngineAgencyRanking.testingMatrixRows: ' + EngineAgencyRanking.testingMatrixRows);
        EngineWrapper.EngineWrapperInput input = new EngineWrapper.EngineWrapperInput(
            'Prospect',
            'Preventivatore digitale Unica',
            new List<String>{'0000'}, 
            (Double) 50.464664,
            (Double) 15.788540,
            new List<String> {'Motor'},
            null
        );

        Test.startTest();
        EngineWrapper.EngineWrapperOutput output = EngineAgencyRanking.rankAgencies(input);
        Test.stopTest();

        Assert.areEqual(null, output.matchingSalespoints, 'Salespoints should have not been returned!');
    }

}
