@IsTest
private class OpportunityControllerNewTest {

    @TestSetup
    static void setupData() {
        Account acc = new Account(Name = 'Test Account');
        insert acc;

        List<Opportunity> opps = new List<Opportunity>{
            new Opportunity(
                Name = 'Opp 1',
                StageName = 'Assegnato',
                CloseDate = Date.today().addDays(10),
                Amount = 1000,
                AccountId = acc.Id,
                AreaOfNeed__c = 'Casa',
                Rating__c = 'Calda',
                JourneyStep__c = 'Acquisto non concluso',
                WorkingSLAExpiryDate__c = Date.today().addDays(3)
            ),
            new Opportunity(
                Name = 'Opp 2',
                StageName = 'Assegnato',
                CloseDate = Date.today().addDays(20),
                Amount = 5000,
                AccountId = acc.Id,
                AreaOfNeed__c = 'Casa',
                Rating__c = 'Calda',
                JourneyStep__c = 'Acquisto non concluso',
                WorkingSLAExpiryDate__c = Date.today().addDays(5)
            )
        };

        insert opps;
    }

    @IsTest
    static void testGetReportOpportunitiesWithFilters() {
        // Parametri filtranti
        OpportunityControllerNew.URLParameters params = new OpportunityControllerNew.URLParameters();
        params.amountFrom = 1000;
        params.amountTo = 10000;
        params.clientType = null;
        params.dateFrom = Date.today().toStartOfMonth().format();  // tipo '2025-06-01'
        params.dateTo = Date.today().toStartOfMonth().addDays(30).format();
        params.areasOfNeed = 'Area1;Area2';
        params.temperature = 'Caldo;Freddo';
        params.callMeBack = true;
        params.takenInChargeSLARemainingHours = 0;
        params.opportunityName = 'test';
        params.accountName = 'test';
        params.journeyStep = 'test';
		params.valus = 0;
        params.workingSLAExpiryDateFrom = 'test';
        params.workingSLAExpiryDateTo = 'test';
        
        OpportunityControllerNew.ReloadOpportunitiesWrapper row = new OpportunityControllerNew.ReloadOpportunitiesWrapper();
        row.parameters = new OpportunityControllerNew.URLParameters();
        row.opportunities = new List<Opportunity>();
        
        OpportunityControllerNew ocn = new OpportunityControllerNew();
       	String s1 = OpportunityControllerNew.DEFAULT_QUERY;
        String s2 = OpportunityControllerNew.ORDER_BY_SUBQUERY;

    }
}