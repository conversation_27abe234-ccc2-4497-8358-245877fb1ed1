global class ControllerDispCanaliDig{

    @AuraEnabled
    public static Map<String,Object> getDispCanaliDigitali(String recordId) {
        String UserAgent;
        system.debug('recordId'+recordId);
        if(recordId != null){
            ServiceResource userInfoByRecordId =  [SELECT id,RelatedRecordId from ServiceResource WHERE id =:recordId Limit 1];
            UserAgent = userInfoByRecordId.RelatedRecordId;
        }else{
            UserAgent = UserInfo.getUserId();
        }

        Map<String,Object> outMap = new Map<String,Object>();
        // Retrieve ServiceTerritoryMember records
        List<ServiceTerritoryMember> serviceTerritoryLst = [ 
            SELECT id, 
                ServiceResourceId, 
                ServiceTerritory.Name, 
                ServiceTerritory.Id, 
                OperatingHoursId, 
                ServiceResource.Name 
            FROM ServiceTerritoryMember
            WHERE ServiceResourceId IN (SELECT id FROM ServiceResource WHERE RelatedRecordId =: UserAgent)
        ];

        
        Map<String, List<String>> stmMap = new Map<String, List<String>>();
        for (ServiceTerritoryMember stm : serviceTerritoryLst) {
            if (!stmMap.containsKey(stm.OperatingHoursId)) {
                    stmMap.put(stm.OperatingHoursId, new List<String>());
            }
            stmMap.get(stm.OperatingHoursId).add(stm.ServiceTerritory.Name);
        }


        // Retrieve TimeSlot records
        List<TimeSlot> timeSlotLst = [ 
            SELECT id,
                StartTime, 
                EndTime, 
                DayOfWeek, 
                TimeSlotNumber, 
                OperatingHoursId, 
                (SELECT id, WorkTypeGroup.Name FROM AppointmentTopicTimeSlots), 
                WorkTypeGroupId 
            FROM TimeSlot
            WHERE OperatingHoursId IN :stmMap.keySet()
        ];

        // Structure for response: Map<dayOfWeek, List<slots>>
        Map<String,String> dayMap = new Map<String,String>{'Monday'=>'Lunedì','Tuesday'=>'Martedì',
        'Wednesday'=>'Mercoledì','Thursday'=>'Giovedì','Friday'=>'Venerdì'};
        
        Map<String, List<itemTimeSlot>> daysMap = new Map<String, List<itemTimeSlot>>();
        for (TimeSlot ts : timeSlotLst) {
            String day = dayMap.get(ts.DayOfWeek);
            
            for(String key : stmMap.keySet()){
                if(ts.OperatingHoursId == key){

                    for(String st : stmMap.get(ts.OperatingHoursId)){
                        itemTimeSlot slot = new itemTimeSlot();
                            slot.ServiceTerritoryName = st;
                            slot.idSlot = ts.Id;
                            slot.startTime = String.valueOf(ts.StartTime).removeEnd(':00.000Z');
                            slot.endTime = String.valueOf(ts.EndTime).removeEnd(':00.000Z'); 
                        // Add to dayOfWeek list
                        if (daysMap.containsKey(day)) {
                            daysMap.get(day).add(slot);
                        } else {
                            daysMap.put(day, new List<itemTimeSlot>{ slot });
                        }
                    }
                    continue;
                }
            }  
          
            
        }

        // Convert daysMap to required format
        List<Object> days = new List<Object>();
        for (String day : daysMap.keySet()) {
            Map<String, Object> dayObject = new Map<String, Object>();
            dayObject.put('dayOfWeek', day);
            dayObject.put('slots', daysMap.get(day));
            days.add(dayObject);
        }

        // Final response
        Map<String, Object> response = new Map<String, Object>();
        response.put('days', days);

        String serializedData = Json.serialize(response);
        Map<String, Object> result = (Map<String, Object>) JSON.deserializeUntyped(serializedData);
        
        outMap.put('DispCanaliDigitali',result);

        return outMap;
    }


    public class itemTimeSlot {
        @AuraEnabled public String idSlot;
        @AuraEnabled public String startTime;
        @AuraEnabled public String endTime;
        @AuraEnabled public String dayOfWeek;
        @AuraEnabled public String OperatingHours;
        @AuraEnabled public String ServiceTerritoryName;
    }

}