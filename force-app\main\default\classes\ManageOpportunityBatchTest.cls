@isTest
private with sharing class ManageOpportunityBatchTest {
    @TestSetup
    static void makeData() {
        ConiAssignmentHelper.checkPermissionCRUD();
        List<Group> lstTestGrp = createGroupList();
        List<Account> lstAccs = createAccountList();
        List<Account> lstAgencyAccs = createAgencyList();
        List<Account> lstSocietyAccs = createSocietyList();
        List<Group__c> lstGroups = createGroupCustomList(lstTestGrp);

        /* INSERT InsurancePolicy records */
        List<InsurancePolicy> lstInsPol = new List<InsurancePolicy>();
        InsurancePolicy insPol = new InsurancePolicy(Name = 'Test Coni Policy 1', NameInsuredId = lstAccs.get(0).Id, Agency__c = lstAgencyAccs.get(0).Id, CIP__c = '050', CompanyCode__c = '1', Society__c = lstSocietyAccs.get(0).Id);
        lstInsPol.add(insPol);
        insert lstInsPol;
        InsurancePolicyShare insPolShare = new InsurancePolicyShare();
        insPolShare.AccessLevel = 'Edit';
        insPolShare.ParentId = lstInsPol.get(0).Id;
        insPolShare.UserOrGroupId = lstTestGrp.get(0).Id;
        insert insPolShare;

        /* INSERT Case records	*/
        List<Case> lstCase = new List<Case>();
        Case case1 = new Case(AccountId = lstAccs.get(1).Id, Status = 'Nuovo', Origin = 'Phone', dueDate__c = Date.today().addDays(365), Area__c = 'Sinistri', Detail__c = 'Contatta per sinistro aperto', Activity__c = 'Memo Sinistri');
        lstCase.add(case1);
        insert lstCase;
        CaseShare cShare = new CaseShare();
        cShare.CaseAccessLevel = 'Edit';
        cShare.CaseId = lstCase.get(0).Id;
        cShare.UserOrGroupId = lstTestGrp.get(0).Id;
        insert cShare;

        /* INSERT Opportunity records	*/
        List<Opportunity> lstOpps = new List<Opportunity>();
        Opportunity newOpp = new Opportunity();
        newOpp.Name = 'OppTestConiGB Mario Rossi - 29/10';
        newOpp.AccountId = lstAccs.get(0).Id;
        newOpp.CloseDate = System.today() + 90;
        newOpp.StageName = 'Nuovo';
        newOpp.Agency__c = lstAgencyAccs.get(0).Id;
        newOpp.AssignedGroup__c = lstGroups.get(0).Id;
        lstOpps.add(newOpp);

        /*Opportunity newOpp2 = new Opportunity();
        newOpp2.Name = 'OppTestConiGB Alvaro Perez - 29/10';
        newOpp2.AccountId = lstAccs.get(1).Id;
        newOpp2.CloseDate = System.today() + 90;
        newOpp2.StageName = 'Nuovo';
        newOpp2.Agency__c = lstAgencyAccs.get(0).Id;
        newOpp2.AssignedGroup__c = lstGroups.get(1).Id;
        lstOpps.add(newOpp2);

        Opportunity newOpp3 = new Opportunity();
        newOpp3.Name = 'OppTestConiGB Alba Rivas - USER - 29/10';
        newOpp3.AccountId = lstAccs.get(0).Id;
        newOpp3.CloseDate = System.today() + 90;
        newOpp3.StageName = 'Nuovo';
        newOpp3.Agency__c = lstAgencyAccs.get(0).Id;
        newOpp3.AssignedTo__c = UserInfo.getUserId();
        lstOpps.add(newOpp3);

        Opportunity newOpp4 = new Opportunity();
        newOpp4.Name = 'OppTestConiGB Alba Rivas - 29/10';
        newOpp4.AccountId = lstAccs.get(1).Id;
        newOpp4.CloseDate = System.today() + 90;
        newOpp4.StageName = 'Nuovo';
        newOpp4.Agency__c = lstAgencyAccs.get(0).Id;
        newOpp4.AssignedGroup__c = lstGroups.get(1).Id;
        lstOpps.add(newOpp4);*/

        insert lstOpps;

        Group gp = new Group();
        gp.Name = 'COLL_' + lstAgencyAccs.get(0).ExternalId__c;
        gp.Description = 'Test Descrizione';
        insert gp;
        Id RTGRPCollaborativo = Schema.SObjectType.Group__c.getRecordTypeInfosByDeveloperName().get('UserGroup').getRecordTypeId();
        Integer rand = Math.round(Math.random() * 1000);
        Group__c collaborativo = new Group__c();
        collaborativo.Name = lstAgencyAccs.get(0).Name;
        collaborativo.RecordTypeId = RTGRPCollaborativo;
        collaborativo.ExternalId__c = lstAgencyAccs.get(0).ExternalId__c + String.valueOf(rand);
        collaborativo.Agenzia__c = lstAgencyAccs.get(0).Id;
        collaborativo.PublicGroupId__c = gp.Id;
        collaborativo.Description__c = gp.Description;
        insert collaborativo;
    }

    @isTest
    static void testBatchWithoutInputParameter() {
        Test.startTest();
        Database.executeBatch(new ManageOpportunityBatch());
        Test.stopTest();
    }

    // @isTest
    // static void testBatchWithoutInputParameterWithAssignedGroup() {
    //     Opportunity opp = [SELECT Id FROM Opportunity LIMIT 1];
    //     opp.AssignedTo__c = UserInfo.getUserId();
    //     opp.AssignedGroup__c = null;
    //     update opp;
    //     Test.startTest();
    //     Database.executeBatch(new ManageOpportunityBatch());
    //     Test.stopTest();
    // }

    @isTest
    static void testBatchWithInputParameter() {
        Test.startTest();
        Database.executeBatch(new ManageOpportunityBatch(Date.today()));
        ConiQueApexShareIns.jsonString = null;
        Test.stopTest();
    }

    @isTest
    static void testBatchWitestBatchWithBadInputParameterthInputParameter() {
        Test.startTest();
        Database.executeBatch(new ManageOpportunityBatch(null));
        Test.stopTest();
    }

    @isTest
    static void otherTest() {
        Id agAccRecTypId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(System.Label.AgencyAccRecType).getRecordTypeId();
        Account acc = [SELECT Id FROM Account WHERE RecordTypeId = :agAccRecTypId LIMIT 1];
        acc.ExternalId__c = 'AGE_01853';
        update acc;
        //Group testGrp1 = new Group(Name = 'R_AGE_01853', Type = 'Regular');
        //insert testGrp1;
        Test.startTest();
        try{
            //ConiAssignmentHelper.deleteForChangeAgencyShareAAR(new Set<String>{ acc.Id }, new Set<String>());
            Case c = new Case();
            ConiAssignmentHelper.checkIsClosed((SObject) c, (SObject) c);
            ConiAssignmentHelper.checkIsClosed((SObject) acc, (SObject) acc);
            ConiAssignmentHelper.checkIfStatusReopen((SObject) c, (SObject) c);
            ConiAssignmentHelper.checkIfStatusReopen((SObject) acc, (SObject) acc);
        }catch(Exception ex){}
        Test.stopTest();
    }

    static List<Group> createGroupList() {
        List<Group> lstTestGrp = new List<Group>();
        Group testGrp1 = new Group(Name = 'TestGroup1', Type = 'Regular');
        lstTestGrp.add(testGrp1);
        Group testGrp2 = new Group(Name = 'TestGroup2', Type = 'Regular');
        lstTestGrp.add(testGrp2);
        Group testGrp3 = new Group(Name = 'TestGroup3', Type = 'Regular');
        lstTestGrp.add(testGrp3);
        insert lstTestGrp;
        return lstTestGrp;
    }

    static List<Account> createAccountList() {
        List<Account> lstAccs = new List<Account>();
        Id perAccRecTypId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(System.Label.PersonAccRecType).getRecordTypeId();
        Account newAcc1 = new Account(FirstName = 'TestConi', LastName = 'Account 1', RecordTypeId = perAccRecTypId);
        lstAccs.add(newAcc1);
        Account newAcc2 = new Account(FirstName = 'TestConi', LastName = 'Account 2', RecordTypeId = perAccRecTypId);
        lstAccs.add(newAcc2);
        Account newAcc3 = new Account(FirstName = 'TestConi', LastName = 'Account 3', RecordTypeId = perAccRecTypId);
        lstAccs.add(newAcc3);
        insert lstAccs;
        return lstAccs;
    }

    static List<Account> createAgencyList() {
        List<Account> lstAgencyAccs = new List<Account>();
        Id agAccRecTypId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(System.Label.AgencyAccRecType).getRecordTypeId();
        Account newAgAcc1 = new Account(Name = 'TestConi Agenzia 1', RecordTypeId = agAccRecTypId, ExternalId__c = 'AGE_01853');
        lstAgencyAccs.add(newAgAcc1);
        Account newAgAcc2 = new Account(Name = 'TestConi Agenzia 2', RecordTypeId = agAccRecTypId, ExternalId__c = 'AGE_01854');
        lstAgencyAccs.add(newAgAcc2);
        insert lstAgencyAccs;
        return lstAgencyAccs;
    }

    static List<Account> createSocietyList() {
        List<Account> lstSocietyAccs = new List<Account>();
        Id soAccRecTypId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get(System.Label.AccountSocietyRT).getRecordTypeId();
        Account newSocAcc1 = new Account(Name = 'TestConi Società 1', RecordTypeId = soAccRecTypId);
        lstSocietyAccs.add(newSocAcc1);
        Account newSocAcc2 = new Account(Name = 'TestConi Società 2', RecordTypeId = soAccRecTypId);
        lstSocietyAccs.add(newSocAcc2);
        insert lstSocietyAccs;
        return lstSocietyAccs;
    }

    static List<Group__c> createGroupCustomList(List<Group> lstTestGrp) {
        List<Group__c> lstGroups = new List<Group__c>();
        Id cObjGroupRecTypeId = Schema.SObjectType.Group__c.getRecordTypeInfosByDeveloperName().get(System.Label.CustomObjGroupRecType).getRecordTypeId();
        Group__c newGroup1 = new Group__c(Name = 'TestGroup1', RecordTypeId = cObjGroupRecTypeId, PublicGroupId__c = lstTestGrp.get(0).Id);
        lstGroups.add(newGroup1);
        Group__c newGroup2 = new Group__c(Name = 'TestGroup2', RecordTypeId = cObjGroupRecTypeId, PublicGroupId__c = lstTestGrp.get(1).Id);
        lstGroups.add(newGroup2);
        Group__c newGroup3 = new Group__c(Name = 'TestGroup3', RecordTypeId = cObjGroupRecTypeId, PublicGroupId__c = lstTestGrp.get(2).Id);
        lstGroups.add(newGroup3);
        insert lstGroups;
        return lstGroups;
    }
}
