({
   init: function(component, event, helper) {
        var flow = component.find("flowData");
        var flowName = component.get("v.flowName");

        if (flow && flowName) {
            flow.startFlow(flowName);
        }
    },

    handleStatusChange: function(component, event, helper) {
        var status = event.getParam("status");

        if (status === "FINISHED") {
            helper.closeComponent(component);
        }
    }
})