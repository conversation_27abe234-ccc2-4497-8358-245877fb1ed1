global class InsurancePolicyAddRelationBatch implements Database.Batchable<SObject>, Database.Stateful {
    
    private Id recordTypeId;

    private Set<Id> idsSet;

    private Id idValueFrom;
    private Id idValueTo;
    
    private String dateType;
    private DateTime dateFrom;
    private DateTime dateTo;
    
    global InsurancePolicyAddRelationBatch(String recordTypeDeveloperName) {
        this.recordTypeId = Schema.SObjectType.InsurancePolicy.getRecordTypeInfosByDeveloperName().get(recordTypeDeveloperName).getRecordTypeId();
    }

    global InsurancePolicyAddRelationBatch(Set<Id> idsSet) {
        this.idsSet = idsSet;
    }

    global InsurancePolicyAddRelationBatch(Id idValueFrom, Id idValueTo) {
        this.idValueFrom = idValueFrom;
        this.idValueTo = idValueTo;
    }

    global InsurancePolicyAddRelationBatch(DateTime dateFrom, DateTime dateTo) {
        this.dateFrom = dateFrom;
        this.dateTo = dateTo;
    }
    
    global InsurancePolicyAddRelationBatch(String recordTypeDeveloperName, Set<Id> idsSet, Id idValueFrom, Id idValueTo, DateTime dateFrom, DateTime dateTo){
        this.recordTypeId = Schema.SObjectType.AccountAgencyDetails__c.getRecordTypeInfosByDeveloperName().get(recordTypeDeveloperName).getRecordTypeId();
        this.idsSet = idsSet;
        this.idValueFrom = idValueFrom;
        this.idValueTo = idValueTo;
        this.dateFrom = dateFrom;
        this.dateTo = dateTo;
    }

    public class BatchException extends Exception {}

    global Database.QueryLocator start(Database.BatchableContext bc) {
        
        List<String> conditions = new List<String>();
        String baseQuery = 'SELECT Id, NPI__c, ExternalId__c FROM InsurancePolicy WHERE ';

        if (recordTypeId != null) {
            conditions.add('RecordTypeId = :recordTypeId');
        }
        
        if(idsSet != null) {
            conditions.add('Id IN :idsSet');
        }
        
        if(idValueFrom != null){
            conditions.add('Id >= :idValueFrom');
        }
        
        if(idValueTo != null){
            conditions.add('Id <= :idValueTo');
        }
        
        if(dateFrom != null){
            conditions.add('LastModifiedDate >= :dateFrom');
        }
        
        if(dateTo != null){
            conditions.add('LastModifiedDate <= :dateTo');
        }

        String fullQuery = conditions.isEmpty() ? baseQuery.removeEnd(' WHERE ') : baseQuery + String.join(conditions, ' AND ');

        return Database.getQueryLocator(fullQuery);
    }

    global void execute(Database.BatchableContext bc, List<InsurancePolicy> scope) {

        List<InsurancePolicy> insPolList = scope;
        Map<String, Id> insPolExtIdMap = new Map<String, Id>();
        for(InsurancePolicy insPol : insPolList) {
            if(insPol.ExternalId__c != null) {
                insPolExtIdMap.put(insPol.ExternalId__c, insPol.Id);
            }
        }

        Map<String, Id> insPolNPIExtIdMap = new Map<String, Id>();
        for(InsurancePolicyNPI__c insPolNPI : [SELECT Id, ExternalId__c FROM InsurancePolicyNPI__c WHERE ExternalId__c IN :insPolExtIdMap.keySet()]) {
            insPolNPIExtIdMap.put(insPolNPI.ExternalId__c, insPolNPI.Id);
        }
        
        List<InsurancePolicy> insPolListToUpdate = new List<InsurancePolicy>();
        for(String extId : insPolExtIdMap.keySet()) {

            if(!insPolNPIExtIdMap.containsKey(extId)) {
                continue;
            }

            if(insPolNPIExtIdMap.get(extId) == null) {
                continue; 
            }

            InsurancePolicy insPol = new InsurancePolicy(
                Id = insPolExtIdMap.get(extId),
                NPI__c = insPolNPIExtIdMap.get(extId)
            );

            insPolListToUpdate.add(insPol);
        }

        if(!insPolListToUpdate.isEmpty()) {
            Database.update(insPolListToUpdate, false);
        }
    }

    global void finish(Database.BatchableContext bc) {}
}