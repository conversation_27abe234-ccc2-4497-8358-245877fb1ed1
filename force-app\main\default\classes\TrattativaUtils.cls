global without sharing class TrattativaUtils implements System.Callable{
    private static final String GENERIC_ERROR = 'Si è verificato un errore';

    public Object call(String action, Map<String, Object> args) {
        Map<String, Object> input;
        Map<String, Object> output;
        Map<String, Object> options;
        Object result;
        try
        {
            input = (Map<String, Object>)args.get('input');
            output = (Map<String, Object>)args.get('output');
            options = (Map<String, Object>)args.get('options');
            

            if(action.equals('countOmniParent'))
            {
                result = countOmniParent(input, output, options);
            }
            System.debug('///Result: ' + result);
        }
        catch(Exception e)
        {       
            output.put('success', false);  
            output.put('errorMessage', GENERIC_ERROR);
            result = false;
        }
        return result;
    }
    private Boolean countOmniParent(Map<String,Object> input, Map<String,Object> output, Map<String,Object> options)
    {
        List<Map<String, Object>> oppList = (List<Map<String, Object>>)input.get('oppList');
        Integer countOmniOpp = 0;
        Boolean isMultiParent = false;
        for(Map<String, Object> opp : oppList){
            Boolean isOmni = (Boolean)opp.get('isOmni');
            if(isOmni == true){
                countOmniOpp = countOmniOpp+1;
            }
        }
        if(countOmniOpp>1)isMultiParent = true;
        output.put('success', true);
        output.put('isMultiParent', isMultiParent);
        return true;
    }
    
}