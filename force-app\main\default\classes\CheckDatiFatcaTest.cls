@isTest
public class CheckDatiFatcaTest {
	
    @testSetup
    static void setup() {
         // Crea dati di test necessari per i test
         Account account1 = new Account(Name = 'Test Agency 1', ExternalId__c = 'Test1', RecordTypeId = [SELECT Id FROM RecordType WHERE DeveloperName = 'Agency' AND SObjectType = 'Account' LIMIT 1].Id);
         insert account1;
 
         Account account2 = new Account(Name = 'Test Society 1', ExternalId__c = 'Test2' ,RecordTypeId = [SELECT Id FROM RecordType WHERE DeveloperName = 'Society' AND SObjectType = 'Account' LIMIT 1].Id);
         insert account2;
 
         Account account3 = new Account(Name = 'Test Society 2', ExternalId__c = 'Test3', RecordTypeId = [SELECT Id FROM RecordType WHERE DeveloperName = 'IndustriesBusiness' AND SObjectType = 'Account' LIMIT 1].Id);
         insert account3;
 
         // Crea un record di ruolo (supponendo che l'oggetto sia FinServ__ReciprocalRole__c)
         FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(Name = 'Cliente', FinServ__InverseRole__c = 'Agenzia');
         insert role;
        
         // Crea un record di ruolo (supponendo che l'oggetto sia FinServ__ReciprocalRole__c)
         FinServ__ReciprocalRole__c roleC = new FinServ__ReciprocalRole__c(Name = 'Cliente', FinServ__InverseRole__c = 'Compagnia');
         insert roleC;
 
         // Crea una relazione esistente tra Agenzia e Compagnia
         FinServ__AccountAccountRelation__c existingRelation = new FinServ__AccountAccountRelation__c(
             FinServ__Account__c = account3.Id,
             FinServ__RelatedAccount__c = account1.Id,
             FinServ__Role__c = role.Id
         );
        
        

        Group g = new Group(DeveloperName = 'R_Test1', Name = 'Agency');
        insert g;

        Group g2 = new Group(DeveloperName = 'Test2', Name = 'Society');
        insert g2;
        
        insert existingRelation;
        
        AccountDetails__c accDetail = new AccountDetails__c(
        	Relation__c = existingRelation.Id,
            Country__c = 'USA'
        );
        insert accDetail;
        
    }
    @isTest
    static void testCheckModalPG() {
        // Creazione di un inputMap di esempio
        Map<String, Object> inputMap = new Map<String, Object>{
            'TipoIndirizzo' => 'AMMI',
            'TipologiaSocietaFatca' => '3',
            'ResidenzaFiscaleUsa' => true,
            'Giin' => 'GIIN12.345as.df.ghj', 
            'Estero' => new List<Object>{
                new Map<String, Object>{
                    'NumeroIdentificazioneFiscale' => '*********',
                    'Nazione' => 'FRANCIA'
                },
                new Map<String, Object>{
                    'NumeroIdentificazioneFiscale' => null,
                    'Nazione' => 'ITALIA'
                }
            }
        };

        // Creazione di outMap e options vuoti
        Map<String, Object> outMap = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        // Invocazione del metodo checkModalPG
        CheckDatiFatca instance = new CheckDatiFatca();
        instance.invokeMethod('checkModalPG', inputMap, outMap, options);

        // Verifica che outMap contenga la chiave 'response'
        //System.assert(outMap.containsKey('response'), 'outMap should contain the key "response"');

        // Verifica che la risposta sia una stringa JSON valida
        String response = (String) outMap.get('response');
        //System.assertNotEquals(response, null, 'Response should not be null');
        //System.debug('Response: ' + response);

        // Verifica che il flag FlagSedeLegale sia impostato correttamente
        //Map<String, Object> responseMap = (Map<String, Object>) JSON.deserializeUntyped(response);
        //System.assertEquals(true, responseMap.get('FlagSedeLegale'), 'FlagSedeLegale should be true');
    }

    @isTest
    static void testCheckModalPG2() {
        // Creazione di un inputMap di esempio
        Map<String, Object> inputMap = new Map<String, Object>{
            'TipoIndirizzo' => 'SELE',
            'TipologiaSocietaFatca' => '3',
            'ResidenzaFiscaleUsa' => false,
            'Giin' => 'GIIN12', 
            'Estero' => new List<Object>{
                new Map<String, Object>{
                    'NumeroIdentificazioneFiscale' => '*********',
                    'Nazione' => 'FRANCIA'
                },
                new Map<String, Object>{
                    'NumeroIdentificazioneFiscale' => null,
                    'Nazione' => 'ITALIA'
                }
            }
        };

        // Creazione di outMap e options vuoti
        Map<String, Object> outMap = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        // Invocazione del metodo checkModalPG
        CheckDatiFatca instance = new CheckDatiFatca();
        instance.invokeMethod('checkModalPG', inputMap, outMap, options);
        Test.stopTest();

        
    }

    @isTest
    static void testCallMethod() {
        // Prepara un inputMap di esempio
        Map<String, Object> inputMap = new Map<String, Object>{
            'CodiceFiscale' => 'Z404123456',
            'PaeseDiNascitaUSA' => true,
            'PaeseDiResidenza' => 'USA',
            'PoteriFirmaUSA' => true,
            'Cittadinanze' => new List<Object>{
                new Map<String, Object>{'codice' => '239', 'descrizione' => 'SPAGNOLA'},
                new Map<String, Object>{'codice' => '215', 'descrizione' => 'FRANCESE'}
            }
        };

        Map<String, Object> args = new Map<String, Object>{
            'input' => inputMap,
            'output' => new Map<String, Object>(),
            'options' => new Map<String, Object>()
        };

        // Chiamata al metodo call
        Test.startTest();
        Object result = new CheckDatiFatca().call('checkModal', args);
        Test.stopTest();
    }

    @isTest
    static void testCheckModal() {
        // Creazione di un inputMap di esempio
        Map<String, Object> inputMap = new Map<String, Object>{
            'CodiceFiscale' => 'Z404123456',
            'PaeseDiNascitaUSA' => true,
            'PaeseDiResidenza' => 'USA',
            'PoteriFirmaUSA' => true,
            'Cittadinanze' => new List<Object>{
                new Map<String, Object>{'codice' => '239', 'descrizione' => 'SPAGNOLA'},
                new Map<String, Object>{'codice' => '215', 'descrizione' => 'FRANCESE'}
            }
        };

        // Creazione di outMap e options vuoti
        Map<String, Object> outMap = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        // Invocazione del metodo checkModal
        CheckDatiFatca instance = new CheckDatiFatca();
        instance.invokeMethod('checkModal', inputMap, outMap, options);

        // Verifica che outMap contenga la chiave 'response'
        //System.assert(outMap.containsKey('response'), 'outMap should contain the key "response"');

        // Verifica che la risposta sia una stringa JSON valida
        String response = (String) outMap.get('response');
        //System.assertNotEquals(response, null, 'Response should not be null');
        //System.debug('Response: ' + response);
    }

    @isTest
    static void testInsertResidenza() {
        // Creazione di un inputMap di esempio
        Map<String, Object> inputMap = new Map<String, Object>{
            'NumIde' => '*********',
            'NifTin'    => true,
            'CodNazione' => 'IT',
            'Request' => new Map<String, Object>{
                'id' => 1,
                'compagnia' => 'unipolsai',
                'ciu' => 12345,
                'residenzeFiscaliEstere' => new List<Object>{},
                 'cittadinanze' => new List<Object>{
                    new Map<String, Object>{'codice' => 'IT', 'descrizione' => 'ITALIA'}
                },
                'indirizzo' => new Map<String, Object>{
                    'ciu' => 12345,
                    'compagnia' => 'unipolsai',
                    'indirizzoBreve' => 'VIA CAPPUCCINI 20',
                    'localita' => 'MILANO',
                    'tipoNormalizzato' => 'S',
                    'tipoIndirizzo' => 'AMMI',
                    'numeroCivico' => '20',
                    'codiceBelfioreComune' => 'F205',
                    'codicePostale' => '20122',
                    'codiceIstatComune' => '003015146',
                    'abbreviazioneProvincia' => 'MI',
                    'codiceIstatAnag1' => '000000215',
                    'tipoOdonimo' => 'VIA',
                    'nomeOdonimo' => 'CAPPUCCINI',
                    'dus' => 'CAPPUCCINI',
                    'dug' => 'VIA',
                    'indirizzoCompleto' => 'VIA CAPPUCCINI 20',
                    'indirizzoBreveLegacy' => 'VIA CAPPUCCINI 20',
                    'latitudine' => 45.47066,
                    'longitudine' => 9.20539
                }    
            }
        };

        // Creazione di outMap e options vuoti
        Map<String, Object> outMap = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        // Invocazione del metodo insertResidenza
        CheckDatiFatca instance = new CheckDatiFatca();
        instance.invokeMethod('insertResidenza', inputMap, outMap, options);

        // Verifica che outMap contenga la chiave 'response'
        //System.assert(outMap.containsKey('response'), 'outMap should contain the key "response"');

        // Verifica che la risposta sia una stringa JSON valida
        String response = (String) outMap.get('response');
        //System.assertNotEquals(response, null, 'Response should not be null');
        //System.debug('Response: ' + response);
    }

    @isTest
    static void testDeleteResidenza() {
        // Creazione di un inputMap di esempio
        Map<String, Object> inputMap = new Map<String, Object>{
            'CodNazione' => 'IT',
            'Request' => new Map<String, Object>{
                'id' => 1,
                'compagnia' => 'unipolsai',
                'ciu' => 12345,
                'residenzeFiscaliEstere' => new List<Object>{
                    new Map<String, Object>{'residenzaFiscaleEstera' => 'IT', 'numeroIdentificazioneFiscale' => '*********'}
                },
                'cittadinanze' => new List<Object>{
                    new Map<String, Object>{'codice' => 'IT', 'descrizione' => 'ITALIA'}
                },
                'indirizzo' => new Map<String, Object>{
                    'ciu' => 12345,
                    'compagnia' => 'unipolsai',
                    'indirizzoBreve' => 'VIA CAPPUCCINI 20',
                    'localita' => 'MILANO',
                    'tipoNormalizzato' => 'S',
                    'tipoIndirizzo' => 'AMMI',
                    'numeroCivico' => '20',
                    'codiceBelfioreComune' => 'F205',
                    'codicePostale' => '20122',
                    'codiceIstatComune' => '003015146',
                    'abbreviazioneProvincia' => 'MI',
                    'codiceIstatAnag1' => '000000215',
                    'tipoOdonimo' => 'VIA',
                    'nomeOdonimo' => 'CAPPUCCINI',
                    'dus' => 'CAPPUCCINI',
                    'dug' => 'VIA',
                    'indirizzoCompleto' => 'VIA CAPPUCCINI 20',
                    'indirizzoBreveLegacy' => 'VIA CAPPUCCINI 20',
                    'latitudine' => 45.47066,
                    'longitudine' => 9.20539
                }   
            }
        };

        // Creazione di outMap e options vuoti
        Map<String, Object> outMap = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        // Invocazione del metodo deleteResidenza
        CheckDatiFatca instance = new CheckDatiFatca();
        instance.invokeMethod('deleteResidenza', inputMap, outMap, options);

        // Verifica che outMap contenga la chiave 'response'
        //System.assert(outMap.containsKey('response'), 'outMap should contain the key "response"');

        // Verifica che la risposta sia una stringa JSON valida
        String response = (String) outMap.get('response');
        //System.assertNotEquals(response, null, 'Response should not be null');
        //System.debug('Response: ' + response);
    }
    
    @isTest
static void testModifyFatcaPf() {
    
    Id accDet = [SELECT Id FROM AccountDetails__c LIMIT 1].Id;
    // Creazione di un inputMap di esempio
    Map<String, Object> inputMap = new Map<String, Object>{
        'Request' => new Map<String, Object>{
            'id' => 1,
            'compagnia' => 'unipolsai',
            'ciu' => 12345,
            'tipologiaSocietaFatca' => '0',
            'residenzaFiscaleUSA' => true,
            'cittadinanzaUSA' => true,
            'poteriFirmaUSA' => true,
            'tin' => '*********',
            'cittadinanze' => new List<Object>{
                new Map<String, Object>{'codice' => '239', 'descrizione' => 'SPAGNOLA'},
                new Map<String, Object>{'codice' => '215', 'descrizione' => 'FRANCESE'}
            },
            'residenzeFiscaliEstere' => new List<Object>{
                new Map<String, Object>{
                    'residenzaFiscaleEstera' => 'IT',
                    'numeroIdentificazioneFiscale' => '*********'
                }
            }
        },
        'AccountDetailId' => accDet,
        'CittadinanzaUSA' => true,
        'ResidenzaFiscaleUSA' => true,
        'PoteriFirmaUSA' => true,
        'TinUSA' => '*********',
        'ActualCittadinanze' => '239;215',
        'PoteriFirmaEstero' => true,
        'ResidenzaFiscaleEstero' => true,
        'AllCittadinanze' => new List<Object>{
            new Map<String, Object>{'codice' => '239', 'descrizione' => 'SPAGNOLA'},
            new Map<String, Object>{'codice' => '215', 'descrizione' => 'FRANCESE'}
        }
    };

    // Creazione di outMap e options vuoti
    Map<String, Object> outMap = new Map<String, Object>();
    Map<String, Object> options = new Map<String, Object>();

    // Simulazione di dati di esempio per AccountDetails__c
    Test.startTest();
    	
    // Invocazione del metodo modifyFatcaPf
    CheckDatiFatca instance = new CheckDatiFatca();
    instance.invokeMethod('modifyFatcaPf', inputMap, outMap, options);

    // Verifica che la risposta sia una stringa JSON valida
    String response = (String) outMap.get('response');
   
    System.debug('Response: ' + response);

    Test.stopTest();
}
    
    @isTest
    static void testModifyFatcaPg() {
    // Creazione di un inputMap di esempio
        Map<String, Object> inputMap = new Map<String, Object>{
            'Request' => new Map<String, Object>{
                'id' => 1,
                'compagnia' => 'unipolsai',
                'ciu' => 12345,
                'TipologiaSocietaFatca' => '3',
                'residenzaFiscaleUSA' => true,
                'cittadinanzaUSA' => true,
                'poteriFirmaUSA' => true,
                'Tin' => '*********',
                'Giin' => 'GIIN12.345as.df.ghj',
                'cittadinanze' => new List<Object>{
                    new Map<String, Object>{'codice' => '239', 'descrizione' => 'SPAGNOLA'},
                    new Map<String, Object>{'codice' => '215', 'descrizione' => 'FRANCESE'}
                },
                'residenzeFiscaliEstere' => new List<Object>{
                    new Map<String, Object>{
                        'residenzaFiscaleEstera' => 'IT',
                        'numeroIdentificazioneFiscale' => '*********'
                    }
                },
                'indirizzo' => new Map<String, Object>{
                    'ciu' => 12345,
                    'compagnia' => 'unipolsai',
                    'indirizzoBreve' => 'VIA CAPPUCCINI 20',
                    'localita' => 'MILANO',
                    'tipoNormalizzato' => 'S',
                    'tipoIndirizzo' => 'AMMI',
                    'numeroCivico' => '20',
                    'codiceBelfioreComune' => 'F205',
                    'codicePostale' => '20122',
                    'codiceIstatComune' => '003015146',
                    'abbreviazioneProvincia' => 'MI',
                    'codiceIstatAnag1' => '000000215',
                    'tipoOdonimo' => 'VIA',
                    'nomeOdonimo' => 'CAPPUCCINI',
                    'dus' => 'CAPPUCCINI',
                    'dug' => 'VIA',
                    'indirizzoCompleto' => 'VIA CAPPUCCINI 20',
                    'indirizzoBreveLegacy' => 'VIA CAPPUCCINI 20',
                    'latitudine' => 45.47066,
                    'longitudine' => 9.20539
                }
            },
            'IndirizzoNorm' => new Map<String, Object>{
                'codiceISTATRegione' => '003',
                'codiceUnipolRegione' => '04',
                'descrizioneRegione' => 'LOMBARDIA',
                'siglaProvincia' => 'MI',
                'codiceISTATProvincia' => '003015',
                'descrizioneProvincia' => 'MILANO',
                'codiceBelfioreComune' => 'F205',
                'codiceISTATComune' => '003015146',
                'descrizioneComune' => 'MILANO',
                'descrizioneLocalita' => 'MILANO',
                'tipoOdonimo' => 'VIA',
                'nomeOdonimo' => 'CAPPUCCINI',
                'civico' => '20',
                'indirizzoBreveLegacy' => 'VIA CAPPUCCINI 20',
                'latitudine' => 45.47066,
                'longitudine' => 9.20539
            },
            'ResidenzaFiscaleUsa' => true,
            'ResidenzaFiscaleStatoEstero' => false,
            'Tin' => '*********',
            'Giin' => 'GIIN12.345as.df.gty'
        };

        // Creazione di outMap e options vuoti
        Map<String, Object> outMap = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        // Invocazione del metodo modifyFatcaPg
        CheckDatiFatca instance = new CheckDatiFatca();
        instance.invokeMethod('modifyFatcaPg', inputMap, outMap, options);

    }

    @isTest
    static void testInsertFatcaPg() {
    // Creazione di un inputMap di esempio
        Map<String, Object> inputMap = new Map<String, Object>{
            'Request' => new Map<String, Object>{
                
                'compagnia' => 'unipolsai',
                'ciu' => 12345,
                'tipologiaSocietaFatca' => '3',
                'residenzaFiscaleUSA' => true,
                'cittadinanzaUSA' => true,
                'poteriFirmaUSA' => true,
                'tin' => '*********',
                'giin' => 'GIIN12.345as.df.gty',
                'cittadinanze' => new List<Object>{
                    new Map<String, Object>{'codice' => '239', 'descrizione' => 'SPAGNOLA'},
                    new Map<String, Object>{'codice' => '215', 'descrizione' => 'FRANCESE'}
                },
                'residenzeFiscaliEstere' => new List<Object>{
                    new Map<String, Object>{
                        'residenzaFiscaleEstera' => 'IT',
                        'numeroIdentificazioneFiscale' => '*********'
                    }
                },
                'indirizzo' => new Map<String, Object>{
                    'ciu' => 12345,
                    'compagnia' => 'unipolsai',
                    'indirizzoBreve' => 'VIA CAPPUCCINI 20',
                    'localita' => 'MILANO',
                    'tipoNormalizzato' => 'S',
                    'tipoIndirizzo' => 'AMMI',
                    'numeroCivico' => '20',
                    'codiceBelfioreComune' => 'F205',
                    'codicePostale' => '20122',
                    'codiceIstatComune' => '003015146',
                    'abbreviazioneProvincia' => 'MI',
                    'codiceIstatAnag1' => '000000215',
                    'tipoOdonimo' => 'VIA',
                    'nomeOdonimo' => 'CAPPUCCINI',
                    'dus' => 'CAPPUCCINI',
                    'dug' => 'VIA',
                    'indirizzoCompleto' => 'VIA CAPPUCCINI 20',
                    'indirizzoBreveLegacy' => 'VIA CAPPUCCINI 20',
                    'latitudine' => 45.47066,
                    'longitudine' => 9.20539
                }
            },
            'IndirizzoNorm' => new Map<String, Object>{
                'codiceISTATRegione' => '003',
                'codiceUnipolRegione' => '04',
                'descrizioneRegione' => 'LOMBARDIA',
                'siglaProvincia' => 'MI',
                'codiceISTATProvincia' => '003015',
                'descrizioneProvincia' => 'MILANO',
                'codiceBelfioreComune' => 'F205',
                'codiceISTATComune' => '003015146',
                'descrizioneComune' => 'MILANO',
                'descrizioneLocalita' => 'MILANO',
                'tipoOdonimo' => 'VIA',
                'nomeOdonimo' => 'CAPPUCCINI',
                'civico' => '20',
                'indirizzoBreveLegacy' => 'VIA CAPPUCCINI 20',
                'latitudine' => 45.47066,
                'longitudine' => 9.20539
            },
            'ResidenzaFiscaleUsa' => true,
            'ResidenzaFiscaleStatoEstero' => false,
            'Tin' => '*********',
            'Giin' => 'GIIN12.345as.df.ghj'
        };

        // Creazione di outMap e options vuoti
        Map<String, Object> outMap = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        // Invocazione del metodo insertFatcaPG
        CheckDatiFatca instance = new CheckDatiFatca();
        instance.invokeMethod('insertFatcaPG', inputMap, outMap, options);

    }
    
    @isTest
	static void testNestedClasses() {
    
    //
    CheckDatiFatca.Cittadinanza c1 = new CheckDatiFatca.Cittadinanza();
    c1.codice = '239';
    c1.descrizione = 'SPAGNOLA';
    CheckDatiFatca.Cittadinanza c2 = new CheckDatiFatca.Cittadinanza();
    c2.codice = '215'; 
    c2.descrizione = 'FRANCESE';   
    List<CheckDatiFatca.Cittadinanza> listCitt = new List<CheckDatiFatca.Cittadinanza>();
    listCitt.add(c1);
    listCitt.add(c2);
    // Test della classe DatiFatcaPF
    CheckDatiFatca.DatiFatcaPF datiFatcaPF = new CheckDatiFatca.DatiFatcaPF();
    datiFatcaPF.Ciu = '123456';
    datiFatcaPF.CodiceFiscale = 'ABCDEF123456';
    datiFatcaPF.CittadinanzaUSA = true;
    datiFatcaPF.PaeseDiNascita = 'USA';
    datiFatcaPF.PaeseDiResidenza = 'ITALIA';
    datiFatcaPF.TinUSA = '*********';
    datiFatcaPF.Cittadinanze = listCitt;
    System.assertEquals('123456', datiFatcaPF.Ciu);
    System.assertEquals('ABCDEF123456', datiFatcaPF.CodiceFiscale);
    System.assertEquals(true, datiFatcaPF.CittadinanzaUSA);

    CheckDatiFatca.Estero es = new CheckDatiFatca.Estero();
    es.CodiceABI = '123';
	es.Nazione = 'FRANCIA';
    es.NumeroIdentificazioneFiscale = '*********L';
    es.FlagIdentificazione = true;
    es.Iterazioni = 1;
    es.Attivo = true;
    es.UnioneEuropea = true;
    es.CodiceBelfiore = 'F205';
    es.CodiceBelfioreOld = 'F205';
    es.Ciu = '123456';
    es.Compagnia = 'unipolsai';
    
    List<CheckDatiFatca.Estero> listEst = new List<CheckDatiFatca.Estero>();
	listEst.add(es);
    
    // Test della classe DatiFatcaPG
    CheckDatiFatca.DatiFatcaPG datiFatcaPG = new CheckDatiFatca.DatiFatcaPG();
    datiFatcaPG.Ciu = '654321';
    datiFatcaPG.TipologiaSocietaFatca = '3';
    datiFatcaPG.ResidenzaFiscaleUsa = true;
    datiFatcaPG.Estero = listEst; 
    datiFatcaPG.IndirizzoBreve = '';
    datiFatcaPG.Stato = '';
    datiFatcaPG.Provincia = '';
    datiFatcaPG.CellaCensuaria = '';
    datiFatcaPG.TipoIndirizzo ='';
    datiFatcaPG.FlagSedeLegale = false;
    datiFatcaPG.CodiceBelfioreComune = '';
    datiFatcaPG.Comune = '';
    datiFatcaPG.Localita = '';
    datiFatcaPG.Giin = '';
    datiFatcaPG.SiglaProvincia = '';
    datiFatcaPG.NumeroCivico = '';
    datiFatcaPG.FlagGiin = false;
    datiFatcaPG.MinLenghtGiin = 1;
    datiFatcaPG.MinLenghtTin = 2;    
    System.assertEquals('654321', datiFatcaPG.Ciu);
    System.assertEquals('3', datiFatcaPG.TipologiaSocietaFatca);
    System.assertEquals(true, datiFatcaPG.ResidenzaFiscaleUsa);

    // Test della classe RequestBodyFatca
    CheckDatiFatca.RequestBodyFatca requestBodyFatca = new CheckDatiFatca.RequestBodyFatca();
    requestBodyFatca.id = 1;
    requestBodyFatca.compagnia = 'unipolsai';
    requestBodyFatca.ciu = 12345;
    requestBodyFatca.tipologiaSocietaFatca = '0';
    requestBodyFatca.residenzaFiscaleUSA = true;
    requestBodyFatca.cittadinanzaUSA = true;
    requestBodyFatca.cittadinanze = listCitt;
    System.assertEquals(1, requestBodyFatca.id);
    System.assertEquals('unipolsai', requestBodyFatca.compagnia);
    System.assertEquals(12345, requestBodyFatca.ciu);

    

    // Test della classe Estero
    CheckDatiFatca.Estero estero = new CheckDatiFatca.Estero();
    estero.CodiceABI = '123';
    estero.Nazione = 'FRANCIA';
    estero.NumeroIdentificazioneFiscale = '*********L';
    estero.FlagIdentificazione = true;
    System.assertEquals('123', estero.CodiceABI);
    System.assertEquals('FRANCIA', estero.Nazione);
    //System.assertEquals(*********L, estero.NumeroIdentificazioneFiscale);
    System.assertEquals(true, estero.FlagIdentificazione);
        
    // Creazione di un'istanza della classe Indirizzo
    CheckDatiFatca.Indirizzo indirizzo = new CheckDatiFatca.Indirizzo();
    
    // Assegnazione di valori ai campi
    indirizzo.ciu = 12345;
    indirizzo.compagnia = 'unipolsai';
    indirizzo.id = 56789;
    indirizzo.distrettoCensuario = 'S0151460000504';
    indirizzo.latitudine = 45.47066;
    indirizzo.longitudine = 9.20539;
    indirizzo.presso = 'C/O BAR';
    indirizzo.indirizzoBreve = 'VIA CAPPUCCINI 20';
    indirizzo.tipoNormalizzato = 'N';
    indirizzo.indirizzoCompleto = 'VIA CAPPUCCINI';
    indirizzo.dus = 'CAPPUCCINI';
    indirizzo.numeroCivico = '20';
    indirizzo.dug = 'VIA';
    indirizzo.localita = 'MILANO';
    indirizzo.codiceBelfioreComune = 'F205';
    indirizzo.codicePostale = '20122';
    indirizzo.codiceIstatComune = '003015146';
    indirizzo.abbreviazioneProvincia = 'MI';
    indirizzo.codiceIstatAnag1 = '000000215';
    indirizzo.tipoIndirizzo = 'AMMI';
    indirizzo.codiceBelfioreStato = 'F205';    
        
    // Creazione di un'istanza della classe DatiTracciatura
    CheckDatiFatca.DatiTracciatura datiTracciatura = new CheckDatiFatca.DatiTracciatura();

    // Assegnazione di valori ai campi
    datiTracciatura.proceduraChiamante = 'PU00A';
    datiTracciatura.applicazioneChiamante = 'PU';
    datiTracciatura.sottosistemaAggiornamento = 'A0022';
    datiTracciatura.sistemaAggiornamento = 'A17';
    datiTracciatura.compagniaAggiornamento = '1';
    datiTracciatura.canaleAggiornamento = 'A01';
    datiTracciatura.sottosistemaCreazione = 'A0022';
    datiTracciatura.sistemaCreazione = 'A17';
    datiTracciatura.compagniaCreazione = '1';
    datiTracciatura.canaleCreazione = 'A01';
    datiTracciatura.usernameUltimoAggiornamento = 'DEFAULT DEFAULT';
    datiTracciatura.userIdUltimoAggiornamento = 'SFDC';
    datiTracciatura.dataUltimoAggiornamento = '2025-02-26T12:58:58.980259';
    datiTracciatura.usernameCreazione = 'DEFAULT DEFAULT';
    datiTracciatura.userIdCreazione = 'SFDC';
    datiTracciatura.dataCreazione = '2025-02-26T12:58:58.980259';
        
    // Creazione di un'istanza della classe IndirizzoNormWrapper
    CheckDatiFatca.IndirizzoNormWrapper indirizzoNorm = new CheckDatiFatca.IndirizzoNormWrapper();

    // Assegnazione di valori ai campi
    indirizzoNorm.codiceISTATRegione = '003';
    indirizzoNorm.codiceUnipolRegione = '04';
    indirizzoNorm.descrizioneRegione = 'LOMBARDIA';
    indirizzoNorm.siglaProvincia = 'MI';
    indirizzoNorm.codiceISTATProvincia = '003015';
    indirizzoNorm.descrizioneProvincia = 'MILANO';
    indirizzoNorm.codiceBelfioreComune = 'F205';
    indirizzoNorm.codiceISTATComune = '003015146';
    indirizzoNorm.codiceCABComune = '01600-6';
    indirizzoNorm.descrizioneComune = 'MILANO';
    indirizzoNorm.descrizioneLocalita = 'MILANO';
    indirizzoNorm.descrizioneLocalitaAbbreviata = 'MILANO';
    indirizzoNorm.tipoOdonimo = 'VIA';
    indirizzoNorm.tipoOdonimoAbbreviato = 'V.';
    indirizzoNorm.nomeOdonimo = 'CAPPUCCINI';
    indirizzoNorm.nomeOdonimoAbbreviato = 'CAPPUCCINI';
    indirizzoNorm.civico = '20';
    indirizzoNorm.indirizzoBreveLegacy = 'VIA CAPPUCCINI 20';
    indirizzoNorm.codiceTerritoriale = '20122';
    indirizzoNorm.latitudine = 45.47066;
    indirizzoNorm.longitudine = 9.20539;
    indirizzoNorm.accuratezza = 80;
    indirizzoNorm.cellaCensuaria = 'S0151460000504';
    indirizzoNorm.StatusCode = null;    

    // Creazione di un'istanza della classe StatoEstero
    CheckDatiFatca.StatoEstero statoEstero = new CheckDatiFatca.StatoEstero();
    statoEstero.CodiceABI = 'IT';
    statoEstero.Descrizione = 'ITALIA';
    statoEstero.CodiceBelfiore = 'F205';
    statoEstero.Attivo = true;
    statoEstero.Ue = true;
    

	}

    @isTest
    static void testModifyResidenza() {
        // Creazione di un inputMap di esempio
        Map<String, Object> inputMap = new Map<String, Object>{
            'Request' => new Map<String, Object>{
                'id' => 1,
                'compagnia' => 'unipolsai',
                'ciu' => 12345,
                'residenzaFiscaleUSA' => true,
                'tipologiaSocietaFatca' => '0',
                'cittadinanzaUSA' => true,
                'poteriFirmaUSA' => true,
                'tin' => '*********',
                'cittadinanze' => new List<Object>{
                    new Map<String, Object>{'codice' => '239', 'descrizione' => 'SPAGNOLA'},
                    new Map<String, Object>{'codice' => '215', 'descrizione' => 'FRANCESE'}
                },
                'residenzeFiscaliEstere' => new List<Object>{
                    new Map<String, Object>{
                        'residenzaFiscaleEstera' => 'IT',
                        'numeroIdentificazioneFiscale' => '*********'
                    },
                    new Map<String, Object>{
                        'residenzaFiscaleEstera' => 'FR',
                        'numeroIdentificazioneFiscale' => '*********'
                    }
                },
                'indirizzo' => new Map<String, Object>{
                    'ciu' => 12345,
                    'compagnia' => 'unipolsai',
                    'indirizzoBreve' => 'VIA CAPPUCCINI 20',
                    'localita' => 'MILANO',
                    'tipoNormalizzato' => 'S',
                    'tipoIndirizzo' => 'AMMI',
                    'numeroCivico' => '20',
                    'codiceBelfioreComune' => 'F205',
                    'codicePostale' => '20122',
                    'codiceIstatComune' => '003015146',
                    'abbreviazioneProvincia' => 'MI',
                    'codiceIstatAnag1' => '000000215',
                    'tipoOdonimo' => 'VIA',
                    'nomeOdonimo' => 'CAPPUCCINI',
                    'dus' => 'CAPPUCCINI',
                    'dug' => 'VIA',
                    'indirizzoCompleto' => 'VIA CAPPUCCINI 20',
                    'indirizzoBreveLegacy' => 'VIA CAPPUCCINI 20',
                    'latitudine' => 45.47066,
                    'longitudine' => 9.20539
                }
            },
            'CodNazione' => 'IT',
            'NumeroIdentificazioneFiscale' => '*********'
        };

        // Creazione di outMap e options vuoti
        Map<String, Object> outMap = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        // Simulazione di dati di esempio per AccountDetails__c
        Id accDet = [SELECT Id FROM AccountDetails__c LIMIT 1].Id;

        // Aggiunta dell'ID di AccountDetails__c all'inputMap
        inputMap.put('AccountDetailId', accDet);

        // Invocazione del metodo modifyResidenza
        Test.startTest();
        CheckDatiFatca instance = new CheckDatiFatca();
        instance.invokeMethod('modifyResidenza', inputMap, outMap, options);
        Test.stopTest();
    }

    @isTest
    static void testModifyResidenza2() {
        // Creazione di un inputMap di esempio
        Map<String, Object> inputMap = new Map<String, Object>{
            'Request' => new Map<String, Object>{
                'id' => 1,
                'compagnia' => 'unipolsai',
                'ciu' => 12345,
                'residenzaFiscaleUSA' => true,
                'tipologiaSocietaFatca' => '0',
                'cittadinanzaUSA' => true,
                'poteriFirmaUSA' => true,
                'tin' => '*********',
                'cittadinanze' => new List<Object>{
                    new Map<String, Object>{'codice' => '239', 'descrizione' => 'SPAGNOLA'},
                    new Map<String, Object>{'codice' => '215', 'descrizione' => 'FRANCESE'}
                },
                'residenzeFiscaliEstere' => new List<Object>{
                    new Map<String, Object>{
                        'residenzaFiscaleEstera' => 'IT',
                        'numeroIdentificazioneFiscale' => '*********'
                    },
                    new Map<String, Object>{
                        'residenzaFiscaleEstera' => 'FR',
                        'numeroIdentificazioneFiscale' => '*********'
                    }
                },
                'indirizzo' => new Map<String, Object>{
                    'ciu' => 12345,
                    'compagnia' => 'unipolsai',
                    'indirizzoBreve' => 'VIA CAPPUCCINI 20',
                    'localita' => 'MILANO',
                    'tipoNormalizzato' => 'S',
                    'tipoIndirizzo' => 'AMMI',
                    'numeroCivico' => '20',
                    'codiceBelfioreComune' => 'F205',
                    'codicePostale' => '20122',
                    'codiceIstatComune' => '003015146',
                    'abbreviazioneProvincia' => 'MI',
                    'codiceIstatAnag1' => '000000215',
                    'tipoOdonimo' => 'VIA',
                    'nomeOdonimo' => 'CAPPUCCINI',
                    'dus' => 'CAPPUCCINI',
                    'dug' => 'VIA',
                    'indirizzoCompleto' => 'VIA CAPPUCCINI 20',
                    'indirizzoBreveLegacy' => 'VIA CAPPUCCINI 20',
                    'latitudine' => 45.47066,
                    'longitudine' => 9.20539
                }
            },
            'CodNazione' => 'IT',
            'NumeroIdentificazioneFiscale' => '-',
            'FlagIdentificazione' => true
        };

        // Creazione di outMap e options vuoti
        Map<String, Object> outMap = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        // Simulazione di dati di esempio per AccountDetails__c
        Id accDet = [SELECT Id FROM AccountDetails__c LIMIT 1].Id;

        // Aggiunta dell'ID di AccountDetails__c all'inputMap
        inputMap.put('AccountDetailId', accDet);

        // Invocazione del metodo modifyResidenza
        Test.startTest();
        CheckDatiFatca instance = new CheckDatiFatca();
        instance.invokeMethod('modifyResidenza', inputMap, outMap, options);
        Test.stopTest();
    }

    @isTest
    static void testInsertFatcaPF() {
        // Creazione di un inputMap di esempio
        Map<String, Object> inputMap = new Map<String, Object>{
            'Request' => new Map<String, Object>{
                'id' => 1,
                'compagnia' => 'unipolsai',
                'ciu' => 12345,
                'tipologiaSocietaFatca' => '0',
                'residenzaFiscaleUSA' => true,
                'cittadinanzaUSA' => true,
                'poteriFirmaUSA' => true,
                'tin' => '*********',
                'cittadinanze' => new List<Object>{
                    new Map<String, Object>{'codice' => '239', 'descrizione' => 'SPAGNOLA'},
                    new Map<String, Object>{'codice' => '215', 'descrizione' => 'FRANCESE'}
                },
                'residenzeFiscaliEstere' => new List<Object>{
                    new Map<String, Object>{
                        'residenzaFiscaleEstera' => 'IT',
                        'numeroIdentificazioneFiscale' => '*********'
                    },
                    new Map<String, Object>{
                        'residenzaFiscaleEstera' => 'FR',
                        'numeroIdentificazioneFiscale' => '*********'
                    }
                }
            },
            'CodNazione' => 'IT',
            'NumeroIdentificazioneFiscale' => '*********'
        };

        // Creazione di outMap e options vuoti
        Map<String, Object> outMap = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        // Simulazione di dati di esempio per AccountDetails__c
        Id accDet = [SELECT Id FROM AccountDetails__c LIMIT 1].Id;

        // Aggiunta dell'ID di AccountDetails__c all'inputMap
        inputMap.put('AccountDetailId', accDet);

        // Invocazione del metodo insertFatcaPF
        Test.startTest();
        CheckDatiFatca instance = new CheckDatiFatca();
        instance.invokeMethod('insertFatcaPF', inputMap, outMap, options);
        Test.stopTest();
    }
}