/**
 * @File Name         : AnagraficaTest.cls
 * @Description       :
 * <AUTHOR> <EMAIL>
 * @Group             :
 * @Last Modified On  : 03-02-2025
 * @Last Modified By  : <EMAIL>
**/
@isTest
public with sharing class AnagraficaTest {
    @isTest
    static void getAnagraficaTest() {
        Test.startTest();
        Object result = AnagraficaController.getAnagrafica(new Map<String, Object>());
        Test.stopTest();
        System.assertNotEquals(null, result, 'getAnagrafica deve restituire un risultato');
    }

    @isTest
    static void getGetCompagnieTest() {
        Test.startTest();
        Object result = AnagraficaController.getGetCompagnie();
        Test.stopTest();
        System.assertNotEquals(null, result, 'getGetCompagnie deve restituire un risultato');
    }

    @isTest
    static void getTipoFormaSocietariaTest() {
        Test.startTest();
        Object result = AnagraficaController.getTipoFormaSocietaria();
        Test.stopTest();
        System.assertNotEquals(null, result, 'getTipoFormaSocietaria deve restituire un risultato');
    }

    @isTest
    static void aggiornamentoAnagraficaTest() {
        Test.startTest();
        Object result = AnagraficaController.aggiornamentoAnagrafica(new Map<String, Object>());
        Test.stopTest();
        System.assertNotEquals(null, result, 'aggiornamentoAnagrafica deve restituire un risultato');
    }
    
    @isTest
    static void getAtecoPrimarioTest() {
        Test.startTest();
        Map<String, Object> input = new Map<String, Object>();
        input.put('descrizione', 'Test');
        Object result = AnagraficaController.getAtecoPrimario(input);
        Test.stopTest();
        System.assertNotEquals(null, result, 'getAtecoPrimario deve restituire un risultato');
    }
    
    @isTest
    static void getRelatedAccountFromPersonalAccountTest() {
        Account personalAcc = new Account(
            FirstName = 'Pas', LastName = 'iall',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('PersonAccount').getRecordTypeId(), 
            ExternalId__c = 'LLNPQL78S23A509R'
        );
        insert personalAcc;

        // Crea account society
        Account societyAcc = new Account(
            Name = 'Scoietà 1', 
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId(),  
            ExternalId__c = 'SOC_1'
        );
        insert societyAcc;
        
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(Name = 'Cliente', FinServ__InverseRole__c = 'Agenzia');
        insert role;

        // Crea relazione tra i due account
        FinServ__AccountAccountRelation__c relation = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = personalAcc.Id,
            FinServ__RelatedAccount__c = societyAcc.Id,
            FinServ__Role__c = role.Id
        );
        insert relation;

        // Prepara input e chiama il metodo
        Map<String, Object> input = new Map<String, Object>();
        input.put('accountId', personalAcc.Id);

        Test.startTest();
        List<Map<String, Object>> result = AnagraficaController.getRelatedAccountFromPersonalAccount(input);
        Test.stopTest();

        System.assertNotEquals(null, result, 'Il risultato non deve essere null');
        System.assert(result.size() > 0, 'Deve esserci almeno una relazione restituita');
    }

    @isTest
    static void getAccountDetailsTest() {
        Id societyRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId();
		
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(Name = 'Cliente', FinServ__InverseRole__c = 'Agenzia');
        insert role;

        List<Account> accListToInsert = new List<Account>();
        Account accountCliente1 = new Account(
            FirstName = 'Cliente', LastName = '1',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('PersonAccount').getRecordTypeId(), 
            ExternalId__c = 'Cliente1'
        );
        accListToInsert.add(accountCliente1);
		Account accountSociety1 = new Account(
            Name = 'Scoietà 1', RecordTypeId = societyRecordTypeId,  
            ExternalId__c = 'SOC_1'
        );
		accListToInsert.add(accountSociety1);
		insert accListToInsert;
		
        FinServ__AccountAccountRelation__c accAccRelClienteSociety1 = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = accountCliente1.Id,
            FinServ__RelatedAccount__c = accountSociety1.Id,
            FinServ__Role__c = role.Id
            //RecordTypeId = AccountSocietyRecordTypeId
        );
		insert accAccRelClienteSociety1;
		
		AccountDetails__c accDetails1 = new AccountDetails__c(
        	Relation__c = accAccRelClienteSociety1.Id, SourceSystemIdentifier__c = '7174180', 
            FeaMail__c = '<EMAIL>', FeaMobile__c = '+39 **********',
            Country__c = 'ITA', FiscalCode__c = '****************', Street__c = 'Via del Campo',
            PostalCode__c = '20900', City__c = 'Firenze', State__c = 'TO', Mobile__c = '+39 *********',
            Email__c = '<EMAIL>',FiscalCodeStatus__c = 'N'
        );
		
		insert accDetails1;

        Map<String, Object> data = new Map<String, Object>();
        data.put('recordId', accAccRelClienteSociety1.Id);

        Test.startTest();
        Map<String, Object> result = AnagraficaController.getAccountDetails(data);
        Test.stopTest();

        System.assertEquals(accDetails1.Id, result.get('Id'));
        System.assertEquals(accDetails1.FiscalCode__c, result.get('FiscalCode'));
        System.assertEquals(accDetails1.FiscalCodeStatus__c, result.get('FiscalCodeStatus'));

    }

    @isTest
    static void clonazioneAnagraficaTest() {
        Test.startTest();
        try {
            Map<String, Object> input = new Map<String, Object>();
            input.put('test', 'value');
            Object result = AnagraficaController.clonazioneAnagrafica(input);
            System.assertNotEquals(null, result);
        } catch(Exception e) {}
        Test.stopTest();
    }

    @isTest
    static void getCodiciGestoreTest() {
        Test.startTest();
        try {
            Object result = AnagraficaController.getCodiciGestore();
            System.assertNotEquals(null, result);
        } catch(Exception e) {}
        Test.stopTest();
    }

    @isTest
    static void getAccountDetailsFromPersonalAccountTest() {
        // Recupera RecordType per società
        Id societyRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId();
    
        // Crea ruolo reciproco
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(
            Name = 'Cliente',
            FinServ__InverseRole__c = 'Agenzia'
        );
        insert role;
    
        // Crea account cliente e società
        Account accountCliente1 = new Account(
            FirstName = 'Mario',
            LastName = 'Rossi',
            CF__c = '****************',
            VatNumber__c = 'VAT123',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('PersonAccount').getRecordTypeId(), 
            ExternalId__c = 'Cliente1'
        );
    
        Account accountSociety1 = new Account(
            Name = 'Società 1',
            RecordTypeId = societyRecordTypeId,  
            ExternalId__c = 'SOC_1'
        );
    
        insert new List<Account>{ accountCliente1, accountSociety1 };
    
        // Crea relazione tra cliente e società
        FinServ__AccountAccountRelation__c accAccRelClienteSociety1 = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = accountCliente1.Id,
            FinServ__RelatedAccount__c = accountSociety1.Id,
            FinServ__Role__c = role.Id
        );
        insert accAccRelClienteSociety1;
    
        // Crea dettagli account
        AccountDetails__c accDetails1 = new AccountDetails__c(
            Relation__c = accAccRelClienteSociety1.Id,
            SourceSystemIdentifier__c = '7174180',
            FeaMail__c = '<EMAIL>',
            FeaMobile__c = '+39 **********',
            Country__c = 'ITA',
            FiscalCode__c = '****************',
            Street__c = 'Via del Campo',
            PostalCode__c = '20900',
            City__c = 'Firenze',
            State__c = 'TO',
            Mobile__c = '+39 *********',
            Email__c = '<EMAIL>',
            FiscalCodeStatus__c = 'N'
        );
        insert accDetails1;
    
        // Prepara input
        Map<String, Object> data = new Map<String, Object>();
        data.put('accountId', accountCliente1.Id);
    
        // Simula risposta di getAnagrafica (mock manuale)
        Test.startTest();
        Map<String, Object> result;
        try {
            result = AnagraficaController.getAccountDetailsFromPersonalAccount(data);
        } catch (Exception e) {
            System.debug('Errore: ' + e.getMessage());
        }
        Test.stopTest();
    
        // Asserzioni
        System.assertNotEquals(null, result);
        System.assertEquals('Mario', result.get('Nome'));
        System.assertEquals('Rossi', result.get('Cognome'));
        System.assertEquals('****************', result.get('CF'));
        System.assertEquals('VAT123', result.get('PIVA'));
    
        // Se il metodo getAnagrafica restituisce dati, verifica anche quelli
        if (result.containsKey('data')) {
            Map<String, Object> dataMap = (Map<String, Object>) result.get('data');
            System.assertNotEquals(null, dataMap);
            // Puoi aggiungere qui asserzioni specifiche se conosci la struttura di 'modifica'
        }
    }

    @isTest
    static void getTipoFormaSocietariaCloneTest() {
        Test.startTest();
        try {
            Object result = AnagraficaController.getTipoFormaSocietariaClone();
            System.assertNotEquals(null, result);
        } catch(Exception e) {}
        Test.stopTest();
    }

    @isTest
    static void getSaeCodeAvailableTest() {
        Test.startTest();
        try {
            Map<String, Object> result = AnagraficaController.getSaeCodeAvailable('ATECO123');
            System.assertNotEquals(null, result);
        } catch(Exception e) {}
        Test.stopTest();
    }
}