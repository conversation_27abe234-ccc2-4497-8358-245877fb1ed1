public with sharing class ConiAssignmentServiceBatch {
    private static final String OPPORTUNITY_SHARE = System.label.ConiOpportunityShare;
    private static final String INSURANCE_POLICY_SHARE = System.label.ConiInsurancePolicyShare;
    private static final String CASE_SHARE = System.label.ConiCaseShare;
    private static final String SERVICE_APPOINTMENT_SHARE = System.label.ConiServiceAppointmentShare;
    private static final String ID = System.label.ConiId;
    private static final String SHARE = System.label.ConiShare;
    private static final String USER_OR_GROUP = System.label.ConiUserOrGroup;
    private static final String NAME = System.label.ConiName;
    private static final String EDIT = System.label.ConiEdit;
    private static final String READ = System.label.ConiRead;
    private static final String GRUPPO_REFERENTI = System.label.ConiGruppoReferenti;
    private static final String USER_OR_GROUP_ID = System.label.ConiUserOrGroupId;
    private static final String ACCOUNT_ID = System.label.ConiAccountId;
    private static final String ASSIGNED_TO = System.label.ConiAssignedTo;
    private static final String ASSIGNED_GROUP = System.label.ConiAssignedGroup;
    private static final String GROUP_OBJ = System.label.ConiGroup;
    private static final String AGENCY_FIELD = System.label.ConiAgency;
    private static final String IS_SET_REF = System.label.ConiIsSetRef;

    //Key
    private static final String ACCESS_LEVEL_KEY = System.label.ConiAccessLevelKey;
    private static final String ACCOUNT_KEY = System.label.ConiAccountKey;
    private static final String EDIT_MAP_KEY = System.label.ConiEditMapKey;
    private static final String READ_MAP_KEY = System.label.ConiReadMapKey;
    private static final String ID_KEY = System.label.ConiIdKey;
    private static final String OBJ_NAME = System.label.ConiObjNameKey;
    private static final String REF_UPDATE_ID = System.label.ConiRefUpdateIdKey;

    //Value
    private static final String OPPORTUNITY_ACCESS_LEVEL_VALUE = System.label.ConiOpportunityAccessLevelValue;
    private static final String ACCESS_LEVEL_VALUE = System.label.ConiAccessLevelValue;
    private static final String CASE_ACCESS_LEVEL_VALUE = System.label.ConiCaseAccessLevelValue;

    //Query
    private static final String CONI_ACCOUNT_VALUE = System.label.ConiConiAccountValue;
    private static final String CONI_OBJECT_SHARE = System.label.ConiConiObjectShare;
    private static final String QUERY_FIELD = System.label.ConiQueryField;
    private static final String QUERY_OBJ_SHARE = System.label.ConiQueryObjShare;

    public static Map<String, Map<String, String>> fieldNameMap = new Map<String, Map<String, String>>{
        OPPORTUNITY_SHARE => new Map<String, String>{ ACCESS_LEVEL_KEY => OPPORTUNITY_ACCESS_LEVEL_VALUE, ID_KEY => System.Label.OpportunityId },
        INSURANCE_POLICY_SHARE => new Map<String, String>{ ACCESS_LEVEL_KEY => ACCESS_LEVEL_VALUE, ID_KEY => System.Label.ParentId },
        CASE_SHARE => new Map<String, String>{ ACCESS_LEVEL_KEY => CASE_ACCESS_LEVEL_VALUE, ID_KEY => System.Label.CaseId },
        SERVICE_APPOINTMENT_SHARE => new Map<String, String>{ ACCESS_LEVEL_KEY => ACCESS_LEVEL_VALUE, ID_KEY => System.Label.ParentId }
    };
    public static Map<String, Schema.SObjectType> sObjectMap = Schema.getGlobalDescribe();
    public static Map<String, Set<String>> mapStringARR = ConiAssignmentService.mapStringARR;

    /*public static Map<Id, Set<Id>> getAccountIdToMatchedUserOrGroupIds(Set<Id> accountList) {
        // QUERY to check if there are matches for the accounts related to the opportunities in the trigger.
        // TODO CHANGE QUERY with CustomMetadata
        Query__mdt query = [SELECT Query__c FROM Query__mdt WHERE QualifiedApiName = :CONI_ACCOUNT_VALUE];
        List<Account> accounts = Database.query(query.Query__c);
        //System.debug('>>>> accounts = ' + accounts);

        Map<Id, Set<Id>> accountIdToMatchedUserOrGroupIds = new Map<Id, Set<Id>>();
        // Names of the child relationships whose shares should be retrieved
        String strAux = System.Label.ChildObjectNames;
        List<String> childObjectNames = strAux.split(',');

        // Building the map that will have the Account Id as the key and the set of IDs of the matched UsersOrGroups as the value.
        for (Account acc : accounts) {
            Set<Id> matchedUserOrGroupIds = new Set<Id>();
            // For each child object, iterate over its records, get those records' shares, and add them to the shares list
            for (String childObjectName : childObjectNames) {
                for (SObject childRecord : acc.getSObjects(childObjectName)) {
                    for (Sobject childRecordShare : childRecord.getSObjects(System.Label.Shares)) {
                        matchedUserOrGroupIds.add((Id) childRecordShare.get(System.Label.UserOrGroupId));
                    }
                }
            }
            if (!matchedUserOrGroupIds.isEmpty()) {
                accountIdToMatchedUserOrGroupIds.put(acc.Id, matchedUserOrGroupIds);
            }
        }
        return accountIdToMatchedUserOrGroupIds;
    }

    public static void performShares(List<Case> listCase) {
        Set<Id> setIdAccount = new Set<Id>();
        for (Case c : listCase) {
            setIdAccount.add(c.AccountId);
        }
    }*/

    public static Boolean performShares(List<SObject> newSobjLst, Map<Id, SObject> newSobjMap) {
        if (ConiQueRefUpdate.isQueueableUpdate) {
            System.debug('>>>> performShares in queuable');
            return true;
        }

        System.debug('>>>> performShares: ' + System.now());
        Set<Id> setAccsObj = new Set<Id>();
        List<SObject> lstAux = new List<SObject>();
        Map<Id, Map<String, String>> mapAux = new Map<Id, Map<String, String>>();
        Set<Id> setAccountId = new Set<Id>();
        Map<Id, Id> groupUserIds = new Map<Id, Id>();
        Boolean isReassignment = false;
        Set<Id> refUpdateId = new Set<Id>();

        Map<String, String> fields;
        String objShareName;
        String objName;

        if (!newSobjLst.isEmpty()) {
            //AC: assegno il nome e i campi
            objName = newSobjLst.get(0).getSObjectType().getDescribe().getName();
            objShareName = objName + SHARE;
            fields = fieldNameMap.get(objShareName);
            //Map<Id, Id> grpMap = new Map<Id, Id>();
            Map<Id, Set<Id>> grpMap = new Map<Id, Set<Id>>();
            for (SObject sObj : newSobjLst) {
                if (sObj.get(AGENCY_FIELD) != null) {
                    //AC: in fase di inserimetno creo il referente
                    SObject obEdit = ConiAssignmentService.setReferentsGroup((Boolean) sObj.get(IS_SET_REF), (Id) sObj.get(AGENCY_FIELD), (Id) sObj.get(ID), objShareName);
                    if (obEdit != null) {
                        //System.debug('>>>> lstAux.add: ' + obEdit);
                        lstAux.add(obEdit);
                        mapAux.put((Id) sObj.get(ID), ConiAssignmentService.addMapAux(fields, System.Label.EditAccessLevel, (Id) sObj.get(ID), (Id) obEdit.get(System.Label.UserOrGroupId)));
                        refUpdateId.add((Id) sObj.get(ID));
                    }
                    //AC: verificare anche la nuova sObj abbia l'assegnatario diverso da old
                    if (sObj.get(ASSIGNED_GROUP) != null) {
                        //SObject objEdit = getObject(System.Label.EditAccessLevel, (Id) sObj.get(ID), (Id) sObj.get(ASSIGNED_GROUP));
                        // Add the new share to the list of shares to be inserted
                        //lstAux.add(objEdit);
                        // Put the Opportunity Id and the EDIT share record in the Aux map
                        //mapAux.put((Id) sObj.get(ID), addMapAux(fields, System.Label.EditAccessLevel, (Id) sObj.get(ID), (Id) sObj.get(ASSIGNED_GROUP)));

                        if (!grpMap.containsKey((Id) sObj.get(ASSIGNED_GROUP))) {
                            Set<Id> objIds = new Set<Id>{ (Id) sObj.get(ID) };
                            grpMap.put((Id) sObj.get(ASSIGNED_GROUP), objIds);
                        } else {
                            grpMap.get((Id) sObj.get(ASSIGNED_GROUP)).add((Id) sObj.get(ID));
                        }

                        //grpMap.put((Id) sObj.get(ASSIGNED_GROUP), (Id)sObj.get(ID));
                    }
                    //AC: verificare anche la nuova sObj abbia l'assegnatario diverso da old
                    else if (sObj.get(ASSIGNED_TO) != null) {
                        SObject objEdit = ConiAssignmentService.getObject(System.Label.EditAccessLevel, (Id) sObj.get(ID), (Id) sObj.get(ASSIGNED_TO));
                        // Add the new share to the list of shares to be inserted
                        // Add the Opportunity Id and the EDIT share record in the Aux map
                        //System.debug('>>>> lstAux.add: ' + objEdit);
                        lstAux.add(objEdit);
                        mapAux.put((Id) sObj.get(ID), ConiAssignmentService.addMapAux(fields, System.Label.EditAccessLevel, (Id) sObj.get(ID), (Id) sObj.get(ASSIGNED_TO)));
                    }
                    //System.debug('>>>> mapAux = ' + mapAux);
                    if (!setAccsObj.contains((Id) sObj.get(ACCOUNT_ID))) {
                        setAccsObj.add((Id) sObj.get(ACCOUNT_ID));
                    }
                }
            }
            Map<Id, Id> grpId = new Map<Id, Id>();
            Map<Id, Id> grpIdEdit = new Map<Id, Id>();
            if (!grpMap.isEmpty()) {
                List<Group__c> grp = [SELECT Id, PublicGroupId__c FROM Group__c WHERE Id IN :grpMap.keySet()];
                for (Group__c g : grp) {
                    Set<Id> objIds = grpMap.get(g.Id);
                    for (Id currObjId : objIds) {
                        grpId.put(currObjId, g.PublicGroupId__c);
                    }
                    //grpId.put(grpMap.get(g.Id), g.PublicGroupId__c);
                    grpIdEdit.put(g.Id, g.PublicGroupId__c);
                }
                for (Id gId : grpId.keySet()) {
                    //System.debug('>>> gId: ' + grpId.get(gId));
                    SObject objEditGrp = ConiAssignmentService.getObject(System.Label.EditAccessLevel, gId, grpId.get(gId));
                    // Add the new share to the list of shares to be inserted
                    lstAux.add(objEditGrp);
                    // Put the Opportunity Id and the EDIT share record in the Aux map
                    mapAux.put((Id) gId, ConiAssignmentService.addMapAux(fields, System.Label.EditAccessLevel, gId, (Id) grpId.get(gId)));
                }
            }
            if (!setAccsObj.isEmpty()) {
                //System.debug('>>>> setAccsObj = ' + setAccsObj);
                Map<String, Object> shareData = ConiAssignmentService.prepareData(setAccsObj);
                Map<Id, Set<Id>> accountIdToMatchedUserOrGroupIds = (Map<Id, Set<Id>>) shareData.get(ACCOUNT_KEY);
                Map<String, Map<Id, SObject>> editMap = (Map<String, Map<Id, SObject>>) shareData.get(EDIT_MAP_KEY);
                Map<String, Map<Id, SObject>> readMap = (Map<String, Map<Id, SObject>>) shareData.get(READ_MAP_KEY);

                //AC: aggiungo a editMap l'attuale edit
                for (Id sObjId : mapAux.keySet()) {
                    Map<String, String> priv = mapAux.get(sObjId);
                    String userOrGroupId = priv.get(System.Label.UserOrGroupId);
                    String key = sObjId + '_' + userOrGroupId;
                    SObject editMapValue = ConiAssignmentService.getObject(System.Label.EditAccessLevel, sObjId, userOrGroupId);
                    Map<Id, SObject> valueMap = new Map<Id, SObject>{ userOrGroupId => editMapValue };
                    editMap.put(key, valueMap);
                    //AC: aggiungo agli account l'attuale sObj
                    SObject o = newSobjMap.get(sObjId);
                    Set<Id> matchedUserOrGroupIds = accountIdToMatchedUserOrGroupIds.get((Id) o.get(ACCOUNT_ID));
                    if (matchedUserOrGroupIds != null) {
                        matchedUserOrGroupIds.add(userOrGroupId);
                    }
                }

                //Dare un nome più parlante perchè il valore restituito nella lista contiene oggetti con share tipo Read
                Map<String, SObject> mapEditPriv = null;
                if (accountIdToMatchedUserOrGroupIds != null) {
                    mapEditPriv = ConiAssignmentService.editPriviliges2(accountIdToMatchedUserOrGroupIds, editMap, readMap, newSobjLst, null, grpIdEdit);
                }
                //System.debug('>>>> mapEditPriv: ' + mapEditPriv);
                Map<String, SObject> mapAuxPriv = ConiAssignmentService.lstToMap(lstAux);
                //System.debug('>>>> mapAuxPriv: ' + mapAuxPriv);
                for (String objId : mapEditPriv.keySet()) {
                    String[] objIdSplit = objId.split('_');
                    Boolean add = true;
                    if (objIdSplit[2].equals(EDIT)) {
                        String key = objIdSplit[0] + '_' + objIdSplit[1];
                        if (mapAuxPriv.containsKey(key + '_' + READ)) {
                            mapAuxPriv.remove(key);
                        } else if (mapAuxPriv.containsKey(key + '_' + EDIT)) {
                            add = false;
                        }
                    }
                    if (add && !mapAuxPriv.containsKey(objId)) {
                        lstAux.add(mapEditPriv.get(objId));
                    }
                }

                //System.debug('>>>> accountIdToMatchedUserOrGroupIds = ' + accountIdToMatchedUserOrGroupIds);
                /*Map<Id, Set<Id>> mapSetAuxIDs = new Map<Id, Set<Id>>();

                // Building the map that will have the Opportunity Id as the key and the set of IDs of the matched UsersOrGroups as the value to create the READ shares.
                if (accountIdToMatchedUserOrGroupIds != null) {
                    for (SObject sObj : newSobjLst) {
                        if (accountIdToMatchedUserOrGroupIds.containsKey((Id) sObj.get(ACCOUNT_ID)) && !editMap.isEmpty()) {
                            //System.debug('>>>> editMap: ' + editMap);
                            for (String key : editMap.keySet()) {
                                String[] sObjUG = key.split('_');
                                if (sObjUG[0].equals(sObj.get(ID)) && editMap.containsKey(key)) {
                                    Map<Id, SObject> editObj = editMap.get(key);
                                    String keyEditObj = editObj.keySet().iterator().next();
                                    SObject value = editObj.get(keyEditObj);
                                    if (oldSobjMap != null && oldSobjMap.containsKey((Id) sObj.get(ID))) {
                                        if (checkAddFromEditMap(sObj, oldSobjMap.get((Id) sObj.get(ID)), value)) {
                                            if (mapSetAuxIDs.containsKey((Id) sObj.get(ID))) {
                                                Set<Id> setA = mapSetAuxIDs.get((Id) sObj.get(ID));
                                                setA.add(sObjUG[1]);
                                            } else {
                                                mapSetAuxIDs.put(sObj.Id, new Set<Id> {sObjUG[1]});
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (mapSetAuxIDs != null && !mapSetAuxIDs.isEmpty() && mapSetAuxIDs != null) {
                        //System.debug('>>>> mapSetAuxIDs = ' + mapSetAuxIDs); 
                        for (Id sObjId : mapSetAuxIDs.keySet()) {
                            Set<Id> setAux = mapSetAuxIDs.get(sObjId);
                            if (setAux != null && !setAux.isEmpty()) {
                                for (Id idUsrGrpKey : setAux) {
                                    if (!mapAux.containsKey(sObjId)) {
                                        SObject obj = getObject(System.Label.ReadAccessLevel, sObjId, idUsrGrpKey);
                                        //System.debug('>>>> lstAux.add: ' + obj);
                                        lstAux.add(obj);
                                    } else if (mapAux.containsKey(sObjId)) {
                                        Map<String,String> mapOppAux = mapAux.get(sObjId);
                                        //System.debug('>>>> mapOppAux: ' + mapOppAux);
                                        //System.debug('>>>> String.valueOf(idUsrGrpKey): ' + String.valueOf(idUsrGrpKey));
                                        //System.debug('>>>> sObjId: ' + sObjId);
                                        if (mapOppAux.get(System.Label.UserOrGroupId) != String.valueOf(idUsrGrpKey)) {
                                            SObject obj = getObject(System.Label.ReadAccessLevel, sObjId, idUsrGrpKey);
                                            //System.debug('>>>> lstAux.add: ' + obj);
                                            lstAux.add(obj);                                                
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                */
            }
        }
        System.debug('### DEVCAP => lstAux = ' + lstAux);
        System.debug('### DEVCAP => refUpdateId = ' + refUpdateId);
        System.debug('### DEVCAP => objName = ' + objName);
        //return ConiAssignmentService.persistList(lstAux, refUpdateId, objName);
        return ConiAssignmentService.persistList(lstAux, refUpdateId, objName, mapStringARR);
    }
}
