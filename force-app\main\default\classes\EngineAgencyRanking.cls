/*********************************************************************************
* <AUTHOR>
* @description    Agency Engine class for the ranking of the existing agencies
* @date           2024-03-05
* @group          Agency Engine
**********************************************************************************/
public with sharing class EngineAgencyRanking {
    public class AgencyRankingException extends Exception {}

    // Constants
    private final static String MOTOR_PARAMETERS_MATRIX = 'Parametri_Motore';
    private final static String LOOKUP_TABLE_INPUT_MERITOCRATIC = 'Meritocratico';
    private final static String BEYOND_THE_CAP = 'Oltre il CAP';
    private final static String MAIN_SALESPOINT_SUBTYPE = 'Punto vendita agenziale';
    private final static Integer TOP_AGENCIES_LIMIT = 3;
    private final static List<String> AGENCY_VALID_TYPES = new List<String> {'Privata', 'Assicop', 'Sogeint'};    
    private final static Map<String, String> AREAS_TO_DISCRETIONALITY_FIELDS = new Map<String, String> {
        'Motor' => 'DiscretionalityMotor__c', 
        'Welfare' => 'DiscretionalityWelfare__c', 
        'Property' => 'DiscretionalityProperty__c',
        'Enterprise' => 'DiscretionalityEnterprise__c'
    };
    @TestVisible private static List<CalculationMatrixRow> testingMatrixRows;


    // State
    private static List<EngineWrapper.EngineExclusionAgency> excludedSalespoints = new List<EngineWrapper.EngineExclusionAgency>();
    
    /********************************************************************************************************************
    * <AUTHOR>
    * @date         2024-03-05
    * @description  The method returns the relevant data of the valid and excluded agencies 
    * @param        input (EngineWrapper.EngineWrapperInput): the ranking's request input parameters
    * @return       EngineWrapper.EngineWrapperOutput: the response containing all the relevant data of the valid and excluded agencies
    ********************************************************************************************************************/
    public static EngineWrapper.EngineWrapperOutput rankAgencies(EngineWrapper.EngineWrapperInput input) {
        Map<String, Object> meritocraticData;
        try {
            checkInputParameters(input);
            List<CalculationMatrixRow> matrixRows = getCalculationMatrixData(MOTOR_PARAMETERS_MATRIX);
            meritocraticData = getMeritocraticData(matrixRows, input);
            List<Account> inExtensionRangeSalespoints = getInExtensionRangeSalespoints((Double) meritocraticData.get('RaggioEstensioneKM'), input);
            Map<Id, KPI__c> agencyKPIMap = getAgencyKPIMap(inExtensionRangeSalespoints, (String) meritocraticData.get('AgenziaDefault'));
            List<Account> salespointsAfterPrimaryChecks = getSalespointsAfterPrimaryChecks(inExtensionRangeSalespoints, input, agencyKPIMap);
            List<Account> finalSalespoints = getSalespointsAfterSecondaryChecks(
                salespointsAfterPrimaryChecks, 
                input, 
                meritocraticData,
                agencyKPIMap
            );
            System.debug('# finalSalespoints size: ' + finalSalespoints.size() + ' => ' + JSON.serialize(finalSalespoints));
            System.debug('# excludedSalespoints size: ' + excludedSalespoints.size() + ' => ' + JSON.serialize(excludedSalespoints));

            Map<Id, KPI__c> finalAgencyKPIMap = getParentAgencyKPIMap(agencyKPIMap);
            List<EngineWrapper.EngineAgency> scoredSalespoints = createEngineAgencies(
                finalSalespoints, 
                finalAgencyKPIMap, 
                input,
                (Boolean) meritocraticData.get('Discrezionalità')
            );
            System.debug('# scoredSalespoints size: ' + scoredSalespoints.size() + ' => ' + JSON.serialize(scoredSalespoints));
            scoredSalespoints.sort();
            System.debug('# sortedSalespoints: ' + JSON.serialize(scoredSalespoints));

            EngineWrapper.EngineAgency parentAgency = getParentAgency(scoredSalespoints[0], finalSalespoints);
            EngineWrapper.EngineWrapperOutput response = new EngineWrapper.EngineWrapperOutput(
                input, 
                true, 
                scoredSalespoints, 
                scoredSalespoints[0], 
                parentAgency,
                excludedSalespoints, 
                getEngineSettings(meritocraticData)
            );
            System.debug('# response: ' + JSON.serialize(response));
            return response;
        } catch (AgencyRankingException e) {
            EngineWrapper.EngineWrapperOutput response = new EngineWrapper.EngineWrapperOutput(input, false, new EngineWrapper.EngineError(e.getMessage()), null);
            System.debug('# response: ' + JSON.serialize(response));
            return response;
        } catch (Exception e) {
            EngineWrapper.EngineWrapperOutput response = new EngineWrapper.EngineWrapperOutput(input, false, new EngineWrapper.EngineError('An unknown error has occurred.'), null);
            System.debug('# response: ' + JSON.serialize(response));
            return response;
        }
    }

    private static EngineWrapper.EngineAgency getParentAgency(EngineWrapper.EngineAgency selectedSalespoint, List<Account> finalSalespoints) {
        Id parentAgencyId;
        for(Account sp : finalSalespoints) {
            if(sp.Id == selectedSalespoint.sfAccountId) {
                parentAgencyId = sp.Agency__c;
            }
        }
        List<Account> selectedAgencyList = [
            SELECT Id, Name, ExternalId__c, Blacklist__c, OmnichannelAgreement__c, Type, 
            Discretionality__c, DiscretionalityMotor__c, BillingAddressFormula__c, IsLocatorEnabled__c, 
            CheckCAPAssignedContactActivities__c, BillingAddress, DiscretionalityWelfare__c,
            DiscretionalityProperty__c, DiscretionalityEnterprise__c, Agency__c, SubType__c, 
            District__c, VatCode__c, CAPActivity__c, ExtraCAP__c, 
            AgencyLoad__c, OmnichannelResponsible__c, AccountNumber, Size__c, Cluster__c 
            FROM Account 
            WHERE Id =: parentAgencyId 
            WITH USER_MODE
            LIMIT 1
        ];
        return selectedAgencyList.isEmpty() ? 
            null :
            new EngineWrapper.EngineAgency(
                selectedAgencyList[0],
                selectedSalespoint.score, 
                selectedSalespoint.distance,
                null,
                selectedSalespoint.kpiSet,
                null,
                true
            );
    }

    /********************************************************************************************************************
    * <AUTHOR>
    * @date         2024-03-05
    * @description  The method converts the meritocratic data to an EngineWrapper.EngineSettings object needed for the response 
    * @param        meritocraticData (Map<String, Object>): the meritocratic data
    * @return       EngineWrapper.EngineSettings: the engine settings needed to construct the response
    ********************************************************************************************************************/
    private static EngineWrapper.EngineSettings getEngineSettings(Map<String, Object> meritocraticData) {
        return new EngineWrapper.EngineSettings(
            (Boolean) meritocraticData.get('Discrezionalità'),
            (Double) meritocraticData.get('RaggioEstensioneKM'),
            (String) meritocraticData.get('AgenziaDefault'),
            (String) meritocraticData.get('AssegnazioneDefault'),
            (Integer) meritocraticData.get('NumeroMaxRiassegnazioni'),
            (Double) meritocraticData.get('RaggioCentroideKM'),
            (Double) meritocraticData.get('%TassoContattabilità')
        );
    }

    /********************************************************************************************************************
    * <AUTHOR>
    * @date         2024-03-05
    * @description  The method makes some preliminary checks on the input request provided
    * @param        input (EngineWrapper.EngineWrapperInput): the ranking's request input parameters
    * @return       -
    ********************************************************************************************************************/
    private static void checkInputParameters(EngineWrapper.EngineWrapperInput input) {
        // Checking if all the mandatory inputs exist - If not throw an error
        if(
            String.isBlank(input.clientType) || 
            String.isBlank(input.source) || 
            input.latitude == null || 
            input.longitude == null || 
            input.macroAreas == null || 
            input.macroAreas.isEmpty()
        ) {
            throw new AgencyRankingException('MISSING_INPUT_PARAMETER');
        }

        // Checking if the client type of the input is PureProspect - if yes throw an error
        if(input.clientType == 'Client') {
            throw new AgencyRankingException('CLIENT_TYPE_NOT_COMPATIBLE');
        }
    }

    /********************************************************************************************************************
    * <AUTHOR>
    * @date         2024-03-05
    * @description  The method returns a Lookup Table based on it's unique name
    * @param        calculationMatrixName (String): the UniqueName of the parent CalculationMatrix object
    * @return       List<CalculationMatrixRow>: all the rows of the lookup table
    ********************************************************************************************************************/
    private static List<CalculationMatrixRow> getCalculationMatrixData(String calculationMatrixName) {
        if(Test.isRunningTest()) {
            return testingMatrixRows;
        }
        List<CalculationMatrixRow> matrixRows = [
            SELECT Id, InputData, OutputData 
            FROM CalculationMatrixRow 
            WHERE CalculationMatrixVersion.CalculationMatrix.UniqueName =: calculationMatrixName
            WITH USER_MODE
        ];
        return matrixRows;
    }

    /********************************************************************************************************************
    * <AUTHOR>
    * @date         2024-03-05
    * @description  The method returns a row of Lookup Table based on it's name
    * @param        matrixRows (List<CalculationMatrixRow>): All the rows of the lookup table
    * @param        input (EngineWrapper.EngineWrapperInput): the ranking's request input parameters
    * @return       Map<String, Object>: the row data containing having the LOOKUP_TABLE_INPUT_MERITOCRATIC inputs
    ********************************************************************************************************************/
    private static Map<String, Object> getMeritocraticData(List<CalculationMatrixRow> matrixRows, EngineWrapper.EngineWrapperInput input) {
        Map<String, Object> meritocraticData;
        for(CalculationMatrixRow row : matrixRows) {
            if((row.InputData?.containsIgnoreCase(LOOKUP_TABLE_INPUT_MERITOCRATIC)) && (row.InputData?.containsIgnoreCase(input.source))) {
                meritocraticData = (Map<String, Object>) JSON.deserializeUntyped(row.OutputData);
                break;
            }
        }
        System.debug('# meritocraticData: ' + JSON.serialize(meritocraticData));
        if(meritocraticData == null) {
            throw new AgencyRankingException('NO_MERITOCRATIC_DATA_FOUND_IN_SYSTEM');
        }
        return meritocraticData;
    }

    /********************************************************************************************************************
    * <AUTHOR>
    * @date         2024-03-05
    * @description  The method returns all the agencies in extension range radius from the a target point specified in the input
    * @param        extensionRange (Double): the radius to check for agencies
    * @param        input (EngineWrapper.EngineWrapperInput): the ranking's request input parameters
    * @return       List<Account>: a list of agencies in extension range radius of the target point
    ********************************************************************************************************************/
    private static List<Account> getInExtensionRangeSalespoints(Double extensionRange, EngineWrapper.EngineWrapperInput input) {
        List<Account> inExtensionRangeSalespoints = [
            SELECT Id, Name, Agency__r.ExternalId__c, Agency__r.Blacklist__c, Agency__r.OmnichannelAgreement__c, Agency__r.Type, 
            Agency__r.Discretionality__c, Agency__r.DiscretionalityMotor__c, Agency__r.BillingAddressFormula__c, IsLocatorEnabled__c, 
            Agency__r.CheckCAPAssignedContactActivities__c, BillingAddress, Agency__r.DiscretionalityWelfare__c,
            Agency__r.DiscretionalityProperty__c, Agency__r.DiscretionalityEnterprise__c, Agency__c, SubType__c, 
            Agency__r.District__c, Agency__r.VatCode__c, Agency__r.CAPActivity__c, Agency__r.ExtraCAP__c, 
            Agency__r.AgencyLoad__c, Agency__r.OmnichannelResponsible__c, Agency__r.AccountNumber, Agency__r.Size__c, Agency__r.Cluster__c
            FROM Account
            WHERE RecordType.DeveloperName = 'SalesPoint'
            AND Agency__r.KPI__c != null
            AND DISTANCE(BillingAddress, GEOLOCATION(:input.latitude, :input.longitude), 'km') < :extensionRange
            WITH USER_MODE
            ORDER BY DISTANCE(BillingAddress, GEOLOCATION(:input.latitude, :input.longitude), 'km') ASC
        ];
        System.debug('# inExtensionRangeSalespoints size: ' + inExtensionRangeSalespoints.size() + ' => ' + JSON.serialize(inExtensionRangeSalespoints));

        return inExtensionRangeSalespoints;
    }

    /********************************************************************************************************************
    * <AUTHOR>
    * @date         2024-03-05
    * @description  The method returns all the agencies that pass the primary checks, and adds the ones excluded to the excludedSalespoints list
    * @param        inExtensionRangeSalespoints (List<Account>): the list of agencies in the extension radius
    * @param        input (EngineWrapper.EngineWrapperInput): the ranking's request input parameters
    * @param        agencyKPIMap (Map<Id, KPI__c>): the KPIs of the agencies in the extension radius
    * @return       List<Account>: a list of agencies that pass the primary checks
    ********************************************************************************************************************/
    private static List<Account> getSalespointsAfterPrimaryChecks(List<Account> inExtensionRangeSalespoints, EngineWrapper.EngineWrapperInput input, Map<Id, KPI__c> agencyKPIMap) {
        List<Account> salespointsAfterPrimaryChecks = new List<Account>();
        for(Account a : inExtensionRangeSalespoints) {
            EngineWrapper.KPISet kpiSet = new EngineWrapper.KPISet(agencyKPIMap.get(a?.Agency__r.Id));
            if(a.Agency__r.Blacklist__c) {
                EngineWrapper.EngineAgency engineAgency = new EngineWrapper.EngineAgency(
                    a,
                    (Double) agencyKPIMap.get(a?.Agency__r.Id)?.ScoreTotal__c,
                    Location.getDistance(Location.newInstance(input.latitude, input.longitude), a.BillingAddress, 'km'),
                    null,
                    kpiSet,
                    null,
                    false
                );
                excludedSalespoints.add(new EngineWrapper.EngineExclusionAgency(engineAgency, 'Blacklist'));
            }
            else if(!a.Agency__r.OmnichannelAgreement__c) {
                EngineWrapper.EngineAgency engineAgency = new EngineWrapper.EngineAgency(
                    a,
                    (Double) agencyKPIMap.get(a?.Agency__r.Id)?.ScoreTotal__c,
                    Location.getDistance(Location.newInstance(input.latitude, input.longitude), a.BillingAddress, 'km'),
                    null,
                    kpiSet,
                    null,
                    false
                );
                excludedSalespoints.add(new EngineWrapper.EngineExclusionAgency(engineAgency, 'Omnichannel'));
            }
            else if(!AGENCY_VALID_TYPES.contains(a.Agency__r.Type)) {
                EngineWrapper.EngineAgency engineAgency = new EngineWrapper.EngineAgency(
                    a,
                    (Double) agencyKPIMap.get(a?.Agency__r.Id)?.ScoreTotal__c,
                    Location.getDistance(Location.newInstance(input.latitude, input.longitude), a.BillingAddress, 'km'),
                    null,
                    kpiSet,
                    null,
                    false
                );
                excludedSalespoints.add(new EngineWrapper.EngineExclusionAgency(engineAgency, 'Type'));
            }
            else if(!a.IsLocatorEnabled__c) {
                EngineWrapper.EngineAgency engineAgency = new EngineWrapper.EngineAgency(
                    a,
                    (Double) agencyKPIMap.get(a?.Agency__r.Id)?.ScoreTotal__c,
                    Location.getDistance(Location.newInstance(input.latitude, input.longitude), a.BillingAddress, 'km'),
                    null,
                    kpiSet,
                    null,
                    false
                );
                excludedSalespoints.add(new EngineWrapper.EngineExclusionAgency(engineAgency, 'Locator'));
            }
            else if(input.agenciestoExcludeIds != null && input.agenciestoExcludeIds.contains(a.Agency__r.ExternalId__c)) {
                EngineWrapper.EngineAgency engineAgency = new EngineWrapper.EngineAgency(
                    a,
                    (Double) agencyKPIMap.get(a?.Agency__r.Id)?.ScoreTotal__c,
                    Location.getDistance(Location.newInstance(input.latitude, input.longitude), a.BillingAddress, 'km'),
                    null,
                    kpiSet,
                    null,
                    false
                );
                excludedSalespoints.add(new EngineWrapper.EngineExclusionAgency(engineAgency, 'PreviouslyAssigned'));
            }
            else {
                salespointsAfterPrimaryChecks.add(a);
            }
        }
        System.debug('# salespointsAfterPrimaryChecks size: ' + salespointsAfterPrimaryChecks.size() + ' => ' + JSON.serialize(salespointsAfterPrimaryChecks));
        System.debug('# excludedSalespoints size: ' + excludedSalespoints.size() + ' => ' + JSON.serialize(excludedSalespoints));

        return salespointsAfterPrimaryChecks;
    }

    /********************************************************************************************************************
    * <AUTHOR>
    * @date         2024-03-05
    * @description  The method returns all the KPIs of the agencies in the extension radius and the KPI of the default agency
    * @param        inExtensionRangeSalespoints (List<Account>): the list of agencies in the extension radius
    * @param        defaultAgencyAccountNumber (String): the AccountNumber of the default agency
    * @return       Map<Id, KPI__c>: a map that matches the agency Id to the agency's KPI object
    ********************************************************************************************************************/
    private static Map<Id, KPI__c> getAgencyKPIMap(List<Account> inExtensionRangeSalespoints, String defaultAgencyAccountNumber) {
        Map<Id, KPI__c> agencyKPIMap = new Map<Id, KPI__c>();
        Set<Id> agencyIds = getParentAgencyIds(inExtensionRangeSalespoints);
        System.debug('# agencyIds: ' + agencyIds);

        List<KPI__c> validKPI = [
            SELECT Id, Agency__c, ContactRate__c, ScoreTotal__c, Name, NumberContactableClients__c, NumberClientsPortfolio__c, ContactActivitiesProcessingAssigned__c,
            BenchmarkContactActivitiesAssigned__c, WeightContactActivitiesAssigned__c, ScoreProcessingAssignedActivities__c, ConversionAssignedContactActivities__c,
            BenchmarkConversionAssignedActivities__c, ScoreConversionAssignedActivities__c, WeightConversionAssignedActivities__c, AverageLeadProcessingTime__c,
            BenchmarkAverageLeadProcessingTime__c, ScoreAverageLeadProcessingTime__c, WeightAverageLeadProcessingTime__c, DigitalPenetration__c, BenchmarkDigitalPenetration__c,
            ScoreDigitalPenetration__c, WeightDigitalPenetration__c, PrivateAreaRegistration__c, BenchmarkPrivateAreaRegistration__c, ScorePrivateAreaRegistration__c,
            WeightPrivateAreaRegistration__c, OmnichannelQuotes__c, BenchmarkOmnichannelQuotes__c, ScoreOmnichannelQuotes__c, WeightOmnichannelQuotes__c, Agency__r.AccountNumber
            FROM KPI__c 
            WHERE Agency__c IN :agencyIds OR Agency__r.AccountNumber =: defaultAgencyAccountNumber
            WITH USER_MODE
        ];
        for(KPI__c kpi : validKPI) {
            agencyKPIMap.put(kpi.Agency__c, kpi);
        }
        System.debug('# agencyKPIMap size: ' + agencyKPIMap.keySet().size() + ' => ' + JSON.serialize(agencyKPIMap));

        return agencyKPIMap;
    }

    private static Set<Id> getParentAgencyIds(List<Account> inExtensionRangeSalespoints) {
        Set<Id> agencyIds = new Set<Id>();
        for(Account salespoint : inExtensionRangeSalespoints) {
            agencyIds.add(salespoint.Agency__c);
        }
        return agencyIds;
    }

    /********************************************************************************************************************
    * <AUTHOR>
    * @date         2024-03-05
    * @description  The method returns all the agencies that pass the secondary checks and adds the ones exluded to the excludedSalespoints list
    * @param        salespointsAfterPrimaryChecks (List<Account>): the list of agencies that pass the primary checks
    * @param        input (EngineWrapper.EngineWrapperInput): the ranking's request input parameters
    * @param        meritocraticData (Map<String, Object>): the meritocratic data
    * @param        agencyKPIMap (Map<Id, KPI__c>): a map that matches the agency Id to the agency's KPI object
    * @return       List<Account>: a list with all the agencies that pass the secondary checks
    ********************************************************************************************************************/
    private static List<Account> getSalespointsAfterSecondaryChecks (
        List<Account> salespointsAfterPrimaryChecks, 
        EngineWrapper.EngineWrapperInput input, 
        Map<String, Object> meritocraticData,
        Map<Id, KPI__c> agencyKPIMap
    ) {
        List<Account> outOfCapSalespoints = new List<Account>();
        List<Account> extensionRadiusSalespoints = new List<Account>();
        List<Account> lowContactRateSalespoints = new List<Account>();

        List<Id> idsToBeRemoved = new List<Id>();
        for(Account a : salespointsAfterPrimaryChecks) {
            if(a.Agency__r.CheckCAPAssignedContactActivities__c == BEYOND_THE_CAP) {
                outOfCapSalespoints.add(a);
                idsToBeRemoved.add(a.Id);
            }
            else if(Location.getDistance(Location.newInstance(input.latitude, input.longitude), a.BillingAddress, 'km') > ((Double) meritocraticData.get('RaggioCentroideKM'))) {
                extensionRadiusSalespoints.add(a);
                idsToBeRemoved.add(a.Id);
            }
            else if(!(((Boolean) meritocraticData.get('Discrezionalità')) && a.Agency__r.Discretionality__c) && agencyKPIMap.get(a.Agency__r.Id)?.ContactRate__c < ((Double) meritocraticData.get('%TassoContattabilità'))) {
                lowContactRateSalespoints.add(a);
                idsToBeRemoved.add(a.Id);
            }
        }
        List<Account> agenciesAfterSecondaryChecks = removeAccountsById(salespointsAfterPrimaryChecks, idsToBeRemoved);

        System.debug('# idsToBeRemoved size: ' + idsToBeRemoved.size() + ' => ' + JSON.serialize(idsToBeRemoved));
        System.debug('# agenciesAfterSecondaryChecks size: ' + agenciesAfterSecondaryChecks.size() + ' => ' + JSON.serialize(agenciesAfterSecondaryChecks));
        System.debug('# outOfCapSalespoints size: ' + outOfCapSalespoints.size() + ' => ' + JSON.serialize(outOfCapSalespoints));
        System.debug('# extensionRadiusSalespoints size: ' + extensionRadiusSalespoints.size() + ' => ' + JSON.serialize(extensionRadiusSalespoints));
        System.debug('# lowContactRateSalespoints size: ' + lowContactRateSalespoints.size() + ' => ' + JSON.serialize(lowContactRateSalespoints));

        if(!agenciesAfterSecondaryChecks.isEmpty()) {
            addAccountsToExclusionSalespoint(extensionRadiusSalespoints, 'Extension Rate', input, agencyKPIMap);
            addAccountsToExclusionSalespoint(outOfCapSalespoints, 'Out of Cap', input, agencyKPIMap);
            addAccountsToExclusionSalespoint(lowContactRateSalespoints, 'Low Contact Rate', input, agencyKPIMap);
            return agenciesAfterSecondaryChecks;
        }
        else if(agenciesAfterSecondaryChecks.size() + extensionRadiusSalespoints.size() > 0) {
            addAccountsToExclusionSalespoint(outOfCapSalespoints, 'Out of Cap', input, agencyKPIMap);
            addAccountsToExclusionSalespoint(lowContactRateSalespoints, 'Low Contact Rate', input, agencyKPIMap);
            agenciesAfterSecondaryChecks.addAll(extensionRadiusSalespoints);
            return agenciesAfterSecondaryChecks;
        }
        else if(agenciesAfterSecondaryChecks.size() + extensionRadiusSalespoints.size() + outOfCapSalespoints.size() > 0) {
            addAccountsToExclusionSalespoint(lowContactRateSalespoints, 'Low Contact Rate', input, agencyKPIMap);
            agenciesAfterSecondaryChecks.addAll(extensionRadiusSalespoints);
            agenciesAfterSecondaryChecks.addAll(outOfCapSalespoints);
            return agenciesAfterSecondaryChecks;
        }
        else if(agenciesAfterSecondaryChecks.size() + extensionRadiusSalespoints.size() + outOfCapSalespoints.size() + lowContactRateSalespoints.size() > 0) {
            return salespointsAfterPrimaryChecks;
        }

        return getDefaultAgency((String) meritocraticData.get('AgenziaDefault'));
    }

    /********************************************************************************************************************
    * <AUTHOR>
    * @date         2024-03-05
    * @description  The method returns a list of all the agencies passed minus the accounts to be removed based on their Id
    * @param        initialAccountList (List<Account>): the list of agencies that pass the primary checks
    * @param        idsToBeRemoved (List<Id>): the Ids of the accounts to be removed
    * @return       List<Account>: a list of all the agencies passed minus the accounts to be removed
    ********************************************************************************************************************/
    private static List<Account> removeAccountsById(List<Account> initialAccountList, List<Id> idsToBeRemoved) {
        List<Account> finalAccountList = new List<Account>();
        for(Account a : initialAccountList) {
            if(!idsToBeRemoved.contains(a.Id)) {
                finalAccountList.add(a);
            }
        }
        return finalAccountList;
    }

    /********************************************************************************************************************
    * <AUTHOR>
    * @date         2024-03-05
    * @description  The method addes the agencies provided to the excludedSalespoints list with the relevant exclusion reason
    * @param        salespointsToAdd (List<Account>): the list of agencies to add to the excludedSalespoints list
    * @param        exclusionReason (String): the reason this list of agencies were excluded
    * @param        input (EngineWrapper.EngineWrapperInput): the ranking's request input parameters
    * @param        agencyKPIMap (Map<Id, KPI__c>): a map that matches the agency Id to the agency's KPI object
    * @return       -
    ********************************************************************************************************************/
    private static void addAccountsToExclusionSalespoint(
        List<Account> salespointsToAdd, 
        String exclusionReason, 
        EngineWrapper.EngineWrapperInput input, 
        Map<Id, KPI__c> agencyKPIMap
    ) {
        for(Account a : salespointsToAdd) {
            EngineWrapper.KPISet kpiSet = new EngineWrapper.KPISet(agencyKPIMap.get(a?.Agency__r.Id));
            EngineWrapper.EngineAgency engineAgency = new EngineWrapper.EngineAgency(
                a,
                (Double) agencyKPIMap.get(a?.Agency__r.Id)?.ScoreTotal__c,
                Location.getDistance(Location.newInstance(input.latitude, input.longitude), a.BillingAddress, 'km'),
                null,
                kpiSet,
                null,
                false
            );
            excludedSalespoints.add(new EngineWrapper.EngineExclusionAgency(engineAgency, exclusionReason));
        }
    }

    /********************************************************************************************************************
    * <AUTHOR>
    * @date         2024-03-05
    * @description  The method returns the updated agency to KPI map taking the KPIs of the parent accounts (for accounts which have parent accounts)
    * @param        agencyKPIMap (Map<Id, KPI__c>): a map that matches the agency Id to the agency's KPI object
    * @return       Map<Id, KPI__c>: the updated map that connects the agencies with their KPI object
    ********************************************************************************************************************/
    private static Map<Id, KPI__c> getParentAgencyKPIMap(Map<Id, KPI__c> agencyKPIMap) {
        Map<String, String> parentIdToAccountId = new Map<String, String>();
        List<Account> accountsWithParent = [SELECT Id, ParentId FROM Account WHERE ParentId != null AND Id IN :agencyKPIMap.keySet() WITH USER_MODE];
        for(Account a : accountsWithParent) {
            parentIdToAccountId.put(a.ParentId, a.Id);
        }

        List<KPI__c> validKPI = [
            SELECT Id, Agency__c, ContactRate__c, ScoreTotal__c, Name, NumberContactableClients__c, NumberClientsPortfolio__c, ContactActivitiesProcessingAssigned__c,
            BenchmarkContactActivitiesAssigned__c, WeightContactActivitiesAssigned__c, ScoreProcessingAssignedActivities__c, ConversionAssignedContactActivities__c,
            BenchmarkConversionAssignedActivities__c, ScoreConversionAssignedActivities__c, WeightConversionAssignedActivities__c, AverageLeadProcessingTime__c,
            BenchmarkAverageLeadProcessingTime__c, ScoreAverageLeadProcessingTime__c, WeightAverageLeadProcessingTime__c, DigitalPenetration__c, BenchmarkDigitalPenetration__c,
            ScoreDigitalPenetration__c, WeightDigitalPenetration__c, PrivateAreaRegistration__c, BenchmarkPrivateAreaRegistration__c, ScorePrivateAreaRegistration__c,
            WeightPrivateAreaRegistration__c, OmnichannelQuotes__c, BenchmarkOmnichannelQuotes__c, ScoreOmnichannelQuotes__c, WeightOmnichannelQuotes__c
            FROM KPI__c 
            WHERE Agency__c IN :parentIdToAccountId.keySet()
            WITH USER_MODE
        ];
        for(KPI__c kpi : validKPI) {
            agencyKPIMap.put(parentIdToAccountId.get(kpi.Agency__c), kpi);
        }
        System.debug('# finalAgencyKPIMap size: ' + agencyKPIMap.keySet().size() + ' => ' + JSON.serialize(agencyKPIMap));

        return agencyKPIMap;
    }

    /********************************************************************************************************************
    * <AUTHOR>
    * @date         2024-03-05
    * @description  The method returns the scored valid agencies in a EngineWrapper.EngineAgency format to be added to the response
    * @param        finalSalespoints (List<Account>): the list of agencies to be scored 
    * @param        finalAgencyKPIMap (Map<Id, KPI__c>): a map that matches the agency Id to the agency's KPI object
    * @param        input (EngineWrapper.EngineWrapperInput): the ranking's request input parameters
    * @param        discretionality (Boolean): the discretionality setting from the meritocratic lookup table
    * @return       List<EngineWrapper.EngineAgency>: the list of scored valid agencies to be added to the response
    ********************************************************************************************************************/
    private static List<EngineWrapper.EngineAgency> createEngineAgencies(
        List<Account> finalSalespoints, 
        Map<Id, KPI__c> finalAgencyKPIMap,
        EngineWrapper.EngineWrapperInput input,
        Boolean discretionality
    ) {
        List<EngineWrapper.EngineAgency> engineAgencies = new List<EngineWrapper.EngineAgency>();
        for(Account agency : finalSalespoints) {
            EngineWrapper.KPISet kpiSet = new EngineWrapper.KPISet(finalAgencyKPIMap.get(agency?.Agency__r.Id));
            EngineWrapper.EngineAgency engineAgency = new EngineWrapper.EngineAgency(
                agency,
                (Double) finalAgencyKPIMap.get(agency?.Agency__r.Id)?.ScoreTotal__c,
                Location.getDistance(Location.newInstance(input.latitude, input.longitude), agency.BillingAddress, 'km'),
                discretionality ? (Boolean) ((SObject) agency).getSObject('Agency__r').get(AREAS_TO_DISCRETIONALITY_FIELDS.get(input.macroAreas[0])) : null,
                kpiSet,
                discretionality,
                false
            );
            engineAgencies.add(engineAgency);
        }
        return engineAgencies;
    }

    /********************************************************************************************************************
    * <AUTHOR>
    * @date         2024-03-05
    * @description  The method returns the default agency based on the default AccountNumber in the Meritocratic Lookup table
    * @param        defaultAgencyAccountNumber (String): the AccountName of the default agency
    * @return       List<Account>: a list containing the default agency
    ********************************************************************************************************************/
    private static List<Account> getDefaultAgency(String defaultAgencyAccountNumber) {
        List<Account> defaultAgencies = [
            SELECT Id, Name, Agency__r.ExternalId__c, Agency__r.Blacklist__c, Agency__r.OmnichannelAgreement__c, Agency__r.Type, 
            Agency__r.Discretionality__c, Agency__r.DiscretionalityMotor__c, Agency__r.BillingAddressFormula__c, IsLocatorEnabled__c, 
            Agency__r.CheckCAPAssignedContactActivities__c, BillingAddress, Agency__r.DiscretionalityWelfare__c, 
            Agency__r.DiscretionalityProperty__c, Agency__r.DiscretionalityEnterprise__c, Agency__c, SubType__c, 
            Agency__r.District__c, Agency__r.VatCode__c, Agency__r.CAPActivity__c, Agency__r.ExtraCAP__c, 
            Agency__r.AgencyLoad__c, Agency__r.OmnichannelResponsible__c, Agency__r.AccountNumber, Agency__r.Size__c, Agency__r.Cluster__c
            FROM Account
            WHERE Agency__r.AccountNumber =: defaultAgencyAccountNumber
            AND RecordType.DeveloperName = 'SalesPoint'
            AND SubType__c =: MAIN_SALESPOINT_SUBTYPE
            WITH USER_MODE
        ];

        if(defaultAgencies.isEmpty()) {
            throw new AgencyRankingException('NO_DEFAULT_AGENCY_WAS_FOUND');
        }
        return defaultAgencies;
    }
}
