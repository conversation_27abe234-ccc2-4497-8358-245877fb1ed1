@isTest
private class ReportQueryControllerV2Test {

    @isTest
    static void testGetOpportunities() {
        
        RecordType showInterest = [SELECT Id FROM RecordType WHERE SobjectType = 'Opportunity' AND DeveloperName = 'InterestShow' LIMIT 1];
        
        // Crea dati di test
        Opportunity opp = new Opportunity(
            Name = 'Test Opportunity ',
            StageName='Assegnato',
            Channel__c='Preventivatore digitale Unica',
            ContactChannel__c='Agenzia',
            Amount = 100000,
            AreaOfNeed__c = 'Veicoli',
            CloseDate = Date.TODAY().addDays(30),
            RecordTypeId = showInterest.Id
        );
        insert opp;

        Test.startTest();
        List<Opportunity> results = ReportQueryControllerV2.getOpportunities('1000');
        Test.stopTest();

        // Verifica che almeno un'opportunità venga restituita
        System.assertNotEquals(0, results.size(), 'Dovrebbe restituire almeno un\'opportunità');
        System.assertEquals(opp.Id, results[0].Id, 'L\'opportunità restituita dovrebbe corrispondere a quella inserita');
    }
}