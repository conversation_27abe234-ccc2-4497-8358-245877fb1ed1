@isTest
private class ReportPOCControllerTest {

    @testSetup
    static void setupData() {
        List<Opportunity> opps = new List<Opportunity>();
        List<String> ratings = new List<String>{'Fredda', 'Tiepida', 'Calda', 'Caldissima'};
        Integer ratingIndex = 0;

        for (Integer i = 0; i < 25; i++) {
            String rating = ratings[ratingIndex];
            ratingIndex = (ratingIndex + 1) < ratings.size() ? ratingIndex + 1 : 0;

            opps.add(new Opportunity(
                Name = 'Test Opportunity ' + i,
                Amount = 1000 + i,
                StageName = 'Prospecting',
                CloseDate = Date.today().addDays(30),
                Rating__c = rating
            ));
        }

        insert opps;
    }

    @isTest
    static void testWithSearchAndSort() {
        List<String> searchFields = new List<String>{'Name'};

        Test.startTest();
        ReportPOCController.ResponseObject response = ReportPOCController.getOpportunities(
            10, 0, 'CreatedDate', 'DESC', null, 'Test', searchFields
        );
        Test.stopTest();
    }

    @isTest
    static void testWithLatestQueryWhere() {
        String latestQuery = 'SELECT Name, Amount, Rating__c, CreatedDate FROM Opportunity WHERE StageName = \'Prospecting\' ';
        List<String> searchFields = new List<String>{'Name'};

        Test.startTest();
        ReportPOCController.ResponseObject response = ReportPOCController.getOpportunities(
            5, 0, 'Amount', 'ASC', latestQuery, 'Test', searchFields
        );
        Test.stopTest();
    }

    @isTest
    static void testWithLatestQueryOrderBy() {
        String latestQuery = 'SELECT Name, Amount, Rating__c, CreatedDate FROM Opportunity ORDER BY CreatedDate DESC';
        List<String> searchFields = new List<String>{'Name'};

        Test.startTest();
        ReportPOCController.ResponseObject response = ReportPOCController.getOpportunities(
            5, 0, 'Amount', 'ASC', latestQuery, 'Test', searchFields
        );
        Test.stopTest();
    }

    @isTest
    static void testWithLatestQueryLimit() {
        String latestQuery = 'SELECT Name, Amount, Rating__c, CreatedDate FROM Opportunity LIMIT 10';
        List<String> searchFields = new List<String>{'Name'};

        Test.startTest();
        ReportPOCController.ResponseObject response = ReportPOCController.getOpportunities(
            5, 0, 'Amount', 'ASC', latestQuery, 'Test', searchFields
        );
        Test.stopTest();
    }

    @isTest
    static void testWithoutSearch() {
        Test.startTest();
        ReportPOCController.ResponseObject response = ReportPOCController.getOpportunities(
            5, 5, 'Amount', 'ASC', null, null, new List<String>()
        );
        Test.stopTest();
    }

    @isTest
    static void testGetNumberOfPages() {
        Test.startTest();
        Integer pages = ReportPOCController.getNumberOfPages(10);
        Test.stopTest();
    }
}