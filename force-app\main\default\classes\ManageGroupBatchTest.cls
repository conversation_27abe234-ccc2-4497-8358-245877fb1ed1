@isTest
private with sharing class ManageGroupBatchTest {
    @TestSetup
    static void makeData() {
        Account accAgency = new Account(
            Name = 'BONASS S.R.L.',
            ExternalId__c = 'AGE_01853',
            FullName__c = 'BONASS S.R.L.',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName()
                .get('Agency')
                .getRecordTypeId()
        );
        insert accAgency;
        Group gp = new Group();
        gp.Name = accAgency.ExternalId__c;
        gp.Description = 'Test Descrizione';
        insert gp;
        Id RTGRPCollaborativo = Schema.SObjectType.Group__c.getRecordTypeInfosByDeveloperName()
            .get('UserGroup')
            .getRecordTypeId();
        Integer rand = Math.round(Math.random() * 1000);
        Group__c collaborativo = new Group__c();
        collaborativo.Name = accAgency.Name;
        collaborativo.RecordTypeId = RTGRPCollaborativo;
        collaborativo.ExternalId__c = accAgency.ExternalId__c + String.valueOf(rand);
        collaborativo.Agenzia__c = accAgency.Id;
        collaborativo.PublicGroupId__c = gp.Id;
        collaborativo.Description__c = gp.Description;
        collaborativo.RecordTypeId = Schema.SObjectType.Group__c.getRecordTypeInfosByDeveloperName()
            .get('CIP')
            .getRecordTypeId();
        insert collaborativo;
    }

    @isTest
    static void testBatchWithoutInputParameter() {
        Test.startTest();
        Database.executeBatch(new ManageGroupBatch());
        Test.stopTest();
    }

    @isTest
    static void testBatchWithInputParameter() {
        Test.startTest();
        Database.executeBatch(new ManageGroupBatch(Date.today()));
        Test.stopTest();
    }

    @isTest
    static void testBatchWitestBatchWithBadInputParameterthInputParameter() {
        Test.startTest();
        Object ob = null;
        Database.executeBatch(new ManageGroupBatch(ob));
        Test.stopTest();
    }
}
