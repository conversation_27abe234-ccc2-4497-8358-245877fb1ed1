global class TestBatchClass implements Database.Batchable<sObject> {
    global Database.QueryLocator start(Database.BatchableContext BC) {
        String query = 'SELECT Id FROM Account LIMIT 1';
        return Database.getQueryLocator(query);
    }

    global static void execute(Database.BatchableContext BC, List<sObject> scope) {
        System.debug('Test Batch Class Running');
    }

    global void finish(Database.BatchableContext BC) {
        System.debug('Test Batch Class Finished');
    }
}
