@isTest
private class InsurancePolicyAddRelationBatch_Test {

    @testSetup
    static void makeData() {
        Id agencyRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId();
        Id societyRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId();

        Id accountSocietyRecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AccountSociety').getRecordTypeId();
        Id accountAgencyRecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AccountAgency').getRecordTypeId();

        // Creazione Account
        List<Account> accListToInsert = new List<Account>();
        Account accountCliente1 = new Account(
            FirstName = 'Cliente', LastName = '1',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('PersonAccount').getRecordTypeId(), 
            ExternalId__c = 'PRLGRL95L07F704X'
        );
        accListToInsert.add(accountCliente1);

        Account accountSociety1 = new Account(
            Name = 'Scoietà 1', RecordTypeId = societyRecordTypeId,  
            ExternalId__c = 'SOC_1', Identifier__c = '1'
        );
        accListToInsert.add(accountSociety1);

        Account accountAgenzia1 = new Account(
            Name = 'Agenzia 1', RecordTypeId = agencyRecordTypeId,  
            ExternalId__c = 'AGE_1'
        );
        accListToInsert.add(accountAgenzia1);
        insert accListToInsert;

        // Creazione ReciprocalRole
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(Name = 'Cliente', FinServ__InverseRole__c = 'Agenzia');
        insert role;
       
        FinServ__ReciprocalRole__c roleC = new FinServ__ReciprocalRole__c(Name = 'Cliente', FinServ__InverseRole__c = 'Compagnia');
        insert roleC;

        FinServ__AccountAccountRelation__c accAccRelClienteSociety1 = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = accountCliente1.Id,
            FinServ__RelatedAccount__c = accountSociety1.Id,
            FinServ__Role__c = role.Id,
            RecordTypeId = accountSocietyRecordTypeId,
            FinServ__ExternalId__c = 'PRLGRL95L07F704X_SOC_1_inverse'
        );
        insert accAccRelClienteSociety1;

        // Creazione degli AccountAccountRel
        FinServ__AccountAccountRelation__c accAccRelClienteAgency1 = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = accountCliente1.Id,
            FinServ__RelatedAccount__c = accountAgenzia1.Id,
            FinServ__Role__c = role.Id,
            RecordTypeId = accountAgencyRecordTypeId,
            FinServ__ExternalId__c = 'PRLGRL95L07F704X_SOC_1_AGE_1'
        );
        insert accAccRelClienteAgency1;

        List<AccountAgencyDetails__c> accAgenDetListToInsert = new List<AccountAgencyDetails__c>();
        AccountAgencyDetails__c agencyDetails1 = new AccountAgencyDetails__c( 
            Relation__c = accAccRelClienteAgency1.Id, SubAgencyCode__c = 'SUB1',
            ExternalId__c = 'ANAG2_PRLGRL95L07F704X_SOC_1_AGE_1'
        );
        accAgenDetListToInsert.add(agencyDetails1);
        insert accAgenDetListToInsert;

        List<InsurancePolicy> insPolListToInsert = new List<InsurancePolicy>();
        InsurancePolicy insPol1 = new InsurancePolicy( 
            NameInsuredId = accountCliente1.Id, 
            Name = 'TEST',
            ExternalId__c = 'PU-3942394238492348239',
            Society__c = accountSociety1.Id,
            CompanyCode__c = '1',
            CIP__c = '101'
        );
        insPolListToInsert.add(insPol1);
        insert insPolListToInsert;

        List<InsurancePolicyNPI__c> insPolNPIListToInsert = new List<InsurancePolicyNPI__c>();
        InsurancePolicyNPI__c insPolNPI1 = new InsurancePolicyNPI__c(
            Name = 'TEST',
            ExternalId__c = 'PU-3942394238492348239',
            Relation__c = accAccRelClienteAgency1.Id
        );
        insPolNPIListToInsert.add(insPolNPI1);
        insert insPolNPIListToInsert;
    }

    @isTest
    static void testBatchRT() {

        Test.startTest();
        //Database.executeBatch(new InsurancePolicyAddRelationBatch('Individual'), 200);
        Test.stopTest();
    }

    @isTest
    static void testBatchIdSet() {

        InsurancePolicy ad = [SELECT Id FROM InsurancePolicy LIMIT 1];

        Test.startTest();
        Database.executeBatch(new InsurancePolicyAddRelationBatch(new Set<Id>{ad.Id}), 200);
        Test.stopTest();
    }

    @isTest
    static void testBatchIdRange() {

        InsurancePolicy ad = [SELECT Id FROM InsurancePolicy LIMIT 1];

        Test.startTest();
        Database.executeBatch(new InsurancePolicyAddRelationBatch(ad.Id, ad.Id), 200);
        Test.stopTest();
    }

    @isTest
    static void testBatchLastModified() {

        InsurancePolicy ad = [SELECT Id, LastModifiedDate FROM InsurancePolicy LIMIT 1];

        Test.startTest();
        Database.executeBatch(new InsurancePolicyAddRelationBatch(ad.LastModifiedDate, ad.LastModifiedDate), 200);
        Test.stopTest();
    }
}