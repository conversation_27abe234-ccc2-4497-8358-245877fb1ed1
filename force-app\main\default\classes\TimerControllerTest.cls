@isTest
public class TimerControllerTest {
    @testSetup
    static void makeData(){
        Opportunity testOpportunity = new Opportunity();
        Opportunity testOpportunityExpired = new Opportunity();
        Account testPersonAccount = new Account();
        Account testAgencyAccount = new Account();
        Task testTask = new Task();
        List<Opportunity> testOpportunities = new List<Opportunity>();
        List<Account> accountsToBeInserted = new List<Account>();
        List<RecordType> agencyRT = new List<RecordType>();
        List<RecordType> prospectRT = new List<RecordType>();
        List<RecordType> callmebackRT = new List<RecordType>();
        List<BusinessHours> standardBusinessHours = new List<BusinessHours>();

        agencyRT = [SELECT Id, Name FROM RecordType WHERE DeveloperName = 'Agency' LIMIT 1];
        prospectRT = [SELECT Id, Name FROM RecordType WHERE DeveloperName = 'Prospect' ORDER BY CreatedDate LIMIT 1];
        callmebackRT = [SELECT Id, Name FROM RecordType WHERE DeveloperName = 'CallMeBack' LIMIT 1];
        standardBusinessHours = [SELECT Id, name from BusinessHours where name = 'Standard Office BH' LIMIT 1];

        if(!agencyRT.isEmpty() && !prospectRT.isEmpty() && !standardBusinessHours.isEmpty() && !callmebackRT.isEmpty()){
            testPersonAccount.RecordTypeId = prospectRT.get(0).Id;
            testPersonAccount.BillingCity = 'Roma';
            testPersonAccount.BillingCountry = 'Italy';
            testPersonAccount.BillingLatitude = 41.**************;
            testPersonAccount.BillingLongitude = 12.***************;
            testPersonAccount.BillingPostalCode = '00154';
            testPersonAccount.BillingStreet = '13 Via Cristoforo Colombo';
            testPersonAccount.FirstName = 'Mario';
            testPersonAccount.LastName = 'Test';
            testPersonAccount.Salutation = 'Mr.';
            accountsToBeInserted.add(testPersonAccount);

            testAgencyAccount.RecordTypeId = agencyRT.get(0).Id;
            testAgencyAccount.AccountNumber = 'T0000';
            testAgencyAccount.Area__c = 'Roma';
            testAgencyAccount.BillingCity = 'Roma';
            testAgencyAccount.BillingCountry = 'Italy';
            testAgencyAccount.BillingLatitude = 41.86505;
            testAgencyAccount.BillingLongitude = 12.49762;
            testAgencyAccount.BillingPostalCode = '00147';
            testAgencyAccount.BillingStreet = 'Via Cristoforo Colombo 177';
            testAgencyAccount.BusinessHours__c = standardBusinessHours.get(0).Id;
            testAgencyAccount.Name = 'Test Agenzia 0000';
            accountsToBeInserted.add(testAgencyAccount);
            insert accountsToBeInserted;

            List<Account> subject = [SELECT Id, Name FROM Account WHERE LastName = 'Test' LIMIT 1];
            List<Account> agency = [SELECT Id, BusinessHours__c FROM Account WHERE AccountNumber = 'T0000' LIMIT 1];

            testOpportunity.AccountId = subject.get(0).Id;
            testOpportunity.Agency__c = agency.get(0).Id;
            testOpportunity.AreaOfNeed__c = 'Veicoli';
            testOpportunity.AssignmentMode__c = 'Automatica';
            testOpportunity.Channel__c = 'Preventivatore digitale Unica';
            testOpportunity.CloseDate = Date.today().addDays(60);
            testOpportunity.ContactChannel__c = 'Agenzia';
            testOpportunity.Name = 'T000102610';
            testOpportunity.Probability = 10;
            testOpportunity.Rating__c = 'Caldissima';
            testOpportunity.SingleAreaOfNeed__c = 'Veicoli';
            testOpportunity.StageName = 'Assegnato';
            testOpportunity.TakenInChargeSLAStartDate__c = Datetime.newInstance(2024, 3, 21, 12, 0, 0);
            testOpportunity.TakenInChargeSLAExpiryDate__c = Datetime.newInstance(2024, 3, 25, 12, 0, 0);
            testOpportunity.WorkingSLAStartDate__c = Datetime.newInstance(2024, 3, 21, 12, 0, 0);
            testOpportunity.WorkingSLAExpiryDate__c = Datetime.newInstance(2024, 5, 21, 12, 0, 0);
            testOpportunities.add(testOpportunity);

            testOpportunityExpired.AccountId = subject.get(0).Id;
            testOpportunityExpired.Agency__c = agency.get(0).Id;
            testOpportunityExpired.AreaOfNeed__c = 'Veicoli';
            testOpportunityExpired.AssignmentMode__c = 'Automatica';
            testOpportunityExpired.Channel__c = 'Preventivatore digitale Unica';
            testOpportunityExpired.CloseDate = Date.today().addDays(60);
            testOpportunityExpired.ContactChannel__c = 'Agenzia';
            testOpportunityExpired.Name = 'T000112711';
            testOpportunityExpired.StageName = 'Assegnato';
            testOpportunityExpired.TakenInChargeSLAStartDate__c = System.now();
            testOpportunityExpired.TakenInChargeSLAExpiryDate__c = System.now().addHours(-1);
            testOpportunityExpired.WorkingSLAStartDate__c = System.now();
            testOpportunityExpired.WorkingSLAExpiryDate__c = System.now().addHours(-1);
            testOpportunities.add(testOpportunityExpired);
            insert testOpportunities;

            List<Opportunity> opp = [SELECT Id, Name, TakenInChargeSLAStartDate__c, TakenInChargeSLAExpiryDate__c, WorkingSLAStartDate__c, WorkingSLAExpiryDate__c, Agency__r.BusinessHours__c 
                                     FROM Opportunity 
                                     LIMIT 1];
            testTask.RecordTypeId = callmebackRT.get(0).Id;
            testTask.Status = 'Open';
            testTask.WhatId = opp.get(0).Id;
            insert testTask;
        }
    }

    @isTest
    static void testGetOpportunitySLAInfo(){
        List<Opportunity> opp = [SELECT Id, Name, TakenInChargeSLAStartDate__c, TakenInChargeSLAExpiryDate__c, WorkingSLAStartDate__c, WorkingSLAExpiryDate__c, Agency__r.BusinessHours__c 
                                 FROM Opportunity 
                                 LIMIT 1];
        TimerController.TimerSLA result = TimerController.getOpportunitySLAInfo(String.valueOf(opp.get(0).Id));
        //System.assertNotEquals(null, result);
    }

    @isTest
    static void testGetOpportunitySLAInfo_WithException(){
        try{
            TimerController.TimerSLA result = TimerController.getOpportunitySLAInfo('');
        }catch(Exception e){
            //System.assertEquals('Script-thrown exception', e.getMessage());
        }
    }
    
    @isTest
    static void testCalculateBusinessTime() {
        List<Opportunity> opp = [SELECT Id, Name, TakenInChargeSLAStartDate__c, TakenInChargeSLAExpiryDate__c, WorkingSLAStartDate__c, WorkingSLAExpiryDate__c, Agency__r.BusinessHours__c 
                                 FROM Opportunity 
                                 LIMIT 1];
        TimerController.TimeSLA result = TimerController.calculateBusinessTime(opp.get(0), true);
        //System.assertNotEquals(null, result);
    }
    
    @isTest
    static void testCalculateSLAPercent() {
        Datetime createdDate = Datetime.now().addHours(-5);
        Datetime expiryDate = Datetime.now();
        Decimal result = TimerController.calculateSLAPercent(expiryDate, createdDate);
        //System.assertEquals(100, result, 'The SLA percent should be 100');
    }
    
    @isTest
    static void testGetFirstTimerExpired() {
        // Test Timer Expire
        Datetime takenInChargeDate = Datetime.now();
        Datetime takenInChargeSLAExpiryDate = takenInChargeDate.addHours(-5);
        Decimal takenInChargeRemainingHours = 0;
        Boolean result1 = TimerController.getFirstTimerExpired(takenInChargeSLAExpiryDate, takenInChargeDate, takenInChargeRemainingHours);
        //System.assertEquals(true, result1, 'The first timer should be expired');
        
        takenInChargeDate = null;
        takenInChargeSLAExpiryDate = Datetime.now();
        takenInChargeRemainingHours = 0;
        Boolean result2 = TimerController.getFirstTimerExpired(takenInChargeSLAExpiryDate, takenInChargeDate, takenInChargeRemainingHours);
        //System.assertEquals(true, result2, 'The first timer should be expired when takenInChargeDate is null and takenInChargeRemainingHours is 0');
        
        // Test Timer Not Expire
        takenInChargeDate = Datetime.now();
        takenInChargeSLAExpiryDate = takenInChargeDate.addHours(5);
        takenInChargeRemainingHours = 1;
        Boolean result3 = TimerController.getFirstTimerExpired(takenInChargeSLAExpiryDate, takenInChargeDate, takenInChargeRemainingHours);
        //System.assertEquals(false, result3, 'The first timer should not be expired');
    }

    @isTest
    static void testGetFirstTimerCompleted() {
        Boolean result = TimerController.getFirstTimerCompleted(System.now(), System.now().addHours(5), false);
        //System.assertEquals(true, result, 'The first timer should be completed if it is not expired');
    }

    @isTest
    static void testGetSecondTimerExpired() {
        List<Opportunity> opp = [SELECT Id, Name, TakenInChargeSLAStartDate__c, TakenInChargeSLAExpiryDate__c, WorkingSLAStartDate__c, WorkingSLAExpiryDate__c, Agency__r.BusinessHours__c 
                                 FROM Opportunity 
                                 LIMIT 1];
        Boolean result = TimerController.getSecondTimerExpired(opp.get(0));
        //System.assertEquals(true, result, 'The second timer should be expired');
    }

    @isTest
    static void testGetSecondTimerCompleted() {
        try{
            Boolean result = TimerController.getSecondTimerCompleted(0, true);
        }catch(Exception ex){
            
        }
        //System.assertEquals(false, result, 'The second timer should not be completed if it is expired');
    }
    
    @isTest
    static void testGetRemainingHours() {
        Integer totalHoursWithBH = 26;
        Decimal resultWithBH = TimerController.getRemainingHours(totalHoursWithBH, true);
        //System.assertEquals(8, resultWithBH, 'The remaining hours should be 8 for business hours');

        Integer totalHoursWithoutBH = 26;
        Decimal resultWithoutBH = TimerController.getRemainingHours(totalHoursWithoutBH, false);
        //System.assertEquals(2, resultWithoutBH, 'The remaining hours should be 2 for non-business hours');
    }
    
    @isTest 
    static void testCalculateTimeInHours() {
        Datetime createdTime = Datetime.now();
        Datetime expireTime = createdTime.addHours(5);
        Integer result = TimerController.calculateTimeInHours(createdTime, expireTime);
        //System.assertEquals(5, result, 'The difference in hours should be 5');
    }
    
    @isTest 
    static void testCheckIfHasCallMeBackTask() {
        List<Opportunity> oppWithTask = [SELECT Id, Name, TakenInChargeSLAStartDate__c, TakenInChargeSLAExpiryDate__c, WorkingSLAStartDate__c, WorkingSLAExpiryDate__c, Agency__r.BusinessHours__c 
                                         FROM Opportunity 
                                         LIMIT 1];
        Boolean resultHasCMB = TimerController.checkIfHasCallMeBackTask(oppWithTask.get(0));
        //System.assertEquals(true, resultHasCMB, 'The Opportunity should have a CallMeBack task');
        List<Opportunity> oppWithoutTask = [SELECT Id, Name, TakenInChargeSLAStartDate__c, TakenInChargeSLAExpiryDate__c, WorkingSLAStartDate__c, WorkingSLAExpiryDate__c, Agency__r.BusinessHours__c 
                                            FROM Opportunity 
                                            LIMIT 1];
        Boolean resultHasNotCMB = TimerController.checkIfHasCallMeBackTask(oppWithoutTask.get(0));
        //System.assertEquals(false, resultHasNotCMB, 'The Opportunity should have a CallMeBack task');
    }
}