public without sharing class ConiAssignmentUtility {
    private static final Id ID_ACCOUNT_AGENCY = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AccountAgency').getRecordTypeId();
    private static final Id ID_AGENCY_SOCIETY = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName().get('AgencySociety').getRecordTypeId();
    private static final Id ID_ATTIVITA_CONTATTO = Schema.SObjectType.Case.getRecordTypeInfosByDeveloperName().get('AttivitaContatto').getRecordTypeId();

    public static List<FinServ__AccountAccountRelation__Share> getAccountAccountRelationShares(Set<String> setOldUser, Set<String> seAccountId, Set<String> seAgencyId) {
        return [
            SELECT Id
            FROM FinServ__AccountAccountRelation__Share
            WHERE
                ParentId IN (
                    SELECT Id
                    FROM FinServ__AccountAccountRelation__c
                    WHERE RecordTypeId = :ID_ACCOUNT_AGENCY AND FinServ__Account__c IN :seAccountId AND FinServ__RelatedAccount__c IN :seAgencyId
                )
                AND UserOrGroupId IN :setOldUser
                AND RowCause = 'Manual'
        ];
    }

    public static Set<Id> getRelatedAccountIds(Set<String> seAgencyId) {
        Set<Id> idRelatedAccount = new Set<Id>();
        List<FinServ__AccountAccountRelation__c> listAAR = [
            SELECT FinServ__RelatedAccount__c
            FROM FinServ__AccountAccountRelation__c
            WHERE RecordTypeId = :ID_AGENCY_SOCIETY AND FinServ__Account__c IN :seAgencyId AND (FinServ__StartDate__c = NULL OR FinServ__StartDate__c < TODAY) AND (FinServ__EndDate__c = NULL OR FinServ__EndDate__c > TODAY)
        ];
        for (FinServ__AccountAccountRelation__c aar : listAAR) {
            idRelatedAccount.add(aar.FinServ__RelatedAccount__c);
        }
        return idRelatedAccount;
    }

    public static List<AccountDetailsNPI__Share> getAccountDetailsNPIshares(Set<String> setOldUser, Set<Id> idRelatedAccount) {
        return [
            SELECT Id
            FROM AccountDetailsNPI__Share
            WHERE
                ParentId IN (
                    SELECT AccountDetailsNPI__c
                    FROM AccountDetails__c
                    WHERE Relation__c IN :idRelatedAccount
                )
                AND UserOrGroupId IN :setOldUser
                AND RowCause = 'Manual'
        ];
    }

    public static List<CaseShare> buildCaseShares(List<Case> cases, Set<String> userIds) {
        List<CaseShare> shares = new List<CaseShare>();
        for (String idUser : userIds) {
            for (Case c : cases) {
                CaseShare share = new CaseShare();
                share.CaseId = c.Id;
                share.UserOrGroupId = idUser;
                share.CaseAccessLevel = 'Read';
                share.RowCause = Schema.CaseShare.RowCause.Manual;
                shares.add(share);
            }
        }
        return shares;
    }

    public static List<OpportunityShare> buildOpportunityShares(List<Opportunity> opps, Set<String> userIds) {
        List<OpportunityShare> shares = new List<OpportunityShare>();
        for (String idUser : userIds) {
            for (Opportunity opp : opps) {
                OpportunityShare share = new OpportunityShare();
                share.OpportunityId = opp.Id;
                share.UserOrGroupId = idUser;
                share.OpportunityAccessLevel = 'Read';
                share.RowCause = Schema.OpportunityShare.RowCause.Manual;
                shares.add(share);
            }
        }
        return shares;
    }

    public static List<SObject> buildCaseSharesForAgencyChange(Map<Id, Id> setIdCaseWithNewAgency, Map<Id, String> mapAgencyIdWithExternalId, Map<String, Id> mapGroupDeveloperNameWithId) {
        List<SObject> listShareToInsert = new List<SObject>();
        for (Id idCase : setIdCaseWithNewAgency.keySet()) {
            Id idAgency = setIdCaseWithNewAgency.get(idCase);
            String nameGroupAgency = mapAgencyIdWithExternalId.get(idAgency);
            Id idGroupAgency = mapGroupDeveloperNameWithId.get(nameGroupAgency);
            if (idGroupAgency != null) {
                CaseShare share = new CaseShare();
                share.CaseId = idCase;
                share.UserOrGroupId = idGroupAgency;
                share.CaseAccessLevel = 'Edit';
                listShareToInsert.add(share);
            }
        }
        return listShareToInsert;
    }

    public static List<SObject> buildOpportunitySharesForAgencyChange(Map<Id, Id> setIdOpportunityWithNewAgency, Map<Id, String> mapAgencyIdWithExternalId, Map<String, Id> mapGroupDeveloperNameWithId) {
        List<SObject> listShareToInsert = new List<SObject>();
        for (Id idOpportunity : setIdOpportunityWithNewAgency.keySet()) {
            Id idAgency = setIdOpportunityWithNewAgency.get(idOpportunity);
            String nameGroupAgency = mapAgencyIdWithExternalId.get(idAgency);
            Id idGroupAgency = mapGroupDeveloperNameWithId.get(nameGroupAgency);
            if (idGroupAgency != null) {
                OpportunityShare share = new OpportunityShare();
                share.OpportunityId = idOpportunity;
                share.UserOrGroupId = idGroupAgency;
                share.OpportunityAccessLevel = 'Edit';
                listShareToInsert.add(share);
            }
        }
        return listShareToInsert;
    }

    public static Set<Id> extractOldAgencyIds(Map<Id, SObject> mapAttivitaOldAgency) {
        Set<Id> setOldAgencyId = new Set<Id>();
        for (Id idObjOld : mapAttivitaOldAgency.keySet()) {
            setOldAgencyId.add((Id) mapAttivitaOldAgency.get(idObjOld).get('Agency__c'));
        }
        return setOldAgencyId;
    }

    public static Set<String> extractOldExternalIds(Set<Id> setOldAgencyId) {
        Set<String> setOldExternalId = new Set<String>();
        for (Account acc : [SELECT Id, ExternalId__c FROM Account WHERE Id = :setOldAgencyId]) {
            setOldExternalId.add(System.Label.AgencyExtIdAAR + acc.ExternalId__c);
        }
        return setOldExternalId;
    }

    public static Set<Id> extractGroupIdsByDeveloperName(Set<String> setOldExternalId) {
        return new Map<Id, Group>([SELECT Id FROM Group WHERE DeveloperName IN :setOldExternalId]).keySet();
    }

    public static void extractNewAgencyMaps(List<SObject> listAttivitaNewAgency, Map<Id, Id> setIdCaseWithNewAgency, Map<Id, Id> setIdOpportunityWithNewAgency, Set<Id> setIdNewAgency) {
        for (SObject sobj : listAttivitaNewAgency) {
            if ('Opportunity'.equalsIgnoreCase(sObj.getSObjectType().getDescribe().getName())) {
                setIdOpportunityWithNewAgency.put((Id) sObj.get('Id'), (Id) sObj.get('Agency__c'));
                setIdNewAgency.add((Id) sObj.get('Agency__c'));
            } else if ('Case'.equalsIgnoreCase(sObj.getSObjectType().getDescribe().getName())) {
                setIdCaseWithNewAgency.put((Id) sObj.get('Id'), (Id) sObj.get('Agency__c'));
                setIdNewAgency.add((Id) sObj.get('Agency__c'));
            }
        }
    }

    public static Map<Id, String> extractAgencyIdWithExternalId(Set<Id> setIdNewAgency) {
        Map<Id, String> mapAgencyIdWithExternalId = new Map<Id, String>();
        for (Account acc : [SELECT Id, ExternalId__c FROM Account WHERE Id = :setIdNewAgency]) {
            mapAgencyIdWithExternalId.put(acc.Id, System.Label.AgencyExtIdAAR + acc.ExternalId__c);
        }
        return mapAgencyIdWithExternalId;
    }

    public static Map<String, Id> extractGroupDeveloperNameWithId(Map<Id, String> mapAgencyIdWithExternalId) {
        Map<String, Id> mapGroupDeveloperNameWithId = new Map<String, Id>();
        for (Group gr : [SELECT Id, DeveloperName FROM Group WHERE DeveloperName IN :mapAgencyIdWithExternalId.values()]) {
            mapGroupDeveloperNameWithId.put(gr.DeveloperName, gr.Id);
        }
        return mapGroupDeveloperNameWithId;
    }

    public static List<FinServ__AccountAccountRelation__c> getAccountAccountRelation(Set<String> seAccountId, Set<String> seAgencyId) {
        return [
            SELECT Id
            FROM FinServ__AccountAccountRelation__c
            WHERE RecordTypeId = :ID_ACCOUNT_AGENCY AND FinServ__Account__c IN :seAccountId AND FinServ__RelatedAccount__c IN :seAgencyId
        ];
    }

    public static List<CaseShare> getCaseSharesToDelete(Map<Id, Id> setIdCaseWithNewAgency, Set<Id> setIdGroupResp) {
        return [SELECT Id FROM CaseShare WHERE CaseId IN :setIdCaseWithNewAgency.keySet() AND UserOrGroupId IN :setIdGroupResp AND RowCause = 'Manual'];
    }

    public static List<OpportunityShare> getOpportunitySharesToDelete(Map<Id, Id> setIdOpportunityWithNewAgency, Set<Id> setIdGroupResp) {
        return [SELECT Id FROM OpportunityShare WHERE OpportunityId IN :setIdOpportunityWithNewAgency.keySet() AND UserOrGroupId IN :setIdGroupResp AND RowCause = 'Manual'];
    }

    public static List<Case> getCaseClosed(Set<String> seAccountId, Set<String> seAgencyId) {
        return [
            SELECT Id
            FROM Case
            WHERE AccountId IN :seAccountId AND Agency__c IN :seAgencyId AND Status = 'Closed' /*AND RecordTypeId = :ID_ATTIVITA_CONTATTO*/
        ];
    }

    public static List<Opportunity> getOpportunityClosed(Set<String> seAccountId, Set<String> seAgencyId) {
        return [
            SELECT Id
            FROM Opportunity
            WHERE AccountId IN :seAccountId AND Agency__c IN :seAgencyId AND StageName = 'Chiuso'
        ];
    }
}
