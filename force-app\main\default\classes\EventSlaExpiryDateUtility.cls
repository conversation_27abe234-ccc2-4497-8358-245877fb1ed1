public without sharing class EventSlaExpiryDateUtility {
     @InvocableMethod
    public static List<String>  getTipoSoggetto(List<String> accAccRelLs) {
        String accAccRel = accAccRelLs[0];
        String tipoSoggetto = '';
        //Opportunity opp = [SELECT AccountId, Agency__c FROM Opportunity WHERE Id =: opportunityIds[0]];
        /* String accountId = opp.AccountId;
        String agencyId = opp.Agency__c; */
        //FinServ__AccountAccountRelation__c accAccRel = [SELECT Id, FinServ__RelatedAccount__c, FinServ__Account__c FROM FinServ__AccountAccountRelation__c WHERE FinServ__RelatedAccount__c =: agencyId AND FinServ__Account__c =: accountId AND FinServ__Role__r.FinServ__InverseRole__c = 'Agenzia'];
 
        List<Asset> assetList = [SELECT Key__c, Value__c from Asset WHERE MasterRecordId__c =: accAccRel AND  Key__c = 'PP_AGENZIA_SOCIETA_STATO'];
 
        if (!assetList.isEmpty()) {
            tipoSoggetto = assetList[0].Value__c;
        } else {
            tipoSoggetto = 'Prospect Puro';
        }
        List<String> returnedList = new List<String>{tipoSoggetto};
        return returnedList;
    } 
   
  


}