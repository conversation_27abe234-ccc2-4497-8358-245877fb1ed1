@isTest
public class QueueableUserProvisioningTest {

    @isTest
    static void test_coverage(){

        NetworkUser__c nu = new NetworkUser__c();
        nu.FiscalCode__c = 'TEST';
        nu.NetworkUser__c = 'TEST';
        insert nu;

        QueueableUserProvisioning.InputWrapper input = new QueueableUserProvisioning.InputWrapper();
        input.fiscalCode = 'TEST';
        input.networkIdentifier = 'TEST';
        input.permissionSets = 'TEST';

        Test.startTest();

        try{
            System.enqueueJob(new QueueableUserProvisioning(new List<QueueableUserProvisioning.InputWrapper>{input}));
        }catch(Exception ex){}

        Test.stopTest();

    }

}